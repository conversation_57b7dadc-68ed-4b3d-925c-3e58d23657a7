#!/bin/bash

# Script to add responsive-universal.css to all projects
echo "Adding responsive-universal.css to all projects..."

# Array of project directories
projects=(
    "projects/autoexpert-krakow"
    "projects/design-by-michal"
    "projects/english-express"
    "projects/fitgenius-ai-coach"
    "projects/fitlife-pro"
    "projects/gastro-ai-restaurant-optimizer"
    "projects/smart-event-planner"
    "projects/style-studio-warszawa"
    "projects/technomax"
    "projects/terapia-dostepna"
    "projects/zmien-zycie-z-anna"
)

# Function to add responsive CSS to HTML file
add_responsive_css() {
    local html_file="$1"
    local project_dir="$2"
    
    # Calculate relative path to orchestrator-full-v2
    local relative_path="../../orchestrator-full-v2/assets/css/responsive-universal.css"
    
    # Check if file exists and doesn't already contain responsive-universal.css
    if [[ -f "$html_file" ]] && ! grep -q "responsive-universal.css" "$html_file"; then
        echo "Adding responsive CSS to: $html_file"
        
        # Find the last CSS link and add our responsive CSS after it
        sed -i.bak '/rel="stylesheet"/a\
    <link rel="stylesheet" href="'"$relative_path"'">
' "$html_file"
        
        # Remove backup file
        rm "${html_file}.bak" 2>/dev/null
        
        echo "✓ Added to $html_file"
    else
        echo "⚠ Skipped $html_file (not found or already contains responsive CSS)"
    fi
}

# Process each project
for project in "${projects[@]}"; do
    echo ""
    echo "Processing project: $project"
    
    if [[ -d "$project" ]]; then
        # Find all HTML files in the project
        find "$project" -name "*.html" -type f | while read -r html_file; do
            add_responsive_css "$html_file" "$project"
        done
    else
        echo "⚠ Project directory not found: $project"
    fi
done

echo ""
echo "✅ Responsive CSS addition completed!"
echo ""
echo "Summary:"
echo "- Added responsive-universal.css to all HTML files in projects"
echo "- Each project now has complete responsive coverage from 320px to 8K"
echo "- Breakpoints: 320px, 375px, 414px, 480px, 768px, 1024px, 1200px, 1440px, 1920px, 2560px, 3840px, 7680px+"
echo ""
echo "Test the responsiveness by:"
echo "1. Opening any project in browser"
echo "2. Using browser dev tools to test different screen sizes"
echo "3. Checking on actual devices"
