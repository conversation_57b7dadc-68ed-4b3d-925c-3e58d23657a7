#!/bin/bash

# ===== PIXELGARAGE.SPACE COMPLETE DEPLOYMENT SCRIPT =====
# Complete setup, configuration and live deployment for pixelgarage.space
# Author: Robert <PERSON> PixelGarage Founder
# Server: natan359.mikrus.xyz:10359
# IPv6: 2a01:4f9:3a:1288::359

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Configuration
DOMAIN="pixelgarage.space"
WWW_DOMAIN="www.pixelgarage.space"
SERVER_PATH="/var/www/pixelgarage.space"
NGINX_CONFIG="/etc/nginx/sites-available/pixelgarage.conf"
BACKUP_DIR="/var/backups/pixelgarage-$(date +%Y%m%d-%H%M%S)"
SOURCE_PATH="$HOME/hub/orchestrator-full-v2"

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

echo "🚀 PixelGarage.space Complete Deployment Script"
echo "=============================================="
echo "Domain: $DOMAIN"
echo "Server: mikr.us (natan359.mikrus.xyz)"
echo "IPv6: 2a01:4f9:3a:1288::359"
echo "Source: $SOURCE_PATH"
echo ""

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root"
   exit 1
fi

# Step 1: System preparation
prepare_system() {
    log_step "Preparing system and installing packages..."
    
    # Update system
    apt update && apt upgrade -y
    
    # Install required packages
    apt install -y nginx certbot python3-certbot-nginx curl wget unzip git
    
    # Enable and start nginx
    systemctl enable nginx
    systemctl start nginx
    
    log_success "System prepared and packages installed"
}

# Step 2: Create backup
create_backup() {
    log_step "Creating backup of existing configuration..."
    
    mkdir -p /var/backups
    
    # Backup existing nginx config
    if [ -f "$NGINX_CONFIG" ]; then
        cp "$NGINX_CONFIG" "/var/backups/pixelgarage.conf.backup.$(date +%Y%m%d-%H%M%S)"
        log_info "Existing nginx config backed up"
    fi
    
    # Backup existing website files
    if [ -d "$SERVER_PATH" ]; then
        cp -r "$SERVER_PATH" "$BACKUP_DIR"
        log_info "Existing website files backed up to: $BACKUP_DIR"
    fi
    
    log_success "Backup completed"
}

# Step 3: Prepare directories
prepare_directories() {
    log_step "Preparing directory structure..."
    
    # Create main directory
    mkdir -p "$SERVER_PATH"
    mkdir -p "$SERVER_PATH/assets/css"
    mkdir -p "$SERVER_PATH/assets/js"
    mkdir -p "$SERVER_PATH/services"
    mkdir -p "$SERVER_PATH/gallery"
    mkdir -p "$SERVER_PATH/academy"
    mkdir -p "$SERVER_PATH/communication"
    mkdir -p "$SERVER_PATH/projects"
    
    # Create certbot directory
    mkdir -p /var/www/certbot
    
    log_success "Directory structure prepared"
}

# Step 4: Deploy files
deploy_files() {
    log_step "Deploying files to production directory..."
    
    if [ -d "$SOURCE_PATH" ]; then
        # Copy all files from source to production
        cp -r "$SOURCE_PATH"/* "$SERVER_PATH/"
        log_info "Files copied from $SOURCE_PATH to $SERVER_PATH"
        
        # Remove deployment scripts from production
        rm -f "$SERVER_PATH"/*.sh
        rm -f "$SERVER_PATH"/*.md
        
        log_info "Deployment scripts removed from production"
    else
        log_error "Source files not found at $SOURCE_PATH"
        log_error "Please ensure your application files are in $SOURCE_PATH"
        exit 1
    fi
    
    # Set proper permissions
    chown -R www-data:www-data "$SERVER_PATH"
    find "$SERVER_PATH" -type d -exec chmod 755 {} \;
    find "$SERVER_PATH" -type f -exec chmod 644 {} \;
    
    log_success "Files deployed and permissions set"
}

# Step 5: Configure nginx
configure_nginx() {
    log_step "Configuring nginx for pixelgarage.space..."
    
    cat > "$NGINX_CONFIG" << 'EOF'
# PixelGarage.space Configuration
server {
    listen 80;
    listen [::]:80;
    server_name pixelgarage.space;

    # Let's Encrypt challenge
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    root /var/www/pixelgarage.space;
    index index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data: blob:; img-src 'self' https: data: blob:; font-src 'self' https: data:;" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types 
        text/plain 
        text/css 
        text/xml 
        text/javascript 
        application/javascript 
        application/xml+rss 
        application/json
        application/xml
        image/svg+xml;

    # Cache static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg|webp)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Main location
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Services section
    location /services/ {
        try_files $uri $uri/ /services/index.html;
    }

    # Gallery section
    location /gallery/ {
        try_files $uri $uri/ /gallery/index.html;
    }

    # Academy section
    location /academy/ {
        try_files $uri $uri/ /academy/index.html;
    }

    # Communication section
    location /communication/ {
        try_files $uri $uri/ /communication/index.html;
    }

    # Projects section
    location /projects/ {
        try_files $uri $uri/ =404;
    }

    # Error pages
    error_page 404 /index.html;
    error_page 500 502 503 504 /index.html;

    # Logs
    access_log /var/log/nginx/pixelgarage.space.access.log;
    error_log /var/log/nginx/pixelgarage.space.error.log;
}
EOF

    # Enable the site
    ln -sf "$NGINX_CONFIG" /etc/nginx/sites-enabled/pixelgarage.conf
    
    # Remove conflicting default site if exists
    rm -f /etc/nginx/sites-enabled/default
    
    # Test nginx configuration
    nginx -t
    
    # Reload nginx
    systemctl reload nginx
    
    log_success "Nginx configured and reloaded"
}

# Step 6: Setup SSL (only for main domain initially)
setup_ssl() {
    log_step "Setting up SSL certificate for main domain..."
    
    # Get SSL certificate for main domain only
    log_info "Requesting SSL certificate for $DOMAIN..."
    
    if certbot --nginx -d "$DOMAIN" --non-interactive --agree-tos --email <EMAIL>; then
        log_success "SSL certificate installed for $DOMAIN"
        
        # Update nginx config for HTTPS redirect
        update_nginx_for_ssl
    else
        log_warning "SSL certificate installation failed. This is normal if DNS is not configured yet."
        log_info "You can run SSL setup later with: certbot --nginx -d $DOMAIN"
    fi
}

# Step 7: Update nginx for HTTPS
update_nginx_for_ssl() {
    log_step "Updating nginx configuration for HTTPS..."
    
    cat > "$NGINX_CONFIG" << 'EOF'
# PixelGarage.space Configuration with SSL
server {
    listen 80;
    listen [::]:80;
    server_name pixelgarage.space;

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name pixelgarage.space;

    ssl_certificate /etc/letsencrypt/live/pixelgarage.space/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/pixelgarage.space/privkey.pem;

    root /var/www/pixelgarage.space;
    index index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data: blob:; img-src 'self' https: data: blob:; font-src 'self' https: data:;" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types 
        text/plain 
        text/css 
        text/xml 
        text/javascript 
        application/javascript 
        application/xml+rss 
        application/json
        application/xml
        image/svg+xml;

    # Cache static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg|webp)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Main location
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Services section
    location /services/ {
        try_files $uri $uri/ /services/index.html;
    }

    # Gallery section
    location /gallery/ {
        try_files $uri $uri/ /gallery/index.html;
    }

    # Academy section
    location /academy/ {
        try_files $uri $uri/ /academy/index.html;
    }

    # Communication section
    location /communication/ {
        try_files $uri $uri/ /communication/index.html;
    }

    # Projects section
    location /projects/ {
        try_files $uri $uri/ =404;
    }

    # Error pages
    error_page 404 /index.html;
    error_page 500 502 503 504 /index.html;

    # Logs
    access_log /var/log/nginx/pixelgarage.space.access.log;
    error_log /var/log/nginx/pixelgarage.space.error.log;
}
EOF

    # Test and reload nginx
    nginx -t && systemctl reload nginx
    
    log_success "Nginx updated for HTTPS"
}

# Step 8: Verify deployment
verify_deployment() {
    log_step "Verifying deployment..."

    # Check source directory
    if [ -d "$SOURCE_PATH" ]; then
        log_success "✓ Source directory found: $SOURCE_PATH"
    else
        log_warning "⚠ Source directory not found: $SOURCE_PATH"
    fi

    # Check if files exist in production
    if [ -f "$SERVER_PATH/index.html" ]; then
        log_success "✓ Main index.html found"
    else
        log_error "✗ Main index.html missing"
        return 1
    fi

    # Check essential directories
    for dir in assets services gallery academy communication; do
        if [ -d "$SERVER_PATH/$dir" ]; then
            log_success "✓ $dir directory found"
        else
            log_warning "⚠ $dir directory missing"
        fi
    done

    # Check translation system
    if [ -f "$SERVER_PATH/assets/js/translations.js" ]; then
        log_success "✓ Translation system found"
    else
        log_warning "⚠ Translation system missing"
    fi

    # Test HTTP response
    if curl -s -o /dev/null -w "%{http_code}" "http://localhost" | grep -q "200"; then
        log_success "✓ Website responding on HTTP"
    else
        log_error "✗ Website not responding"
    fi

    # Check nginx status
    if systemctl is-active --quiet nginx; then
        log_success "✓ Nginx is running"
    else
        log_error "✗ Nginx is not running"
    fi

    # Check SSL certificate
    if [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]; then
        log_success "✓ SSL certificate found"
    else
        log_warning "⚠ SSL certificate not found (normal if DNS not configured)"
    fi

    log_success "Deployment verification completed"
}

# Step 9: Show DNS configuration instructions
show_dns_instructions() {
    log_step "DNS Configuration Instructions"
    echo ""
    echo "🌐 CONFIGURE CLOUDFLARE DNS:"
    echo "=============================="
    echo ""
    echo "1. Go to https://cloudflare.com and add domain: pixelgarage.space"
    echo ""
    echo "2. Add these DNS records in Cloudflare:"
    echo ""
    echo "   Type: AAAA"
    echo "   Name: @"
    echo "   Content: 2a01:4f9:3a:1288::359"
    echo "   Proxy: Enabled (🟠 orange cloud)"
    echo ""
    echo "   Type: AAAA"
    echo "   Name: www"
    echo "   Content: 2a01:4f9:3a:1288::359"
    echo "   Proxy: Enabled (🟠 orange cloud)"
    echo ""
    echo "3. Configure SSL in Cloudflare:"
    echo "   - SSL/TLS → Overview: Set to 'Full (strict)'"
    echo "   - SSL/TLS → Edge Certificates: Enable 'Always Use HTTPS'"
    echo ""
    echo "4. Change nameservers in home.pl to Cloudflare nameservers"
    echo ""
    echo "5. Wait 2-24 hours for DNS propagation"
    echo ""
    echo "6. After DNS propagation, add www subdomain to SSL:"
    echo "   certbot --nginx -d pixelgarage.space -d www.pixelgarage.space --expand"
    echo ""
}

# Step 10: Show useful commands
show_useful_commands() {
    echo "🔧 USEFUL COMMANDS:"
    echo "==================="
    echo ""
    echo "Check website status:"
    echo "  curl -I http://pixelgarage.space"
    echo "  curl -I https://pixelgarage.space"
    echo ""
    echo "View logs:"
    echo "  tail -f /var/log/nginx/pixelgarage.space.access.log"
    echo "  tail -f /var/log/nginx/pixelgarage.space.error.log"
    echo ""
    echo "Nginx management:"
    echo "  systemctl status nginx"
    echo "  systemctl restart nginx"
    echo "  nginx -t"
    echo ""
    echo "SSL management:"
    echo "  certbot certificates"
    echo "  certbot renew --dry-run"
    echo "  certbot --nginx -d pixelgarage.space -d www.pixelgarage.space --expand"
    echo ""
    echo "File management:"
    echo "  ls -la /var/www/pixelgarage.space/"
    echo "  chown -R www-data:www-data /var/www/pixelgarage.space"
    echo ""
}

# Main deployment function
main() {
    log_info "Starting complete PixelGarage.space deployment..."
    echo ""

    # System preparation
    prepare_system

    # Deployment steps
    create_backup
    prepare_directories
    deploy_files
    configure_nginx

    # SSL setup (will fail gracefully if DNS not configured)
    setup_ssl

    # Verification
    verify_deployment

    # Instructions
    show_dns_instructions
    show_useful_commands

    echo ""
    log_success "🎉 PixelGarage.space deployment completed!"
    echo ""
    echo "📍 Current status:"
    echo "   • Website files deployed to: $SERVER_PATH"
    echo "   • Nginx configured for: $DOMAIN"
    echo "   • SSL certificate: $([ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ] && echo "✓ Installed" || echo "⚠ Pending DNS")"
    echo ""
    echo "📋 Next steps:"
    echo "   1. Configure Cloudflare DNS (see instructions above)"
    echo "   2. Change nameservers in home.pl"
    echo "   3. Wait for DNS propagation"
    echo "   4. Test website: https://pixelgarage.space"
    echo "   5. Add www subdomain to SSL after DNS propagation"
    echo ""
    echo "🌟 Your PixelGarage platform is ready to go live!"
    echo ""
}

# Confirmation before running
echo "This script will:"
echo "  ✓ Install and configure nginx"
echo "  ✓ Deploy PixelGarage files from $SOURCE_PATH"
echo "  ✓ Configure SSL certificate"
echo "  ✓ Set up complete production environment"
echo ""
read -p "Do you want to proceed? (y/n): " confirm

if [[ ! $confirm =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 0
fi

# Run main deployment
main "$@"
