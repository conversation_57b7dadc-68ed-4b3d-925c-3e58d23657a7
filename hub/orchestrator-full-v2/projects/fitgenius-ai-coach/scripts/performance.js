/**
 * FitGenius.pl - Performance Optimization Engine
 * Enterprise-grade performance monitoring and optimization
 */

const PerformanceOptimizer = {
    // Performance metrics
    metrics: {
        loadTime: 0,
        domContentLoaded: 0,
        firstPaint: 0,
        firstContentfulPaint: 0,
        largestContentfulPaint: 0,
        cumulativeLayoutShift: 0,
        firstInputDelay: 0,
        timeToInteractive: 0
    },
    
    // Performance budgets
    budgets: {
        loadTime: 3000,        // 3 seconds
        firstPaint: 1000,      // 1 second
        firstContentfulPaint: 1500, // 1.5 seconds
        largestContentfulPaint: 2500, // 2.5 seconds
        cumulativeLayoutShift: 0.1,   // CLS threshold
        firstInputDelay: 100,  // 100ms
        scriptSize: 250000,    // 250KB
        cssSize: 100000,       // 100KB
        imageSize: 500000      // 500KB per image
    },
    
    // Optimization state
    state: {
        isOptimized: false,
        lazyLoadEnabled: true,
        compressionEnabled: true,
        cacheEnabled: true,
        criticalResourcesLoaded: false,
        performanceMode: 'auto' // auto, high, balanced, battery
    },
    
    // Resource cache
    cache: {
        scripts: new Map(),
        styles: new Map(),
        images: new Map(),
        data: new Map()
    },
    
    // Initialize performance optimizer
    init() {
        this.measurePerformanceMetrics();
        this.setupResourceOptimization();
        this.setupLazyLoading();
        this.setupCacheStrategies();
        this.setupPerformanceMonitoring();
        this.setupAdaptiveLoading();
        this.setupImageOptimization();
        this.setupScriptOptimization();
        this.optimizeRenderingPerformance();
        this.setupPerformanceBudgetMonitoring();
        console.log('⚡ Performance Optimizer initialized - Maximum speed achieved!');
    },
    
    measurePerformanceMetrics() {
        // Use Performance Observer API for accurate measurements
        if ('PerformanceObserver' in window) {
            this.setupPerformanceObservers();
        }
        
        // Measure load time
        window.addEventListener('load', () => {
            setTimeout(() => {
                this.collectLoadMetrics();
            }, 0);
        });
        
        // Measure DOM Content Loaded
        document.addEventListener('DOMContentLoaded', () => {
            this.metrics.domContentLoaded = performance.now();
        });
        
        // Measure Time to Interactive
        this.measureTimeToInteractive();
    },
    
    setupPerformanceObservers() {
        // Paint metrics
        const paintObserver = new PerformanceObserver((list) => {
            list.getEntries().forEach((entry) => {
                if (entry.name === 'first-paint') {
                    this.metrics.firstPaint = entry.startTime;
                } else if (entry.name === 'first-contentful-paint') {
                    this.metrics.firstContentfulPaint = entry.startTime;
                }
            });
        });
        paintObserver.observe({ entryTypes: ['paint'] });
        
        // Largest Contentful Paint
        const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            this.metrics.largestContentfulPaint = lastEntry.startTime;
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        
        // Cumulative Layout Shift
        const clsObserver = new PerformanceObserver((list) => {
            list.getEntries().forEach((entry) => {
                if (!entry.hadRecentInput) {
                    this.metrics.cumulativeLayoutShift += entry.value;
                }
            });
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        
        // First Input Delay
        const fidObserver = new PerformanceObserver((list) => {
            list.getEntries().forEach((entry) => {
                this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
            });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
    },
    
    collectLoadMetrics() {
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
            this.metrics.loadTime = navigation.loadEventEnd - navigation.loadEventStart;
            this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
        }
        
        // Analyze performance and apply optimizations
        this.analyzePerformance();
    },
    
    measureTimeToInteractive() {
        // Simplified TTI measurement
        let startTime = performance.now();
        let isInteractive = false;
        
        const checkInteractive = () => {
            if (!isInteractive && document.readyState === 'complete') {
                // Wait for main thread to be idle
                setTimeout(() => {
                    this.metrics.timeToInteractive = performance.now() - startTime;
                    isInteractive = true;
                }, 100);
            } else if (!isInteractive) {
                requestAnimationFrame(checkInteractive);
            }
        };
        
        requestAnimationFrame(checkInteractive);
    },
    
    analyzePerformance() {
        const issues = [];
        
        // Check against performance budgets
        Object.keys(this.budgets).forEach(metric => {
            if (this.metrics[metric] && this.metrics[metric] > this.budgets[metric]) {
                issues.push({
                    metric: metric,
                    actual: this.metrics[metric],
                    budget: this.budgets[metric],
                    severity: this.calculateSeverity(metric)
                });
            }
        });
        
        // Apply optimizations based on issues
        if (issues.length > 0) {
            this.applyPerformanceOptimizations(issues);
        }
        
        // Log performance report
        this.generatePerformanceReport(issues);
    },
    
    calculateSeverity(metric) {
        const actual = this.metrics[metric];
        const budget = this.budgets[metric];
        const ratio = actual / budget;
        
        if (ratio > 2) return 'critical';
        if (ratio > 1.5) return 'high';
        if (ratio > 1.2) return 'medium';
        return 'low';
    },
    
    applyPerformanceOptimizations(issues) {
        issues.forEach(issue => {
            switch (issue.metric) {
                case 'loadTime':
                    this.optimizeLoadTime();
                    break;
                case 'firstPaint':
                    this.optimizeCriticalRenderingPath();
                    break;
                case 'largestContentfulPaint':
                    this.optimizeLCP();
                    break;
                case 'cumulativeLayoutShift':
                    this.optimizeCLS();
                    break;
                case 'firstInputDelay':
                    this.optimizeFID();
                    break;
            }
        });
    },
    
    optimizeLoadTime() {
        // Enable resource compression
        this.enableResourceCompression();
        
        // Defer non-critical resources
        this.deferNonCriticalResources();
        
        // Enable service worker caching
        this.enableServiceWorkerCaching();
    },
    
    optimizeCriticalRenderingPath() {
        // Inline critical CSS
        this.inlineCriticalCSS();
        
        // Preload critical resources
        this.preloadCriticalResources();
        
        // Remove render-blocking resources
        this.removeRenderBlockingResources();
    },
    
    optimizeLCP() {
        // Optimize images
        this.optimizeImages();
        
        // Preload LCP resources
        this.preloadLCPResources();
        
        // Use resource hints
        this.addResourceHints();
    },
    
    optimizeCLS() {
        // Set size attributes for images
        this.setSizeAttributesForImages();
        
        // Reserve space for dynamic content
        this.reserveSpaceForDynamicContent();
        
        // Avoid inserting content above existing content
        this.avoidContentInsertion();
    },
    
    optimizeFID() {
        // Break up long tasks
        this.breakUpLongTasks();
        
        // Use web workers for heavy computations
        this.useWebWorkers();
        
        // Optimize event handlers
        this.optimizeEventHandlers();
    },
    
    setupResourceOptimization() {
        // Implement resource loading strategies
        this.implementResourceLoadingStrategies();
        
        // Setup compression
        this.setupCompression();
        
        // Setup bundling optimization
        this.setupBundlingOptimization();
    },
    
    implementResourceLoadingStrategies() {
        // Preload critical resources
        const criticalResources = [
            'styles/main.css',
            'scripts/main.js',
            'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'
        ];
        
        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource;
            
            if (resource.endsWith('.css')) {
                link.as = 'style';
            } else if (resource.endsWith('.js')) {
                link.as = 'script';
            } else if (resource.includes('fonts')) {
                link.as = 'style';
            }
            
            document.head.appendChild(link);
        });
        
        // Prefetch next-page resources
        this.prefetchNextPageResources();
    },
    
    prefetchNextPageResources() {
        // Prefetch resources for likely next navigation
        const prefetchCandidates = [
            '/workout-plans',
            '/nutrition-plans',
            '/analytics'
        ];
        
        prefetchCandidates.forEach(url => {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = url;
            document.head.appendChild(link);
        });
    },
    
    setupLazyLoading() {
        if (!this.state.lazyLoadEnabled) return;
        
        // Intersection Observer for lazy loading
        const lazyLoadObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.lazyLoadElement(entry.target);
                    lazyLoadObserver.unobserve(entry.target);
                }
            });
        }, {
            rootMargin: '50px 0px',
            threshold: 0.1
        });
        
        // Observe lazy-loadable elements
        document.querySelectorAll('[data-lazy]').forEach(element => {
            lazyLoadObserver.observe(element);
        });
        
        // Lazy load images
        this.setupLazyImageLoading();
        
        // Lazy load modules
        this.setupLazyModuleLoading();
    },
    
    lazyLoadElement(element) {
        const lazyType = element.dataset.lazy;
        
        switch (lazyType) {
            case 'image':
                this.lazyLoadImage(element);
                break;
            case 'video':
                this.lazyLoadVideo(element);
                break;
            case 'iframe':
                this.lazyLoadIframe(element);
                break;
            case 'component':
                this.lazyLoadComponent(element);
                break;
        }
    },
    
    lazyLoadImage(img) {
        const src = img.dataset.src;
        const srcset = img.dataset.srcset;
        
        if (src) {
            img.src = src;
        }
        if (srcset) {
            img.srcset = srcset;
        }
        
        img.classList.add('lazy-loaded');
        img.removeAttribute('data-lazy');
    },
    
    setupLazyImageLoading() {
        // Convert existing images to lazy loading
        document.querySelectorAll('img:not([data-lazy])').forEach(img => {
            if (img.offsetTop > window.innerHeight) {
                const src = img.src;
                img.dataset.src = src;
                img.dataset.lazy = 'image';
                img.src = this.generatePlaceholder(img.width, img.height);
                
                // Re-observe for lazy loading
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            this.lazyLoadImage(entry.target);
                            observer.unobserve(entry.target);
                        }
                    });
                });
                observer.observe(img);
            }
        });
    },
    
    generatePlaceholder(width, height) {
        // Generate a lightweight SVG placeholder
        const svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
            <rect width="100%" height="100%" fill="#f3f4f6"/>
            <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial" font-size="14" fill="#9ca3af">Loading...</text>
        </svg>`;
        
        return `data:image/svg+xml;base64,${btoa(svg)}`;
    },
    
    setupLazyModuleLoading() {
        // Dynamic import for non-critical modules
        const lazyModules = {
            analytics: () => import('./analytics-advanced.js'),
            charts: () => import('./charts.js'),
            animations: () => import('./animations-advanced.js')
        };
        
        // Load modules when needed
        document.addEventListener('moduleNeeded', (e) => {
            const moduleName = e.detail.module;
            if (lazyModules[moduleName]) {
                lazyModules[moduleName]().then(module => {
                    console.log(`📦 Lazy loaded module: ${moduleName}`);
                });
            }
        });
    },
    
    setupCacheStrategies() {
        // Implement various caching strategies
        this.setupBrowserCaching();
        this.setupServiceWorkerCaching();
        this.setupMemoryCache();
        this.setupLocalStorageCache();
    },
    
    setupBrowserCaching() {
        // Add cache headers for static resources
        const cacheableResources = document.querySelectorAll('link[rel="stylesheet"], script[src]');
        
        cacheableResources.forEach(resource => {
            // Add version parameter for cache busting
            const url = new URL(resource.href || resource.src, window.location.href);
            url.searchParams.set('v', this.getResourceVersion());
            
            if (resource.href) {
                resource.href = url.toString();
            } else {
                resource.src = url.toString();
            }
        });
    },
    
    getResourceVersion() {
        // Generate version based on build or timestamp
        return Date.now().toString(36);
    },
    
    setupServiceWorkerCaching() {
        if ('serviceWorker' in navigator) {
            this.registerServiceWorker();
        }
    },
    
    registerServiceWorker() {
        navigator.serviceWorker.register('/sw.js').then(registration => {
            console.log('🔧 Service Worker registered:', registration);
            
            // Update service worker when new version available
            registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                newWorker.addEventListener('statechange', () => {
                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                        this.showUpdateAvailableNotification();
                    }
                });
            });
        }).catch(error => {
            console.log('Service Worker registration failed:', error);
        });
    },
    
    showUpdateAvailableNotification() {
        const notification = document.createElement('div');
        notification.className = 'update-notification';
        notification.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary-color);
            color: white;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            font-weight: 600;
            z-index: 10000;
            box-shadow: var(--shadow-lg);
        `;
        
        notification.innerHTML = `
            <div>Nowa wersja dostępna!</div>
            <button onclick="window.location.reload()" style="margin-left: 1rem; padding: 0.5rem; background: white; color: var(--primary-color); border: none; border-radius: 0.25rem; cursor: pointer;">
                Odśwież
            </button>
        `;
        
        document.body.appendChild(notification);
    },
    
    setupMemoryCache() {
        // In-memory cache for frequently accessed data
        this.cache.data.set('userPreferences', {
            data: null,
            timestamp: 0,
            ttl: 300000 // 5 minutes
        });
        
        this.cache.data.set('workoutTemplates', {
            data: null,
            timestamp: 0,
            ttl: 600000 // 10 minutes
        });
    },
    
    setupLocalStorageCache() {
        // Enhanced localStorage caching with TTL
        const originalSetItem = localStorage.setItem;
        const originalGetItem = localStorage.getItem;
        
        localStorage.setItem = function(key, value, ttl = null) {
            const item = {
                value: value,
                timestamp: Date.now(),
                ttl: ttl
            };
            originalSetItem.call(this, key, JSON.stringify(item));
        };
        
        localStorage.getItem = function(key) {
            const itemStr = originalGetItem.call(this, key);
            if (!itemStr) return null;
            
            try {
                const item = JSON.parse(itemStr);
                
                // Check if item has expired
                if (item.ttl && Date.now() - item.timestamp > item.ttl) {
                    localStorage.removeItem(key);
                    return null;
                }
                
                return item.value;
            } catch (e) {
                // Fallback to original value for backwards compatibility
                return itemStr;
            }
        };
    },
    
    setupPerformanceMonitoring() {
        // Real-time performance monitoring
        this.monitorFrameRate();
        this.monitorMemoryUsage();
        this.monitorNetworkUsage();
        this.monitorBatteryUsage();
    },
    
    monitorFrameRate() {
        let frameCount = 0;
        let lastTime = performance.now();
        
        const measureFrameRate = () => {
            frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - lastTime >= 1000) {
                const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                
                if (fps < 30) {
                    this.handleLowFrameRate();
                }
                
                frameCount = 0;
                lastTime = currentTime;
            }
            
            requestAnimationFrame(measureFrameRate);
        };
        
        requestAnimationFrame(measureFrameRate);
    },
    
    handleLowFrameRate() {
        // Reduce animation complexity
        document.body.classList.add('low-performance');
        
        // Disable non-essential animations
        const style = document.createElement('style');
        style.textContent = `
            .low-performance * {
                animation-duration: 0.1s !important;
                transition-duration: 0.1s !important;
            }
            .low-performance .glass-effect {
                backdrop-filter: none !important;
            }
        `;
        document.head.appendChild(style);
    },
    
    monitorMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                const usageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
                
                if (usageRatio > 0.8) {
                    this.handleHighMemoryUsage();
                }
            }, 5000);
        }
    },
    
    handleHighMemoryUsage() {
        // Clear unnecessary caches
        this.clearNonEssentialCaches();
        
        // Trigger garbage collection (if available)
        if (window.gc) {
            window.gc();
        }
        
        // Reduce memory-intensive operations
        this.reduceMemoryIntensiveOperations();
    },
    
    clearNonEssentialCaches() {
        // Clear image cache
        this.cache.images.clear();
        
        // Clear old data cache entries
        this.cache.data.forEach((value, key) => {
            if (Date.now() - value.timestamp > value.ttl) {
                this.cache.data.delete(key);
            }
        });
    },
    
    reduceMemoryIntensiveOperations() {
        // Reduce animation complexity
        document.body.classList.add('memory-conscious');
        
        // Limit concurrent operations
        this.limitConcurrentOperations();
    },
    
    limitConcurrentOperations() {
        // Implementation would limit simultaneous async operations
        const maxConcurrentOps = 3;
        let currentOps = 0;
        const opQueue = [];
        
        window.queueOperation = function(operation) {
            if (currentOps < maxConcurrentOps) {
                currentOps++;
                operation().finally(() => {
                    currentOps--;
                    if (opQueue.length > 0) {
                        const nextOp = opQueue.shift();
                        window.queueOperation(nextOp);
                    }
                });
            } else {
                opQueue.push(operation);
            }
        };
    },
    
    setupAdaptiveLoading() {
        // Adapt loading strategy based on connection
        if ('connection' in navigator) {
            this.adaptToConnection();
        }
        
        // Adapt based on device capabilities
        this.adaptToDeviceCapabilities();
    },
    
    adaptToConnection() {
        const connection = navigator.connection;
        
        if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
            this.enableLowBandwidthMode();
        } else if (connection.effectiveType === '4g' && connection.downlink > 10) {
            this.enableHighBandwidthMode();
        }
        
        connection.addEventListener('change', () => {
            this.adaptToConnection();
        });
    },
    
    enableLowBandwidthMode() {
        document.body.classList.add('low-bandwidth');
        
        // Disable autoplay videos
        document.querySelectorAll('video[autoplay]').forEach(video => {
            video.removeAttribute('autoplay');
        });
        
        // Use lower quality images
        this.useLowerQualityImages();
        
        // Defer non-critical resources
        this.deferNonCriticalResources();
    },
    
    enableHighBandwidthMode() {
        document.body.classList.add('high-bandwidth');
        
        // Preload more resources
        this.preloadAdditionalResources();
        
        // Enable high-quality features
        this.enableHighQualityFeatures();
    },
    
    useLowerQualityImages() {
        document.querySelectorAll('img').forEach(img => {
            const src = img.src;
            if (src && !src.includes('low-quality')) {
                // Replace with lower quality version
                img.src = src.replace(/\.(jpg|jpeg|png)/, '-low.$1');
            }
        });
    },
    
    preloadAdditionalResources() {
        const additionalResources = [
            'scripts/analytics.js',
            'scripts/animations.js',
            'data/nutrition-database.js'
        ];
        
        additionalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource;
            link.as = 'script';
            document.head.appendChild(link);
        });
    },
    
    setupImageOptimization() {
        // Implement responsive images
        this.implementResponsiveImages();
        
        // Add loading="lazy" to images
        this.addLazyLoadingToImages();
        
        // Optimize image formats
        this.optimizeImageFormats();
    },
    
    implementResponsiveImages() {
        document.querySelectorAll('img:not([srcset])').forEach(img => {
            const src = img.src;
            if (src && !src.startsWith('data:')) {
                // Generate srcset for different screen densities
                const baseName = src.replace(/\.(jpg|jpeg|png|webp)$/, '');
                const extension = src.match(/\.(jpg|jpeg|png|webp)$/)?.[1] || 'jpg';
                
                img.srcset = `
                    ${baseName}.${extension} 1x,
                    ${baseName}@2x.${extension} 2x,
                    ${baseName}@3x.${extension} 3x
                `;
            }
        });
    },
    
    addLazyLoadingToImages() {
        document.querySelectorAll('img:not([loading])').forEach(img => {
            img.loading = 'lazy';
        });
    },
    
    optimizeImageFormats() {
        // Use WebP when supported
        if (this.supportsWebP()) {
            document.querySelectorAll('img').forEach(img => {
                const src = img.src;
                if (src && (src.includes('.jpg') || src.includes('.png'))) {
                    img.src = src.replace(/\.(jpg|jpeg|png)/, '.webp');
                }
            });
        }
    },
    
    supportsWebP() {
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    },
    
    setupScriptOptimization() {
        // Defer non-critical scripts
        this.deferNonCriticalScripts();
        
        // Bundle and minify scripts
        this.optimizeScriptLoading();
        
        // Use script streaming
        this.enableScriptStreaming();
    },
    
    deferNonCriticalScripts() {
        const nonCriticalScripts = [
            'scripts/analytics.js',
            'scripts/gamification.js',
            'scripts/ai-coach.js'
        ];
        
        nonCriticalScripts.forEach(scriptSrc => {
            const script = document.querySelector(`script[src*="${scriptSrc}"]`);
            if (script) {
                script.defer = true;
            }
        });
    },
    
    optimizeScriptLoading() {
        // Load scripts in optimal order
        const loadOrder = [
            'scripts/main.js',
            'scripts/mobile.js',
            'scripts/workout-generator.js',
            'scripts/analytics.js',
            'scripts/animations.js'
        ];
        
        this.loadScriptsInOrder(loadOrder);
    },
    
    loadScriptsInOrder(scripts) {
        let index = 0;
        
        const loadNext = () => {
            if (index >= scripts.length) return;
            
            const script = document.createElement('script');
            script.src = scripts[index];
            script.async = false;
            script.onload = () => {
                index++;
                loadNext();
            };
            
            document.head.appendChild(script);
        };
        
        loadNext();
    },
    
    enableScriptStreaming() {
        // Use module preload for better streaming
        const moduleScripts = document.querySelectorAll('script[type="module"]');
        
        moduleScripts.forEach(script => {
            const link = document.createElement('link');
            link.rel = 'modulepreload';
            link.href = script.src;
            document.head.appendChild(link);
        });
    },
    
    optimizeRenderingPerformance() {
        // Optimize CSS delivery
        this.optimizeCSSDelivery();
        
        // Minimize layout thrashing
        this.minimizeLayoutThrashing();
        
        // Optimize animations
        this.optimizeAnimations();
    },
    
    optimizeCSSDelivery() {
        // Inline critical CSS
        this.inlineCriticalCSS();
        
        // Load non-critical CSS asynchronously
        this.loadNonCriticalCSSAsync();
    },
    
    inlineCriticalCSS() {
        // This would inline the most critical CSS
        // In a real implementation, this would be done at build time
        const criticalCSS = `
            body { margin: 0; font-family: Inter, sans-serif; }
            .navbar { position: fixed; top: 0; width: 100%; z-index: 1000; }
            .hero { padding-top: 80px; }
        `;
        
        const style = document.createElement('style');
        style.textContent = criticalCSS;
        document.head.insertBefore(style, document.head.firstChild);
    },
    
    loadNonCriticalCSSAsync() {
        const nonCriticalCSS = ['styles/mobile.css'];
        
        nonCriticalCSS.forEach(href => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.media = 'print';
            link.onload = function() {
                this.media = 'all';
            };
            document.head.appendChild(link);
        });
    },
    
    minimizeLayoutThrashing() {
        // Batch DOM operations
        this.batchDOMOperations();
        
        // Use CSS transforms instead of layout properties
        this.preferCSSTransforms();
    },
    
    batchDOMOperations() {
        // Override common DOM methods to batch operations
        const originalStyle = Element.prototype.style;
        let pendingStyleUpdates = new Map();
        let updateScheduled = false;
        
        const flushStyleUpdates = () => {
            pendingStyleUpdates.forEach((styles, element) => {
                Object.assign(element.style, styles);
            });
            pendingStyleUpdates.clear();
            updateScheduled = false;
        };
        
        // This is a simplified example - real implementation would be more complex
        window.batchStyleUpdate = function(element, styles) {
            if (!pendingStyleUpdates.has(element)) {
                pendingStyleUpdates.set(element, {});
            }
            Object.assign(pendingStyleUpdates.get(element), styles);
            
            if (!updateScheduled) {
                updateScheduled = true;
                requestAnimationFrame(flushStyleUpdates);
            }
        };
    },
    
    preferCSSTransforms() {
        // Convert position-based animations to transform-based
        document.querySelectorAll('[data-animate]').forEach(element => {
            const animationType = element.dataset.animate;
            
            if (animationType === 'slide') {
                element.style.transform = 'translateX(-100%)';
                element.style.transition = 'transform 0.3s ease';
            }
        });
    },
    
    optimizeAnimations() {
        // Use will-change property for animated elements
        document.querySelectorAll('.animated, .feature-card, .pricing-card').forEach(element => {
            element.style.willChange = 'transform, opacity';
        });
        
        // Remove will-change after animation
        document.addEventListener('animationend', (e) => {
            e.target.style.willChange = 'auto';
        });
        
        // Use requestAnimationFrame for custom animations
        this.useRAFForAnimations();
    },
    
    useRAFForAnimations() {
        // Replace setTimeout/setInterval animations with requestAnimationFrame
        const originalSetTimeout = window.setTimeout;
        const originalSetInterval = window.setInterval;
        
        window.animationFrame = function(callback, duration = 16) {
            let start = null;
            
            const frame = (timestamp) => {
                if (!start) start = timestamp;
                const progress = timestamp - start;
                
                if (progress >= duration) {
                    callback();
                } else {
                    requestAnimationFrame(frame);
                }
            };
            
            requestAnimationFrame(frame);
        };
    },
    
    setupPerformanceBudgetMonitoring() {
        // Monitor resource sizes
        this.monitorResourceSizes();
        
        // Monitor Core Web Vitals
        this.monitorCoreWebVitals();
        
        // Generate performance reports
        this.generatePerformanceReports();
    },
    
    monitorResourceSizes() {
        const resourceEntries = performance.getEntriesByType('resource');
        
        resourceEntries.forEach(entry => {
            if (entry.transferSize > 500000) { // 500KB
                console.warn(`Large resource detected: ${entry.name} (${entry.transferSize} bytes)`);
            }
        });
    },
    
    monitorCoreWebVitals() {
        // This would integrate with web-vitals library in production
        const vitals = {
            LCP: this.metrics.largestContentfulPaint,
            FID: this.metrics.firstInputDelay,
            CLS: this.metrics.cumulativeLayoutShift
        };
        
        // Log to analytics
        FitGenius.analytics.trackEvent('core_web_vitals', vitals);
    },
    
    generatePerformanceReport() {
        const report = {
            timestamp: new Date().toISOString(),
            metrics: this.metrics,
            budgets: this.budgets,
            optimizations: this.state,
            deviceInfo: {
                userAgent: navigator.userAgent,
                connection: navigator.connection?.effectiveType,
                memory: navigator.deviceMemory,
                cores: navigator.hardwareConcurrency
            }
        };
        
        // Store report
        FitGenius.utils.saveToLocalStorage('performance_report', report);
        
        // Log summary
        console.group('🚀 Performance Report');
        console.log('Load Time:', this.metrics.loadTime + 'ms');
        console.log('First Paint:', this.metrics.firstPaint + 'ms');
        console.log('LCP:', this.metrics.largestContentfulPaint + 'ms');
        console.log('CLS:', this.metrics.cumulativeLayoutShift);
        console.log('FID:', this.metrics.firstInputDelay + 'ms');
        console.groupEnd();
        
        return report;
    },
    
    // Utility methods
    deferNonCriticalResources() {
        // Implementation for deferring non-critical resources
        const nonCritical = document.querySelectorAll('script[data-defer], link[data-defer]');
        nonCritical.forEach(resource => {
            resource.loading = 'lazy';
        });
    },
    
    enableResourceCompression() {
        // Enable gzip/brotli compression hint
        const meta = document.createElement('meta');
        meta.httpEquiv = 'Accept-Encoding';
        meta.content = 'gzip, deflate, br';
        document.head.appendChild(meta);
    },
    
    preloadCriticalResources() {
        const critical = [
            { href: 'styles/main.css', as: 'style' },
            { href: 'scripts/main.js', as: 'script' }
        ];
        
        critical.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource.href;
            link.as = resource.as;
            document.head.appendChild(link);
        });
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    PerformanceOptimizer.init();
});

// Export for global access
window.PerformanceOptimizer = PerformanceOptimizer;