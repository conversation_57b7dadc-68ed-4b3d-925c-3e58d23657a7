/**
 * FitGenius.pl - Analytics Dashboard
 * Advanced fitness analytics and data visualization
 */

const AnalyticsDashboard = {
    // Chart instances
    charts: {},
    
    // Mock user data for demonstration
    userData: {
        profile: {
            name: '<PERSON>',
            age: 28,
            height: 168,
            weight: 65,
            goal: 'weight-loss',
            startDate: '2024-09-01'
        },
        
        workoutHistory: [
            { date: '2024-12-01', type: 'strength', duration: 45, calories: 320, exercises: 6 },
            { date: '2024-12-02', type: 'cardio', duration: 30, calories: 280, exercises: 4 },
            { date: '2024-12-03', type: 'strength', duration: 50, calories: 380, exercises: 7 },
            { date: '2024-12-05', type: 'endurance', duration: 40, calories: 300, exercises: 5 },
            { date: '2024-12-06', type: 'strength', duration: 45, calories: 340, exercises: 6 },
            { date: '2024-12-08', type: 'cardio', duration: 35, calories: 290, exercises: 4 },
            { date: '2024-12-09', type: 'strength', duration: 55, calories: 400, exercises: 8 },
            { date: '2024-12-11', type: 'endurance', duration: 42, calories: 310, exercises: 5 },
            { date: '2024-12-12', type: 'strength', duration: 48, calories: 360, exercises: 7 },
            { date: '2024-12-13', type: 'cardio', duration: 32, calories: 270, exercises: 4 }
        ],
        
        strengthProgress: [
            { exercise: 'Przysiad', weight: [40, 45, 50, 52, 55, 58, 60], dates: ['2024-10-01', '2024-10-15', '2024-11-01', '2024-11-15', '2024-12-01', '2024-12-15', '2024-12-30'] },
            { exercise: 'Wyciskanie', weight: [30, 32, 35, 37, 40, 42, 45], dates: ['2024-10-01', '2024-10-15', '2024-11-01', '2024-11-15', '2024-12-01', '2024-12-15', '2024-12-30'] },
            { exercise: 'Martwy ciąg', weight: [50, 55, 60, 65, 70, 72, 75], dates: ['2024-10-01', '2024-10-15', '2024-11-01', '2024-11-15', '2024-12-01', '2024-12-15', '2024-12-30'] }
        ],
        
        bodyComposition: {
            weight: [68, 67.5, 67, 66.2, 65.8, 65.3, 65],
            bodyFat: [22, 21.5, 21, 20.2, 19.8, 19.3, 18.5],
            muscle: [38, 38.5, 39, 39.8, 40.2, 40.7, 41.5],
            water: [58, 58.5, 59, 59.5, 60, 60.2, 60],
            dates: ['2024-09-01', '2024-09-15', '2024-10-01', '2024-10-15', '2024-11-01', '2024-11-15', '2024-12-01']
        },
        
        nutritionData: {
            dailyCalories: [1850, 1920, 1800, 1950, 1820, 1880, 1900],
            targetCalories: 1850,
            macros: {
                protein: [120, 125, 115, 130, 118, 125, 128],
                carbs: [180, 190, 170, 200, 175, 185, 190],
                fats: [65, 70, 60, 75, 62, 68, 72]
            },
            dates: ['2024-12-07', '2024-12-08', '2024-12-09', '2024-12-10', '2024-12-11', '2024-12-12', '2024-12-13']
        },
        
        sleepData: [7.5, 6.8, 8.2, 7.0, 8.5, 6.5, 7.8, 8.0, 7.2, 8.3],
        stressLevels: [3, 4, 2, 5, 3, 6, 2, 3, 4, 2] // 1-10 scale
    },
    
    // Initialize dashboard
    init() {
        this.setupEventListeners();
        this.loadUserData();
        this.initializeCharts();
        this.updateCalendar();
        this.updateProgress();
        this.setupRealTimeUpdates();
        console.log('📊 Analytics Dashboard initialized');
    },
    
    setupEventListeners() {
        // Period selector
        document.querySelectorAll('.period-selector').forEach(selector => {
            selector.addEventListener('change', (e) => {
                this.updateChartsForPeriod(e.target.value);
            });
        });
        
        // Export buttons
        document.querySelectorAll('.export-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const format = e.target.dataset.format;
                this.exportData(format);
            });
        });
        
        // Refresh data button
        const refreshBtn = document.querySelector('.refresh-data');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshDashboard());
        }
    },
    
    loadUserData() {
        // Load saved user data or use mock data
        const saved = FitGenius.utils.loadFromLocalStorage('user_analytics_data');
        if (saved) {
            this.userData = { ...this.userData, ...saved };
        }
    },
    
    initializeCharts() {
        this.createStrengthChart();
        this.createCalorieProgress();
        this.createBodyCompositionChart();
        this.createWorkoutFrequencyChart();
        this.updateProgressRings();
    },
    
    createStrengthChart() {
        const canvas = document.getElementById('strengthChart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        // Create gradient
        const gradient = ctx.createLinearGradient(0, 0, 0, 200);
        gradient.addColorStop(0, 'rgba(99, 102, 241, 0.8)');
        gradient.addColorStop(1, 'rgba(99, 102, 241, 0.1)');
        
        // Simulate Chart.js-like functionality with canvas
        this.drawLineChart(ctx, {
            width: 300,
            height: 200,
            data: this.userData.strengthProgress[0].weight,
            labels: this.userData.strengthProgress[0].dates.map(date => 
                new Date(date).toLocaleDateString('pl-PL', { month: 'short', day: 'numeric' })
            ),
            gradient: gradient,
            lineColor: '#6366f1',
            pointColor: '#ffffff'
        });
    },
    
    drawLineChart(ctx, config) {
        const { width, height, data, labels, gradient, lineColor, pointColor } = config;
        const padding = 40;
        const chartWidth = width - padding * 2;
        const chartHeight = height - padding * 2;
        
        // Clear canvas
        ctx.clearRect(0, 0, width, height);
        
        // Find min/max values
        const minValue = Math.min(...data) * 0.9;
        const maxValue = Math.max(...data) * 1.1;
        const valueRange = maxValue - minValue;
        
        // Draw background
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.moveTo(padding, height - padding);
        
        // Plot points and create path
        const points = data.map((value, index) => {
            const x = padding + (index / (data.length - 1)) * chartWidth;
            const y = height - padding - ((value - minValue) / valueRange) * chartHeight;
            return { x, y, value };
        });
        
        // Draw area under curve
        points.forEach((point, index) => {
            if (index === 0) {
                ctx.moveTo(point.x, point.y);
            } else {
                ctx.lineTo(point.x, point.y);
            }
        });
        ctx.lineTo(points[points.length - 1].x, height - padding);
        ctx.lineTo(padding, height - padding);
        ctx.fill();
        
        // Draw line
        ctx.strokeStyle = lineColor;
        ctx.lineWidth = 3;
        ctx.beginPath();
        points.forEach((point, index) => {
            if (index === 0) {
                ctx.moveTo(point.x, point.y);
            } else {
                ctx.lineTo(point.x, point.y);
            }
        });
        ctx.stroke();
        
        // Draw points
        ctx.fillStyle = pointColor;
        ctx.strokeStyle = lineColor;
        ctx.lineWidth = 2;
        points.forEach(point => {
            ctx.beginPath();
            ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();
        });
        
        // Draw labels
        ctx.fillStyle = '#94a3b8';
        ctx.font = '12px Inter';
        ctx.textAlign = 'center';
        
        labels.forEach((label, index) => {
            const x = padding + (index / (labels.length - 1)) * chartWidth;
            ctx.fillText(label, x, height - 10);
        });
        
        // Draw values
        ctx.fillStyle = '#f8fafc';
        ctx.font = 'bold 12px Inter';
        points.forEach(point => {
            ctx.fillText(`${point.value}kg`, point.x, point.y - 10);
        });
    },
    
    createCalorieProgress() {
        const container = document.querySelector('.calories-chart');
        if (!container) return;
        
        const today = new Date();
        const todayCalories = 2100;
        const targetCalories = 2800;
        const progress = (todayCalories / targetCalories) * 100;
        
        this.updateProgressRing(container.querySelector('.ring-progress'), progress);
    },
    
    updateProgressRing(element, progress) {
        if (!element) return;
        
        element.style.setProperty('--progress', progress);
        
        // Animate the progress
        let currentProgress = 0;
        const animate = () => {
            if (currentProgress < progress) {
                currentProgress += 2;
                element.style.setProperty('--progress', currentProgress);
                requestAnimationFrame(animate);
            }
        };
        animate();
    },
    
    createBodyCompositionChart() {
        const muscleBar = document.querySelector('.composition-fill.muscle');
        const fatBar = document.querySelector('.composition-fill.fat');
        const waterBar = document.querySelector('.composition-fill.water');
        
        if (muscleBar) this.animateProgressBar(muscleBar, 42);
        if (fatBar) this.animateProgressBar(fatBar, 18);
        if (waterBar) this.animateProgressBar(waterBar, 60);
    },
    
    animateProgressBar(element, targetWidth) {
        let currentWidth = 0;
        const animate = () => {
            if (currentWidth < targetWidth) {
                currentWidth += 1;
                element.style.width = `${currentWidth}%`;
                requestAnimationFrame(animate);
            }
        };
        setTimeout(animate, 500);
    },
    
    createWorkoutFrequencyChart() {
        this.updateCalendar();
    },
    
    updateCalendar() {
        const calendar = document.getElementById('workoutCalendar');
        if (!calendar) return;
        
        const today = new Date();
        const currentMonth = today.getMonth();
        const currentYear = today.getFullYear();
        const firstDay = new Date(currentYear, currentMonth, 1);
        const lastDay = new Date(currentYear, currentMonth + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());
        
        calendar.innerHTML = '';
        
        // Generate calendar days
        const workoutDates = new Set(this.userData.workoutHistory.map(w => w.date));
        
        for (let i = 0; i < 42; i++) {
            const date = new Date(startDate);
            date.setDate(startDate.getDate() + i);
            
            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            dayElement.textContent = date.getDate();
            
            const dateString = date.toISOString().split('T')[0];
            
            if (date.getMonth() !== currentMonth) {
                dayElement.classList.add('empty');
            }
            
            if (workoutDates.has(dateString)) {
                dayElement.classList.add('workout');
            }
            
            if (date.toDateString() === today.toDateString()) {
                dayElement.classList.add('today');
            }
            
            // Add click handler for workout details
            if (workoutDates.has(dateString)) {
                dayElement.addEventListener('click', () => {
                    this.showWorkoutDetails(dateString);
                });
            }
            
            calendar.appendChild(dayElement);
        }
    },
    
    showWorkoutDetails(date) {
        const workout = this.userData.workoutHistory.find(w => w.date === date);
        if (!workout) return;
        
        const modal = document.createElement('div');
        modal.className = 'workout-details-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
        `;
        
        modal.innerHTML = `
            <div class="modal-content glass-effect" style="padding: 2rem; border-radius: 1rem; max-width: 400px; width: 90%; text-align: center;">
                <h3 style="margin-bottom: 1rem; color: var(--text-primary);">Trening z ${new Date(date).toLocaleDateString('pl-PL')}</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 2rem;">
                    <div class="stat-card glass-effect" style="padding: 1rem; border-radius: 0.5rem;">
                        <div class="stat-icon" style="font-size: 1.5rem;">🏋️</div>
                        <div class="stat-value" style="font-size: 1.2rem; font-weight: bold;">${workout.type}</div>
                        <div class="stat-label" style="font-size: 0.8rem; color: var(--text-muted);">Typ</div>
                    </div>
                    <div class="stat-card glass-effect" style="padding: 1rem; border-radius: 0.5rem;">
                        <div class="stat-icon" style="font-size: 1.5rem;">⏱️</div>
                        <div class="stat-value" style="font-size: 1.2rem; font-weight: bold;">${workout.duration}</div>
                        <div class="stat-label" style="font-size: 0.8rem; color: var(--text-muted);">Minuty</div>
                    </div>
                    <div class="stat-card glass-effect" style="padding: 1rem; border-radius: 0.5rem;">
                        <div class="stat-icon" style="font-size: 1.5rem;">🔥</div>
                        <div class="stat-value" style="font-size: 1.2rem; font-weight: bold;">${workout.calories}</div>
                        <div class="stat-label" style="font-size: 0.8rem; color: var(--text-muted);">Kalorie</div>
                    </div>
                    <div class="stat-card glass-effect" style="padding: 1rem; border-radius: 0.5rem;">
                        <div class="stat-icon" style="font-size: 1.5rem;">💪</div>
                        <div class="stat-value" style="font-size: 1.2rem; font-weight: bold;">${workout.exercises}</div>
                        <div class="stat-label" style="font-size: 0.8rem; color: var(--text-muted);">Ćwiczenia</div>
                    </div>
                </div>
                <button class="btn-primary" onclick="this.closest('.workout-details-modal').remove();">Zamknij</button>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
        
        // Track modal view
        FitGenius.analytics.trackEvent('workout_details_viewed', {
            date: date,
            workoutType: workout.type
        });
    },
    
    updateProgress() {
        this.updateProgressRings();
        this.updateStatistics();
    },
    
    updateProgressRings() {
        // Calorie ring
        const calorieRing = document.querySelector('.calorie-ring .ring-progress');
        if (calorieRing) {
            const progress = 75; // 2100/2800 * 100
            this.updateProgressRing(calorieRing, progress);
        }
        
        // Other progress rings can be added here
    },
    
    updateStatistics() {
        // Update current month statistics
        const currentMonth = new Date().getMonth();
        const monthlyWorkouts = this.userData.workoutHistory.filter(w => 
            new Date(w.date).getMonth() === currentMonth
        );
        
        // Calculate streak
        const streak = this.calculateCurrentStreak();
        
        // Update DOM elements
        const workoutCountEl = document.querySelector('[data-stat="monthly-workouts"]');
        const streakEl = document.querySelector('[data-stat="streak"]');
        
        if (workoutCountEl) workoutCountEl.textContent = `${monthlyWorkouts.length} treningów`;
        if (streakEl) streakEl.textContent = `${streak} dni`;
    },
    
    calculateCurrentStreak() {
        const sortedWorkouts = [...this.userData.workoutHistory].sort((a, b) => 
            new Date(b.date) - new Date(a.date)
        );
        
        let streak = 0;
        let currentDate = new Date();
        
        for (const workout of sortedWorkouts) {
            const workoutDate = new Date(workout.date);
            const daysDiff = Math.floor((currentDate - workoutDate) / (1000 * 60 * 60 * 24));
            
            if (daysDiff === streak) {
                streak++;
            } else if (daysDiff > streak + 1) {
                break;
            }
        }
        
        return streak;
    },
    
    setupRealTimeUpdates() {
        // Simulate real-time data updates
        setInterval(() => {
            this.updateLiveStats();
        }, 30000); // Every 30 seconds
        
        // Update time-sensitive displays
        setInterval(() => {
            this.updateTimeDisplays();
        }, 1000); // Every second
    },
    
    updateLiveStats() {
        // Simulate new data coming in
        const newCalories = Math.random() * 50 + 2050;
        const calorieRing = document.querySelector('.ring-number');
        if (calorieRing) {
            calorieRing.textContent = Math.round(newCalories);
        }
        
        // Update progress ring
        const progress = (newCalories / 2800) * 100;
        const ringProgress = document.querySelector('.calorie-ring .ring-progress');
        if (ringProgress) {
            this.updateProgressRing(ringProgress, progress);
        }
    },
    
    updateTimeDisplays() {
        // Update any time-based displays
        const timeElements = document.querySelectorAll('[data-time="live"]');
        timeElements.forEach(el => {
            el.textContent = new Date().toLocaleTimeString('pl-PL');
        });
    },
    
    exportData(format) {
        const exportData = {
            profile: this.userData.profile,
            workouts: this.userData.workoutHistory,
            progress: this.userData.strengthProgress,
            bodyComposition: this.userData.bodyComposition,
            exportDate: new Date().toISOString()
        };
        
        switch (format) {
            case 'csv':
                this.exportCSV(exportData);
                break;
            case 'pdf':
                this.exportPDF(exportData);
                break;
            case 'json':
                this.exportJSON(exportData);
                break;
            default:
                FitGenius.forms.showNotification('Nieobsługiwany format eksportu', 'error');
        }
        
        FitGenius.analytics.trackEvent('data_exported', {
            format: format,
            recordCount: this.userData.workoutHistory.length
        });
    },
    
    exportCSV(data) {
        const csv = this.convertToCSV(data.workouts);
        const blob = new Blob([csv], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `fitgenius-workouts-${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
        
        FitGenius.forms.showNotification('Dane wyeksportowane do CSV', 'success');
    },
    
    convertToCSV(workouts) {
        const headers = ['Data', 'Typ', 'Czas (min)', 'Kalorie', 'Ćwiczenia'];
        const rows = workouts.map(w => [
            w.date,
            w.type,
            w.duration,
            w.calories,
            w.exercises
        ]);
        
        return [headers, ...rows]
            .map(row => row.join(','))
            .join('\n');
    },
    
    exportPDF(data) {
        // Simulate PDF generation
        FitGenius.forms.showNotification('Generowanie PDF...', 'info');
        
        setTimeout(() => {
            FitGenius.forms.showNotification('PDF będzie dostępny w pełnej wersji aplikacji', 'info');
        }, 2000);
    },
    
    exportJSON(data) {
        const json = JSON.stringify(data, null, 2);
        const blob = new Blob([json], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `fitgenius-data-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        window.URL.revokeObjectURL(url);
        
        FitGenius.forms.showNotification('Dane wyeksportowane do JSON', 'success');
    },
    
    refreshDashboard() {
        FitGenius.forms.showNotification('Odświeżanie danych...', 'info');
        
        // Simulate data refresh
        setTimeout(() => {
            // Add some random variation to simulate new data
            this.userData.workoutHistory.forEach(workout => {
                workout.calories += Math.floor(Math.random() * 20 - 10);
            });
            
            // Refresh charts
            this.initializeCharts();
            this.updateCalendar();
            this.updateProgress();
            
            FitGenius.forms.showNotification('Dashboard odświeżony', 'success');
        }, 1500);
        
        FitGenius.analytics.trackEvent('dashboard_refreshed');
    },
    
    updateChartsForPeriod(period) {
        // Filter data based on selected period
        let filteredData;
        const now = new Date();
        
        switch (period) {
            case '7d':
                filteredData = this.filterDataByDays(7);
                break;
            case '30d':
                filteredData = this.filterDataByDays(30);
                break;
            case '90d':
                filteredData = this.filterDataByDays(90);
                break;
            case '1y':
                filteredData = this.filterDataByDays(365);
                break;
            default:
                filteredData = this.userData;
        }
        
        // Update charts with filtered data
        this.userData = filteredData;
        this.initializeCharts();
        
        FitGenius.analytics.trackEvent('analytics_period_changed', {
            period: period
        });
    },
    
    filterDataByDays(days) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);
        
        return {
            ...this.userData,
            workoutHistory: this.userData.workoutHistory.filter(w => 
                new Date(w.date) >= cutoffDate
            )
        };
    },
    
    // Predictive Analytics
    generateInsights() {
        const insights = [];
        
        // Workout frequency analysis
        const avgWorkoutsPerWeek = this.userData.workoutHistory.length / 4;
        if (avgWorkoutsPerWeek < 3) {
            insights.push({
                type: 'warning',
                message: 'Trenuj częściej! Zalecamy minimum 3 treningi tygodniowo dla lepszych rezultatów.',
                action: 'increase_frequency'
            });
        }
        
        // Progress analysis
        const recentProgress = this.analyzeProgressTrend();
        if (recentProgress === 'plateau') {
            insights.push({
                type: 'info',
                message: 'Twoje postępy spowolniły. Rozważ zmianę programu treningowego.',
                action: 'change_program'
            });
        }
        
        // Calorie analysis
        const avgCalories = this.userData.workoutHistory.reduce((sum, w) => sum + w.calories, 0) / this.userData.workoutHistory.length;
        if (avgCalories < 250) {
            insights.push({
                type: 'suggestion',
                message: 'Zwiększ intensywność treningów, aby spalać więcej kalorii.',
                action: 'increase_intensity'
            });
        }
        
        return insights;
    },
    
    analyzeProgressTrend() {
        const recentWeights = this.userData.strengthProgress[0].weight.slice(-3);
        const improvement = recentWeights[2] - recentWeights[0];
        
        if (improvement < 2) return 'plateau';
        if (improvement > 5) return 'excellent';
        return 'good';
    },
    
    showInsights() {
        const insights = this.generateInsights();
        
        if (insights.length === 0) {
            FitGenius.forms.showNotification('Świetnie! Twoje postępy są na dobrej drodze! 🎉', 'success');
            return;
        }
        
        const modal = document.createElement('div');
        modal.className = 'insights-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
        `;
        
        const insightsHTML = insights.map(insight => `
            <div class="insight-item glass-effect" style="padding: 1rem; margin-bottom: 1rem; border-radius: 0.5rem; border-left: 4px solid var(--${insight.type === 'warning' ? 'accent' : 'primary'}-color);">
                <div class="insight-icon" style="font-size: 1.5rem; margin-bottom: 0.5rem;">
                    ${insight.type === 'warning' ? '⚠️' : insight.type === 'info' ? 'ℹ️' : '💡'}
                </div>
                <p style="color: var(--text-secondary); line-height: 1.5;">${insight.message}</p>
            </div>
        `).join('');
        
        modal.innerHTML = `
            <div class="modal-content glass-effect" style="padding: 2rem; border-radius: 1rem; max-width: 600px; width: 90%;">
                <h3 style="margin-bottom: 1.5rem; color: var(--text-primary);">🧠 Analiza AI Twoich Postępów</h3>
                ${insightsHTML}
                <div style="text-align: center; margin-top: 2rem;">
                    <button class="btn-primary" onclick="this.closest('.insights-modal').remove();">
                        Rozumiem
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
        
        FitGenius.analytics.trackEvent('insights_viewed', {
            insightCount: insights.length
        });
    },
    
    // Achievement System
    checkAchievements() {
        const achievements = [];
        
        // Workout streak achievement
        const streak = this.calculateCurrentStreak();
        if (streak >= 7 && !this.hasAchievement('week_streak')) {
            achievements.push({
                id: 'week_streak',
                name: 'Tygodniowy Wojownik',
                description: '7 dni treningu z rzędu!',
                icon: '🔥',
                points: 100
            });
        }
        
        // Total workouts achievement
        const totalWorkouts = this.userData.workoutHistory.length;
        if (totalWorkouts >= 50 && !this.hasAchievement('fifty_workouts')) {
            achievements.push({
                id: 'fifty_workouts',
                name: 'Pół Setki',
                description: '50 ukończonych treningów',
                icon: '🏆',
                points: 500
            });
        }
        
        // Strength progress achievement
        const strengthGain = this.userData.strengthProgress[0].weight[6] - this.userData.strengthProgress[0].weight[0];
        if (strengthGain >= 20 && !this.hasAchievement('strength_beast')) {
            achievements.push({
                id: 'strength_beast',
                name: 'Bestia Siły',
                description: '20kg wzrostu w przysiadzie',
                icon: '💪',
                points: 300
            });
        }
        
        // Award new achievements
        achievements.forEach(achievement => {
            this.awardAchievement(achievement);
        });
        
        return achievements;
    },
    
    hasAchievement(achievementId) {
        const achievements = FitGenius.utils.loadFromLocalStorage('user_achievements') || [];
        return achievements.some(a => a.id === achievementId);
    },
    
    awardAchievement(achievement) {
        const achievements = FitGenius.utils.loadFromLocalStorage('user_achievements') || [];
        achievements.push({
            ...achievement,
            unlockedAt: new Date().toISOString()
        });
        
        FitGenius.utils.saveToLocalStorage('user_achievements', achievements);
        
        // Show achievement notification
        this.showAchievementNotification(achievement);
        
        FitGenius.analytics.trackEvent('achievement_unlocked', {
            achievementId: achievement.id,
            points: achievement.points
        });
    },
    
    showAchievementNotification(achievement) {
        const notification = document.createElement('div');
        notification.className = 'achievement-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            padding: 1.5rem;
            border-radius: 1rem;
            box-shadow: 0 16px 64px rgba(0, 0, 0, 0.25);
            z-index: 10001;
            transform: translateX(100%);
            transition: transform 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            max-width: 300px;
        `;
        
        notification.innerHTML = `
            <div style="text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 0.5rem;">${achievement.icon}</div>
                <div style="font-size: 1.2rem; font-weight: bold; margin-bottom: 0.5rem;">Osiągnięcie Odblokowane!</div>
                <div style="font-size: 1rem; margin-bottom: 0.25rem;">${achievement.name}</div>
                <div style="font-size: 0.875rem; opacity: 0.9;">${achievement.description}</div>
                <div style="font-size: 0.875rem; margin-top: 0.5rem; opacity: 0.8;">+${achievement.points} punktów</div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Auto remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 500);
        }, 5000);
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if analytics section exists
    if (document.getElementById('analytics')) {
        AnalyticsDashboard.init();
        
        // Check for achievements on load
        setTimeout(() => {
            AnalyticsDashboard.checkAchievements();
        }, 3000);
    }
});

// Export for global access
window.AnalyticsDashboard = AnalyticsDashboard;