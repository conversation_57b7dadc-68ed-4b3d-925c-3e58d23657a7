/**
 * FitGenius.pl - Polish Nutrition Database
 * Comprehensive Polish food database with nutritional information
 */

const NutritionDatabase = {
    // Polish food categories with detailed nutritional data
    foods: {
        // Grains and Cereals - Zboża i Płatki
        grains: [
            {
                id: 'chleb_zytni',
                name: 'Chle<PERSON>ytni',
                category: 'grains',
                calories: 250,
                protein: 8.5,
                carbs: 48.0,
                fat: 1.2,
                fiber: 6.0,
                sugar: 1.5,
                sodium: 540,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'kromka', amount: 30, unit: 'g' },
                    { name: 'toster', amount: 25, unit: 'g' }
                ],
                vitamins: { B1: 0.3, B2: 0.1, B3: 3.2 },
                minerals: { iron: 2.8, magnesium: 75, phosphorus: 158 }
            },
            {
                id: 'chleb_razowy',
                name: 'Chleb razowy',
                category: 'grains',
                calories: 220,
                protein: 9.2,
                carbs: 41.0,
                fat: 1.8,
                fiber: 8.5,
                sugar: 2.1,
                sodium: 480,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'kromka', amount: 35, unit: 'g' },
                    { name: 'kanapka', amount: 70, unit: 'g' }
                ]
            },
            {
                id: 'platki_owsiane',
                name: 'Płatki owsiane',
                category: 'grains',
                calories: 368,
                protein: 13.2,
                carbs: 58.7,
                fat: 7.0,
                fiber: 10.1,
                sugar: 0.8,
                sodium: 6,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'porcja', amount: 50, unit: 'g' },
                    { name: 'łyżka', amount: 15, unit: 'g' }
                ]
            },
            {
                id: 'kasza_gryczana',
                name: 'Kasza gryczana',
                category: 'grains',
                calories: 343,
                protein: 13.2,
                carbs: 71.5,
                fat: 3.4,
                fiber: 10.0,
                sugar: 1.5,
                sodium: 1,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'porcja gotowana', amount: 150, unit: 'g' },
                    { name: 'szklanka sucha', amount: 80, unit: 'g' }
                ]
            }
        ],
        
        // Vegetables - Warzywa
        vegetables: [
            {
                id: 'ziemniaki',
                name: 'Ziemniaki',
                category: 'vegetables',
                calories: 77,
                protein: 2.0,
                carbs: 17.5,
                fat: 0.1,
                fiber: 2.2,
                sugar: 0.8,
                sodium: 6,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'średni ziemniak', amount: 150, unit: 'g' },
                    { name: 'mały ziemniak', amount: 100, unit: 'g' }
                ],
                vitamins: { C: 19.7, B6: 0.3 },
                minerals: { potassium: 421, magnesium: 23 }
            },
            {
                id: 'kapusta_biala',
                name: 'Kapusta biała',
                category: 'vegetables',
                calories: 25,
                protein: 1.3,
                carbs: 5.8,
                fat: 0.1,
                fiber: 2.5,
                sugar: 3.2,
                sodium: 18,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'porcja surówki', amount: 80, unit: 'g' },
                    { name: 'liść', amount: 15, unit: 'g' }
                ]
            },
            {
                id: 'marchew',
                name: 'Marchew',
                category: 'vegetables',
                calories: 41,
                protein: 0.9,
                carbs: 9.6,
                fat: 0.2,
                fiber: 2.8,
                sugar: 4.7,
                sodium: 69,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'średnia marchew', amount: 60, unit: 'g' },
                    { name: 'baby marchew', amount: 15, unit: 'g' }
                ],
                vitamins: { A: 16706, C: 5.9 },
                minerals: { potassium: 320 }
            },
            {
                id: 'cebula',
                name: 'Cebula',
                category: 'vegetables',
                calories: 40,
                protein: 1.1,
                carbs: 9.3,
                fat: 0.1,
                fiber: 1.7,
                sugar: 4.2,
                sodium: 4,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'średnia cebula', amount: 110, unit: 'g' },
                    { name: 'łyżka posiekana', amount: 10, unit: 'g' }
                ]
            },
            {
                id: 'pomidor',
                name: 'Pomidor',
                category: 'vegetables',
                calories: 18,
                protein: 0.9,
                carbs: 3.9,
                fat: 0.2,
                fiber: 1.2,
                sugar: 2.6,
                sodium: 5,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'średni pomidor', amount: 120, unit: 'g' },
                    { name: 'pomidor koktajlowy', amount: 15, unit: 'g' }
                ]
            }
        ],
        
        // Fruits - Owoce
        fruits: [
            {
                id: 'jablko',
                name: 'Jabłko',
                category: 'fruits',
                calories: 52,
                protein: 0.3,
                carbs: 13.8,
                fat: 0.2,
                fiber: 2.4,
                sugar: 10.4,
                sodium: 1,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'średnie jabłko', amount: 180, unit: 'g' },
                    { name: 'małe jabłko', amount: 150, unit: 'g' }
                ],
                vitamins: { C: 4.6 },
                minerals: { potassium: 107 }
            },
            {
                id: 'banan',
                name: 'Banan',
                category: 'fruits',
                calories: 89,
                protein: 1.1,
                carbs: 22.8,
                fat: 0.3,
                fiber: 2.6,
                sugar: 12.2,
                sodium: 1,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'średni banan', amount: 120, unit: 'g' },
                    { name: 'duży banan', amount: 150, unit: 'g' }
                ]
            },
            {
                id: 'truskawki',
                name: 'Truskawki',
                category: 'fruits',
                calories: 32,
                protein: 0.7,
                carbs: 7.7,
                fat: 0.3,
                fiber: 2.0,
                sugar: 4.9,
                sodium: 1,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'szklanka', amount: 150, unit: 'g' },
                    { name: 'średnia truskawka', amount: 12, unit: 'g' }
                ]
            }
        ],
        
        // Meat and Fish - Mięso i Ryby
        proteins: [
            {
                id: 'pierś_kurczaka',
                name: 'Pierś kurczaka (bez skóry)',
                category: 'proteins',
                calories: 165,
                protein: 31.0,
                carbs: 0.0,
                fat: 3.6,
                fiber: 0.0,
                sugar: 0.0,
                sodium: 74,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'porcja', amount: 150, unit: 'g' },
                    { name: 'kotlet', amount: 120, unit: 'g' }
                ],
                vitamins: { B3: 10.9, B6: 0.6 },
                minerals: { phosphorus: 220, selenium: 27.6 }
            },
            {
                id: 'losos',
                name: 'Łosoś',
                category: 'proteins',
                calories: 208,
                protein: 25.4,
                carbs: 0.0,
                fat: 11.0,
                fiber: 0.0,
                sugar: 0.0,
                sodium: 48,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'filet', amount: 120, unit: 'g' },
                    { name: 'stek', amount: 150, unit: 'g' }
                ],
                omega3: 2.3
            },
            {
                id: 'jajko',
                name: 'Jajko kurze',
                category: 'proteins',
                calories: 155,
                protein: 13.0,
                carbs: 1.1,
                fat: 11.0,
                fiber: 0.0,
                sugar: 1.1,
                sodium: 124,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'duże jajko', amount: 50, unit: 'g' },
                    { name: 'średnie jajko', amount: 45, unit: 'g' }
                ]
            },
            {
                id: 'wolowina_mielona',
                name: 'Wołowina mielona (chuda)',
                category: 'proteins',
                calories: 250,
                protein: 26.0,
                carbs: 0.0,
                fat: 15.0,
                fiber: 0.0,
                sugar: 0.0,
                sodium: 66,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'kotlet', amount: 100, unit: 'g' },
                    { name: 'burger', amount: 80, unit: 'g' }
                ]
            }
        ],
        
        // Dairy - Nabiał
        dairy: [
            {
                id: 'mleko_2',
                name: 'Mleko 2%',
                category: 'dairy',
                calories: 50,
                protein: 3.4,
                carbs: 4.8,
                fat: 2.0,
                fiber: 0.0,
                sugar: 4.8,
                sodium: 44,
                servingSize: 100,
                servingUnit: 'ml',
                commonPortions: [
                    { name: 'szklanka', amount: 250, unit: 'ml' },
                    { name: 'łyżka', amount: 15, unit: 'ml' }
                ],
                vitamins: { B12: 0.5, D: 0.1 },
                minerals: { calcium: 125 }
            },
            {
                id: 'jogurt_naturalny',
                name: 'Jogurt naturalny',
                category: 'dairy',
                calories: 59,
                protein: 10.0,
                carbs: 3.6,
                fat: 0.4,
                fiber: 0.0,
                sugar: 3.6,
                sodium: 36,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'kubek', amount: 170, unit: 'g' },
                    { name: 'łyżka', amount: 15, unit: 'g' }
                ]
            },
            {
                id: 'ser_bialy',
                name: 'Ser biały twarogowy',
                category: 'dairy',
                calories: 98,
                protein: 11.1,
                carbs: 3.4,
                fat: 4.3,
                fiber: 0.0,
                sugar: 2.7,
                sodium: 364,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'porcja', amount: 100, unit: 'g' },
                    { name: 'łyżka', amount: 20, unit: 'g' }
                ]
            }
        ],
        
        // Nuts and Seeds - Orzechy i Nasiona
        nuts: [
            {
                id: 'migdaly',
                name: 'Migdały',
                category: 'nuts',
                calories: 579,
                protein: 21.2,
                carbs: 21.6,
                fat: 49.9,
                fiber: 12.5,
                sugar: 4.4,
                sodium: 1,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'garstka', amount: 30, unit: 'g' },
                    { name: 'migdał', amount: 1, unit: 'g' }
                ]
            },
            {
                id: 'orzechy_wloskie',
                name: 'Orzechy włoskie',
                category: 'nuts',
                calories: 654,
                protein: 15.2,
                carbs: 13.7,
                fat: 65.2,
                fiber: 6.7,
                sugar: 2.6,
                sodium: 2,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'garstka', amount: 30, unit: 'g' },
                    { name: 'połówka orzecha', amount: 2, unit: 'g' }
                ]
            }
        ],
        
        // Fats and Oils - Tłuszcze i Oleje
        fats: [
            {
                id: 'olej_rzepakowy',
                name: 'Olej rzepakowy',
                category: 'fats',
                calories: 884,
                protein: 0.0,
                carbs: 0.0,
                fat: 100.0,
                fiber: 0.0,
                sugar: 0.0,
                sodium: 0,
                servingSize: 100,
                servingUnit: 'ml',
                commonPortions: [
                    { name: 'łyżka', amount: 15, unit: 'ml' },
                    { name: 'łyżeczka', amount: 5, unit: 'ml' }
                ]
            },
            {
                id: 'maslo',
                name: 'Masło',
                category: 'fats',
                calories: 717,
                protein: 0.9,
                carbs: 0.1,
                fat: 81.1,
                fiber: 0.0,
                sugar: 0.1,
                sodium: 643,
                servingSize: 100,
                servingUnit: 'g',
                commonPortions: [
                    { name: 'łyżka', amount: 15, unit: 'g' },
                    { name: 'kostka', amount: 10, unit: 'g' }
                ]
            }
        ]
    },
    
    // Popular Polish recipes with ingredients
    recipes: [
        {
            id: 'kotlet_schabowy',
            name: 'Kotlet schabowy z ziemniakami',
            category: 'main_course',
            servings: 4,
            prepTime: 30,
            cookTime: 20,
            totalCalories: 2400,
            ingredients: [
                { foodId: 'wolowina_mielona', amount: 600, unit: 'g' },
                { foodId: 'ziemniaki', amount: 800, unit: 'g' },
                { foodId: 'jajko', amount: 100, unit: 'g' },
                { foodId: 'olej_rzepakowy', amount: 30, unit: 'ml' }
            ],
            instructions: [
                'Przygotuj mięso i obtocz w jajku',
                'Usmaż na oleju na złoty kolor',
                'Ugotuj ziemniaki w osolonej wodzie',
                'Podawaj z mizeria lub surówką'
            ],
            tags: ['tradycyjne', 'obiad', 'mięso'],
            difficulty: 'medium',
            nutritionPer100g: {
                calories: 200,
                protein: 18,
                carbs: 12,
                fat: 10
            }
        },
        {
            id: 'owsianka_z_owocami',
            name: 'Owsianka z owocami',
            category: 'breakfast',
            servings: 1,
            prepTime: 5,
            cookTime: 10,
            totalCalories: 350,
            ingredients: [
                { foodId: 'platki_owsiane', amount: 50, unit: 'g' },
                { foodId: 'mleko_2', amount: 200, unit: 'ml' },
                { foodId: 'banan', amount: 120, unit: 'g' },
                { foodId: 'truskawki', amount: 100, unit: 'g' }
            ],
            instructions: [
                'Ugotuj płatki owsiane na mleku',
                'Dodaj pokrojone owoce',
                'Posyp cynamonem do smaku'
            ],
            tags: ['śniadanie', 'zdrowe', 'owoce'],
            difficulty: 'easy'
        },
        {
            id: 'salatka_z_lososiem',
            name: 'Sałatka z łososiem i awokado',
            category: 'salad',
            servings: 2,
            prepTime: 15,
            cookTime: 10,
            totalCalories: 600,
            ingredients: [
                { foodId: 'losos', amount: 200, unit: 'g' },
                { foodId: 'jablko', amount: 150, unit: 'g' },
                { foodId: 'olej_rzepakowy', amount: 15, unit: 'ml' }
            ],
            instructions: [
                'Ugrilluj łososia na patelni',
                'Przygotuj sałatkę z liści',
                'Dodaj awokado i jabłko',
                'Polej dressingiem z oleju i cytryny'
            ],
            tags: ['sałatka', 'zdrowe', 'ryba'],
            difficulty: 'medium'
        }
    ],
    
    // Meal planning templates
    mealPlans: {
        weightLoss: {
            name: 'Plan Redukcyjny',
            targetCalories: 1600,
            macroRatio: { protein: 30, carbs: 40, fat: 30 },
            meals: {
                breakfast: { calories: 350, protein: 20, carbs: 35, fat: 15 },
                lunch: { calories: 450, protein: 35, carbs: 40, fat: 20 },
                dinner: { calories: 400, protein: 30, carbs: 35, fat: 18 },
                snacks: { calories: 400, protein: 15, carbs: 50, fat: 17 }
            }
        },
        muscleGain: {
            name: 'Plan Budowy Masy',
            targetCalories: 2800,
            macroRatio: { protein: 25, carbs: 45, fat: 30 },
            meals: {
                breakfast: { calories: 600, protein: 25, carbs: 70, fat: 25 },
                lunch: { calories: 800, protein: 45, carbs: 80, fat: 35 },
                dinner: { calories: 700, protein: 40, carbs: 65, fat: 30 },
                snacks: { calories: 700, protein: 30, carbs: 85, fat: 30 }
            }
        },
        maintenance: {
            name: 'Plan Utrzymania',
            targetCalories: 2200,
            macroRatio: { protein: 25, carbs: 45, fat: 30 },
            meals: {
                breakfast: { calories: 450, protein: 20, carbs: 50, fat: 20 },
                lunch: { calories: 650, protein: 35, carbs: 65, fat: 25 },
                dinner: { calories: 600, protein: 30, carbs: 60, fat: 25 },
                snacks: { calories: 500, protein: 20, carbs: 60, fat: 20 }
            }
        }
    },
    
    // Nutritional recommendations by goal
    recommendations: {
        weightLoss: {
            dailyCalorieDeficit: 500,
            proteinPerKg: 1.6,
            waterIntake: 35, // ml per kg body weight
            supplementsRecommended: ['omega3', 'vitaminD', 'magnesium'],
            avoidFoods: ['processed_snacks', 'sugary_drinks', 'refined_carbs'],
            preferredFoods: ['lean_proteins', 'vegetables', 'whole_grains']
        },
        muscleGain: {
            dailyCalorieSurplus: 300,
            proteinPerKg: 2.0,
            waterIntake: 40,
            supplementsRecommended: ['protein_powder', 'creatine', 'vitaminD'],
            avoidFoods: ['empty_calories', 'trans_fats'],
            preferredFoods: ['lean_proteins', 'complex_carbs', 'healthy_fats']
        },
        maintenance: {
            dailyCalorieBalance: 0,
            proteinPerKg: 1.2,
            waterIntake: 30,
            supplementsRecommended: ['multivitamin', 'omega3'],
            avoidFoods: ['excessive_processed_foods'],
            preferredFoods: ['balanced_macros', 'variety_of_foods']
        }
    },
    
    // Common Polish food combinations and cultural eating patterns
    culturalPatterns: {
        traditionalBreakfast: [
            'chleb_razowy', 'maslo', 'ser_bialy', 'pomidor', 'jajko'
        ],
        traditionalLunch: [
            'kotlet_schabowy', 'ziemniaki', 'kapusta_biala'
        ],
        traditionalDinner: [
            'chleb_zytni', 'ser_bialy', 'cebula', 'pomidor'
        ],
        healthyModernMeals: [
            'owsianka_z_owocami', 'salatka_z_lososiem', 'kasza_gryczana'
        ]
    },
    
    // Utility functions
    utils: {
        calculateCalories(foodId, amount) {
            const food = this.findFood(foodId);
            if (!food) return 0;
            return (food.calories * amount) / food.servingSize;
        },
        
        calculateMacros(foodId, amount) {
            const food = this.findFood(foodId);
            if (!food) return { protein: 0, carbs: 0, fat: 0 };
            
            const ratio = amount / food.servingSize;
            return {
                protein: food.protein * ratio,
                carbs: food.carbs * ratio,
                fat: food.fat * ratio,
                fiber: food.fiber * ratio
            };
        },
        
        findFood(foodId) {
            for (const category in NutritionDatabase.foods) {
                const food = NutritionDatabase.foods[category].find(f => f.id === foodId);
                if (food) return food;
            }
            return null;
        },
        
        searchFoods(query) {
            const results = [];
            const queryLower = query.toLowerCase();
            
            for (const category in NutritionDatabase.foods) {
                NutritionDatabase.foods[category].forEach(food => {
                    if (food.name.toLowerCase().includes(queryLower)) {
                        results.push({ ...food, score: this.calculateRelevanceScore(food.name, query) });
                    }
                });
            }
            
            return results.sort((a, b) => b.score - a.score);
        },
        
        calculateRelevanceScore(foodName, query) {
            const name = foodName.toLowerCase();
            const q = query.toLowerCase();
            
            if (name === q) return 100;
            if (name.startsWith(q)) return 90;
            if (name.includes(q)) return 70;
            
            // Check for partial matches
            const words = q.split(' ');
            let score = 0;
            words.forEach(word => {
                if (name.includes(word)) score += 20;
            });
            
            return score;
        },
        
        generateMealPlan(goal, targetCalories, days = 7) {
            const plan = NutritionDatabase.mealPlans[goal] || NutritionDatabase.mealPlans.maintenance;
            const dailyPlan = [];
            
            for (let day = 0; day < days; day++) {
                const dayMeals = {
                    day: day + 1,
                    date: new Date(Date.now() + day * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    meals: {},
                    totalCalories: 0,
                    totalMacros: { protein: 0, carbs: 0, fat: 0 }
                };
                
                Object.keys(plan.meals).forEach(mealType => {
                    const mealTarget = plan.meals[mealType];
                    const meal = this.generateMeal(mealType, mealTarget);
                    dayMeals.meals[mealType] = meal;
                    dayMeals.totalCalories += meal.calories;
                    dayMeals.totalMacros.protein += meal.macros.protein;
                    dayMeals.totalMacros.carbs += meal.macros.carbs;
                    dayMeals.totalMacros.fat += meal.macros.fat;
                });
                
                dailyPlan.push(dayMeals);
            }
            
            return {
                goal: goal,
                targetCalories: targetCalories,
                plan: plan,
                days: dailyPlan,
                generatedAt: new Date().toISOString()
            };
        },
        
        generateMeal(mealType, targets) {
            // Simple meal generation - in production this would be more sophisticated
            const mealFoods = [];
            let totalCalories = 0;
            let totalMacros = { protein: 0, carbs: 0, fat: 0 };
            
            // Select appropriate foods for meal type
            const appropriateFoods = this.getFoodsForMealType(mealType);
            
            // Add foods until targets are approximately met
            while (totalCalories < targets.calories * 0.9 && mealFoods.length < 5) {
                const randomFood = appropriateFoods[Math.floor(Math.random() * appropriateFoods.length)];
                const amount = this.calculateAppropriateAmount(randomFood, targets.calories - totalCalories);
                
                const foodCalories = this.calculateCalories(randomFood.id, amount);
                const foodMacros = this.calculateMacros(randomFood.id, amount);
                
                mealFoods.push({
                    food: randomFood,
                    amount: amount,
                    unit: randomFood.servingUnit,
                    calories: foodCalories,
                    macros: foodMacros
                });
                
                totalCalories += foodCalories;
                totalMacros.protein += foodMacros.protein;
                totalMacros.carbs += foodMacros.carbs;
                totalMacros.fat += foodMacros.fat;
            }
            
            return {
                type: mealType,
                foods: mealFoods,
                calories: totalCalories,
                macros: totalMacros,
                targets: targets
            };
        },
        
        getFoodsForMealType(mealType) {
            const allFoods = [];
            for (const category in NutritionDatabase.foods) {
                allFoods.push(...NutritionDatabase.foods[category]);
            }
            
            switch (mealType) {
                case 'breakfast':
                    return allFoods.filter(f => 
                        ['grains', 'dairy', 'fruits'].includes(f.category) || 
                        f.id === 'jajko'
                    );
                case 'lunch':
                case 'dinner':
                    return allFoods.filter(f => 
                        ['proteins', 'vegetables', 'grains'].includes(f.category)
                    );
                case 'snacks':
                    return allFoods.filter(f => 
                        ['fruits', 'nuts', 'dairy'].includes(f.category)
                    );
                default:
                    return allFoods;
            }
        },
        
        calculateAppropriateAmount(food, remainingCalories) {
            const baseAmount = food.servingSize;
            const caloriesPerServing = food.calories;
            
            if (caloriesPerServing === 0) return baseAmount;
            
            const targetServings = Math.min(remainingCalories / caloriesPerServing, 2);
            return Math.max(baseAmount * targetServings, baseAmount * 0.5);
        }
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NutritionDatabase;
} else {
    window.NutritionDatabase = NutritionDatabase;
}