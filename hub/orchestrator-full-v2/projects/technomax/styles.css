:root {
    --primary-blue: #2563eb;
    --secondary-blue: #3b82f6;
    --accent-orange: #f97316;
    --dark-blue: #1e40af;
    --light-gray: #f8fafc;
    --medium-gray: #64748b;
    --dark-gray: #334155;
    --white: #ffffff;
    --gradient-primary: linear-gradient(135deg, #2563eb 0%, #3b82f6 50%, #f97316 100%);
    --gradient-secondary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--dark-gray);
    background: var(--white);
}

/* Header */
.header {
    background: var(--white);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.nav-brand h1 {
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 1.75rem;
    font-weight: 700;
}

.nav-search {
    flex: 1;
    max-width: 400px;
    position: relative;
    display: flex;
}

.nav-search input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.nav-search input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.nav-search button {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: var(--gradient-primary);
    border: none;
    padding: 0.5rem;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-search button:hover {
    transform: translateY(-50%) scale(1.05);
}

.cart-btn {
    position: relative;
    background: var(--gradient-primary);
    border: none;
    padding: 0.75rem;
    border-radius: 12px;
    color: white;
    cursor: pointer;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.cart-btn:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.cart-count {
    position: absolute;
    top: -0.5rem;
    right: -0.5rem;
    background: var(--accent-orange);
    color: white;
    border-radius: 50%;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Hero Section */
.hero {
    background: var(--gradient-secondary);
    padding: 4rem 2rem;
    display: flex;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    gap: 3rem;
    min-height: 500px;
}

.hero-content {
    flex: 1;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--medium-gray);
    margin-bottom: 2rem;
}

.hero-image {
    flex: 1;
}

.hero-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: var(--shadow-xl);
}

.cta-btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: none;
}

.cta-btn.primary {
    background: var(--gradient-primary);
    color: white;
}

.cta-btn.secondary {
    background: var(--accent-orange);
    color: white;
}

.cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Features */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 4rem auto;
    padding: 0 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    background: var(--white);
    border-radius: 16px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-card i {
    font-size: 2.5rem;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Products */
.products {
    max-width: 1200px;
    margin: 4rem auto;
    padding: 0 2rem;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.product-card {
    background: var(--white);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    text-align: center;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.product-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 12px;
    margin-bottom: 1rem;
}

.product-card h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.add-to-cart-btn {
    width: 100%;
    padding: 0.75rem;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-to-cart-btn:hover {
    transform: scale(1.02);
}

/* Testimonials */
.testimonials {
    background: var(--gradient-secondary);
    padding: 4rem 2rem;
    margin: 4rem 0;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.testimonial-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 16px;
    box-shadow: var(--shadow-md);
}

.stars {
    color: #fbbf24;
    margin-bottom: 1rem;
}

.testimonial-card p {
    font-style: italic;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.author strong {
    display: block;
    margin-bottom: 0.25rem;
}

.author span {
    color: var(--medium-gray);
    font-size: 0.9rem;
}

/* Newsletter */
.newsletter {
    background: var(--gradient-primary);
    color: white;
    padding: 4rem 2rem;
    text-align: center;
}

.newsletter-content {
    max-width: 600px;
    margin: 0 auto;
}

.newsletter h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.newsletter p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    gap: 1rem;
    max-width: 400px;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
}

.newsletter-form input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

/* Cart Modal */
.cart-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.cart-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-content {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
}

.cart-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.close-cart {
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    color: var(--medium-gray);
}

.cart-items {
    padding: 1.5rem;
    min-height: 200px;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.cart-item:last-child {
    border-bottom: none;
}

.item-info h4 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.item-price {
    color: var(--primary-blue);
    font-weight: 600;
}

.remove-item {
    background: var(--accent-orange);
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

.cart-footer {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
}

.cart-total {
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.checkout-btn {
    width: 100%;
}

.empty-cart {
    text-align: center;
    color: var(--medium-gray);
    font-style: italic;
    padding: 2rem 0;
}

/* Footer */
.footer {
    background: var(--dark-gray);
    color: white;
    text-align: center;
    padding: 2rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero {
        flex-direction: column;
        gap: 2rem;
        padding: 3rem 2rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .features {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }
    
    .nav-search {
        order: 3;
        max-width: 100%;
    }
    
    .hero {
        flex-direction: column;
        text-align: center;
        padding: 2rem 1rem;
        gap: 2rem;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .features {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        padding: 0 1rem;
        gap: 1.5rem;
    }
    
    .products {
        padding: 0 1rem;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }
    
    .testimonials {
        padding: 3rem 1rem;
    }
    
    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .newsletter {
        padding: 3rem 1rem;
    }
    
    .newsletter-form {
        flex-direction: column;
        gap: 1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .cart-content {
        width: 90%;
        max-width: 400px;
    }
}

@media (max-width: 480px) {
    .nav {
        padding: 0.75rem;
    }
    
    .hero {
        padding: 1.5rem 0.75rem;
    }
    
    .hero-title {
        font-size: 1.75rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .features {
        grid-template-columns: 1fr;
        padding: 0 0.75rem;
        gap: 1rem;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    .products {
        padding: 0 0.75rem;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .product-card {
        padding: 1rem;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .testimonials {
        padding: 2rem 0.75rem;
    }
    
    .testimonial-card {
        padding: 1.5rem;
    }
    
    .newsletter {
        padding: 2rem 0.75rem;
    }
    
    .newsletter h2 {
        font-size: 1.75rem;
    }
    
    .newsletter p {
        font-size: 1rem;
    }
    
    .cart-content {
        width: 95%;
        margin: 0.5rem;
    }
    
    .cart-header,
    .cart-items,
    .cart-footer {
        padding: 1rem;
    }
    
    .cta-btn {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }
}

@media (max-width: 360px) {
    .hero-title {
        font-size: 1.5rem;
    }
    
    .section-title {
        font-size: 1.5rem;
    }
    
    .feature-card h3,
    .product-card h3 {
        font-size: 1rem;
    }
    
    .price {
        font-size: 1.1rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content,
.feature-card,
.product-card,
.testimonial-card {
    animation: fadeInUp 0.6s ease forwards;
}

.product-card:nth-child(2) { animation-delay: 0.1s; }
.product-card:nth-child(3) { animation-delay: 0.2s; }
.product-card:nth-child(4) { animation-delay: 0.3s; }
.product-card:nth-child(5) { animation-delay: 0.4s; }
.product-card:nth-child(6) { animation-delay: 0.5s; }