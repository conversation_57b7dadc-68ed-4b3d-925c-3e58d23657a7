:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    
    --bg-dark: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-card: #ffffff;
    --text-primary: #1a1a1a;
    --text-secondary: #666666;
    --text-light: #999999;
    --border-color: #e5e5e5;
    --shadow: 0 20px 40px rgba(0,0,0,0.1);
    --shadow-lg: 0 40px 80px rgba(0,0,0,0.15);
    
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --border-radius: 16px;
    --border-radius-lg: 24px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-primary);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Header & Navigation */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 1000;
    transition: var(--transition);
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-brand {
    font-size: 24px;
    font-weight: 600;
}

.brand-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 32px;
    align-items: center;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-menu a:hover {
    color: #667eea;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: var(--transition);
}

.nav-menu a:hover::after {
    width: 100%;
}

.cta-nav {
    background: var(--primary-gradient);
    color: white !important;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
}

.cta-nav::after {
    display: none;
}

.cta-nav:hover {
    color: white !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 24px;
    height: 3px;
    background: var(--text-primary);
    border-radius: 2px;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    display: flex;
    align-items: center;
    min-height: 100vh;
    padding: 120px 24px 80px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%" r="50%"><stop offset="0%" stop-color="%23667eea" stop-opacity="0.1"/><stop offset="100%" stop-color="%23667eea" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="300" cy="800" r="120" fill="url(%23a)"/></svg>');
    opacity: 0.5;
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(2deg); }
}

.hero-content {
    flex: 1;
    max-width: 600px;
    z-index: 2;
    position: relative;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 24px;
    letter-spacing: -0.02em;
}

.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 40px;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 4px;
}

.hero-actions {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    display: inline-flex;
    align-items: center;
    padding: 16px 32px;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-family: inherit;
    font-size: 1rem;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: white;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-2px);
}

.hero-visual {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-cards {
    position: relative;
    width: 300px;
    height: 400px;
}

.hero-card {
    position: absolute;
    width: 200px;
    height: 120px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: var(--transition);
}

.hero-card:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: var(--shadow-lg);
}

.card-1 {
    top: 0;
    left: 0;
    animation: cardFloat1 6s ease-in-out infinite;
}

.card-2 {
    top: 140px;
    right: 0;
    animation: cardFloat2 6s ease-in-out infinite 2s;
}

.card-3 {
    bottom: 0;
    left: 50px;
    animation: cardFloat3 6s ease-in-out infinite 4s;
}

@keyframes cardFloat1 {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes cardFloat2 {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

@keyframes cardFloat3 {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

.card-logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    flex-shrink: 0;
}

.card-1 .card-logo {
    background: var(--primary-gradient);
}

.card-2 .card-logo {
    background: var(--success-gradient);
}

.card-3 .card-logo {
    background: var(--warning-gradient);
}

.card-content h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 4px;
}

.card-content p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Section Styles */
.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    text-align: center;
    margin-bottom: 16px;
    letter-spacing: -0.02em;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: 60px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Portfolio Section */
.portfolio {
    padding: 100px 0;
    background: white;
}

.portfolio-filter {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-bottom: 60px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 12px 24px;
    border: 2px solid var(--border-color);
    background: white;
    border-radius: 50px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    font-family: inherit;
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--primary-gradient);
    border-color: transparent;
    color: white;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
}

.portfolio-item {
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.portfolio-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.portfolio-image {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.portfolio-placeholder {
    width: 100%;
    height: 100%;
    border-radius: 0;
}

.logo-placeholder-1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.logo-placeholder-1::after {
    content: 'TechVision';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 1.5rem;
    font-weight: 800;
}

.web-placeholder-1 {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    position: relative;
}

.web-placeholder-1::after {
    content: '🛒';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3rem;
}

.logo-placeholder-2 {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    position: relative;
}

.logo-placeholder-2::after {
    content: '💪';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3rem;
}

.print-placeholder-1 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    position: relative;
}

.print-placeholder-1::after {
    content: '🍽️';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3rem;
}

.web-placeholder-2 {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    position: relative;
}

.web-placeholder-2::after {
    content: '🏥';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3rem;
}

.logo-placeholder-3 {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    position: relative;
}

.logo-placeholder-3::after {
    content: '🔨';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3rem;
}

.portfolio-info {
    padding: 24px;
}

.portfolio-info h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.portfolio-info p {
    color: var(--text-secondary);
    margin-bottom: 16px;
}

.portfolio-tag {
    display: inline-block;
    padding: 6px 12px;
    background: var(--primary-gradient);
    color: white;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Success Stories */
.success-stories {
    padding: 100px 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.stories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.story-card {
    background: white;
    padding: 40px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.story-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.story-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.story-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.story-result {
    font-size: 1.25rem;
    font-weight: 700;
    background: var(--success-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 16px;
}

.story-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Process Section */
.process {
    padding: 100px 0;
    background: white;
}

.process-steps {
    display: grid;
    gap: 40px;
    max-width: 800px;
    margin: 0 auto;
}

.process-step {
    display: flex;
    gap: 24px;
    align-items: flex-start;
}

.step-number {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    font-weight: 700;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 12px;
}

.step-content p {
    color: var(--text-secondary);
    margin-bottom: 16px;
    line-height: 1.6;
}

.step-content ul {
    list-style: none;
    color: var(--text-secondary);
}

.step-content li {
    position: relative;
    padding-left: 20px;
    margin-bottom: 8px;
}

.step-content li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #43e97b;
    font-weight: bold;
}

/* Pricing Section */
.pricing {
    padding: 100px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.pricing .section-title {
    color: white;
}

.pricing .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-bottom: 60px;
}

.pricing-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 40px;
    position: relative;
    transition: var(--transition);
    color: var(--text-primary);
}

.pricing-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.pricing-card.featured {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.pricing-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--success-gradient);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.pricing-header {
    text-align: center;
    margin-bottom: 30px;
}

.pricing-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.price {
    margin-bottom: 8px;
}

.price-amount {
    font-size: 3rem;
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.price-currency {
    font-size: 1.25rem;
    color: var(--text-secondary);
    font-weight: 600;
}

.price-description {
    color: var(--text-secondary);
}

.pricing-features {
    list-style: none;
    margin-bottom: 30px;
}

.pricing-features li {
    position: relative;
    padding: 12px 0 12px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.pricing-features li:last-child {
    border-bottom: none;
}

.pricing-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #43e97b;
    font-weight: bold;
}

.btn-pricing {
    width: 100%;
    text-align: center;
    background: var(--primary-gradient);
    color: white;
    padding: 16px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    display: block;
}

.btn-pricing:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.pricing-note {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 24px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.pricing-note p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
}

/* Testimonials */
.testimonials {
    padding: 100px 0;
    background: white;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
}

.testimonial-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 40px;
    position: relative;
    transition: var(--transition);
}

.testimonial-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.testimonial-rating {
    font-size: 1.25rem;
    margin-bottom: 20px;
}

.testimonial-card blockquote {
    font-size: 1.125rem;
    line-height: 1.6;
    margin-bottom: 24px;
    font-style: italic;
    color: var(--text-secondary);
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
}

.author-info h4 {
    font-weight: 600;
    margin-bottom: 4px;
}

.author-info p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.testimonial-result {
    display: inline-block;
    background: var(--success-gradient);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

/* CTA Section */
.cta-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    text-align: center;
}

.cta-content h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    margin-bottom: 16px;
    color: white;
}

.cta-content p {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 32px;
    flex-wrap: wrap;
}

.cta-offer {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.2);
    padding: 12px 20px;
    border-radius: 50px;
    color: white;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.offer-icon {
    font-size: 1.25rem;
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    max-width: 1000px;
    margin: 0 auto;
}

.contact-form {
    display: grid;
    gap: 24px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    transition: var(--transition);
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.checkbox-group {
    flex-direction: row;
    align-items: flex-start;
    gap: 12px;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    cursor: pointer;
    font-weight: 400;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    cursor: pointer;
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked {
    background: var(--primary-gradient);
    border-color: transparent;
}

.checkbox-label input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.875rem;
    font-weight: bold;
}

.checkbox-label a {
    color: #667eea;
    text-decoration: underline;
}

.btn-submit {
    position: relative;
    overflow: hidden;
}

.btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.contact-card,
.contact-guarantee {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 32px;
}

.contact-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 24px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 20px;
}

.contact-item:last-child {
    margin-bottom: 0;
}

.contact-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 12px;
    flex-shrink: 0;
}

.contact-item strong {
    display: block;
    margin-bottom: 4px;
    font-weight: 600;
}

.contact-item p {
    color: var(--text-secondary);
    margin: 0;
}

.contact-guarantee h4 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 16px;
}

.contact-guarantee ul {
    list-style: none;
}

.contact-guarantee li {
    position: relative;
    padding-left: 24px;
    margin-bottom: 8px;
    color: var(--text-secondary);
}

.contact-guarantee li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #43e97b;
    font-weight: bold;
}

/* Footer */
.footer {
    background: var(--bg-dark);
    color: white;
    padding: 60px 0 24px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 60px;
    margin-bottom: 40px;
}

.footer-brand h3 {
    font-size: 1.5rem;
    margin-bottom: 8px;
}

.footer-brand p {
    color: rgba(255, 255, 255, 0.7);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 40px;
}

.footer-column h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 16px;
}

.footer-column ul {
    list-style: none;
}

.footer-column li {
    margin-bottom: 8px;
}

.footer-column a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition);
}

.footer-column a:hover {
    color: white;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 24px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.5);
    margin: 0;
}

.footer-social {
    display: flex;
    gap: 16px;
}

.footer-social a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 1.25rem;
    transition: var(--transition);
}

.footer-social a:hover {
    background: var(--primary-gradient);
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: white;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 60px;
        transition: var(--transition);
        box-shadow: var(--shadow);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 16px 0;
    }

    .hero {
        flex-direction: column;
        text-align: center;
        padding: 100px 24px 60px;
    }

    .hero-content {
        margin-bottom: 60px;
    }

    .hero-stats {
        justify-content: center;
    }

    .hero-actions {
        justify-content: center;
    }

    .hero-visual {
        order: -1;
        margin-bottom: 40px;
    }

    .hero-cards {
        width: 250px;
        height: 300px;
        transform: scale(0.8);
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
    }

    .stories-grid {
        grid-template-columns: 1fr;
    }

    .process-step {
        flex-direction: column;
        text-align: center;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }

    .cta-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 16px;
    }

    .nav {
        padding: 16px;
    }

    .hero {
        padding: 80px 16px 40px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-stats {
        gap: 24px;
    }

    .hero-actions {
        flex-direction: column;
        width: 100%;
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
        justify-content: center;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .pricing-card {
        padding: 24px;
    }

    .contact-card,
    .contact-guarantee {
        padding: 24px;
    }
}

/* Animation utilities */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Portfolio filter animations */
.portfolio-item.hidden {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.portfolio-item.visible {
    opacity: 1;
    transform: scale(1);
    transition: all 0.3s ease;
}