// AI Service with HuggingFace Integration for SmartCard.pl
class AIService {
    constructor() {
        this.config = window.configManager.getHuggingFaceConfig();
        this.cache = new Map();
        this.requestQueue = [];
        this.isProcessing = false;
        this.init();
    }

    init() {
        this.setupRateLimiting();
        this.setupErrorHandling();
        this.loadCacheFromStorage();
    }

    setupRateLimiting() {
        this.rateLimiter = {
            requests: 0,
            windowStart: Date.now(),
            maxRequests: 100, // per hour
            windowSize: 60 * 60 * 1000 // 1 hour
        };
    }

    setupErrorHandling() {
        this.errorHandler = {
            maxRetries: 3,
            retryDelay: 1000,
            backoffMultiplier: 2
        };
    }

    // Core API request method
    async makeRequest(modelName, payload, options = {}) {
        if (!this.checkRateLimit()) {
            throw new Error('Rate limit exceeded. Please try again later.');
        }

        const url = `${this.config.baseUrl}/${modelName}`;
        const cacheKey = this.generateCacheKey(modelName, payload);

        // Check cache first
        if (this.cache.has(cacheKey) && !options.bypassCache) {
            return this.cache.get(cacheKey);
        }

        const requestOptions = {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.config.token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ...payload,
                options: {
                    ...this.config.options,
                    ...options
                }
            })
        };

        try {
            const response = await this.executeWithRetry(url, requestOptions);
            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || `HTTP ${response.status}`);
            }

            // Cache successful response
            this.cache.set(cacheKey, result);
            this.saveCacheToStorage();

            this.updateRateLimit();
            return result;

        } catch (error) {
            console.error('AI Service request failed:', error);
            throw this.handleAPIError(error);
        }
    }

    async executeWithRetry(url, options, attempt = 1) {
        try {
            const response = await fetch(url, options);
            
            // If model is loading, wait and retry
            if (response.status === 503) {
                const result = await response.json();
                if (result.error?.includes('loading')) {
                    const waitTime = result.estimated_time || 10000;
                    await this.sleep(waitTime);
                    return this.executeWithRetry(url, options, attempt);
                }
            }

            return response;

        } catch (error) {
            if (attempt < this.errorHandler.maxRetries) {
                const delay = this.errorHandler.retryDelay * Math.pow(this.errorHandler.backoffMultiplier, attempt - 1);
                await this.sleep(delay);
                return this.executeWithRetry(url, options, attempt + 1);
            }
            throw error;
        }
    }

    // AI-powered text optimization
    async optimizeBusinessCardText(data) {
        const model = this.config.models.textOptimizer;
        const prompt = this.buildTextOptimizationPrompt(data);

        try {
            const result = await this.makeRequest(model, {
                inputs: prompt,
                parameters: {
                    max_length: 150,
                    min_length: 50,
                    do_sample: true,
                    temperature: 0.7
                }
            });

            return this.parseTextOptimizationResult(result);

        } catch (error) {
            console.error('Text optimization failed:', error);
            return this.getFallbackTextOptimization(data);
        }
    }

    buildTextOptimizationPrompt(data) {
        return `Optimize this business card description to be more professional and engaging:

Name: ${data.name || 'Professional'}
Company: ${data.company || 'Company'}
Title: ${data.title || 'Position'}
Current Description: ${data.description || 'No description provided'}

Industry Context: ${this.detectIndustry(data)}

Create a compelling, professional description in Polish that:
- Highlights key strengths and expertise
- Uses action-oriented language
- Stays under 100 characters
- Maintains professional tone
- Includes relevant keywords for the industry

Optimized description:`;
    }

    detectIndustry(data) {
        const industries = {
            tech: ['developer', 'programmer', 'engineer', 'tech', 'IT', 'software'],
            marketing: ['marketing', 'brand', 'digital', 'social media', 'content'],
            sales: ['sales', 'business development', 'account', 'revenue'],
            design: ['designer', 'creative', 'UI', 'UX', 'graphic', 'artist'],
            finance: ['finance', 'accounting', 'bank', 'investment', 'consultant'],
            healthcare: ['doctor', 'nurse', 'medical', 'health', 'therapy'],
            education: ['teacher', 'professor', 'education', 'training', 'academic'],
            consulting: ['consultant', 'advisor', 'strategy', 'management']
        };

        const text = `${data.title} ${data.company} ${data.description}`.toLowerCase();
        
        for (const [industry, keywords] of Object.entries(industries)) {
            if (keywords.some(keyword => text.includes(keyword))) {
                return industry;
            }
        }
        
        return 'general';
    }

    parseTextOptimizationResult(result) {
        if (Array.isArray(result) && result[0]?.generated_text) {
            const optimized = result[0].generated_text
                .split('Optimized description:')[1]
                ?.trim()
                .substring(0, 100);
            
            return optimized || 'Professional specialist focused on delivering exceptional results';
        }
        
        return 'Professional specialist focused on delivering exceptional results';
    }

    getFallbackTextOptimization(data) {
        const templates = {
            tech: 'Innowacyjny specjalista technologii z pasją do rozwiązań cyfrowych',
            marketing: 'Kreatywny ekspert marketingu budujący silne marki i relacje',
            sales: 'Doświadczony profesjonalista sprzedaży zorientowany na rezultaty',
            design: 'Kreatywny designer tworzący inspirujące rozwiązania wizualne',
            finance: 'Ekspert finansowy wspierający rozwój biznesu przez mądre inwestycje',
            consulting: 'Doradca strategiczny pomagający firmom osiągać cele biznesowe',
            general: 'Doświadczony profesjonalista zorientowany na doskonałość'
        };

        const industry = this.detectIndustry(data);
        return templates[industry] || templates.general;
    }

    // AI-powered avatar generation
    async generateAvatar(imageData, style = 'professional', options = {}) {
        const model = this.config.models.avatarGenerator;
        
        try {
            // If we have an uploaded image, enhance it
            if (imageData) {
                return await this.enhanceExistingImage(imageData, style, options);
            } else {
                return await this.generateAvatarFromText(options.name || 'Professional', style);
            }

        } catch (error) {
            console.error('Avatar generation failed:', error);
            return this.generateFallbackAvatar(style);
        }
    }

    async enhanceExistingImage(imageData, style, options) {
        // For now, apply client-side filters since HF image models are complex
        return this.applyStyleFilters(imageData, style, options);
    }

    async generateAvatarFromText(name, style) {
        const model = this.config.models.avatarGenerator;
        const prompt = this.buildAvatarPrompt(name, style);

        try {
            const result = await this.makeRequest(model, {
                inputs: prompt,
                parameters: {
                    guidance_scale: 7.5,
                    num_inference_steps: 50,
                    width: 512,
                    height: 512
                }
            });

            if (result && result.length > 0) {
                return `data:image/jpeg;base64,${result}`;
            }

        } catch (error) {
            console.error('Text-to-image generation failed:', error);
        }

        return this.generateFallbackAvatar(style);
    }

    buildAvatarPrompt(name, style) {
        const stylePrompts = {
            professional: 'professional business headshot, corporate attire, clean background, high quality, realistic',
            cartoon: 'cartoon avatar, friendly animated character, clean vector style, bright colors',
            abstract: 'abstract geometric portrait, modern art style, colorful shapes, artistic',
            minimal: 'minimalist line art portrait, simple clean design, monochrome'
        };

        return `${stylePrompts[style] || stylePrompts.professional}, ${name}, portrait photography, studio lighting`;
    }

    applyStyleFilters(imageData, style, options) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                canvas.width = 200;
                canvas.height = 200;

                // Draw original image
                ctx.drawImage(img, 0, 0, 200, 200);

                // Apply style-specific filters
                this.applyImageFilters(ctx, style, options);

                resolve(canvas.toDataURL('image/png'));
            };

            img.src = imageData;
        });
    }

    applyImageFilters(ctx, style, options) {
        const imageData = ctx.getImageData(0, 0, 200, 200);
        const data = imageData.data;

        switch (style) {
            case 'professional':
                this.enhanceContrast(data, 1.2);
                this.adjustSaturation(data, 0.9);
                break;

            case 'cartoon':
                this.posterize(data, 16);
                this.enhanceSaturation(data, 1.3);
                break;

            case 'abstract':
                this.posterize(data, 8);
                this.shiftHues(data, 30);
                break;

            case 'minimal':
                this.desaturate(data);
                this.increaseContrast(data, 1.5);
                break;
        }

        ctx.putImageData(imageData, 0, 0);
    }

    enhanceContrast(data, factor) {
        for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.min(255, Math.max(0, (data[i] - 128) * factor + 128));
            data[i + 1] = Math.min(255, Math.max(0, (data[i + 1] - 128) * factor + 128));
            data[i + 2] = Math.min(255, Math.max(0, (data[i + 2] - 128) * factor + 128));
        }
    }

    adjustSaturation(data, factor) {
        for (let i = 0; i < data.length; i += 4) {
            const gray = 0.3 * data[i] + 0.59 * data[i + 1] + 0.11 * data[i + 2];
            data[i] = Math.min(255, Math.max(0, gray + factor * (data[i] - gray)));
            data[i + 1] = Math.min(255, Math.max(0, gray + factor * (data[i + 1] - gray)));
            data[i + 2] = Math.min(255, Math.max(0, gray + factor * (data[i + 2] - gray)));
        }
    }

    posterize(data, levels) {
        const step = 255 / (levels - 1);
        for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.round(data[i] / step) * step;
            data[i + 1] = Math.round(data[i + 1] / step) * step;
            data[i + 2] = Math.round(data[i + 2] / step) * step;
        }
    }

    enhanceSaturation(data, factor) {
        this.adjustSaturation(data, factor);
    }

    shiftHues(data, degrees) {
        const shift = degrees * Math.PI / 180;
        for (let i = 0; i < data.length; i += 4) {
            const [h, s, l] = this.rgbToHsl(data[i], data[i + 1], data[i + 2]);
            const newH = (h + shift) % (2 * Math.PI);
            const [r, g, b] = this.hslToRgb(newH, s, l);
            data[i] = r;
            data[i + 1] = g;
            data[i + 2] = b;
        }
    }

    desaturate(data) {
        for (let i = 0; i < data.length; i += 4) {
            const gray = 0.3 * data[i] + 0.59 * data[i + 1] + 0.11 * data[i + 2];
            data[i] = gray;
            data[i + 1] = gray;
            data[i + 2] = gray;
        }
    }

    increaseContrast(data, factor) {
        this.enhanceContrast(data, factor);
    }

    rgbToHsl(r, g, b) {
        r /= 255; g /= 255; b /= 255;
        const max = Math.max(r, g, b), min = Math.min(r, g, b);
        let h, s, l = (max + min) / 2;

        if (max === min) {
            h = s = 0;
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }

        return [h * 2 * Math.PI, s, l];
    }

    hslToRgb(h, s, l) {
        h = h / (2 * Math.PI);
        let r, g, b;

        if (s === 0) {
            r = g = b = l;
        } else {
            const hue2rgb = (p, q, t) => {
                if (t < 0) t += 1;
                if (t > 1) t -= 1;
                if (t < 1/6) return p + (q - p) * 6 * t;
                if (t < 1/2) return q;
                if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                return p;
            };

            const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
            const p = 2 * l - q;
            r = hue2rgb(p, q, h + 1/3);
            g = hue2rgb(p, q, h);
            b = hue2rgb(p, q, h - 1/3);
        }

        return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
    }

    generateFallbackAvatar(style) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = 200;
        canvas.height = 200;

        const colors = {
            professional: ['#2c3e50', '#3498db'],
            cartoon: ['#ff6b6b', '#4ecdc4'],
            abstract: ['#a8e6cf', '#ffd93d'],
            minimal: ['#95a5a6', '#ecf0f1']
        };

        const [color1, color2] = colors[style] || colors.professional;

        // Create gradient background
        const gradient = ctx.createLinearGradient(0, 0, 200, 200);
        gradient.addColorStop(0, color1);
        gradient.addColorStop(1, color2);

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 200, 200);

        // Add simple pattern
        ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.beginPath();
        ctx.arc(100, 100, 60, 0, Math.PI * 2);
        ctx.fill();

        return canvas.toDataURL('image/png');
    }

    // Smart content suggestions
    async generateContentSuggestions(cardData) {
        const model = this.config.models.llama;
        const prompt = this.buildContentSuggestionsPrompt(cardData);

        try {
            const result = await this.makeRequest(model, {
                inputs: prompt,
                parameters: {
                    max_new_tokens: 200,
                    temperature: 0.8,
                    return_full_text: false
                }
            });

            return this.parseContentSuggestions(result);

        } catch (error) {
            console.error('Content suggestions failed:', error);
            return this.getFallbackSuggestions(cardData);
        }
    }

    buildContentSuggestionsPrompt(cardData) {
        return `Jako ekspert marketingu i personal brandingu, wygeneruj profesjonalne sugestie dla wizytówki:

Dane:
- Imię: ${cardData.name || '[Brak]'}
- Stanowisko: ${cardData.title || '[Brak]'}
- Firma: ${cardData.company || '[Brak]'}
- Branża: ${this.detectIndustry(cardData)}

Wygeneruj w języku polskim:

1. OPIS (max 80 znaków):
[Krótki, chwytliwy opis pozycji/specjalizacji]

2. TAGLINE (max 50 znaków):
[Motto/hasło osobiste]

3. SŁOWA KLUCZOWE (5 słów):
[Kluczowe kompetencje/obszary]

4. CALL TO ACTION:
[Zachęta do kontaktu]

Odpowiedź:`;
    }

    parseContentSuggestions(result) {
        if (!result || !result[0]?.generated_text) {
            return this.getFallbackSuggestions();
        }

        const text = result[0].generated_text;
        const suggestions = {
            description: this.extractSection(text, 'OPIS', 80),
            tagline: this.extractSection(text, 'TAGLINE', 50),
            keywords: this.extractSection(text, 'SŁOWA KLUCZOWE', 100)?.split(',').map(k => k.trim()),
            callToAction: this.extractSection(text, 'CALL TO ACTION', 60)
        };

        return suggestions;
    }

    extractSection(text, sectionName, maxLength) {
        const regex = new RegExp(`${sectionName}[^\\n]*\\n([^\\n]+)`, 'i');
        const match = text.match(regex);
        if (match && match[1]) {
            return match[1].replace(/^\[|\]$/g, '').trim().substring(0, maxLength);
        }
        return null;
    }

    getFallbackSuggestions(cardData = {}) {
        const industry = this.detectIndustry(cardData);
        
        const fallbacks = {
            tech: {
                description: 'Innowacyjny developer tworzący rozwiązania przyszłości',
                tagline: 'Kod to moja pasja',
                keywords: ['JavaScript', 'React', 'Node.js', 'AI', 'Innovation'],
                callToAction: 'Stwórzmy coś niesamowitego razem!'
            },
            marketing: {
                description: 'Kreatywny marketer budujący silne marki',
                tagline: 'Marka to emocje',
                keywords: ['Digital Marketing', 'Brand', 'Strategy', 'Growth', 'ROI'],
                callToAction: 'Zwiększmy zasięg Twojej marki!'
            },
            sales: {
                description: 'Ekspert sprzedaży zorientowany na rezultaty',
                tagline: 'Przekształcam leads w success',
                keywords: ['Sales', 'B2B', 'Negotiations', 'CRM', 'Growth'],
                callToAction: 'Porozmawiajmy o Twoich celach!'
            },
            general: {
                description: 'Profesjonalista zorientowany na doskonałość',
                tagline: 'Jakość to mój priorytet',
                keywords: ['Excellence', 'Innovation', 'Results', 'Collaboration', 'Growth'],
                callToAction: 'Skontaktuj się ze mną!'
            }
        };

        return fallbacks[industry] || fallbacks.general;
    }

    // Translation services
    async translateContent(text, targetLanguage = 'pl') {
        if (!text) return text;

        const model = this.config.models.translator;
        
        try {
            const result = await this.makeRequest(model, {
                inputs: text,
                parameters: {
                    src_lang: 'en',
                    tgt_lang: targetLanguage
                }
            });

            return result[0]?.translation_text || text;

        } catch (error) {
            console.error('Translation failed:', error);
            return text;
        }
    }

    // Utility methods
    checkRateLimit() {
        const now = Date.now();
        
        // Reset window if needed
        if (now - this.rateLimiter.windowStart > this.rateLimiter.windowSize) {
            this.rateLimiter.requests = 0;
            this.rateLimiter.windowStart = now;
        }

        return this.rateLimiter.requests < this.rateLimiter.maxRequests;
    }

    updateRateLimit() {
        this.rateLimiter.requests++;
    }

    generateCacheKey(model, payload) {
        const key = `${model}_${JSON.stringify(payload)}`;
        return btoa(key).substring(0, 50); // Base64 encode and limit length
    }

    loadCacheFromStorage() {
        try {
            const stored = localStorage.getItem('smartcard-ai-cache');
            if (stored) {
                const cacheData = JSON.parse(stored);
                this.cache = new Map(cacheData);
            }
        } catch (error) {
            console.error('Failed to load AI cache:', error);
        }
    }

    saveCacheToStorage() {
        try {
            // Limit cache size
            if (this.cache.size > 100) {
                const entries = Array.from(this.cache.entries());
                this.cache = new Map(entries.slice(-50)); // Keep last 50 entries
            }

            localStorage.setItem('smartcard-ai-cache', JSON.stringify(Array.from(this.cache.entries())));
        } catch (error) {
            console.error('Failed to save AI cache:', error);
        }
    }

    handleAPIError(error) {
        const errorMap = {
            401: 'Invalid API token',
            429: 'Rate limit exceeded',
            503: 'Model is currently loading',
            500: 'Server error occurred'
        };

        if (error.message.includes('fetch')) {
            return new Error('Network connection failed');
        }

        return new Error(errorMap[error.status] || error.message || 'Unknown AI service error');
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Health check
    async healthCheck() {
        try {
            const model = this.config.models.llama;
            await this.makeRequest(model, {
                inputs: 'Health check',
                parameters: { max_new_tokens: 5 }
            });
            return { status: 'healthy', timestamp: new Date().toISOString() };
        } catch (error) {
            return { status: 'unhealthy', error: error.message, timestamp: new Date().toISOString() };
        }
    }

    // Get service stats
    getStats() {
        return {
            cacheSize: this.cache.size,
            rateLimitInfo: {
                requests: this.rateLimiter.requests,
                remaining: this.rateLimiter.maxRequests - this.rateLimiter.requests,
                resetTime: new Date(this.rateLimiter.windowStart + this.rateLimiter.windowSize)
            },
            queueLength: this.requestQueue.length
        };
    }
}

// Initialize AI Service
document.addEventListener('DOMContentLoaded', () => {
    window.aiService = new AIService();
});

// Global functions for AI operations
window.optimizeText = (data) => window.aiService?.optimizeBusinessCardText(data);
window.generateAvatar = (imageData, style, options) => window.aiService?.generateAvatar(imageData, style, options);
window.getContentSuggestions = (cardData) => window.aiService?.generateContentSuggestions(cardData);
window.translateText = (text, lang) => window.aiService?.translateContent(text, lang);

// Export for use in other modules
window.AIService = AIService;