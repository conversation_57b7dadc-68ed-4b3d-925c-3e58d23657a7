// QR Code Generator and NFC Simulator for SmartCard.pl
class QRCodeGenerator {
    constructor() {
        this.init();
    }

    init() {
        this.setupQRGeneration();
        this.setupNFCSimulator();
        this.loadQRLibrary();
    }

    async loadQRLibrary() {
        // Load QR code generation library
        if (!window.QRCode) {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js';
            script.onload = () => {
                console.log('QR Code library loaded successfully');
            };
            document.head.appendChild(script);
        }
    }

    setupQRGeneration() {
        this.qrTypes = {
            vcard: this.generateVCard,
            url: this.generateURL,
            linkedin: this.generateLinkedIn,
            email: this.generateEmail,
            phone: this.generatePhone,
            sms: this.generateSMS,
            wifi: this.generateWiFi,
            location: this.generateLocation,
            custom: this.generateCustom
        };
    }

    setupNFCSimulator() {
        this.nfcActions = {
            addContact: this.simulateAddContact,
            openWebsite: this.simulateOpenWebsite,
            sendEmail: this.simulateSendEmail,
            callPhone: this.simulateCallPhone,
            shareCard: this.simulateShareCard
        };
    }

    // QR Code Generation Methods
    async generateQRCode(data, type = 'vcard', options = {}) {
        try {
            const qrData = await this.qrTypes[type].call(this, data);
            const qrOptions = {
                errorCorrectionLevel: 'M',
                type: 'image/png',
                quality: 0.92,
                margin: 1,
                color: {
                    dark: options.dark || '#000000',
                    light: options.light || '#FFFFFF'
                },
                width: options.width || 200,
                ...options
            };

            if (window.QRCode) {
                return await QRCode.toDataURL(qrData, qrOptions);
            } else {
                // Fallback to simple QR pattern
                return this.generateFallbackQR(qrData, qrOptions);
            }
        } catch (error) {
            console.error('Error generating QR code:', error);
            return this.generateErrorQR();
        }
    }

    generateVCard(data) {
        const vcard = [
            'BEGIN:VCARD',
            'VERSION:3.0',
            `FN:${data.name || ''}`,
            `ORG:${data.company || ''}`,
            `TITLE:${data.title || ''}`,
            `EMAIL:${data.email || ''}`,
            `TEL:${data.phone || ''}`,
            `URL:${data.website || ''}`,
            data.address ? `ADR:;;${data.address};;;;` : '',
            data.linkedin ? `URL:${data.linkedin}` : '',
            data.description ? `NOTE:${data.description}` : '',
            'END:VCARD'
        ].filter(line => line && !line.endsWith(':'));

        return vcard.join('\n');
    }

    generateURL(data) {
        return data.website || data.url || 'https://smartcard.pl';
    }

    generateLinkedIn(data) {
        return data.linkedin || `https://linkedin.com/in/${data.name?.replace(/\s+/g, '').toLowerCase()}`;
    }

    generateEmail(data) {
        const subject = encodeURIComponent(`Kontakt od ${data.name || 'SmartCard'}`);
        const body = encodeURIComponent(`Cześć!\n\nSkontaktuj się ze mną:\n${data.name || ''}\n${data.company || ''}\n${data.phone || ''}`);
        return `mailto:${data.email}?subject=${subject}&body=${body}`;
    }

    generatePhone(data) {
        return `tel:${data.phone || ''}`;
    }

    generateSMS(data) {
        const message = encodeURIComponent(`Cześć! To ${data.name} z ${data.company}. Miło Cię poznać!`);
        return `sms:${data.phone}?body=${message}`;
    }

    generateWiFi(data) {
        // WiFi QR format: WIFI:T:WPA;S:network-name;P:password;H:false;;
        return `WIFI:T:${data.security || 'WPA'};S:${data.ssid || ''};P:${data.password || ''};H:${data.hidden || 'false'};;`;
    }

    generateLocation(data) {
        if (data.latitude && data.longitude) {
            return `geo:${data.latitude},${data.longitude}`;
        }
        if (data.address) {
            return `geo:0,0?q=${encodeURIComponent(data.address)}`;
        }
        return 'geo:52.2297,21.0122'; // Warsaw coordinates as default
    }

    generateCustom(data) {
        return data.customContent || data.content || 'https://smartcard.pl';
    }

    generateFallbackQR(data, options) {
        // Create a simple visual QR pattern when library isn't available
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const size = options.width || 200;
        
        canvas.width = size;
        canvas.height = size;
        
        // Fill background
        ctx.fillStyle = options.color?.light || '#FFFFFF';
        ctx.fillRect(0, 0, size, size);
        
        // Draw QR pattern
        ctx.fillStyle = options.color?.dark || '#000000';
        
        // Create a simple pattern
        const cellSize = size / 25;
        for (let i = 0; i < 25; i++) {
            for (let j = 0; j < 25; j++) {
                if (this.shouldFillCell(i, j, data)) {
                    ctx.fillRect(i * cellSize, j * cellSize, cellSize, cellSize);
                }
            }
        }
        
        return canvas.toDataURL('image/png');
    }

    shouldFillCell(x, y, data) {
        // Simple pattern based on data hash
        const hash = this.simpleHash(data + x + y);
        return hash % 3 === 0;
    }

    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash);
    }

    generateErrorQR() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = 200;
        canvas.height = 200;
        
        // Fill with error pattern
        ctx.fillStyle = '#ff4757';
        ctx.fillRect(0, 0, 200, 200);
        
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('QR ERROR', 100, 100);
        
        return canvas.toDataURL('image/png');
    }

    // Advanced QR Features
    async generateAnimatedQR(data, type = 'vcard', options = {}) {
        const frames = [];
        const colors = [
            '#667eea', '#764ba2', '#f093fb', '#f5576c',
            '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
        ];
        
        for (let i = 0; i < colors.length; i++) {
            const frameOptions = {
                ...options,
                color: {
                    dark: colors[i],
                    light: options.color?.light || '#FFFFFF'
                }
            };
            const frame = await this.generateQRCode(data, type, frameOptions);
            frames.push(frame);
        }
        
        return this.createAnimatedGIF(frames);
    }

    createAnimatedGIF(frames) {
        // For demo purposes, return the first frame
        // In a real implementation, you'd use a GIF library
        return frames[0];
    }

    async generateLogoQR(data, type = 'vcard', logoUrl, options = {}) {
        try {
            const qrDataUrl = await this.generateQRCode(data, type, options);
            return this.overlayLogo(qrDataUrl, logoUrl, options);
        } catch (error) {
            console.error('Error generating logo QR:', error);
            return this.generateErrorQR();
        }
    }

    async overlayLogo(qrDataUrl, logoUrl, options = {}) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const qrImg = new Image();
            
            qrImg.onload = () => {
                canvas.width = qrImg.width;
                canvas.height = qrImg.height;
                
                // Draw QR code
                ctx.drawImage(qrImg, 0, 0);
                
                // Load and draw logo
                const logoImg = new Image();
                logoImg.onload = () => {
                    const logoSize = Math.min(qrImg.width, qrImg.height) * (options.logoSize || 0.2);
                    const logoX = (qrImg.width - logoSize) / 2;
                    const logoY = (qrImg.height - logoSize) / 2;
                    
                    // Create white background for logo
                    ctx.fillStyle = 'white';
                    ctx.fillRect(logoX - 5, logoY - 5, logoSize + 10, logoSize + 10);
                    
                    // Draw logo
                    ctx.drawImage(logoImg, logoX, logoY, logoSize, logoSize);
                    
                    resolve(canvas.toDataURL('image/png'));
                };
                
                logoImg.onerror = () => resolve(qrDataUrl);
                logoImg.src = logoUrl;
            };
            
            qrImg.src = qrDataUrl;
        });
    }

    // NFC Simulation Methods
    setupNFCSimulation() {
        this.createNFCInterface();
        this.setupNFCEvents();
    }

    createNFCInterface() {
        if (document.getElementById('nfcSimulator')) return;

        const nfcInterface = document.createElement('div');
        nfcInterface.id = 'nfcSimulator';
        nfcInterface.className = 'nfc-simulator hidden';
        nfcInterface.innerHTML = `
            <div class="nfc-modal">
                <div class="nfc-header">
                    <h3><i class="fas fa-wifi"></i> Symulacja NFC</h3>
                    <button class="close-nfc" onclick="hideNFCSimulator()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="nfc-content">
                    <div class="phone-mockup-nfc">
                        <div class="phone-screen-nfc">
                            <div class="nfc-animation">
                                <div class="nfc-waves">
                                    <div class="wave wave-1"></div>
                                    <div class="wave wave-2"></div>
                                    <div class="wave wave-3"></div>
                                </div>
                                <div class="nfc-icon">
                                    <i class="fas fa-wifi"></i>
                                </div>
                                <div class="card-icon">
                                    <i class="fas fa-id-card"></i>
                                </div>
                            </div>
                            
                            <div class="nfc-status" id="nfcStatus">
                                <span>Zbliż telefon do wizytówki</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="nfc-actions">
                        <h4>Dostępne akcje:</h4>
                        <div class="action-buttons">
                            <button class="action-btn" onclick="simulateNFCAction('addContact')">
                                <i class="fas fa-user-plus"></i>
                                <span>Dodaj kontakt</span>
                            </button>
                            <button class="action-btn" onclick="simulateNFCAction('openWebsite')">
                                <i class="fas fa-globe"></i>
                                <span>Otwórz stronę</span>
                            </button>
                            <button class="action-btn" onclick="simulateNFCAction('sendEmail')">
                                <i class="fas fa-envelope"></i>
                                <span>Wyślij email</span>
                            </button>
                            <button class="action-btn" onclick="simulateNFCAction('callPhone')">
                                <i class="fas fa-phone"></i>
                                <span>Zadzwoń</span>
                            </button>
                            <button class="action-btn" onclick="simulateNFCAction('shareCard')">
                                <i class="fas fa-share"></i>
                                <span>Udostępnij</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="nfc-info">
                        <div class="info-section">
                            <h5>Co to jest NFC?</h5>
                            <p>Near Field Communication pozwala na przesyłanie danych między urządzeniami w odległości do 4cm.</p>
                        </div>
                        <div class="info-section">
                            <h5>Jak to działa?</h5>
                            <p>Zbliż telefon z włączonym NFC do wizytówki, a automatycznie otrzymasz wszystkie dane kontaktowe.</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(nfcInterface);
    }

    showNFCSimulator(cardData) {
        this.currentCardData = cardData;
        this.createNFCInterface();
        
        const simulator = document.getElementById('nfcSimulator');
        simulator.classList.remove('hidden');
        
        setTimeout(() => {
            simulator.classList.add('show');
        }, 10);
        
        this.startNFCAnimation();
    }

    hideNFCSimulator() {
        const simulator = document.getElementById('nfcSimulator');
        if (simulator) {
            simulator.classList.remove('show');
            setTimeout(() => {
                simulator.classList.add('hidden');
            }, 300);
        }
    }

    startNFCAnimation() {
        const waves = document.querySelectorAll('.wave');
        waves.forEach((wave, index) => {
            wave.style.animationDelay = `${index * 0.5}s`;
        });
    }

    simulateNFCAction(action) {
        const statusElement = document.getElementById('nfcStatus');
        const cardData = this.currentCardData || {};
        
        // Update status
        statusElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Łączenie...';
        
        setTimeout(() => {
            switch (action) {
                case 'addContact':
                    this.simulateAddContact(cardData);
                    break;
                case 'openWebsite':
                    this.simulateOpenWebsite(cardData);
                    break;
                case 'sendEmail':
                    this.simulateSendEmail(cardData);
                    break;
                case 'callPhone':
                    this.simulateCallPhone(cardData);
                    break;
                case 'shareCard':
                    this.simulateShareCard(cardData);
                    break;
            }
        }, 1500);
    }

    simulateAddContact(cardData) {
        const statusElement = document.getElementById('nfcStatus');
        statusElement.innerHTML = '<i class="fas fa-check text-success"></i> Kontakt dodany!';
        
        // Show contact preview
        this.showContactPreview(cardData);
        
        setTimeout(() => {
            statusElement.innerHTML = '<span>Zbliż telefon do wizytówki</span>';
        }, 3000);
    }

    simulateOpenWebsite(cardData) {
        const statusElement = document.getElementById('nfcStatus');
        statusElement.innerHTML = '<i class="fas fa-external-link-alt text-primary"></i> Otwieranie strony...';
        
        setTimeout(() => {
            if (cardData.website) {
                window.open(cardData.website, '_blank');
            } else {
                this.showNotification('Brak adresu strony internetowej', 'warning');
            }
            statusElement.innerHTML = '<span>Zbliż telefon do wizytówki</span>';
        }, 1000);
    }

    simulateSendEmail(cardData) {
        const statusElement = document.getElementById('nfcStatus');
        statusElement.innerHTML = '<i class="fas fa-envelope text-info"></i> Otwieranie klienta email...';
        
        setTimeout(() => {
            if (cardData.email) {
                const subject = encodeURIComponent(`Kontakt od ${cardData.name || 'SmartCard'}`);
                const body = encodeURIComponent(`Cześć!\n\nMiło było Cię poznać. Oto moje dane kontaktowe:\n\n${cardData.name || ''}\n${cardData.company || ''}\n${cardData.phone || ''}\n\nPozdrawiam!`);
                window.open(`mailto:${cardData.email}?subject=${subject}&body=${body}`);
            } else {
                this.showNotification('Brak adresu email', 'warning');
            }
            statusElement.innerHTML = '<span>Zbliż telefon do wizytówki</span>';
        }, 1000);
    }

    simulateCallPhone(cardData) {
        const statusElement = document.getElementById('nfcStatus');
        statusElement.innerHTML = '<i class="fas fa-phone text-success"></i> Wybieranie numeru...';
        
        setTimeout(() => {
            if (cardData.phone) {
                // On mobile devices, this would initiate a call
                if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
                    window.open(`tel:${cardData.phone}`);
                } else {
                    this.showNotification(`Numer telefonu: ${cardData.phone}`, 'info');
                }
            } else {
                this.showNotification('Brak numeru telefonu', 'warning');
            }
            statusElement.innerHTML = '<span>Zbliż telefon do wizytówki</span>';
        }, 1000);
    }

    simulateShareCard(cardData) {
        const statusElement = document.getElementById('nfcStatus');
        statusElement.innerHTML = '<i class="fas fa-share text-primary"></i> Udostępnianie...';
        
        setTimeout(() => {
            if (navigator.share) {
                navigator.share({
                    title: `Wizytówka - ${cardData.name}`,
                    text: `Sprawdź wizytówkę: ${cardData.name} z ${cardData.company}`,
                    url: window.location.href
                });
            } else {
                this.showShareModal(cardData);
            }
            statusElement.innerHTML = '<span>Zbliż telefon do wizytówki</span>';
        }, 1000);
    }

    showContactPreview(cardData) {
        const preview = document.createElement('div');
        preview.className = 'contact-preview-modal';
        preview.innerHTML = `
            <div class="contact-preview">
                <div class="preview-header">
                    <h4><i class="fas fa-user-plus"></i> Dodano kontakt</h4>
                    <button onclick="this.closest('.contact-preview-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="preview-content">
                    <div class="contact-avatar">
                        ${cardData.avatar ? 
                            `<img src="${cardData.avatar}" alt="${cardData.name}">` :
                            `<i class="fas fa-user"></i>`
                        }
                    </div>
                    <div class="contact-info">
                        <h5>${cardData.name || 'Nowy kontakt'}</h5>
                        <p>${cardData.title || ''} ${cardData.company ? `• ${cardData.company}` : ''}</p>
                        <div class="contact-details">
                            ${cardData.email ? `<div><i class="fas fa-envelope"></i> ${cardData.email}</div>` : ''}
                            ${cardData.phone ? `<div><i class="fas fa-phone"></i> ${cardData.phone}</div>` : ''}
                            ${cardData.website ? `<div><i class="fas fa-globe"></i> ${cardData.website}</div>` : ''}
                        </div>
                    </div>
                </div>
                <div class="preview-actions">
                    <button class="btn-primary" onclick="this.closest('.contact-preview-modal').remove()">
                        Gotowe
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(preview);
        
        setTimeout(() => {
            preview.classList.add('show');
        }, 10);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (document.body.contains(preview)) {
                preview.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(preview);
                }, 300);
            }
        }, 5000);
    }

    showShareModal(cardData) {
        const shareModal = document.createElement('div');
        shareModal.className = 'share-modal-overlay';
        shareModal.innerHTML = `
            <div class="share-modal">
                <div class="share-header">
                    <h4><i class="fas fa-share"></i> Udostępnij wizytówkę</h4>
                    <button onclick="this.closest('.share-modal-overlay').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="share-content">
                    <div class="share-options">
                        <button class="share-option" onclick="shareVia('whatsapp', ${JSON.stringify(cardData).replace(/"/g, '&quot;')})">
                            <i class="fab fa-whatsapp"></i>
                            <span>WhatsApp</span>
                        </button>
                        <button class="share-option" onclick="shareVia('telegram', ${JSON.stringify(cardData).replace(/"/g, '&quot;')})">
                            <i class="fab fa-telegram"></i>
                            <span>Telegram</span>
                        </button>
                        <button class="share-option" onclick="shareVia('messenger', ${JSON.stringify(cardData).replace(/"/g, '&quot;')})">
                            <i class="fab fa-facebook-messenger"></i>
                            <span>Messenger</span>
                        </button>
                        <button class="share-option" onclick="shareVia('sms', ${JSON.stringify(cardData).replace(/"/g, '&quot;')})">
                            <i class="fas fa-sms"></i>
                            <span>SMS</span>
                        </button>
                        <button class="share-option" onclick="copyToClipboard('${this.generateShareText(cardData)}')">
                            <i class="fas fa-copy"></i>
                            <span>Kopiuj</span>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(shareModal);
        
        setTimeout(() => {
            shareModal.classList.add('show');
        }, 10);
    }

    generateShareText(cardData) {
        return `Sprawdź wizytówkę: ${cardData.name || 'SmartCard'}\n${cardData.company || ''}\n${cardData.email || ''}\n${cardData.phone || ''}\n\nWizytówka stworzona z SmartCard.pl`;
    }

    // Utility Methods
    showNotification(message, type = 'info') {
        if (window.SmartCardApp) {
            const app = new window.SmartCardApp();
            app.showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    // Advanced QR Analytics
    trackQRScan(qrId, cardData) {
        const scanData = {
            qrId: qrId,
            cardId: cardData.id,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            referrer: document.referrer,
            location: this.getApproximateLocation()
        };
        
        // Store scan data
        this.storeScanData(scanData);
        
        // Update analytics
        this.updateAnalytics(cardData.id);
    }

    getApproximateLocation() {
        // Get approximate location without GPS
        return {
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            language: navigator.language,
            country: this.getCountryFromTimezone()
        };
    }

    getCountryFromTimezone() {
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const countryMap = {
            'Europe/Warsaw': 'Poland',
            'Europe/London': 'United Kingdom',
            'America/New_York': 'United States',
            'Europe/Berlin': 'Germany',
            'Europe/Paris': 'France'
        };
        return countryMap[timezone] || 'Unknown';
    }

    storeScanData(scanData) {
        const existingScans = JSON.parse(localStorage.getItem('qr-scans') || '[]');
        existingScans.push(scanData);
        
        // Keep only last 1000 scans
        if (existingScans.length > 1000) {
            existingScans.splice(0, existingScans.length - 1000);
        }
        
        localStorage.setItem('qr-scans', JSON.stringify(existingScans));
    }

    updateAnalytics(cardId) {
        if (window.authSystem?.currentUser) {
            const user = window.authSystem.currentUser;
            user.analytics.totalScans = (user.analytics.totalScans || 0) + 1;
            
            const card = user.cards.find(c => c.id === cardId);
            if (card) {
                card.scans = (card.scans || 0) + 1;
                card.lastScan = new Date().toISOString();
            }
            
            window.authSystem.saveUserToStorage(user);
        }
    }

    // QR Code Batch Generation
    async generateBatchQRCodes(cardsData, type = 'vcard', options = {}) {
        const results = [];
        
        for (const cardData of cardsData) {
            try {
                const qrCode = await this.generateQRCode(cardData, type, {
                    ...options,
                    width: options.batchWidth || 150
                });
                
                results.push({
                    cardId: cardData.id,
                    cardName: cardData.name,
                    qrCode: qrCode,
                    status: 'success'
                });
            } catch (error) {
                results.push({
                    cardId: cardData.id,
                    cardName: cardData.name,
                    error: error.message,
                    status: 'error'
                });
            }
        }
        
        return results;
    }

    // QR Code Templates
    getQRTemplates() {
        return {
            business: {
                name: 'Business Card',
                description: 'Standard business card QR',
                defaultType: 'vcard',
                color: { dark: '#2c3e50', light: '#ffffff' }
            },
            social: {
                name: 'Social Media',
                description: 'Social media profile QR',
                defaultType: 'url',
                color: { dark: '#3498db', light: '#ffffff' }
            },
            contact: {
                name: 'Quick Contact',
                description: 'Phone or email contact',
                defaultType: 'phone',
                color: { dark: '#27ae60', light: '#ffffff' }
            },
            wifi: {
                name: 'WiFi Access',
                description: 'WiFi network credentials',
                defaultType: 'wifi',
                color: { dark: '#9b59b6', light: '#ffffff' }
            },
            location: {
                name: 'Location',
                description: 'Geographic location',
                defaultType: 'location',
                color: { dark: '#e74c3c', light: '#ffffff' }
            }
        };
    }
}

// Global functions for QR and NFC operations
function showNFCSimulator(cardData) {
    if (!window.qrGenerator) {
        window.qrGenerator = new QRCodeGenerator();
    }
    window.qrGenerator.showNFCSimulator(cardData);
}

function hideNFCSimulator() {
    if (window.qrGenerator) {
        window.qrGenerator.hideNFCSimulator();
    }
}

function simulateNFCAction(action) {
    if (window.qrGenerator) {
        window.qrGenerator.simulateNFCAction(action);
    }
}

function shareVia(platform, cardData) {
    const shareUrls = {
        whatsapp: `https://wa.me/?text=${encodeURIComponent(window.qrGenerator.generateShareText(cardData))}`,
        telegram: `https://t.me/share/url?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(cardData.name)}`,
        messenger: `https://www.messenger.com/new`,
        sms: `sms:?body=${encodeURIComponent(window.qrGenerator.generateShareText(cardData))}`
    };
    
    if (shareUrls[platform]) {
        window.open(shareUrls[platform], '_blank');
    }
    
    // Close share modal
    const modal = document.querySelector('.share-modal-overlay');
    if (modal) {
        modal.remove();
    }
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        if (window.qrGenerator) {
            window.qrGenerator.showNotification('Skopiowano do schowka!', 'success');
        }
        
        // Close share modal
        const modal = document.querySelector('.share-modal-overlay');
        if (modal) {
            modal.remove();
        }
    });
}

// Initialize QR Generator
document.addEventListener('DOMContentLoaded', () => {
    window.qrGenerator = new QRCodeGenerator();
});

// Export for global access
window.QRCodeGenerator = QRCodeGenerator;