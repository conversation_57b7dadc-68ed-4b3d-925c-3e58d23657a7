// SmartCard.pl - Payment System with Stripe Integration
class PaymentSystem {
    constructor() {
        this.stripe = null;
        this.plans = this.initializePlans();
        this.currentUser = null;
        this.init();
    }

    async init() {
        // Initialize Stripe (use test key for development)
        this.stripe = Stripe('pk_test_51234567890abcdef'); // Replace with your Stripe publishable key
        this.currentUser = this.getCurrentUser();
        this.setupEventListeners();
        this.checkSubscriptionStatus();
    }

    initializePlans() {
        return {
            free: {
                id: 'free',
                name: 'Darmowy',
                price: 0,
                currency: 'PLN',
                interval: 'month',
                features: [
                    '1 wizytówka',
                    '3 szablony',
                    'Kod QR',
                    'Podstawowe analytics',
                    'Eksport PNG'
                ],
                limits: {
                    cards: 1,
                    templates: 3,
                    exports: 5,
                    analytics: 'basic',
                    customDomain: false,
                    aiFeatures: false
                }
            },
            pro: {
                id: 'pro',
                name: 'Pro',
                price: 29,
                currency: 'PLN',
                interval: 'month',
                stripeId: 'price_pro_monthly', // Replace with actual Stripe price ID
                features: [
                    '10 wizytówek',
                    'Wszystkie szablony',
                    'AI Avatar Generator',
                    'Zaawansowane analytics',
                    'Wszystkie formaty eksportu',
                    'Własna domena',
                    'Priorytetowe wsparcie'
                ],
                limits: {
                    cards: 10,
                    templates: 'unlimited',
                    exports: 'unlimited',
                    analytics: 'advanced',
                    customDomain: true,
                    aiFeatures: true
                }
            },
            business: {
                id: 'business',
                name: 'Business',
                price: 99,
                currency: 'PLN',
                interval: 'month',
                stripeId: 'price_business_monthly', // Replace with actual Stripe price ID
                features: [
                    'Nieograniczone wizytówki',
                    'Zarządzanie zespołem',
                    'White-label',
                    'API dostęp',
                    'Integracje CRM',
                    'Dedykowany manager',
                    'SLA 99.9%'
                ],
                limits: {
                    cards: 'unlimited',
                    templates: 'unlimited',
                    exports: 'unlimited',
                    analytics: 'enterprise',
                    customDomain: true,
                    aiFeatures: true,
                    teamManagement: true,
                    whiteLabel: true
                }
            }
        };
    }

    getCurrentUser() {
        const userData = localStorage.getItem('smartcard-user');
        return userData ? JSON.parse(userData) : null;
    }

    setupEventListeners() {
        // Upgrade buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-upgrade-plan]')) {
                const planId = e.target.getAttribute('data-upgrade-plan');
                this.showUpgradeModal(planId);
            }
        });

        // Feature limit warnings
        document.addEventListener('featureLimitReached', (e) => {
            this.showLimitWarning(e.detail.feature, e.detail.limit);
        });
    }

    checkSubscriptionStatus() {
        if (!this.currentUser) return;

        const subscription = this.currentUser.subscription;
        if (subscription && subscription.status === 'active') {
            const expiryDate = new Date(subscription.currentPeriodEnd);
            const now = new Date();
            
            if (expiryDate < now) {
                this.handleExpiredSubscription();
            } else if (expiryDate - now < 7 * 24 * 60 * 60 * 1000) { // 7 days
                this.showRenewalReminder();
            }
        }
    }

    async createCheckoutSession(planId) {
        const plan = this.plans[planId];
        if (!plan || !plan.stripeId) {
            throw new Error('Invalid plan selected');
        }

        try {
            // In production, this would be a call to your backend
            const response = await fetch('/api/create-checkout-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    priceId: plan.stripeId,
                    userId: this.currentUser.id,
                    successUrl: `${window.location.origin}/success?session_id={CHECKOUT_SESSION_ID}`,
                    cancelUrl: `${window.location.origin}/pricing`
                })
            });

            const session = await response.json();
            
            // Redirect to Stripe Checkout
            const result = await this.stripe.redirectToCheckout({
                sessionId: session.id
            });

            if (result.error) {
                throw new Error(result.error.message);
            }

        } catch (error) {
            console.error('Payment error:', error);
            this.showPaymentError(error.message);
        }
    }

    showUpgradeModal(planId) {
        const plan = this.plans[planId];
        const modal = this.createUpgradeModal(plan);
        document.body.appendChild(modal);
        
        // Animate in
        requestAnimationFrame(() => {
            modal.classList.add('active');
        });
    }

    createUpgradeModal(plan) {
        const modal = document.createElement('div');
        modal.className = 'payment-modal';
        modal.innerHTML = `
            <div class="payment-modal-overlay" onclick="this.parentElement.remove()"></div>
            <div class="payment-modal-content">
                <div class="payment-header">
                    <h2>Upgrade do ${plan.name}</h2>
                    <button class="close-btn" onclick="this.closest('.payment-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="payment-plan-details">
                    <div class="plan-price">
                        <span class="amount">${plan.price}</span>
                        <span class="currency">${plan.currency}</span>
                        <span class="period">/${plan.interval}</span>
                    </div>
                    
                    <div class="plan-features">
                        <h3>Co otrzymasz:</h3>
                        <ul>
                            ${plan.features.map(feature => `<li><i class="fas fa-check"></i>${feature}</li>`).join('')}
                        </ul>
                    </div>
                </div>
                
                <div class="payment-actions">
                    <button class="btn-primary btn-large" onclick="paymentSystem.createCheckoutSession('${plan.id}')">
                        <i class="fas fa-credit-card"></i>
                        Wybierz plan ${plan.name}
                    </button>
                    <p class="payment-security">
                        <i class="fas fa-shield-alt"></i>
                        Bezpieczne płatności przez Stripe
                    </p>
                </div>
            </div>
        `;
        return modal;
    }

    checkFeatureLimit(feature) {
        if (!this.currentUser) return false;
        
        const userPlan = this.currentUser.subscription?.plan || 'free';
        const limits = this.plans[userPlan].limits;
        
        switch (feature) {
            case 'cards':
                if (limits.cards === 'unlimited') return true;
                return this.currentUser.cards.length < limits.cards;
                
            case 'exports':
                if (limits.exports === 'unlimited') return true;
                const todayExports = this.getTodayExports();
                return todayExports < limits.exports;
                
            case 'aiFeatures':
                return limits.aiFeatures;
                
            case 'customDomain':
                return limits.customDomain;
                
            default:
                return true;
        }
    }

    getTodayExports() {
        const today = new Date().toDateString();
        const exports = this.currentUser.analytics?.exports || [];
        return exports.filter(exp => new Date(exp.date).toDateString() === today).length;
    }

    showLimitWarning(feature, limit) {
        const warnings = {
            cards: `Osiągnąłeś limit ${limit} wizytówek. Przejdź na plan Pro, aby tworzyć więcej!`,
            exports: `Osiągnąłeś dzienny limit ${limit} eksportów. Przejdź na plan Pro dla nieograniczonych eksportów!`,
            aiFeatures: 'Funkcje AI są dostępne tylko w planach Pro i Business.',
            customDomain: 'Własna domena jest dostępna tylko w planach Pro i Business.'
        };

        this.showUpgradePrompt(warnings[feature], feature);
    }

    showUpgradePrompt(message, feature) {
        const prompt = document.createElement('div');
        prompt.className = 'upgrade-prompt';
        prompt.innerHTML = `
            <div class="upgrade-prompt-content">
                <div class="upgrade-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <p>${message}</p>
                <div class="upgrade-actions">
                    <button class="btn-primary" data-upgrade-plan="pro">
                        Przejdź na Pro
                    </button>
                    <button class="btn-secondary" onclick="this.closest('.upgrade-prompt').remove()">
                        Może później
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(prompt);
        
        // Auto remove after 10 seconds
        setTimeout(() => {
            if (prompt.parentElement) {
                prompt.remove();
            }
        }, 10000);
    }

    handleSuccessfulPayment(sessionId) {
        // This would be called after successful Stripe checkout
        // Update user subscription status
        if (this.currentUser) {
            this.currentUser.subscription = {
                status: 'active',
                plan: 'pro', // This should come from the session data
                currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                stripeSessionId: sessionId
            };
            
            localStorage.setItem('smartcard-user', JSON.stringify(this.currentUser));
            this.showSuccessMessage();
            this.updateUIForPremiumUser();
        }
    }

    updateUIForPremiumUser() {
        // Remove ads, unlock features, update navigation
        document.querySelectorAll('.premium-feature').forEach(el => {
            el.classList.remove('locked');
        });
        
        document.querySelectorAll('.upgrade-banner').forEach(el => {
            el.style.display = 'none';
        });
        
        // Update navigation to show premium status
        const userInfo = document.querySelector('.user-info');
        if (userInfo) {
            userInfo.innerHTML += '<span class="premium-badge">PRO</span>';
        }
    }

    showSuccessMessage() {
        const message = document.createElement('div');
        message.className = 'success-message';
        message.innerHTML = `
            <div class="success-content">
                <i class="fas fa-check-circle"></i>
                <h3>Gratulacje!</h3>
                <p>Twoje konto zostało pomyślnie zaktualizowane do planu Pro.</p>
                <button class="btn-primary" onclick="this.parentElement.parentElement.remove()">
                    Rozpocznij korzystanie
                </button>
            </div>
        `;
        document.body.appendChild(message);
    }

    showPaymentError(error) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'payment-error';
        errorDiv.innerHTML = `
            <div class="error-content">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Błąd płatności</h3>
                <p>${error}</p>
                <button class="btn-secondary" onclick="this.parentElement.parentElement.remove()">
                    Zamknij
                </button>
            </div>
        `;
        document.body.appendChild(errorDiv);
    }
}

// Initialize payment system
const paymentSystem = new PaymentSystem();
window.paymentSystem = paymentSystem;
