/* SmartCard.pl - Payment System Styles */

/* Payment Modal */
.payment-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.payment-modal.active {
    opacity: 1;
    visibility: visible;
}

.payment-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.payment-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transition: transform 0.3s ease;
}

.payment-modal.active .payment-modal-content {
    transform: translate(-50%, -50%) scale(1);
}

.payment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.payment-header h2 {
    color: #fff;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    color: #fff;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Plan Details */
.payment-plan-details {
    margin-bottom: 2rem;
}

.plan-price {
    text-align: center;
    margin-bottom: 2rem;
}

.plan-price .amount {
    font-size: 3rem;
    font-weight: 700;
    color: #4F46E5;
}

.plan-price .currency {
    font-size: 1.2rem;
    color: #fff;
    margin-left: 0.5rem;
}

.plan-price .period {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
}

.plan-features h3 {
    color: #fff;
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.plan-features li {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    color: rgba(255, 255, 255, 0.9);
}

.plan-features li i {
    color: #10B981;
    margin-right: 0.75rem;
    font-size: 0.9rem;
}

/* Payment Actions */
.payment-actions {
    text-align: center;
}

.payment-actions .btn-large {
    width: 100%;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    padding: 1rem 2rem;
}

.payment-security {
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin: 0;
}

.payment-security i {
    margin-right: 0.5rem;
    color: #10B981;
}

/* Upgrade Prompt */
.upgrade-prompt {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: linear-gradient(135deg, #4F46E5, #7C3AED);
    border-radius: 15px;
    padding: 1.5rem;
    max-width: 350px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    z-index: 9999;
    animation: slideInUp 0.5s ease;
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.upgrade-prompt-content {
    text-align: center;
}

.upgrade-icon {
    font-size: 2rem;
    color: #FFD700;
    margin-bottom: 1rem;
}

.upgrade-prompt p {
    color: #fff;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

.upgrade-actions {
    display: flex;
    gap: 0.75rem;
}

.upgrade-actions .btn-primary,
.upgrade-actions .btn-secondary {
    flex: 1;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
}

/* Success Message */
.success-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.success-content {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.success-content i {
    font-size: 4rem;
    color: #10B981;
    margin-bottom: 1rem;
}

.success-content h3 {
    color: #fff;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.success-content p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Payment Error */
.payment-error {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.error-content {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.error-content i {
    font-size: 4rem;
    color: #EF4444;
    margin-bottom: 1rem;
}

.error-content h3 {
    color: #fff;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.error-content p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Premium Badge */
.premium-badge {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #000;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-left: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Premium Features */
.premium-feature.locked {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.premium-feature.locked::after {
    content: '🔒 PRO';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: #FFD700;
    padding: 0.5rem 1rem;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 600;
    z-index: 10;
}

/* Pricing Cards Enhancement */
.pricing-card.popular {
    position: relative;
    transform: scale(1.05);
    border: 2px solid #4F46E5;
}

.pricing-card.popular::before {
    content: 'Najpopularniejszy';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(45deg, #4F46E5, #7C3AED);
    color: #fff;
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .payment-modal-content {
        padding: 1.5rem;
        margin: 1rem;
        width: calc(100% - 2rem);
    }
    
    .plan-price .amount {
        font-size: 2.5rem;
    }
    
    .upgrade-prompt {
        bottom: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }
    
    .upgrade-actions {
        flex-direction: column;
    }
    
    .success-content,
    .error-content {
        padding: 2rem;
        margin: 1rem;
        width: calc(100% - 2rem);
    }
}

/* Loading States */
.payment-loading {
    position: relative;
    pointer-events: none;
}

.payment-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Upgrade Banner */
.upgrade-banner {
    background: linear-gradient(135deg, #4F46E5, #7C3AED);
    padding: 2rem 0;
    margin: 4rem 0;
    position: relative;
    overflow: hidden;
}

.upgrade-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.upgrade-banner-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 1;
}

.upgrade-banner-text h3 {
    color: #fff;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.upgrade-banner-text p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    margin: 0;
    max-width: 600px;
}

.upgrade-banner-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.upgrade-banner-actions .btn-primary {
    background: #fff;
    color: #4F46E5;
    border: none;
    font-weight: 600;
    white-space: nowrap;
}

.upgrade-banner-actions .btn-primary:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

.upgrade-banner-actions .btn-secondary {
    background: transparent;
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.upgrade-banner-actions .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Premium Feature Badges */
.premium-feature {
    position: relative;
}

.premium-feature .premium-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #000;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 10;
}

/* Mobile Responsiveness for Upgrade Banner */
@media (max-width: 768px) {
    .upgrade-banner-content {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }

    .upgrade-banner-text h3 {
        font-size: 1.25rem;
    }

    .upgrade-banner-actions {
        flex-direction: column;
        width: 100%;
    }

    .upgrade-banner-actions .btn-primary,
    .upgrade-banner-actions .btn-secondary {
        width: 100%;
        justify-content: center;
    }
}

/* Email Notifications */
.email-notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: linear-gradient(135deg, #10B981, #059669);
    border-radius: 15px;
    padding: 1rem;
    max-width: 350px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    z-index: 9999;
    animation: slideInRight 0.5s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.email-notification-content {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    color: white;
}

.email-notification-content i {
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.email-notification-content div {
    flex: 1;
}

.email-notification-content strong {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.email-notification-content p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

.email-notification-content button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.email-notification-content button:hover {
    background: rgba(255, 255, 255, 0.2);
}

@media (max-width: 768px) {
    .email-notification {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }
}
