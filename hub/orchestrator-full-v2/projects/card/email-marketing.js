// SmartCard.pl - Email Marketing System
class EmailMarketing {
    constructor() {
        this.subscribers = new Map();
        this.campaigns = new Map();
        this.templates = this.initializeTemplates();
        this.init();
    }

    init() {
        this.loadData();
        this.setupEventListeners();
        this.startAutomatedCampaigns();
        console.log('📧 Email Marketing System initialized');
    }

    initializeTemplates() {
        return {
            welcome: {
                subject: 'Witaj w SmartCard.pl! 🎉',
                html: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <div style="background: linear-gradient(135deg, #4F46E5, #7C3AED); padding: 2rem; text-align: center;">
                            <h1 style="color: white; margin: 0;">Witaj w SmartCard.pl!</h1>
                            <p style="color: rgba(255,255,255,0.9); margin: 1rem 0 0 0;"><PERSON>ja wizytówka przyszłości już dziś</p>
                        </div>
                        <div style="padding: 2rem;">
                            <h2><PERSON><PERSON><PERSON><PERSON> {{name}}!</h2>
                            <p>Dziękujemy za dołączenie do SmartCard.pl! Jesteś teraz częścią rewolucji w networkingu.</p>
                            
                            <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; margin: 2rem 0;">
                                <h3>Co możesz zrobić już teraz:</h3>
                                <ul>
                                    <li>✨ Stwórz swoją pierwszą wizytówkę AI</li>
                                    <li>🎨 Wybierz spośród 15+ szablonów</li>
                                    <li>📱 Wygeneruj kod QR</li>
                                    <li>📊 Śledź analytics</li>
                                </ul>
                            </div>
                            
                            <div style="text-align: center; margin: 2rem 0;">
                                <a href="{{app_url}}" style="background: #4F46E5; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 10px; display: inline-block;">
                                    Stwórz pierwszą wizytówkę
                                </a>
                            </div>
                            
                            <p>Potrzebujesz pomocy? Odpowiedz na tego maila - jesteśmy tutaj dla Ciebie!</p>
                            
                            <p>Pozdrawiamy,<br>Zespół SmartCard.pl</p>
                        </div>
                    </div>
                `
            },
            
            upgrade_reminder: {
                subject: 'Odblokuj pełny potencjał SmartCard.pl 🚀',
                html: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <div style="background: linear-gradient(135deg, #FFD700, #FFA500); padding: 2rem; text-align: center;">
                            <h1 style="color: #000; margin: 0;">Czas na upgrade! 🚀</h1>
                        </div>
                        <div style="padding: 2rem;">
                            <h2>Cześć {{name}}!</h2>
                            <p>Widzimy, że aktywnie korzystasz z SmartCard.pl. To fantastyczne! 🎉</p>
                            
                            <p>Czy wiesz, że użytkownicy planu Pro:</p>
                            <ul>
                                <li>📈 Zwiększają swój networking o 340%</li>
                                <li>🎯 Otrzymują 5x więcej kontaktów biznesowych</li>
                                <li>⚡ Oszczędzają 10+ godzin miesięcznie</li>
                            </ul>
                            
                            <div style="background: #f0f9ff; border: 2px solid #0ea5e9; padding: 1.5rem; border-radius: 10px; margin: 2rem 0;">
                                <h3 style="color: #0ea5e9; margin-top: 0;">Specjalna oferta tylko dla Ciebie!</h3>
                                <p style="margin: 0;"><strong>50% zniżki na pierwszy miesiąc planu Pro</strong></p>
                                <p style="margin: 0.5rem 0 0 0; font-size: 0.9rem; color: #64748b;">Oferta ważna tylko 48 godzin!</p>
                            </div>
                            
                            <div style="text-align: center; margin: 2rem 0;">
                                <a href="{{upgrade_url}}" style="background: #4F46E5; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 10px; display: inline-block; font-weight: bold;">
                                    Przejdź na Pro za 14.50zł
                                </a>
                            </div>
                            
                            <p style="font-size: 0.9rem; color: #64748b;">Nie chcesz otrzymywać takich wiadomości? <a href="{{unsubscribe_url}}">Wypisz się</a></p>
                        </div>
                    </div>
                `
            },
            
            feature_announcement: {
                subject: 'Nowa funkcja: AI Avatar Generator! 🤖',
                html: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <div style="background: linear-gradient(135deg, #10B981, #059669); padding: 2rem; text-align: center;">
                            <h1 style="color: white; margin: 0;">Nowość w SmartCard.pl! 🤖</h1>
                        </div>
                        <div style="padding: 2rem;">
                            <h2>AI Avatar Generator już dostępny!</h2>
                            <p>Cześć {{name}}!</p>
                            <p>Mamy dla Ciebie fantastyczne wieści! Właśnie uruchomiliśmy nową funkcję AI Avatar Generator.</p>
                            
                            <div style="background: #f0fdf4; padding: 1.5rem; border-radius: 10px; margin: 2rem 0;">
                                <h3>Co nowego:</h3>
                                <ul>
                                    <li>🎭 Generowanie profesjonalnych avatarów AI</li>
                                    <li>🎨 3 style: Business, Cartoon, Abstract</li>
                                    <li>⚡ Gotowe w 30 sekund</li>
                                    <li>🔥 Zwiększa zapamiętywanie o 85%</li>
                                </ul>
                            </div>
                            
                            <div style="text-align: center; margin: 2rem 0;">
                                <a href="{{app_url}}" style="background: #10B981; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 10px; display: inline-block;">
                                    Wypróbuj AI Avatar Generator
                                </a>
                            </div>
                            
                            <p>Funkcja dostępna w planie Pro. Nie masz jeszcze Pro? <a href="{{upgrade_url}}">Przejdź na Pro</a> i zyskaj dostęp do wszystkich funkcji AI!</p>
                        </div>
                    </div>
                `
            },
            
            abandoned_card: {
                subject: 'Twoja wizytówka czeka na dokończenie 📝',
                html: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <div style="padding: 2rem;">
                            <h2>Cześć {{name}}!</h2>
                            <p>Zauważyliśmy, że zacząłeś tworzyć wizytówkę, ale nie dokończyłeś procesu.</p>
                            
                            <div style="background: #fef3c7; border-left: 4px solid #f59e0b; padding: 1rem; margin: 2rem 0;">
                                <p style="margin: 0;"><strong>Pamiętaj:</strong> Twoja wizytówka może zwiększyć Twoje możliwości networkingowe o 340%!</p>
                            </div>
                            
                            <p>Dokończenie zajmie Ci tylko 2 minuty:</p>
                            <ol>
                                <li>Dodaj swoje dane kontaktowe</li>
                                <li>Wybierz szablon</li>
                                <li>Pobierz i udostępnij</li>
                            </ol>
                            
                            <div style="text-align: center; margin: 2rem 0;">
                                <a href="{{app_url}}" style="background: #4F46E5; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 10px; display: inline-block;">
                                    Dokończ wizytówkę
                                </a>
                            </div>
                        </div>
                    </div>
                `
            }
        };
    }

    setupEventListeners() {
        // Listen for user registration
        window.addEventListener('userRegistered', (event) => {
            this.sendWelcomeEmail(event.detail.user);
        });

        // Listen for card creation start
        window.addEventListener('cardCreationStarted', (event) => {
            this.scheduleAbandonedCardEmail(event.detail.user);
        });

        // Listen for card completion
        window.addEventListener('cardCompleted', (event) => {
            this.cancelAbandonedCardEmail(event.detail.user);
        });
    }

    async sendWelcomeEmail(user) {
        const template = this.templates.welcome;
        const personalizedContent = this.personalizeTemplate(template, {
            name: user.name,
            app_url: window.location.origin
        });

        await this.sendEmail(user.email, personalizedContent.subject, personalizedContent.html);
        
        // Schedule upgrade reminder for 3 days later
        this.scheduleUpgradeReminder(user, 3);
    }

    scheduleUpgradeReminder(user, daysDelay) {
        setTimeout(() => {
            if (this.shouldSendUpgradeReminder(user)) {
                this.sendUpgradeReminderEmail(user);
            }
        }, daysDelay * 24 * 60 * 60 * 1000);
    }

    shouldSendUpgradeReminder(user) {
        // Check if user is still on free plan
        const currentUser = JSON.parse(localStorage.getItem('smartcard-user') || '{}');
        return !currentUser.subscription || currentUser.subscription.plan === 'free';
    }

    async sendUpgradeReminderEmail(user) {
        const template = this.templates.upgrade_reminder;
        const personalizedContent = this.personalizeTemplate(template, {
            name: user.name,
            upgrade_url: `${window.location.origin}#pricing`,
            unsubscribe_url: `${window.location.origin}/unsubscribe?email=${user.email}`
        });

        await this.sendEmail(user.email, personalizedContent.subject, personalizedContent.html);
    }

    scheduleAbandonedCardEmail(user) {
        // Cancel any existing abandoned card email
        this.cancelAbandonedCardEmail(user);
        
        // Schedule new one for 1 hour later
        const timeoutId = setTimeout(() => {
            this.sendAbandonedCardEmail(user);
        }, 60 * 60 * 1000); // 1 hour

        // Store timeout ID to cancel later if needed
        localStorage.setItem(`abandoned_card_${user.id}`, timeoutId.toString());
    }

    cancelAbandonedCardEmail(user) {
        const timeoutId = localStorage.getItem(`abandoned_card_${user.id}`);
        if (timeoutId) {
            clearTimeout(parseInt(timeoutId));
            localStorage.removeItem(`abandoned_card_${user.id}`);
        }
    }

    async sendAbandonedCardEmail(user) {
        const template = this.templates.abandoned_card;
        const personalizedContent = this.personalizeTemplate(template, {
            name: user.name,
            app_url: window.location.origin
        });

        await this.sendEmail(user.email, personalizedContent.subject, personalizedContent.html);
    }

    personalizeTemplate(template, variables) {
        let subject = template.subject;
        let html = template.html;

        Object.keys(variables).forEach(key => {
            const placeholder = `{{${key}}}`;
            subject = subject.replace(new RegExp(placeholder, 'g'), variables[key]);
            html = html.replace(new RegExp(placeholder, 'g'), variables[key]);
        });

        return { subject, html };
    }

    async sendEmail(to, subject, html) {
        // In production, this would integrate with SendGrid, Mailgun, etc.
        // For demo, we'll simulate email sending
        console.log('📧 Sending email:', { to, subject });
        
        // Simulate email delivery
        await this.delay(1000);
        
        // Store email in "sent" log
        const sentEmails = JSON.parse(localStorage.getItem('sent_emails') || '[]');
        sentEmails.push({
            to,
            subject,
            html,
            sentAt: new Date().toISOString()
        });
        localStorage.setItem('sent_emails', JSON.stringify(sentEmails));
        
        // Show notification in demo
        this.showEmailNotification(to, subject);
        
        return { success: true, messageId: 'msg_' + Math.random().toString(36).substr(2, 9) };
    }

    showEmailNotification(to, subject) {
        const notification = document.createElement('div');
        notification.className = 'email-notification';
        notification.innerHTML = `
            <div class="email-notification-content">
                <i class="fas fa-envelope"></i>
                <div>
                    <strong>Email wysłany!</strong>
                    <p>Do: ${to}</p>
                    <p>Temat: ${subject}</p>
                </div>
                <button onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    startAutomatedCampaigns() {
        // Check for users who should receive emails
        setInterval(() => {
            this.checkForScheduledEmails();
        }, 60000); // Check every minute
    }

    checkForScheduledEmails() {
        // This would check database for scheduled emails in production
        // For demo, we'll skip this implementation
    }

    loadData() {
        const data = localStorage.getItem('email_marketing_data');
        if (data) {
            const parsed = JSON.parse(data);
            this.subscribers = new Map(parsed.subscribers || []);
            this.campaigns = new Map(parsed.campaigns || []);
        }
    }

    saveData() {
        const data = {
            subscribers: Array.from(this.subscribers.entries()),
            campaigns: Array.from(this.campaigns.entries())
        };
        localStorage.setItem('email_marketing_data', JSON.stringify(data));
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize email marketing
const emailMarketing = new EmailMarketing();
window.emailMarketing = emailMarketing;
