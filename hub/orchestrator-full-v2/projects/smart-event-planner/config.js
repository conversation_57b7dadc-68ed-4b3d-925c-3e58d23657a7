// EventAI.pl Configuration Manager

class Config {
    constructor() {
        this.config = {
            // API Configuration
            HF_TOKEN: '*************************************',
            LLAMA_MODEL: 'meta-llama/Meta-Llama-3.1-8B-Instruct',
            DEEPSEEK_MODEL: 'deepseek-ai/DeepSeek-R1-Distill-Llama-8B',
            USE_LLAMA: true,
            
            // API Endpoints
            HF_INFERENCE_URL: 'https://api-inference.huggingface.co/models',
            HF_CHAT_URL: 'https://api-inference.huggingface.co/models/meta-llama/Meta-Llama-3.1-8B-Instruct/v1/chat/completions',
            
            // Rate Limiting
            MAX_REQUESTS_PER_MINUTE: 30,
            CACHE_DURATION_MS: 1800000, // 30 minutes
            REQUEST_TIMEOUT_MS: 30000, // 30 seconds
            
            // Application Settings
            APP_NAME: 'EventAI.pl',
            APP_VERSION: '1.0.0',
            DEBUG: false,
            
            // Storage
            STORAGE_PREFIX: 'eventai_',
            MAX_STORAGE_SIZE: 52428800, // 50MB
            
            // Features
            ENABLE_OFFLINE_MODE: true,
            ENABLE_PWA: true,
            ENABLE_ANALYTICS: true,
            ENABLE_ERROR_TRACKING: true,
            
            // Event Planning Defaults
            DEFAULT_EVENT_TYPES: ['wedding', 'corporate', 'birthday', 'conference'],
            DEFAULT_BUDGET_RANGE: { min: 5000, max: 200000 },
            DEFAULT_GUEST_RANGE: { min: 10, max: 1000 },
            
            // AI Configuration
            AI_MODELS: {
                primary: 'meta-llama/Meta-Llama-3.1-8B-Instruct',
                fallback: 'deepseek-ai/DeepSeek-R1-Distill-Llama-8B',
                sentiment: 'cardiffnlp/twitter-roberta-base-sentiment-latest',
                classification: 'facebook/bart-large-mnli'
            },
            
            // Prompt Templates
            PROMPTS: {
                event_planning: `Jesteś ekspertem od planowania wydarzeń w Polsce. Analizuj szczegóły wydarzenia i dostarczaj konkretne, praktyczne rekomendacje.
                
Kontekst: {context}
Typ wydarzenia: {eventType}
Liczba gości: {guests}
Budżet: {budget} PLN
Lokalizacja: {location}
Data: {date}

Zadanie: Przeanalizuj te informacje i dostarcz szczegółowe rekomendacje dla:
1. Miejsce (konkretne sugestie z cenami)
2. Catering (menu i koszty na osobę)
3. Rozrywka (opcje dostosowane do typu wydarzenia)
4. Optymalizacja budżetu (konkretne oszczędności)
5. Timeline (harmonogram przygotowań)

Odpowiadaj w formacie JSON z kluczami: venue, catering, entertainment, budget_tips, timeline.
Wszystkie ceny podawaj w PLN. Bądź konkretny i praktyczny.`,

                budget_optimization: `Jako ekspert finansowy od wydarzeń, zoptymalizuj budżet:

Budżet całkowity: {budget} PLN
Liczba gości: {guests}
Typ wydarzenia: {eventType}
Ograniczenia: {constraints}

Podaj optymalny podział budżetu w procentach dla:
- Miejsce (venue)
- Catering
- Rozrywka (entertainment)  
- Dekoracje (decorations)
- Inne (other)

Uwzględnij polskie realia cenowe i typ wydarzenia.
Odpowiedź w formacie JSON: {"venue": X, "catering": Y, "entertainment": Z, "decorations": W, "other": V}`,

                risk_assessment: `Oceń ryzyko dla wydarzenia:

Typ: {eventType}
Data: {date}
Lokalizacja: {location}
Liczba gości: {guests}
Budżet: {budget} PLN

Przeanalizuj:
1. Ryzyko pogodowe (sezonowe)
2. Ryzyko logistyczne 
3. Ryzyko budżetowe
4. Ryzyko związane z dostawcami

Podaj ogólną ocenę ryzyka (low/medium/high) i konkretne środki zapobiegawcze.
Format JSON: {"risk_level": "...", "factors": [...], "mitigation": [...]}`,

                vendor_matching: `Dopasuj dostawców do wymagań:

Typ wydarzenia: {eventType}
Budżet: {budget} PLN
Lokalizacja: {location}
Liczba gości: {guests}
Preferencje: {preferences}

Na podstawie polskiego rynku eventowego, zasugeruj:
1. Najlepsze opcje cateringowe (nazwa, opis, cena za osobę)
2. Miejsca (nazwa, opis, cena za dzień)
3. Rozrywka (nazwa, opis, cena)

Format JSON z listami dla każdej kategorii.`
            }
        };
        
        // Load environment variables if available
        this.loadEnvironmentConfig();
    }
    
    loadEnvironmentConfig() {
        // In production, these would come from environment variables
        // For now, using hardcoded secure values
        if (typeof process !== 'undefined' && process.env) {
            this.config.HF_TOKEN = process.env.HF_TOKEN || this.config.HF_TOKEN;
            this.config.LLAMA_MODEL = process.env.LLAMA_MODEL || this.config.LLAMA_MODEL;
            this.config.USE_LLAMA = process.env.USE_LLAMA === 'true';
            this.config.DEBUG = process.env.NODE_ENV === 'development';
        }
    }
    
    get(key) {
        return this.config[key];
    }
    
    set(key, value) {
        this.config[key] = value;
    }
    
    getAIModel(type = 'primary') {
        return this.config.AI_MODELS[type] || this.config.AI_MODELS.primary;
    }
    
    getPrompt(type) {
        return this.config.PROMPTS[type] || '';
    }
    
    formatPrompt(type, variables) {
        let prompt = this.getPrompt(type);
        
        Object.keys(variables).forEach(key => {
            const placeholder = `{${key}}`;
            prompt = prompt.replace(new RegExp(placeholder, 'g'), variables[key]);
        });
        
        return prompt;
    }
    
    getHeaders() {
        return {
            'Authorization': `Bearer ${this.config.HF_TOKEN}`,
            'Content-Type': 'application/json',
            'User-Agent': `${this.config.APP_NAME}/${this.config.APP_VERSION}`
        };
    }
    
    isDebugMode() {
        return this.config.DEBUG;
    }
    
    log(message, data = null) {
        if (this.isDebugMode()) {
            console.log(`[${this.config.APP_NAME}] ${message}`, data);
        }
    }
    
    error(message, error = null) {
        console.error(`[${this.config.APP_NAME}] ERROR: ${message}`, error);
        
        if (this.config.ENABLE_ERROR_TRACKING) {
            this.trackError(message, error);
        }
    }
    
    trackError(message, error) {
        // In production, this would send to error tracking service
        const errorData = {
            message,
            error: error ? error.toString() : null,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };
        
        // Store locally for now
        const errors = JSON.parse(localStorage.getItem(`${this.config.STORAGE_PREFIX}errors`) || '[]');
        errors.push(errorData);
        
        // Keep only last 50 errors
        if (errors.length > 50) {
            errors.splice(0, errors.length - 50);
        }
        
        localStorage.setItem(`${this.config.STORAGE_PREFIX}errors`, JSON.stringify(errors));
    }
    
    getStorageKey(key) {
        return `${this.config.STORAGE_PREFIX}${key}`;
    }
    
    validateConfig() {
        const required = ['HF_TOKEN', 'LLAMA_MODEL'];
        const missing = required.filter(key => !this.config[key]);
        
        if (missing.length > 0) {
            throw new Error(`Missing required configuration: ${missing.join(', ')}`);
        }
        
        return true;
    }
}

// Create global config instance
const config = new Config();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = config;
} else {
    window.AppConfig = config;
}