// EventAI.pl - Enterprise-Grade JavaScript Functionality

// Application State Management
class EventPlannerApp {
    constructor() {
        // Initialize core services
        this.config = window.AppConfig;
        this.dataManager = new DataManager();
        this.aiService = new AIService();
        
        // Application state
        this.currentSection = 'dashboard';
        this.wizardStep = 1;
        this.selectedEventType = null;
        this.currentEvent = null;
        this.currentUser = null;
        
        // UI state
        this.isLoading = false;
        this.errors = [];
        this.notifications = [];
        
        // Data cache
        this.cache = {
            events: [],
            vendors: [],
            analytics: null,
            lastUpdated: null
        };
        
        this.init();
    }

    async init() {
        try {
            // Show initial loading
            this.showGlobalLoading('Inicjalizacja aplikacji...');
            
            // Setup event listeners
            this.setupEventListeners();
            this.setupDataEventListeners();
            this.initializeAnimations();
            this.setupAccessibility();
            
            // Load initial data
            await this.loadInitialData();
            
            // Initialize charts and UI
            this.initializeCharts();
            
            // Setup periodic updates
            this.setupPeriodicUpdates();
            
            // Hide loading
            this.hideGlobalLoading();
            
            this.config.log('Application initialized successfully');
            this.showNotification('Aplikacja załadowana pomyślnie!', 'success');
            
        } catch (error) {
            this.config.error('Application initialization failed:', error);
            this.showNotification('Błąd podczas inicjalizacji aplikacji', 'error');
            this.hideGlobalLoading();
        }
    }

    // Event Listeners Setup
    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => this.handleNavigation(e));
        });

        // Hero Section
        document.getElementById('startPlanningBtn')?.addEventListener('click', () => this.openWizard());
        document.getElementById('watchDemoBtn')?.addEventListener('click', () => this.showDemo());
        document.getElementById('newEventBtn')?.addEventListener('click', () => this.openWizard());

        // Wizard
        document.getElementById('nextStep')?.addEventListener('click', () => this.nextStep());
        document.getElementById('prevStep')?.addEventListener('click', () => this.previousStep());
        document.getElementById('createEvent')?.addEventListener('click', () => this.createEvent());

        // Event Type Selection
        document.querySelectorAll('.event-type-card').forEach(card => {
            card.addEventListener('click', (e) => this.selectEventType(e));
        });

        // Budget Calculator
        const budgetRange = document.getElementById('budgetRange');
        if (budgetRange) {
            budgetRange.addEventListener('input', (e) => this.updateBudget(e));
        }

        // Form Validation
        document.querySelectorAll('input, select').forEach(input => {
            input.addEventListener('blur', (e) => this.validateField(e));
            input.addEventListener('input', (e) => this.clearValidationError(e));
        });

        // Vendor Cards
        document.querySelectorAll('.vendor-card').forEach(card => {
            card.addEventListener('click', (e) => this.handleVendorSelection(e));
        });

        // Modal Close
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal();
            }
        });

        // Keyboard Navigation
        document.addEventListener('keydown', (e) => this.handleKeyboardNavigation(e));

        // Touch Events for Mobile
        this.setupTouchEvents();

        // Search and Filters
        this.setupSearchAndFilters();

        // Window Resize
        window.addEventListener('resize', () => this.handleResize());
    }

    // Data Event Listeners
    setupDataEventListeners() {
        // Listen for data changes
        window.addEventListener('dataChanged', (event) => {
            this.handleDataChange(event.detail);
        });
        
        // Listen for specific entity events
        window.addEventListener('eventsCreated', (event) => {
            this.handleEventCreated(event.detail);
        });
        
        window.addEventListener('eventsUpdated', (event) => {
            this.handleEventUpdated(event.detail);
        });
        
        window.addEventListener('eventsDeleted', (event) => {
            this.handleEventDeleted(event.detail);
        });
    }

    // Data Management
    async loadInitialData() {
        try {
            // Load events
            const eventsResult = await this.dataManager.read('events');
            if (eventsResult.success) {
                this.cache.events = eventsResult.data;
                this.updateEventCards();
            }
            
            // Load vendors
            const vendorsResult = await this.dataManager.read('vendors');
            if (vendorsResult.success) {
                this.cache.vendors = vendorsResult.data;
                this.updateVendorCards();
            }
            
            // Load analytics
            await this.loadAnalyticsData();
            
            // Update UI with loaded data
            this.updateDashboardStats();
            this.cache.lastUpdated = new Date();
            
        } catch (error) {
            this.config.error('Failed to load initial data:', error);
            throw error;
        }
    }

    setupPeriodicUpdates() {
        // Update analytics every 5 minutes
        setInterval(async () => {
            if (this.currentSection === 'analytics') {
                await this.loadAnalyticsData();
            }
        }, 300000);
        
        // Check for data sync every minute
        setInterval(() => {
            if (navigator.onLine) {
                this.dataManager.syncOfflineData();
            }
        }, 60000);
    }

    handleDataChange(detail) {
        const { type, storeName, data } = detail;
        
        this.config.log(`Data changed: ${type} in ${storeName}`, data);
        
        // Update cache
        if (storeName === 'events') {
            this.refreshEventCache();
        } else if (storeName === 'vendors') {
            this.refreshVendorCache();
        }
        
        // Update UI
        this.updateRelevantUI(storeName, type);
    }

    async refreshEventCache() {
        const result = await this.dataManager.read('events');
        if (result.success) {
            this.cache.events = result.data;
            this.updateEventCards();
            this.updateDashboardStats();
        }
    }

    async refreshVendorCache() {
        const result = await this.dataManager.read('vendors');
        if (result.success) {
            this.cache.vendors = result.data;
            this.updateVendorCards();
        }
    }

    updateRelevantUI(storeName, changeType) {
        if (storeName === 'events') {
            this.updateEventCards();
            this.updateDashboardStats();
            
            if (this.currentSection === 'analytics') {
                this.loadAnalyticsData();
            }
        }
        
        if (storeName === 'vendors' && this.currentSection === 'vendors') {
            this.updateVendorCards();
        }
    }

    // Enhanced Event Management
    handleEventCreated(eventData) {
        this.showNotification(`Wydarzenie "${eventData.name}" zostało utworzone!`, 'success');
        this.closeWizard();
        this.showSection('dashboard');
    }

    handleEventUpdated(eventData) {
        this.showNotification(`Wydarzenie "${eventData.name}" zostało zaktualizowane!`, 'info');
    }

    handleEventDeleted(eventData) {
        this.showNotification(`Wydarzenie zostało usunięte`, 'info');
    }

    // Navigation Management
    handleNavigation(e) {
        e.preventDefault();
        const target = e.currentTarget.getAttribute('href').substring(1);
        this.showSection(target);
        
        // Update active state
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
            link.removeAttribute('aria-current');
        });
        e.currentTarget.classList.add('active');
        e.currentTarget.setAttribute('aria-current', 'page');
    }

    showSection(sectionId) {
        // Hide all sections
        document.querySelectorAll('section').forEach(section => {
            section.style.display = 'none';
        });

        // Show target section
        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
            targetSection.style.display = 'block';
            this.currentSection = sectionId;
            
            // Load section-specific data
            if (sectionId === 'analytics') {
                this.loadAnalyticsData();
            } else if (sectionId === 'vendors') {
                this.loadVendorData();
            }
        }

        // Show dashboard by default
        if (sectionId === 'dashboard' || !targetSection) {
            document.getElementById('dashboard').style.display = 'block';
            this.currentSection = 'dashboard';
        }
    }

    // Wizard Management
    openWizard() {
        document.getElementById('event-wizard').style.display = 'flex';
        document.body.style.overflow = 'hidden';
        this.wizardStep = 1;
        this.updateWizardProgress();
        
        // Focus management for accessibility
        setTimeout(() => {
            const firstInput = document.querySelector('.wizard-step.active input, .wizard-step.active .event-type-card');
            if (firstInput) firstInput.focus();
        }, 100);
    }

    closeWizard() {
        document.getElementById('event-wizard').style.display = 'none';
        document.body.style.overflow = 'auto';
        this.resetWizard();
    }

    nextStep() {
        if (this.validateCurrentStep()) {
            this.wizardStep++;
            this.updateWizardProgress();
            this.animateStepTransition();
        }
    }

    previousStep() {
        this.wizardStep--;
        this.updateWizardProgress();
        this.animateStepTransition();
    }

    updateWizardProgress() {
        // Update progress indicators
        document.querySelectorAll('.progress-step').forEach((step, index) => {
            const stepNumber = index + 1;
            if (stepNumber <= this.wizardStep) {
                step.classList.add('active');
            } else {
                step.classList.remove('active');
            }
        });

        // Show/hide steps
        document.querySelectorAll('.wizard-step').forEach((step, index) => {
            const stepNumber = index + 1;
            if (stepNumber === this.wizardStep) {
                step.classList.add('active');
                step.style.display = 'block';
            } else {
                step.classList.remove('active');
                step.style.display = 'none';
            }
        });

        // Update navigation buttons
        const prevBtn = document.getElementById('prevStep');
        const nextBtn = document.getElementById('nextStep');
        const createBtn = document.getElementById('createEvent');

        if (prevBtn) {
            prevBtn.disabled = this.wizardStep === 1;
        }

        if (this.wizardStep === 4) {
            nextBtn.style.display = 'none';
            createBtn.style.display = 'inline-flex';
        } else {
            nextBtn.style.display = 'inline-flex';
            createBtn.style.display = 'none';
        }
    }

    animateStepTransition() {
        const activeStep = document.querySelector('.wizard-step.active');
        if (activeStep) {
            activeStep.style.opacity = '0';
            activeStep.style.transform = 'translateX(20px)';
            
            setTimeout(() => {
                activeStep.style.opacity = '1';
                activeStep.style.transform = 'translateX(0)';
            }, 150);
        }
    }

    validateCurrentStep() {
        switch (this.wizardStep) {
            case 1:
                return this.selectedEventType !== null;
            case 2:
                return this.validateBasicInfo();
            case 3:
                return this.validateBudgetAndGuests();
            default:
                return true;
        }
    }

    validateBasicInfo() {
        const eventName = document.getElementById('eventName').value.trim();
        const eventDate = document.getElementById('eventDate').value;
        const eventLocation = document.getElementById('eventLocation').value.trim();
        
        return eventName && eventDate && eventLocation;
    }

    validateBudgetAndGuests() {
        const guestCount = parseInt(document.getElementById('guestCount').value);
        return guestCount > 0;
    }

    // Event Type Selection
    selectEventType(e) {
        const card = e.currentTarget;
        const eventType = card.getAttribute('data-type');
        
        // Remove previous selection
        document.querySelectorAll('.event-type-card').forEach(c => {
            c.classList.remove('selected');
            c.setAttribute('aria-selected', 'false');
        });
        
        // Select current card
        card.classList.add('selected');
        card.setAttribute('aria-selected', 'true');
        this.selectedEventType = eventType;
        
        // Show AI recommendations
        this.generateAIRecommendations(eventType);
    }

    generateAIRecommendations(eventType) {
        // Simulate AI processing
        this.showLoading();
        
        setTimeout(() => {
            this.hideLoading();
            // Update recommendations based on event type
            this.updateRecommendations(eventType);
        }, 2000);
    }

    updateRecommendations(eventType) {
        const recommendations = this.getRecommendationsForType(eventType);
        
        // Update venue recommendation
        const venueDetails = document.querySelector('.venue-details');
        if (venueDetails && recommendations.venue) {
            venueDetails.querySelector('h5').textContent = recommendations.venue.name;
            venueDetails.querySelector('p').textContent = recommendations.venue.description;
            venueDetails.querySelector('.venue-price').textContent = recommendations.venue.price;
        }
        
        // Update catering recommendation
        const cateringOption = document.querySelector('.catering-option');
        if (cateringOption && recommendations.catering) {
            cateringOption.querySelector('h5').textContent = recommendations.catering.name;
            cateringOption.querySelector('p').textContent = recommendations.catering.description;
            cateringOption.querySelector('.catering-price').textContent = recommendations.catering.price;
        }
        
        // Update entertainment recommendation
        const entertainmentOption = document.querySelector('.entertainment-option');
        if (entertainmentOption && recommendations.entertainment) {
            entertainmentOption.querySelector('h5').textContent = recommendations.entertainment.name;
            entertainmentOption.querySelector('p').textContent = recommendations.entertainment.description;
            entertainmentOption.querySelector('.entertainment-price').textContent = recommendations.entertainment.price;
        }
    }

    getRecommendationsForType(eventType) {
        const recommendations = {
            wedding: {
                venue: {
                    name: "Hotel Bellotto",
                    description: "Elegancka sala balowa, 150 osób",
                    price: "12,000 zł/dzień"
                },
                catering: {
                    name: "Menu Premium Weselne",
                    description: "4-daniowe menu + bar, obsługa kelnerska",
                    price: "220 zł/osoba"
                },
                entertainment: {
                    name: "Zespół + DJ Weselny",
                    description: "6h muzyki na żywo + DJ do końca",
                    price: "12,000 zł"
                }
            },
            corporate: {
                venue: {
                    name: "Centrum Konferencyjne Warsaw",
                    description: "Nowoczesne sale, technologia AV",
                    price: "8,000 zł/dzień"
                },
                catering: {
                    name: "Business Lunch",
                    description: "Coffee breaks + lunch biznesowy",
                    price: "85 zł/osoba"
                },
                entertainment: {
                    name: "Prelegenci motywacyjni",
                    description: "Eksperci branżowi + networking",
                    price: "15,000 zł"
                }
            },
            birthday: {
                venue: {
                    name: "Restauracja Garden",
                    description: "Prywatna sala, klimatyczne wnętrze",
                    price: "3,500 zł/wieczór"
                },
                catering: {
                    name: "Menu Urodzinowe",
                    description: "Tort + bufet + napoje",
                    price: "120 zł/osoba"
                },
                entertainment: {
                    name: "DJ + Karaoke",
                    description: "Profesjonalny DJ + system karaoke",
                    price: "2,500 zł"
                }
            },
            conference: {
                venue: {
                    name: "Expo Center",
                    description: "Duże przestrzenie, stoiska wystawowe",
                    price: "25,000 zł/dzień"
                },
                catering: {
                    name: "Conference Catering",
                    description: "Multiple breaks + networking dinner",
                    price: "150 zł/osoba"
                },
                entertainment: {
                    name: "Keynote Speakers",
                    description: "Międzynarodowi prelegenci",
                    price: "50,000 zł"
                }
            }
        };
        
        return recommendations[eventType] || recommendations.wedding;
    }

    // Budget Calculator
    updateBudget(e) {
        const budget = parseInt(e.target.value);
        document.getElementById('budgetValue').textContent = budget.toLocaleString();
        
        // Update budget breakdown
        this.updateBudgetBreakdown(budget);
        
        // Add visual feedback
        e.target.style.background = `linear-gradient(to right, var(--primary-color) 0%, var(--primary-color) ${((budget - 5000) / 195000) * 100}%, var(--gray-200) ${((budget - 5000) / 195000) * 100}%, var(--gray-200) 100%)`;
    }

    updateBudgetBreakdown(totalBudget) {
        const categories = [
            { name: 'Catering (40%)', percentage: 0.4 },
            { name: 'Miejsce (25%)', percentage: 0.25 },
            { name: 'Rozrywka (20%)', percentage: 0.2 },
            { name: 'Dekoracje (15%)', percentage: 0.15 }
        ];
        
        const categoryElements = document.querySelectorAll('.category-item');
        categories.forEach((category, index) => {
            if (categoryElements[index]) {
                const amount = Math.round(totalBudget * category.percentage);
                categoryElements[index].querySelector('.category-amount').textContent = `${amount.toLocaleString()} zł`;
            }
        });
    }

    // Event Creation with AI Integration
    async createEvent() {
        try {
            this.showLoading('Tworzenie wydarzenia z wykorzystaniem AI...');
            
            // Collect event data
            const eventData = this.collectEventData();
            
            // Validate event data
            if (!this.validateEventData(eventData)) {
                throw new Error('Nieprawidłowe dane wydarzenia');
            }
            
            // Generate AI recommendations
            this.updateLoadingMessage('Generowanie rekomendacji AI...');
            const aiRecommendations = await this.aiService.generateEventRecommendations(eventData);
            
            // Create event in database
            this.updateLoadingMessage('Zapisywanie wydarzenia...');
            const result = await this.dataManager.create('events', {
                ...eventData,
                aiRecommendations,
                status: 'planning',
                progress: 10
            });
            
            if (!result.success) {
                throw new Error(result.error || 'Nie udało się utworzyć wydarzenia');
            }
            
            // Create initial analytics record
            await this.dataManager.create('analytics', {
                eventId: result.id,
                type: 'created',
                value: 1,
                timestamp: new Date().toISOString()
            });
            
            this.hideLoading();
            this.showSuccessModal();
            
            // Store current event for later use
            this.currentEvent = result.data;
            
            this.config.log('Event created successfully:', result.id);
            
        } catch (error) {
            this.config.error('Event creation failed:', error);
            this.hideLoading();
            this.showNotification(`Błąd podczas tworzenia wydarzenia: ${error.message}`, 'error');
        }
    }

    validateEventData(eventData) {
        const required = ['name', 'type', 'date', 'guests', 'budget', 'location'];
        const missing = required.filter(field => !eventData[field]);
        
        if (missing.length > 0) {
            this.showNotification(`Brakuje wymaganych pól: ${missing.join(', ')}`, 'error');
            return false;
        }

        if (eventData.guests < 1 || eventData.guests > 10000) {
            this.showNotification('Liczba gości musi być między 1 a 10,000', 'error');
            return false;
        }

        if (eventData.budget < 1000) {
            this.showNotification('Budżet musi wynosić co najmniej 1,000 PLN', 'error');
            return false;
        }

        const eventDate = new Date(eventData.date);
        const today = new Date();
        if (eventDate <= today) {
            this.showNotification('Data wydarzenia musi być w przyszłości', 'error');
            return false;
        }

        return true;
    }

    addEventToGrid() {
        const eventData = this.collectEventData();
        const eventCard = this.createEventCard(eventData);
        
        const eventsGrid = document.querySelector('.events-grid');
        if (eventsGrid) {
            eventsGrid.insertBefore(eventCard, eventsGrid.firstChild);
        }
        
        // Update statistics
        this.updateStatistics();
    }

    collectEventData() {
        const dietaryRestrictions = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
            .map(cb => cb.value);
            
        return {
            type: this.selectedEventType,
            name: document.getElementById('eventName').value.trim(),
            date: document.getElementById('eventDate').value,
            location: document.getElementById('eventLocation').value.trim(),
            duration: document.getElementById('eventDuration').value,
            guests: parseInt(document.getElementById('guestCount').value),
            budget: parseInt(document.getElementById('budgetRange').value),
            dietaryRestrictions,
            preferences: this.getEventPreferences(),
            createdBy: this.currentUser?.id || 'anonymous',
            status: 'planning',
            progress: 0
        };
    }

    getEventPreferences() {
        // Collect additional preferences based on event type and user selections
        const preferences = [];
        
        if (this.selectedEventType === 'wedding') {
            preferences.push('traditional', 'elegant', 'memorable');
        } else if (this.selectedEventType === 'corporate') {
            preferences.push('professional', 'networking', 'modern');
        } else if (this.selectedEventType === 'birthday') {
            preferences.push('fun', 'casual', 'personal');
        } else if (this.selectedEventType === 'conference') {
            preferences.push('educational', 'professional', 'large-scale');
        }
        
        return preferences;
    }

    createEventCard(eventData) {
        const card = document.createElement('div');
        card.className = 'event-card';
        
        const typeIcons = {
            wedding: 'fa-heart',
            corporate: 'fa-briefcase',
            birthday: 'fa-birthday-cake',
            conference: 'fa-microphone'
        };
        
        const typeLabels = {
            wedding: 'Ślub',
            corporate: 'Wydarzenie firmowe',
            birthday: 'Urodziny',
            conference: 'Konferencja'
        };
        
        card.innerHTML = `
            <div class="event-header">
                <div class="event-type">
                    <i class="fas ${typeIcons[eventData.type]}"></i>
                    <span>${typeLabels[eventData.type]}</span>
                </div>
                <div class="event-status status-planning">Planowanie</div>
            </div>
            <h3 class="event-title">${eventData.name}</h3>
            <div class="event-details">
                <div class="detail-item">
                    <i class="fas fa-calendar"></i>
                    <span>${this.formatDate(eventData.date)}</span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-users"></i>
                    <span>${eventData.guests} gości</span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-money-bill"></i>
                    <span>${eventData.budget.toLocaleString()} zł</span>
                </div>
            </div>
            <div class="event-progress">
                <div class="progress-header">
                    <span>Postęp planowania</span>
                    <span class="progress-percent">${eventData.progress}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${eventData.progress}%"></div>
                </div>
            </div>
            <div class="event-actions">
                <button class="btn-outline" onclick="app.openEventDetails('${eventData.id}')">
                    <i class="fas fa-eye"></i>Szczegóły
                </button>
                <button class="btn-primary" onclick="app.continueEvent('${eventData.id}')">
                    <i class="fas fa-play"></i>Kontynuuj
                </button>
            </div>
        `;
        
        return card;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('pl-PL', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
        });
    }

    // Statistics and Animations
    initializeAnimations() {
        // Animated counters
        this.animateCounters();
        
        // Intersection Observer for scroll animations
        this.setupScrollAnimations();
        
        // Periodic animations
        this.setupPeriodicAnimations();
    }

    animateCounters() {
        const counters = document.querySelectorAll('[data-count]');
        
        const animateCounter = (counter) => {
            const target = parseInt(counter.getAttribute('data-count'));
            const duration = 2000;
            const increment = target / (duration / 16);
            let current = 0;
            
            const updateCounter = () => {
                current += increment;
                if (current < target) {
                    counter.textContent = Math.floor(current).toLocaleString();
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target.toLocaleString();
                }
            };
            
            updateCounter();
        };
        
        // Use Intersection Observer to trigger animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounter(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        });
        
        counters.forEach(counter => observer.observe(counter));
    }

    setupScrollAnimations() {
        const animatedElements = document.querySelectorAll('.stat-card, .event-card, .vendor-card');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animation = 'fadeInUp 0.6s ease-out forwards';
                }
            });
        }, { threshold: 0.1 });
        
        animatedElements.forEach(el => observer.observe(el));
    }

    setupPeriodicAnimations() {
        // Breathing effect for important elements
        setInterval(() => {
            const notifications = document.querySelector('.notification-badge');
            if (notifications) {
                notifications.style.animation = 'pulse 1s ease-in-out';
                setTimeout(() => {
                    notifications.style.animation = '';
                }, 1000);
            }
        }, 5000);
    }

    // Data Management
    loadDashboardData() {
        // Simulate loading dashboard data
        const stats = {
            activeEvents: 12,
            totalGuests: 847,
            savings: 24500,
            rating: 4.9
        };
        
        this.updateDashboardStats(stats);
    }

    updateDashboardStats(stats) {
        const statElements = {
            activeEvents: document.querySelector('.stat-card:nth-child(1) .stat-value'),
            totalGuests: document.querySelector('.stat-card:nth-child(2) .stat-value'),
            savings: document.querySelector('.stat-card:nth-child(3) .stat-value span'),
            rating: document.querySelector('.stat-card:nth-child(4) .stat-value')
        };
        
        Object.keys(stats).forEach(key => {
            if (statElements[key]) {
                statElements[key].setAttribute('data-count', stats[key]);
            }
        });
    }

    updateStatistics() {
        // Increment active events counter
        const activeEventsElement = document.querySelector('.stat-card:nth-child(1) .stat-value');
        if (activeEventsElement) {
            const current = parseInt(activeEventsElement.textContent);
            activeEventsElement.textContent = (current + 1).toString();
        }
    }

    // Charts and Visualization
    initializeCharts() {
        this.createBudgetChart();
        this.createEventTypesChart();
        this.animateProgressBars();
    }

    createBudgetChart() {
        const canvas = document.getElementById('budgetChart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        const data = [
            { label: 'Catering', value: 40, color: '#4F46E5' },
            { label: 'Miejsce', value: 25, color: '#10B981' },
            { label: 'Rozrywka', value: 20, color: '#F59E0B' },
            { label: 'Dekoracje', value: 15, color: '#EF4444' }
        ];
        
        this.drawPieChart(ctx, data, canvas.width, canvas.height);
    }

    createEventTypesChart() {
        const canvas = document.getElementById('eventTypesChart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        const data = [
            { label: 'Śluby', value: 45, color: '#F093FB' },
            { label: 'Firmowe', value: 30, color: '#4FACFE' },
            { label: 'Urodziny', value: 15, color: '#43E97B' },
            { label: 'Konferencje', value: 10, color: '#FA709A' }
        ];
        
        this.drawBarChart(ctx, data, canvas.width, canvas.height);
    }

    drawPieChart(ctx, data, width, height) {
        const centerX = width / 2;
        const centerY = height / 2;
        const radius = Math.min(width, height) / 2 - 20;
        
        let currentAngle = -Math.PI / 2;
        
        data.forEach(item => {
            const sliceAngle = (item.value / 100) * 2 * Math.PI;
            
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            ctx.closePath();
            
            ctx.fillStyle = item.color;
            ctx.fill();
            
            // Add labels
            const labelAngle = currentAngle + sliceAngle / 2;
            const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
            const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Inter';
            ctx.textAlign = 'center';
            ctx.fillText(`${item.value}%`, labelX, labelY);
            
            currentAngle += sliceAngle;
        });
    }

    drawBarChart(ctx, data, width, height) {
        const margin = 20;
        const chartWidth = width - 2 * margin;
        const chartHeight = height - 2 * margin;
        const barWidth = chartWidth / data.length - 10;
        
        const maxValue = Math.max(...data.map(d => d.value));
        
        data.forEach((item, index) => {
            const barHeight = (item.value / maxValue) * chartHeight;
            const x = margin + index * (barWidth + 10);
            const y = height - margin - barHeight;
            
            ctx.fillStyle = item.color;
            ctx.fillRect(x, y, barWidth, barHeight);
            
            // Add labels
            ctx.fillStyle = '#374151';
            ctx.font = '12px Inter';
            ctx.textAlign = 'center';
            ctx.fillText(item.label, x + barWidth / 2, height - 5);
            ctx.fillText(`${item.value}%`, x + barWidth / 2, y - 5);
        });
    }

    animateProgressBars() {
        const progressBars = document.querySelectorAll('.progress-fill');
        
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            
            setTimeout(() => {
                bar.style.width = width;
            }, 500);
        });
    }

    // Search and Filtering
    setupSearchAndFilters() {
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.handleVendorSearch(e));
        }
        
        const categoryFilter = document.getElementById('vendorCategory');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => this.handleCategoryFilter(e));
        }
        
        const ratingFilter = document.getElementById('vendorRating');
        if (ratingFilter) {
            ratingFilter.addEventListener('change', (e) => this.handleRatingFilter(e));
        }
    }

    handleVendorSearch(e) {
        const searchTerm = e.target.value.toLowerCase();
        const vendorCards = document.querySelectorAll('.vendor-card');
        
        vendorCards.forEach(card => {
            const vendorName = card.querySelector('h3').textContent.toLowerCase();
            const vendorDescription = card.querySelector('.vendor-description').textContent.toLowerCase();
            
            if (vendorName.includes(searchTerm) || vendorDescription.includes(searchTerm)) {
                card.style.display = 'block';
                card.style.animation = 'fadeInUp 0.3s ease-out';
            } else {
                card.style.display = 'none';
            }
        });
    }

    handleCategoryFilter(e) {
        const category = e.target.value;
        // Implementation for category filtering
        this.filterVendors({ category });
    }

    handleRatingFilter(e) {
        const minRating = parseFloat(e.target.value);
        // Implementation for rating filtering
        this.filterVendors({ minRating });
    }

    filterVendors(filters) {
        const vendorCards = document.querySelectorAll('.vendor-card');
        
        vendorCards.forEach(card => {
            let showCard = true;
            
            // Apply filters
            if (filters.category && filters.category !== '') {
                // Check if card matches category
                // Implementation depends on data structure
            }
            
            if (filters.minRating) {
                const rating = this.extractRating(card);
                if (rating < filters.minRating) {
                    showCard = false;
                }
            }
            
            card.style.display = showCard ? 'block' : 'none';
        });
    }

    extractRating(card) {
        const ratingText = card.querySelector('.vendor-rating span').textContent;
        return parseFloat(ratingText.split(' ')[0]);
    }

    // Touch Events for Mobile
    setupTouchEvents() {
        let startX, startY, distX, distY;
        
        document.addEventListener('touchstart', (e) => {
            const touch = e.touches[0];
            startX = touch.clientX;
            startY = touch.clientY;
        }, { passive: true });
        
        document.addEventListener('touchmove', (e) => {
            if (!startX || !startY) return;
            
            const touch = e.touches[0];
            distX = touch.clientX - startX;
            distY = touch.clientY - startY;
            
            // Implement swipe gestures for navigation
            if (Math.abs(distX) > Math.abs(distY) && Math.abs(distX) > 50) {
                if (distX > 0) {
                    // Swipe right
                    this.handleSwipeRight();
                } else {
                    // Swipe left
                    this.handleSwipeLeft();
                }
                
                startX = null;
                startY = null;
            }
        }, { passive: true });
    }

    handleSwipeRight() {
        // Navigate to previous section or close modal
        if (this.currentSection === 'vendors') {
            this.showSection('analytics');
        } else if (this.currentSection === 'analytics') {
            this.showSection('events');
        }
    }

    handleSwipeLeft() {
        // Navigate to next section
        if (this.currentSection === 'dashboard') {
            this.showSection('events');
        } else if (this.currentSection === 'events') {
            this.showSection('vendors');
        }
    }

    // Accessibility Features
    setupAccessibility() {
        // Keyboard navigation
        this.setupKeyboardNavigation();
        
        // Screen reader announcements
        this.setupScreenReaderSupport();
        
        // Focus management
        this.setupFocusManagement();
    }

    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
                this.closeWizard();
            }
            
            if (e.key === 'Tab' && document.getElementById('event-wizard').style.display === 'flex') {
                this.handleWizardTabNavigation(e);
            }
        });
    }

    handleWizardTabNavigation(e) {
        const wizard = document.getElementById('event-wizard');
        const focusableElements = wizard.querySelectorAll(
            'button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }

    setupScreenReaderSupport() {
        // Create live region for announcements
        const liveRegion = document.createElement('div');
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'sr-only';
        liveRegion.id = 'announcements';
        document.body.appendChild(liveRegion);
    }

    announceToScreenReader(message) {
        const liveRegion = document.getElementById('announcements');
        if (liveRegion) {
            liveRegion.textContent = message;
            setTimeout(() => {
                liveRegion.textContent = '';
            }, 1000);
        }
    }

    setupFocusManagement() {
        // Store focus when opening modals
        this.previousFocus = null;
        
        // Restore focus when closing modals
        const originalCloseModal = this.closeModal;
        this.closeModal = () => {
            originalCloseModal.call(this);
            if (this.previousFocus) {
                this.previousFocus.focus();
                this.previousFocus = null;
            }
        };
    }

    // Loading and Modal Management
    showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
        document.body.style.overflow = 'hidden';
        this.announceToScreenReader('Przetwarzanie żądania...');
    }

    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    showSuccessModal() {
        this.previousFocus = document.activeElement;
        document.getElementById('successModal').style.display = 'flex';
        document.body.style.overflow = 'hidden';
        
        setTimeout(() => {
            const firstButton = document.querySelector('#successModal .btn-secondary');
            if (firstButton) firstButton.focus();
        }, 100);
        
        this.announceToScreenReader('Wydarzenie zostało pomyślnie utworzone');
    }

    closeModal() {
        document.getElementById('successModal').style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    // Form Validation
    validateField(e) {
        const field = e.target;
        const value = field.value.trim();
        let isValid = true;
        let message = '';
        
        // Remove existing error
        this.clearValidationError(e);
        
        // Validation rules
        switch (field.type) {
            case 'email':
                isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
                message = 'Wprowadź poprawny adres email';
                break;
            case 'tel':
                isValid = /^\d{9}$/.test(value.replace(/\s/g, ''));
                message = 'Wprowadź poprawny numer telefonu';
                break;
            case 'text':
                if (field.required) {
                    isValid = value.length > 0;
                    message = 'To pole jest wymagane';
                }
                break;
            case 'number':
                if (field.required) {
                    isValid = !isNaN(parseFloat(value)) && isFinite(value);
                    message = 'Wprowadź poprawną liczbę';
                }
                break;
        }
        
        if (!isValid) {
            this.showValidationError(field, message);
        }
        
        return isValid;
    }

    showValidationError(field, message) {
        field.classList.add('error');
        
        let errorElement = field.parentNode.querySelector('.error-message');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'error-message';
            errorElement.setAttribute('role', 'alert');
            field.parentNode.appendChild(errorElement);
        }
        
        errorElement.textContent = message;
    }

    clearValidationError(e) {
        const field = e.target;
        field.classList.remove('error');
        
        const errorElement = field.parentNode.querySelector('.error-message');
        if (errorElement) {
            errorElement.remove();
        }
    }

    // Utility Functions
    resetWizard() {
        this.wizardStep = 1;
        this.selectedEventType = null;
        
        // Clear form fields
        document.querySelectorAll('#event-wizard input, #event-wizard select').forEach(field => {
            field.value = '';
        });
        
        // Reset selections
        document.querySelectorAll('.event-type-card').forEach(card => {
            card.classList.remove('selected');
            card.setAttribute('aria-selected', 'false');
        });
    }

    handleResize() {
        // Redraw charts on resize
        if (this.currentSection === 'analytics') {
            setTimeout(() => {
                this.initializeCharts();
            }, 100);
        }
    }

    // Data Loading Functions
    loadEvents() {
        // Simulate loading from API
        return [];
    }

    loadVendors() {
        // Simulate loading from API
        return [];
    }

    loadAnalyticsData() {
        // Load analytics when section is shown
        setTimeout(() => {
            this.initializeCharts();
        }, 100);
    }

    loadVendorData() {
        // Refresh vendor data when section is shown
        this.announceToScreenReader('Marketplace dostawców załadowany');
    }

    // Demo Functionality
    showDemo() {
        this.announceToScreenReader('Odtwarzanie demonstracji');
        // Implementation for demo video/tutorial
        alert('Demo: Kompleksowe planowanie ślubu w 10 minut dzięki AI!');
    }

    // Event Management Functions
    openEventDetails(eventId) {
        this.announceToScreenReader('Otwieranie szczegółów wydarzenia');
        // Implementation for event details modal
    }

    continueEvent(eventId) {
        this.announceToScreenReader('Kontynuowanie planowania wydarzenia');
        // Implementation for continuing event planning
    }

    viewReport(eventId) {
        this.announceToScreenReader('Otwieranie raportu wydarzenia');
        // Implementation for event report
    }

    repeatEvent(eventId) {
        this.announceToScreenReader('Tworzenie kopii wydarzenia');
        // Implementation for repeating event
    }

    goToDashboard() {
        this.closeModal();
        this.showSection('dashboard');
        this.announceToScreenReader('Przechodzenie do panelu kontrolnego');
    }

    handleVendorSelection(e) {
        const card = e.currentTarget;
        const vendorName = card.querySelector('h3').textContent;
        this.announceToScreenReader(`Wybrano dostawcę: ${vendorName}`);
        
        // Add selection visual feedback
        card.style.transform = 'scale(0.98)';
        setTimeout(() => {
            card.style.transform = '';
        }, 150);
    }

    handleKeyboardNavigation(e) {
        // Global keyboard shortcuts
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case '1':
                    e.preventDefault();
                    this.showSection('dashboard');
                    break;
                case '2':
                    e.preventDefault();
                    this.showSection('events');
                    break;
                case '3':
                    e.preventDefault();
                    this.showSection('vendors');
                    break;
                case '4':
                    e.preventDefault();
                    this.showSection('analytics');
                    break;
                case 'n':
                    e.preventDefault();
                    this.openWizard();
                    break;
            }
        }
    }
}

// Global Functions (for HTML onclick handlers)
function closeWizard() {
    app.closeWizard();
}

function nextStep() {
    app.nextStep();
}

function previousStep() {
    app.previousStep();
}

function createEvent() {
    app.createEvent();
}

function closeModal() {
    app.closeModal();
}

function goToDashboard() {
    app.goToDashboard();
}

function openEventDetails(eventId) {
    app.openEventDetails(eventId);
}

function continueEvent(eventId) {
    app.continueEvent(eventId);
}

function viewReport(eventId) {
    app.viewReport(eventId);
}

function repeatEvent(eventId) {
    app.repeatEvent(eventId);
}

// Initialize Application
let app;

document.addEventListener('DOMContentLoaded', () => {
    // Use enhanced app if available, otherwise fall back to base app
    app = window.EnhancedEventPlannerApp ? 
        new EnhancedEventPlannerApp() : 
        new EventPlannerApp();
    
    // Add CSS animation keyframes dynamically
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .error {
            border-color: var(--error-color) !important;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
        }
        
        .error-message {
            color: var(--error-color);
            font-size: var(--font-size-sm);
            margin-top: var(--spacing-xs);
        }
    `;
    document.head.appendChild(style);
});

// Service Worker Registration (for PWA capabilities)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
                console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}