// Enhanced Features for EventAI.pl - Complete User Flow Implementation

// Extend the EventPlannerApp class with additional methods
class EnhancedEventPlannerApp extends EventPlannerApp {
    constructor() {
        super();
        this.setupEnhancedFeatures();
    }

    setupEnhancedFeatures() {
        this.setupNotificationSystem();
        this.setupAdvancedAnalytics();
        this.setupEventTemplates();
        this.setupCollaborationFeatures();
        this.setupAdvancedVendorManagement();
        this.setupRealtimeUpdates();
    }

    // Notification System
    setupNotificationSystem() {
        // Create notification container
        if (!document.getElementById('notificationContainer')) {
            const container = document.createElement('div');
            container.id = 'notificationContainer';
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
    }

    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notificationContainer');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        const icon = this.getNotificationIcon(type);
        
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${icon}"></i>
                <span class="notification-message">${message}</span>
                <button class="notification-close" aria-label="Zamknij powiadomienie">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="notification-progress"></div>
        `;

        // Add event listeners
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => this.closeNotification(notification));

        // Add to container with animation
        container.appendChild(notification);
        
        // Trigger animation
        setTimeout(() => {
            notification.classList.add('notification-show');
        }, 10);

        // Auto-remove after duration
        if (duration > 0) {
            const progressBar = notification.querySelector('.notification-progress');
            progressBar.style.animation = `notificationProgress ${duration}ms linear`;
            
            setTimeout(() => {
                this.closeNotification(notification);
            }, duration);
        }

        this.announceToScreenReader(message);
        
        return notification;
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        
        return icons[type] || icons.info;
    }

    closeNotification(notification) {
        notification.classList.add('notification-hide');
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    // Enhanced Loading States
    showLoading(message = 'Przetwarzanie żądania...') {
        this.isLoading = true;
        const overlay = document.getElementById('loadingOverlay');
        const messageElement = overlay.querySelector('p');
        
        if (messageElement) {
            messageElement.textContent = message;
        }
        
        overlay.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        this.announceToScreenReader(message);
    }

    updateLoadingMessage(message) {
        if (this.isLoading) {
            const overlay = document.getElementById('loadingOverlay');
            const messageElement = overlay.querySelector('p');
            
            if (messageElement) {
                messageElement.textContent = message;
            }
            
            this.announceToScreenReader(message);
        }
    }

    hideLoading() {
        this.isLoading = false;
        document.getElementById('loadingOverlay').style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    showGlobalLoading(message = 'Ładowanie...') {
        let globalLoader = document.getElementById('globalLoadingOverlay');
        
        if (!globalLoader) {
            globalLoader = document.createElement('div');
            globalLoader.id = 'globalLoadingOverlay';
            globalLoader.className = 'loading-overlay';
            globalLoader.innerHTML = `
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <h3>EventAI.pl</h3>
                    <p>${message}</p>
                </div>
            `;
            document.body.appendChild(globalLoader);
        }
        
        const messageElement = globalLoader.querySelector('p');
        if (messageElement) {
            messageElement.textContent = message;
        }
        
        globalLoader.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }

    hideGlobalLoading() {
        const globalLoader = document.getElementById('globalLoadingOverlay');
        if (globalLoader) {
            globalLoader.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    }

    // Event Templates
    setupEventTemplates() {
        this.eventTemplates = {
            wedding: {
                name: 'Szablon ślubu',
                description: 'Kompleksowy szablon dla organizacji ślubu',
                tasks: [
                    { name: 'Wybór miejsca', weeks: 24, priority: 'high' },
                    { name: 'Rezerwacja kościoła/USC', weeks: 20, priority: 'high' },
                    { name: 'Wybór fotografa', weeks: 16, priority: 'medium' },
                    { name: 'Zamówienie sukni', weeks: 12, priority: 'high' },
                    { name: 'Wybór cateringu', weeks: 8, priority: 'high' },
                    { name: 'Dekoracje i kwiaty', weeks: 4, priority: 'medium' },
                    { name: 'Próba generalna', weeks: 1, priority: 'high' }
                ]
            },
            corporate: {
                name: 'Szablon wydarzenia firmowego',
                description: 'Profesjonalny szablon dla eventów biznesowych',
                tasks: [
                    { name: 'Określenie celów wydarzenia', weeks: 12, priority: 'high' },
                    { name: 'Wybór miejsca', weeks: 10, priority: 'high' },
                    { name: 'Zaproszenie prelegentów', weeks: 8, priority: 'high' },
                    { name: 'Marketing i promocja', weeks: 6, priority: 'medium' },
                    { name: 'Organizacja cateringu', weeks: 4, priority: 'medium' },
                    { name: 'Przygotowanie materiałów', weeks: 2, priority: 'high' },
                    { name: 'Finalne przygotowania', weeks: 1, priority: 'high' }
                ]
            }
        };
    }

    async applyEventTemplate(eventType, eventData) {
        const template = this.eventTemplates[eventType];
        if (!template) return null;

        const eventDate = new Date(eventData.date);
        const tasks = template.tasks.map(task => {
            const taskDate = new Date(eventDate);
            taskDate.setDate(taskDate.getDate() - (task.weeks * 7));

            return {
                ...task,
                eventId: eventData.id,
                dueDate: taskDate.toISOString().split('T')[0],
                status: 'pending',
                assignedTo: eventData.createdBy
            };
        });

        // Save tasks to database
        for (const task of tasks) {
            await this.dataManager.create('tasks', task);
        }

        return tasks;
    }

    // Advanced Analytics
    setupAdvancedAnalytics() {
        this.analyticsMetrics = {
            eventSuccess: 'success_rate',
            budgetEfficiency: 'budget_variance',
            guestSatisfaction: 'satisfaction_score',
            vendorPerformance: 'vendor_rating',
            timelineAdherence: 'timeline_score'
        };
    }

    async calculateAdvancedAnalytics(timeRange = '30d') {
        try {
            const stats = await this.dataManager.getEventStatistics(timeRange);
            if (!stats.success) return null;

            const events = await this.dataManager.getEventsByDateRange(
                this.getDateRange(timeRange).start,
                this.getDateRange(timeRange).end
            );

            if (!events.success) return stats;

            const analytics = {
                ...stats.data,
                advanced: {
                    successRate: this.calculateSuccessRate(events.data),
                    budgetEfficiency: this.calculateBudgetEfficiency(events.data),
                    vendorPerformance: await this.calculateVendorPerformance(),
                    trendAnalysis: this.calculateTrends(events.data),
                    predictions: await this.generatePredictions(events.data)
                }
            };

            this.cache.analytics = analytics;
            return analytics;

        } catch (error) {
            this.config.error('Advanced analytics calculation failed:', error);
            return null;
        }
    }

    calculateSuccessRate(events) {
        const completedEvents = events.filter(e => e.status === 'completed');
        const successfulEvents = completedEvents.filter(e => e.rating >= 4.0);
        
        return completedEvents.length > 0 ? 
            (successfulEvents.length / completedEvents.length) * 100 : 0;
    }

    calculateBudgetEfficiency(events) {
        const eventsWithBudget = events.filter(e => e.budget && e.actualCost);
        
        if (eventsWithBudget.length === 0) return 0;

        const totalSavings = eventsWithBudget.reduce((sum, event) => {
            return sum + Math.max(0, event.budget - event.actualCost);
        }, 0);

        const totalBudget = eventsWithBudget.reduce((sum, event) => sum + event.budget, 0);
        
        return totalBudget > 0 ? (totalSavings / totalBudget) * 100 : 0;
    }

    async calculateVendorPerformance() {
        const vendors = await this.dataManager.read('vendors');
        if (!vendors.success) return [];

        return vendors.data.map(vendor => ({
            id: vendor.id,
            name: vendor.name,
            type: vendor.type,
            averageRating: vendor.rating || 0,
            totalEvents: vendor.eventCount || 0,
            reliability: this.calculateVendorReliability(vendor),
            costEfficiency: this.calculateVendorCostEfficiency(vendor)
        })).sort((a, b) => b.averageRating - a.averageRating);
    }

    calculateVendorReliability(vendor) {
        // Mock calculation - in real app this would be based on actual performance data
        return Math.random() * 0.3 + 0.7; // 70-100%
    }

    calculateVendorCostEfficiency(vendor) {
        // Mock calculation - in real app this would compare vendor prices to market average
        return Math.random() * 0.4 + 0.6; // 60-100%
    }

    calculateTrends(events) {
        const monthlyData = this.groupEventsByMonth(events);
        const trends = {
            eventCount: this.calculateTrend(monthlyData.map(m => m.count)),
            averageBudget: this.calculateTrend(monthlyData.map(m => m.avgBudget)),
            averageGuests: this.calculateTrend(monthlyData.map(m => m.avgGuests))
        };

        return trends;
    }

    groupEventsByMonth(events) {
        const monthlyData = {};
        
        events.forEach(event => {
            const month = new Date(event.date).toISOString().substring(0, 7); // YYYY-MM
            
            if (!monthlyData[month]) {
                monthlyData[month] = {
                    count: 0,
                    totalBudget: 0,
                    totalGuests: 0,
                    events: []
                };
            }
            
            monthlyData[month].count++;
            monthlyData[month].totalBudget += event.budget || 0;
            monthlyData[month].totalGuests += event.guests || 0;
            monthlyData[month].events.push(event);
        });

        return Object.entries(monthlyData).map(([month, data]) => ({
            month,
            count: data.count,
            avgBudget: data.count > 0 ? data.totalBudget / data.count : 0,
            avgGuests: data.count > 0 ? data.totalGuests / data.count : 0
        })).sort((a, b) => a.month.localeCompare(b.month));
    }

    calculateTrend(values) {
        if (values.length < 2) return 0;
        
        const firstHalf = values.slice(0, Math.floor(values.length / 2));
        const secondHalf = values.slice(Math.floor(values.length / 2));
        
        const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
        const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
        
        return firstAvg > 0 ? ((secondAvg - firstAvg) / firstAvg) * 100 : 0;
    }

    async generatePredictions(events) {
        // Use AI service for predictions
        try {
            const messages = [
                {
                    role: "system",
                    content: "Jesteś ekspertem od analizy trendów w branży eventowej. Na podstawie danych historycznych przewidujesz trendy i rekomendacje."
                },
                {
                    role: "user",
                    content: `Przeanalizuj następujące dane wydarzeń i wygeneruj prognozy na następny kwartał:
                    
                    Liczba wydarzeń: ${events.length}
                    Średni budżet: ${events.reduce((sum, e) => sum + (e.budget || 0), 0) / events.length} PLN
                    Najpopularniejszy typ: ${this.getMostPopularEventType(events)}
                    
                    Podaj prognozy w formacie JSON z kluczami: expectedGrowth, budgetTrends, popularTypes, recommendations`
                }
            ];

            const response = await this.aiService.makeLlamaRequest(messages, {
                temperature: 0.3,
                max_tokens: 600
            });

            if (response.choices && response.choices[0]) {
                const content = response.choices[0].message.content;
                try {
                    const jsonMatch = content.match(/\{.*\}/s);
                    if (jsonMatch) {
                        return JSON.parse(jsonMatch[0]);
                    }
                } catch (parseError) {
                    this.config.log('Failed to parse AI predictions, using fallback');
                }
            }
        } catch (error) {
            this.config.error('AI predictions failed:', error);
        }

        // Fallback predictions
        return {
            expectedGrowth: 15,
            budgetTrends: 'Wzrost o 8% względem poprzedniego kwartału',
            popularTypes: ['wedding', 'corporate'],
            recommendations: [
                'Rozważ ekspansję usług weselnych',
                'Inwestuj w technologie eventowe',
                'Zwiększ ofertę dla wydarzeń hybrydowych'
            ]
        };
    }

    getMostPopularEventType(events) {
        const typeCounts = {};
        events.forEach(event => {
            typeCounts[event.type] = (typeCounts[event.type] || 0) + 1;
        });
        
        return Object.entries(typeCounts)
            .sort(([,a], [,b]) => b - a)[0]?.[0] || 'wedding';
    }

    getDateRange(timeRange) {
        const end = new Date();
        const start = new Date();
        
        switch (timeRange) {
            case '7d':
                start.setDate(end.getDate() - 7);
                break;
            case '30d':
                start.setDate(end.getDate() - 30);
                break;
            case '90d':
                start.setDate(end.getDate() - 90);
                break;
            case '1y':
                start.setFullYear(end.getFullYear() - 1);
                break;
        }
        
        return {
            start: start.toISOString().split('T')[0],
            end: end.toISOString().split('T')[0]
        };
    }

    // Complete User Flow Implementation
    async openEventDetails(eventId) {
        try {
            this.showLoading('Ładowanie szczegółów wydarzenia...');
            
            const result = await this.dataManager.read('events', eventId);
            if (!result.success || !result.data) {
                throw new Error('Nie znaleziono wydarzenia');
            }

            const event = result.data;
            this.currentEvent = event;
            
            // Generate detailed view
            await this.showEventDetailsModal(event);
            
            this.hideLoading();
            
        } catch (error) {
            this.config.error('Failed to open event details:', error);
            this.hideLoading();
            this.showNotification(`Błąd: ${error.message}`, 'error');
        }
    }

    async showEventDetailsModal(event) {
        // Create modal if it doesn't exist
        let modal = document.getElementById('eventDetailsModal');
        
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'eventDetailsModal';
            modal.className = 'modal-overlay';
            document.body.appendChild(modal);
        }

        // Generate modal content
        modal.innerHTML = `
            <div class="modal-content event-details-modal">
                <div class="modal-header">
                    <h2>${event.name}</h2>
                    <button class="btn-close" onclick="app.closeEventDetailsModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${await this.generateEventDetailsContent(event)}
                </div>
                <div class="modal-actions">
                    <button class="btn-secondary" onclick="app.editEvent('${event.id}')">
                        <i class="fas fa-edit"></i>Edytuj
                    </button>
                    <button class="btn-primary" onclick="app.continueEvent('${event.id}')">
                        <i class="fas fa-play"></i>Kontynuuj planowanie
                    </button>
                    ${event.status === 'completed' ? 
                        `<button class="btn-outline" onclick="app.generateEventReport('${event.id}')">
                            <i class="fas fa-chart-bar"></i>Raport
                        </button>` : ''
                    }
                </div>
            </div>
        `;

        modal.style.display = 'flex';
        this.previousFocus = document.activeElement;
        
        // Focus management
        setTimeout(() => {
            const firstButton = modal.querySelector('.btn-secondary');
            if (firstButton) firstButton.focus();
        }, 100);
    }

    async generateEventDetailsContent(event) {
        const aiRecommendations = event.aiRecommendations;
        const progress = event.progress || 0;
        
        return `
            <div class="event-details-grid">
                <div class="detail-section">
                    <h3>Podstawowe informacje</h3>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>Typ wydarzenia:</label>
                            <span>${this.getEventTypeLabel(event.type)}</span>
                        </div>
                        <div class="detail-item">
                            <label>Data:</label>
                            <span>${this.formatDate(event.date)}</span>
                        </div>
                        <div class="detail-item">
                            <label>Lokalizacja:</label>
                            <span>${event.location}</span>
                        </div>
                        <div class="detail-item">
                            <label>Liczba gości:</label>
                            <span>${event.guests}</span>
                        </div>
                        <div class="detail-item">
                            <label>Budżet:</label>
                            <span>${event.budget.toLocaleString()} PLN</span>
                        </div>
                        <div class="detail-item">
                            <label>Status:</label>
                            <span class="status-badge status-${event.status}">${this.getStatusLabel(event.status)}</span>
                        </div>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h3>Postęp planowania</h3>
                    <div class="progress-section">
                        <div class="progress-bar-large">
                            <div class="progress-fill" style="width: ${progress}%"></div>
                            <span class="progress-text">${progress}%</span>
                        </div>
                        <div class="progress-details">
                            ${await this.generateProgressDetails(event)}
                        </div>
                    </div>
                </div>
                
                ${aiRecommendations ? `
                <div class="detail-section">
                    <h3>Rekomendacje AI</h3>
                    <div class="ai-recommendations-grid">
                        ${this.generateAIRecommendationsHTML(aiRecommendations)}
                    </div>
                </div>
                ` : ''}
                
                <div class="detail-section">
                    <h3>Timeline i zadania</h3>
                    <div class="timeline-section">
                        ${await this.generateTimelineHTML(event)}
                    </div>
                </div>
            </div>
        `;
    }

    getEventTypeLabel(type) {
        const labels = {
            wedding: 'Ślub',
            corporate: 'Wydarzenie firmowe',
            birthday: 'Urodziny',
            conference: 'Konferencja'
        };
        
        return labels[type] || type;
    }

    getStatusLabel(status) {
        const labels = {
            planning: 'Planowanie',
            active: 'W trakcie',
            completed: 'Zakończone',
            cancelled: 'Anulowane'
        };
        
        return labels[status] || status;
    }

    async generateProgressDetails(event) {
        // Get tasks for this event
        const tasks = await this.dataManager.query('tasks', {
            filters: { eventId: event.id }
        });

        if (!tasks.success || tasks.data.length === 0) {
            return '<p>Brak szczegółowych zadań</p>';
        }

        const completedTasks = tasks.data.filter(t => t.status === 'completed').length;
        const totalTasks = tasks.data.length;
        
        return `
            <div class="task-summary">
                <div class="task-stat">
                    <span class="task-number">${completedTasks}</span>
                    <span class="task-label">Ukończone zadania</span>
                </div>
                <div class="task-stat">
                    <span class="task-number">${totalTasks - completedTasks}</span>
                    <span class="task-label">Pozostałe zadania</span>
                </div>
                <div class="task-stat">
                    <span class="task-number">${totalTasks}</span>
                    <span class="task-label">Łącznie zadań</span>
                </div>
            </div>
        `;
    }

    generateAIRecommendationsHTML(recommendations) {
        let html = '';
        
        if (recommendations.venue) {
            html += `
                <div class="ai-rec-card">
                    <h4><i class="fas fa-map-marker-alt"></i> Miejsce</h4>
                    <p><strong>${recommendations.venue.name}</strong></p>
                    <p>${recommendations.venue.description}</p>
                    <p class="ai-rec-price">${recommendations.venue.price}</p>
                </div>
            `;
        }
        
        if (recommendations.catering) {
            html += `
                <div class="ai-rec-card">
                    <h4><i class="fas fa-utensils"></i> Catering</h4>
                    <p><strong>${recommendations.catering.name}</strong></p>
                    <p>${recommendations.catering.description}</p>
                    <p class="ai-rec-price">${recommendations.catering.pricePerPerson} zł/osoba</p>
                </div>
            `;
        }
        
        if (recommendations.entertainment) {
            html += `
                <div class="ai-rec-card">
                    <h4><i class="fas fa-music"></i> Rozrywka</h4>
                    <p><strong>${recommendations.entertainment.name}</strong></p>
                    <p>${recommendations.entertainment.description}</p>
                    <p class="ai-rec-price">${recommendations.entertainment.price} zł</p>
                </div>
            `;
        }
        
        return html;
    }

    async generateTimelineHTML(event) {
        const timeline = event.aiRecommendations?.timeline || this.aiService.generateEventTimeline(event);
        
        let html = '<div class="timeline-items">';
        
        timeline.forEach((phase, index) => {
            const isCompleted = phase.weeks === 0 || event.progress > (index * 25);
            
            html += `
                <div class="timeline-item ${isCompleted ? 'completed' : ''}">
                    <div class="timeline-marker">
                        <i class="fas ${isCompleted ? 'fa-check' : 'fa-circle'}"></i>
                    </div>
                    <div class="timeline-content">
                        <h4>${phase.phase}</h4>
                        <p>Termin: ${this.calculatePhaseDeadline(phase.weeks, event.date)}</p>
                        <div class="timeline-tasks">
                            ${phase.tasks ? phase.tasks.map(task => 
                                `<span class="task-tag">${task}</span>`
                            ).join('') : ''}
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        return html;
    }

    calculatePhaseDeadline(weeksFromEvent, eventDate) {
        const eventDateTime = new Date(eventDate);
        const deadline = new Date(eventDateTime);
        deadline.setDate(deadline.getDate() - (weeksFromEvent * 7));
        
        return deadline.toLocaleDateString('pl-PL');
    }

    closeEventDetailsModal() {
        const modal = document.getElementById('eventDetailsModal');
        if (modal) {
            modal.style.display = 'none';
            
            if (this.previousFocus) {
                this.previousFocus.focus();
                this.previousFocus = null;
            }
        }
    }

    async editEvent(eventId) {
        try {
            const result = await this.dataManager.read('events', eventId);
            if (!result.success || !result.data) {
                throw new Error('Nie znaleziono wydarzenia');
            }

            const event = result.data;
            
            // Close details modal
            this.closeEventDetailsModal();
            
            // Open edit wizard with pre-filled data
            this.openEditWizard(event);
            
        } catch (error) {
            this.config.error('Failed to edit event:', error);
            this.showNotification(`Błąd: ${error.message}`, 'error');
        }
    }

    openEditWizard(event) {
        // Pre-fill wizard with event data
        this.selectedEventType = event.type;
        this.currentEvent = event;
        
        // Open wizard
        this.openWizard();
        
        // Fill form fields
        setTimeout(() => {
            document.getElementById('eventName').value = event.name;
            document.getElementById('eventDate').value = event.date;
            document.getElementById('eventLocation').value = event.location;
            document.getElementById('eventDuration').value = event.duration || '6-8';
            document.getElementById('guestCount').value = event.guests;
            document.getElementById('budgetRange').value = event.budget;
            
            // Update budget display
            this.updateBudget({ target: { value: event.budget } });
            
            // Select event type
            const eventTypeCard = document.querySelector(`[data-type="${event.type}"]`);
            if (eventTypeCard) {
                this.selectEventType({ currentTarget: eventTypeCard });
            }
            
            // Update dietary restrictions
            if (event.dietaryRestrictions) {
                event.dietaryRestrictions.forEach(restriction => {
                    const checkbox = document.querySelector(`input[value="${restriction}"]`);
                    if (checkbox) checkbox.checked = true;
                });
            }
        }, 100);
    }

    async continueEvent(eventId) {
        try {
            const result = await this.dataManager.read('events', eventId);
            if (!result.success || !result.data) {
                throw new Error('Nie znaleziono wydarzenia');
            }

            const event = result.data;
            this.currentEvent = event;
            
            // Close any open modals
            this.closeEventDetailsModal();
            
            // Show event management interface
            await this.showEventManagementInterface(event);
            
        } catch (error) {
            this.config.error('Failed to continue event:', error);
            this.showNotification(`Błąd: ${error.message}`, 'error');
        }
    }

    async showEventManagementInterface(event) {
        // This would open a comprehensive event management interface
        // For now, we'll show the analytics section with event-specific data
        this.showSection('analytics');
        
        // Filter analytics for this specific event
        await this.loadEventSpecificAnalytics(event.id);
        
        this.showNotification(`Zarządzanie wydarzeniem: ${event.name}`, 'info');
    }

    async loadEventSpecificAnalytics(eventId) {
        try {
            const analytics = await this.dataManager.getEventAnalytics(eventId);
            
            if (analytics.success) {
                // Update analytics display with event-specific data
                this.updateAnalyticsDisplay(analytics.data, eventId);
            }
            
        } catch (error) {
            this.config.error('Failed to load event analytics:', error);
        }
    }

    // Update UI Methods
    updateEventCards() {
        const eventsGrid = document.querySelector('.events-grid');
        if (!eventsGrid) return;

        // Clear existing cards (except template)
        const existingCards = eventsGrid.querySelectorAll('.event-card:not(.event-card-template)');
        existingCards.forEach(card => card.remove());

        // Add new cards
        this.cache.events.forEach(event => {
            const card = this.createEventCardFromData(event);
            eventsGrid.appendChild(card);
        });
    }

    createEventCardFromData(event) {
        const card = document.createElement('div');
        card.className = 'event-card';
        card.dataset.eventId = event.id;
        
        if (event.priority === 'high') {
            card.classList.add('priority-high');
        }

        const typeIcons = {
            wedding: 'fa-heart',
            corporate: 'fa-briefcase',
            birthday: 'fa-birthday-cake',
            conference: 'fa-microphone'
        };
        
        const typeLabels = {
            wedding: 'Ślub',
            corporate: 'Wydarzenie firmowe',
            birthday: 'Urodziny',
            conference: 'Konferencja'
        };

        card.innerHTML = `
            <div class="event-header">
                <div class="event-type">
                    <i class="fas ${typeIcons[event.type]}"></i>
                    <span>${typeLabels[event.type]}</span>
                </div>
                <div class="event-status status-${event.status}">${this.getStatusLabel(event.status)}</div>
            </div>
            <h3 class="event-title">${event.name}</h3>
            <div class="event-details">
                <div class="detail-item">
                    <i class="fas fa-calendar"></i>
                    <span>${this.formatDate(event.date)}</span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-users"></i>
                    <span>${event.guests} gości</span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-money-bill"></i>
                    <span>${event.budget.toLocaleString()} zł</span>
                </div>
                ${event.location ? `
                <div class="detail-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>${event.location}</span>
                </div>
                ` : ''}
            </div>
            
            ${event.status !== 'completed' ? `
            <div class="event-progress">
                <div class="progress-header">
                    <span>Postęp planowania</span>
                    <span class="progress-percent">${event.progress || 0}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${event.progress || 0}%"></div>
                </div>
            </div>
            ` : ''}
            
            ${event.status === 'completed' && event.rating ? `
            <div class="event-rating">
                <div class="rating-stars">
                    ${this.generateStars(event.rating)}
                </div>
                <span class="rating-text">Ocena: ${event.rating}/5</span>
            </div>
            ` : ''}
            
            <div class="event-actions">
                <button class="btn-outline" onclick="app.openEventDetails('${event.id}')">
                    <i class="fas fa-eye"></i>Szczegóły
                </button>
                ${event.status === 'completed' ? `
                <button class="btn-secondary" onclick="app.repeatEvent('${event.id}')">
                    <i class="fas fa-copy"></i>Powtórz
                </button>
                ` : `
                <button class="btn-primary" onclick="app.continueEvent('${event.id}')">
                    <i class="fas fa-play"></i>Kontynuuj
                </button>
                `}
            </div>
        `;

        return card;
    }

    generateStars(rating) {
        let stars = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                stars += '<i class="fas fa-star"></i>';
            } else if (i - 0.5 <= rating) {
                stars += '<i class="fas fa-star-half-alt"></i>';
            } else {
                stars += '<i class="far fa-star"></i>';
            }
        }
        return stars;
    }

    updateVendorCards() {
        const vendorsGrid = document.querySelector('.vendors-grid');
        if (!vendorsGrid) return;

        // Clear existing cards
        vendorsGrid.innerHTML = '';

        // Add vendor cards
        this.cache.vendors.forEach(vendor => {
            const card = this.createVendorCardFromData(vendor);
            vendorsGrid.appendChild(card);
        });
    }

    createVendorCardFromData(vendor) {
        const card = document.createElement('div');
        card.className = 'vendor-card';
        card.dataset.vendorId = vendor.id;
        
        if (vendor.featured) {
            card.classList.add('featured');
        }

        card.innerHTML = `
            ${vendor.featured ? '<div class="vendor-badge">Polecane</div>' : ''}
            
            <div class="vendor-image">
                <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 300 200'><rect width='300' height='200' fill='%23f3f4f6'/><text x='150' y='100' text-anchor='middle' font-size='16' fill='%236b7280'>${vendor.name}</text></svg>" alt="${vendor.name}">
            </div>
            
            <div class="vendor-content">
                <div class="vendor-header">
                    <h3>${vendor.name}</h3>
                    <div class="vendor-rating">
                        <div class="stars">${this.generateStars(vendor.rating)}</div>
                        <span>${vendor.rating} (${vendor.reviewCount || 0} opinii)</span>
                    </div>
                </div>
                
                <p class="vendor-description">${vendor.description}</p>
                
                <div class="vendor-features">
                    ${vendor.features ? vendor.features.map(feature => 
                        `<span class="feature-tag">${feature}</span>`
                    ).join('') : ''}
                </div>
                
                <div class="vendor-pricing">
                    <span class="price-range">od ${vendor.basePrice.toLocaleString()} zł</span>
                    <button class="btn-primary" onclick="app.contactVendor('${vendor.id}')">
                        Zapytaj o wycenę
                    </button>
                </div>
            </div>
        `;

        return card;
    }

    updateDashboardStats() {
        const stats = this.calculateDashboardStats();
        
        // Update stat cards
        this.updateStatCard(0, stats.activeEvents);
        this.updateStatCard(1, stats.totalGuests);
        this.updateStatCard(2, stats.savings);
        this.updateStatCard(3, stats.rating.toFixed(1));
    }

    calculateDashboardStats() {
        const events = this.cache.events;
        
        const activeEvents = events.filter(e => ['planning', 'active'].includes(e.status)).length;
        const totalGuests = events.reduce((sum, e) => sum + (e.guests || 0), 0);
        const totalBudget = events.reduce((sum, e) => sum + (e.budget || 0), 0);
        const totalActualCost = events.reduce((sum, e) => sum + (e.actualCost || e.budget || 0), 0);
        const savings = Math.max(0, totalBudget - totalActualCost);
        
        const completedEvents = events.filter(e => e.status === 'completed');
        const avgRating = completedEvents.length > 0 ? 
            completedEvents.reduce((sum, e) => sum + (e.rating || 4.5), 0) / completedEvents.length : 4.9;

        return {
            activeEvents,
            totalGuests,
            savings,
            rating: avgRating
        };
    }

    updateStatCard(index, value) {
        const statCards = document.querySelectorAll('.stat-card');
        if (statCards[index]) {
            const valueElement = statCards[index].querySelector('.stat-value');
            if (valueElement) {
                // Animate counter
                this.animateCounter(valueElement, parseInt(valueElement.textContent.replace(/[^\d]/g, '')) || 0, value);
            }
        }
    }

    animateCounter(element, from, to) {
        const duration = 1000;
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = Math.floor(from + (to - from) * progress);
            element.textContent = currentValue.toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('pl-PL', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
        });
    }

    // Additional vendor management
    async contactVendor(vendorId) {
        try {
            const result = await this.dataManager.read('vendors', vendorId);
            if (!result.success || !result.data) {
                throw new Error('Nie znaleziono dostawcy');
            }

            const vendor = result.data;
            
            // Show contact modal or redirect to contact form
            this.showVendorContactModal(vendor);
            
        } catch (error) {
            this.config.error('Failed to contact vendor:', error);
            this.showNotification(`Błąd: ${error.message}`, 'error');
        }
    }

    showVendorContactModal(vendor) {
        // Implementation for vendor contact modal
        this.showNotification(`Kontakt z ${vendor.name} - funkcja w przygotowaniu`, 'info');
    }

    async repeatEvent(eventId) {
        try {
            const result = await this.dataManager.read('events', eventId);
            if (!result.success || !result.data) {
                throw new Error('Nie znaleziono wydarzenia');
            }

            const originalEvent = result.data;
            
            // Create copy of event with new date
            const newEvent = {
                ...originalEvent,
                name: `${originalEvent.name} (kopia)`,
                date: '', // User will need to set new date
                status: 'planning',
                progress: 0,
                id: undefined, // Will get new ID
                createdAt: undefined,
                updatedAt: undefined
            };

            // Open wizard with pre-filled data
            this.selectedEventType = newEvent.type;
            this.currentEvent = newEvent;
            this.openWizard();
            
            this.showNotification('Szablon wydarzenia został załadowany', 'success');
            
        } catch (error) {
            this.config.error('Failed to repeat event:', error);
            this.showNotification(`Błąd: ${error.message}`, 'error');
        }
    }

    async generateEventReport(eventId) {
        try {
            this.showLoading('Generowanie raportu...');
            
            const result = await this.dataManager.read('events', eventId);
            if (!result.success || !result.data) {
                throw new Error('Nie znaleziono wydarzenia');
            }

            const event = result.data;
            const analytics = await this.dataManager.getEventAnalytics(eventId);
            
            // Generate comprehensive report
            const report = await this.generateComprehensiveReport(event, analytics.data || []);
            
            // Show report modal
            this.showReportModal(report);
            
            this.hideLoading();
            
        } catch (error) {
            this.config.error('Failed to generate report:', error);
            this.hideLoading();
            this.showNotification(`Błąd: ${error.message}`, 'error');
        }
    }

    async generateComprehensiveReport(event, analytics) {
        // Use AI to generate insights
        try {
            const messages = [
                {
                    role: "system",
                    content: "Jesteś ekspertem od analizy wydarzeń. Generujesz szczegółowe raporty z podsumowaniami i rekomendacjami."
                },
                {
                    role: "user",
                    content: `Wygeneruj raport dla wydarzenia:
                    
                    Nazwa: ${event.name}
                    Typ: ${event.type}
                    Data: ${event.date}
                    Goście: ${event.guests}
                    Budżet: ${event.budget} PLN
                    Ocena: ${event.rating || 'brak'}/5
                    
                    Stwórz raport z sekcjami: podsumowanie, budżet, goście, sukcesy, rekomendacje na przyszłość.`
                }
            ];

            const response = await this.aiService.makeLlamaRequest(messages, {
                temperature: 0.3,
                max_tokens: 800
            });

            if (response.choices && response.choices[0]) {
                return {
                    event,
                    analytics,
                    aiInsights: response.choices[0].message.content,
                    generatedAt: new Date().toISOString()
                };
            }
        } catch (error) {
            this.config.error('AI report generation failed:', error);
        }

        // Fallback report
        return {
            event,
            analytics,
            aiInsights: 'Raport podstawowy - szczegółowa analiza AI obecnie niedostępna.',
            generatedAt: new Date().toISOString()
        };
    }

    showReportModal(report) {
        // Implementation for report modal
        this.showNotification('Raport został wygenerowany - szczegółowy widok w przygotowaniu', 'success');
        
        // In a real implementation, this would show a comprehensive report modal
        // with charts, insights, and export options
    }
}

// Export enhanced app class
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedEventPlannerApp;
} else {
    window.EnhancedEventPlannerApp = EnhancedEventPlannerApp;
}