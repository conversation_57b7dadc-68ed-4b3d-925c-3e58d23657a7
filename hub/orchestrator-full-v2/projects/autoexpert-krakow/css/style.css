* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-blue: #1e3a8a;
    --secondary-blue: #3b82f6;
    --accent-orange: #f97316;
    --light-orange: #fed7aa;
    --dark-gray: #1f2937;
    --medium-gray: #6b7280;
    --light-gray: #f3f4f6;
    --white: #ffffff;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--dark-gray);
    background-color: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header & Navigation */
.header {
    background: var(--white);
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-blue);
}

.nav-logo i {
    color: var(--accent-orange);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: var(--dark-gray);
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: var(--accent-orange);
}

.nav-emergency {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--accent-orange);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 600;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    padding: 8rem 0 4rem;
    margin-top: 80px;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-orange);
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
}

.cta-button {
    background: var(--accent-orange);
    color: var(--white);
    border: none;
    padding: 1rem 2rem;
    font-size: 1.125rem;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cta-button:hover {
    background: #ea580c;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.hero-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
}

/* Services Section */
.services {
    padding: 6rem 0;
    background: var(--light-gray);
}

.services h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 3rem;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.service-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--light-orange);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.service-icon i {
    font-size: 2rem;
    color: var(--accent-orange);
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.service-card p {
    color: var(--medium-gray);
    margin-bottom: 1.5rem;
}

.service-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--accent-orange);
}

/* Diagnosis Form */
.diagnosis-form {
    padding: 6rem 0;
    background: var(--white);
}

.diagnosis-form h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.diagnosis-form p {
    text-align: center;
    font-size: 1.125rem;
    color: var(--medium-gray);
    margin-bottom: 3rem;
}

.diagnosis-form-container {
    max-width: 600px;
    margin: 0 auto;
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 12px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-gray);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-blue);
}

.form-submit {
    width: 100%;
    background: var(--primary-blue);
    color: var(--white);
    border: none;
    padding: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.form-submit:hover {
    background: var(--secondary-blue);
}

/* Price Calculator */
.price-calculator {
    padding: 6rem 0;
    background: var(--light-gray);
}

.price-calculator h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 3rem;
}

.calculator-container {
    max-width: 800px;
    margin: 0 auto;
}

.calculator-form h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 2rem;
    text-align: center;
}

.service-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.service-option {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.service-option:hover {
    border-color: var(--secondary-blue);
    transform: translateY(-2px);
}

.service-option.selected {
    border-color: var(--accent-orange);
    background: var(--light-orange);
}

.service-option i {
    font-size: 2rem;
    color: var(--accent-orange);
    margin-bottom: 1rem;
}

.service-option span {
    display: block;
    font-weight: 600;
    color: var(--dark-gray);
    margin-bottom: 0.5rem;
}

.service-option .price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-blue);
}

.selected-services {
    background: var(--white);
    padding: 2rem;
    border-radius: 12px;
    margin-top: 2rem;
}

.selected-services h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.total-price {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 2px solid var(--light-gray);
    font-size: 1.5rem;
    color: var(--primary-blue);
}

/* Reviews */
.reviews {
    padding: 6rem 0;
    background: var(--white);
}

.reviews h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 3rem;
}

.reviews-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.review-card {
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.review-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: var(--secondary-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.author-info h4 {
    font-weight: 600;
    color: var(--dark-gray);
    margin-bottom: 0.25rem;
}

.review-stars {
    color: var(--accent-orange);
}

.review-source {
    background: var(--accent-orange);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.review-card p {
    color: var(--medium-gray);
    font-style: italic;
}

/* Team */
.team {
    padding: 6rem 0;
    background: var(--light-gray);
}

.team h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 3rem;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.team-member {
    background: var(--white);
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: var(--shadow);
}

.team-member img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1rem;
}

.team-member h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.team-member p {
    color: var(--medium-gray);
    margin-bottom: 1rem;
}

.certifications {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.cert {
    background: var(--light-orange);
    color: var(--accent-orange);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

/* Gallery */
.gallery {
    padding: 6rem 0;
    background: var(--white);
}

.gallery h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 3rem;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

@media (max-width: 768px) {
    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: var(--shadow);
}

.gallery-item img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.3s ease;
    display: block;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.9));
    color: var(--white);
    padding: 1.5rem 1rem 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay h4 {
    font-size: 1.125rem;
    font-weight: 600;
}

/* Contact */
.contact {
    padding: 6rem 0;
    background: var(--light-gray);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
}

.contact-item i {
    font-size: 1.5rem;
    color: var(--accent-orange);
    margin-top: 0.25rem;
}

.contact-item h4 {
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 0.25rem;
}

.contact-item p {
    color: var(--medium-gray);
}

.emergency-cta {
    margin-top: 2rem;
}

.emergency-button {
    background: var(--accent-orange);
    color: var(--white);
    border: none;
    padding: 1rem 2rem;
    font-size: 1.125rem;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.emergency-button:hover {
    background: #ea580c;
    transform: translateY(-2px);
}

.contact-map iframe {
    border-radius: 12px;
    box-shadow: var(--shadow);
}

/* Footer */
.footer {
    background: var(--dark-gray);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.footer-logo i {
    color: var(--accent-orange);
}

.footer-section h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--accent-orange);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--accent-orange);
}

.footer-section p {
    color: #d1d5db;
    margin-bottom: 0.5rem;
}

.footer-section i {
    color: var(--accent-orange);
    margin-right: 0.5rem;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 2rem;
    text-align: center;
    color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-menu {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero h1 {
        font-size: 2.5rem;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
    }
    
    .services-grid,
    .reviews-grid,
    .team-grid {
        grid-template-columns: 1fr;
    }
    
    .service-selector {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 6rem 0 2rem;
    }
    
    .hero h1 {
        font-size: 2rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .container {
        padding: 0 1rem;
    }
}