// Assessment Test Functionality
let currentQuestion = 1;
let score = 0;
const correctAnswers = ['a', 'b', 'a', 'c', 'c'];

function initializeAssessment() {
    const answerButtons = document.querySelectorAll('.answer-btn');
    
    answerButtons.forEach(button => {
        button.addEventListener('click', function() {
            const question = this.closest('.question');
            const questionId = question.id;
            const questionNumber = parseInt(questionId.split('-')[1]);
            
            // Remove selected class from all buttons in this question
            question.querySelectorAll('.answer-btn').forEach(btn => {
                btn.classList.remove('selected');
            });
            
            // Add selected class to clicked button
            this.classList.add('selected');
            
            // Check answer
            const selectedAnswer = this.dataset.answer;
            if (selectedAnswer === correctAnswers[questionNumber - 1]) {
                score++;
            }
            
            // Move to next question after a delay
            setTimeout(() => {
                showNextQuestion(questionNumber);
            }, 1000);
        });
    });
}

function showNextQuestion(currentQuestionNumber) {
    const currentQuestionEl = document.getElementById(`question-${currentQuestionNumber}`);
    currentQuestionEl.classList.add('hidden');
    
    if (currentQuestionNumber < 5) {
        const nextQuestionEl = document.getElementById(`question-${currentQuestionNumber + 1}`);
        nextQuestionEl.classList.remove('hidden');
    } else {
        showTestResult();
    }
}

function showTestResult() {
    const resultEl = document.getElementById('test-result');
    const levelEl = resultEl.querySelector('.result-level');
    const descriptionEl = resultEl.querySelector('.result-description');
    
    let level, description;
    
    if (score >= 4) {
        level = 'Zaawansowany (B2-C1)';
        description = 'Gratulacje! Masz solidne podstawy. Nasz kurs pomoże Ci osiągnąć poziom native speakera w biznesowym angielskim.';
    } else if (score >= 2) {
        level = 'Średniozaawansowany (A2-B1)';
        description = 'Dobry start! Znasz podstawy, ale potrzebujesz więcej praktyki w kontekście biznesowym. Nasz kurs jest idealny dla Ciebie.';
    } else {
        level = 'Początkujący (A1)';
        description = 'Nie martw się! Każdy kiedyś zaczynał. Nasz kurs został zaprojektowany tak, aby szybko i skutecznie podnieść Twój poziom.';
    }
    
    levelEl.textContent = level;
    descriptionEl.textContent = description;
    resultEl.classList.remove('hidden');
}

// Smooth scrolling for navigation links
function initializeSmoothScrolling() {
    const navLinks = document.querySelectorAll('.nav a, .btn-primary, .btn-hero');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href && href.startsWith('#')) {
                e.preventDefault();
                const targetId = href.substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                } else if (href === '#') {
                    // Scroll to trial section for "Zacznij za darmo" buttons
                    document.querySelector('.trial').scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    });
}

// Video preview functionality
function initializeVideoPreview() {
    const videoPlaceholder = document.querySelector('.video-placeholder');
    
    videoPlaceholder.addEventListener('click', function() {
        // Simulate video modal or redirect
        alert('Demo lekcji zostanie wkrótce udostępniona! Skontaktuj się z nami, aby umówić się na darmową lekcję próbną.');
    });
}

// Form handling
function initializeFormHandling() {
    const trialForm = document.getElementById('trial-form');
    
    trialForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const name = document.getElementById('name').value;
        const email = document.getElementById('email').value;
        const phone = document.getElementById('phone').value;
        const level = document.getElementById('level').value;
        
        // Validate form
        if (!name || !email || !phone || !level) {
            alert('Proszę wypełnić wszystkie pola.');
            return;
        }
        
        // Simulate form submission
        const submitButton = this.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        
        submitButton.textContent = 'Wysyłanie...';
        submitButton.disabled = true;
        
        setTimeout(() => {
            alert('Dziękujemy! Skontaktujemy się z Tobą w ciągu 24 godzin, aby umówić darmową lekcję próbną.');
            this.reset();
            submitButton.textContent = originalText;
            submitButton.disabled = false;
        }, 2000);
    });
}

// Pricing card interactions
function initializePricingCards() {
    const pricingButtons = document.querySelectorAll('.btn-pricing');
    
    pricingButtons.forEach(button => {
        button.addEventListener('click', function() {
            const card = this.closest('.pricing-card');
            const planName = card.querySelector('h4').textContent;
            
            if (planName === 'Enterprise') {
                // Scroll to contact form
                document.querySelector('.trial').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            } else {
                // Simulate plan selection
                alert(`Wybrałeś plan ${planName}. Przekierowanie do płatności...`);
            }
        });
    });
}

// Intersection Observer for animations
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.curriculum-item, .testimonial, .pricing-card');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Counter animation for hero stats
function initializeCounterAnimation() {
    const stats = document.querySelectorAll('.stat-number');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = entry.target;
                const text = target.textContent;
                
                // Extract number from text
                const match = text.match(/\d+/);
                if (match) {
                    const finalNumber = parseInt(match[0]);
                    animateCounter(target, finalNumber, text);
                }
                
                observer.unobserve(target);
            }
        });
    });
    
    stats.forEach(stat => observer.observe(stat));
}

function animateCounter(element, target, originalText) {
    let current = 0;
    const increment = target / 50;
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        
        const newText = originalText.replace(/\d+/, Math.floor(current));
        element.textContent = newText;
    }, 30);
}

// Mobile menu toggle (for responsive design)
function initializeMobileMenu() {
    // Add mobile menu button if needed
    const header = document.querySelector('.header .container');
    const nav = document.querySelector('.nav');
    
    if (window.innerWidth <= 768) {
        const menuButton = document.createElement('button');
        menuButton.innerHTML = '☰';
        menuButton.className = 'mobile-menu-btn';
        menuButton.style.cssText = `
            display: block;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #2563eb;
        `;
        
        header.appendChild(menuButton);
        
        menuButton.addEventListener('click', function() {
            nav.style.display = nav.style.display === 'flex' ? 'none' : 'flex';
            nav.style.position = 'absolute';
            nav.style.top = '100%';
            nav.style.left = '0';
            nav.style.right = '0';
            nav.style.background = 'white';
            nav.style.flexDirection = 'column';
            nav.style.padding = '1rem';
            nav.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
        });
    }
}

// Initialize all functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeAssessment();
    initializeSmoothScrolling();
    initializeVideoPreview();
    initializeFormHandling();
    initializePricingCards();
    initializeScrollAnimations();
    initializeCounterAnimation();
    initializeMobileMenu();
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            const nav = document.querySelector('.nav');
            nav.style.display = 'flex';
            nav.style.position = 'static';
            nav.style.flexDirection = 'row';
            nav.style.padding = '0';
            nav.style.boxShadow = 'none';
        }
    });
});

// Add loading animation
window.addEventListener('load', function() {
    document.body.style.opacity = '1';
    document.body.style.transition = 'opacity 0.5s ease';
});

// Add smooth loading
document.body.style.opacity = '0';