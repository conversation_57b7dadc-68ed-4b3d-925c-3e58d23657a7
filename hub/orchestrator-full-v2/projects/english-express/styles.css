* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: #fff;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo h1 {
    color: #2563eb;
    font-size: 1.8rem;
    font-weight: 700;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav a {
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    transition: color 0.3s;
}

.nav a:hover {
    color: #2563eb;
}

.btn-primary {
    background: #2563eb;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 0.9rem;
}

.btn-primary:hover {
    background: #1d4ed8;
    transform: translateY(-1px);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 4rem 0;
}

.hero .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.hero-content h2 {
    font-size: 3rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-content p {
    font-size: 1.2rem;
    color: #64748b;
    margin-bottom: 2rem;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #2563eb;
}

.stat-text {
    font-size: 0.9rem;
    color: #64748b;
}

.btn-hero {
    background: #2563eb;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-hero:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
}

.hero-image img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    border-radius: 1rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* Assessment Section */
.assessment {
    padding: 4rem 0;
    background: #fff;
}

.assessment h3 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 1rem;
    color: #1e293b;
}

.assessment p {
    text-align: center;
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 3rem;
}

.assessment-test {
    max-width: 600px;
    margin: 0 auto;
    background: #f8fafc;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.question h4 {
    color: #2563eb;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.question p {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    color: #1e293b;
    text-align: left;
}

.answers {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.answer-btn {
    background: #fff;
    border: 2px solid #e2e8f0;
    padding: 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 1rem;
}

.answer-btn:hover {
    border-color: #2563eb;
    background: #eff6ff;
}

.answer-btn.selected {
    background: #2563eb;
    color: white;
    border-color: #2563eb;
}

.hidden {
    display: none;
}

.test-result {
    text-align: center;
}

.result-level {
    display: inline-block;
    background: #2563eb;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.result-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    color: #64748b;
}

/* Curriculum Section */
.curriculum {
    padding: 4rem 0;
    background: #f8fafc;
}

.curriculum h3 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #1e293b;
}

.curriculum-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.curriculum-item {
    background: #fff;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    text-align: center;
    transition: transform 0.3s;
}

.curriculum-item:hover {
    transform: translateY(-5px);
}

.curriculum-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.curriculum-item h4 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #1e293b;
}

.curriculum-item p {
    color: #64748b;
    line-height: 1.6;
}

/* Testimonials Section */
.testimonials {
    padding: 4rem 0;
    background: #fff;
}

.testimonials h3 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #1e293b;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.testimonial {
    display: flex;
    gap: 1rem;
    padding: 2rem;
    background: #f8fafc;
    border-radius: 1rem;
    border-left: 4px solid #2563eb;
}

.testimonial img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.testimonial-content p {
    font-style: italic;
    margin-bottom: 1rem;
    color: #475569;
    line-height: 1.6;
}

.testimonial-author strong {
    display: block;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.testimonial-author span {
    color: #64748b;
    font-size: 0.9rem;
}

/* Preview Section */
.preview {
    padding: 4rem 0;
    background: #f8fafc;
}

.preview h3 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #1e293b;
}

.preview-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.video-placeholder {
    background: #1e293b;
    aspect-ratio: 16/9;
    border-radius: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: transform 0.3s;
}

.video-placeholder:hover {
    transform: scale(1.02);
}

.play-button {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.preview-features {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.feature {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.feature-icon {
    font-size: 2rem;
    width: 60px;
    text-align: center;
}

.feature h4 {
    margin-bottom: 0.5rem;
    color: #1e293b;
}

.feature p {
    color: #64748b;
}

/* Certification Section */
.certification {
    padding: 4rem 0;
    background: #fff;
}

.certification-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.certification h3 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: #1e293b;
}

.guarantee-item {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.guarantee-icon {
    font-size: 2rem;
    width: 60px;
    text-align: center;
}

.guarantee-item h4 {
    margin-bottom: 0.5rem;
    color: #1e293b;
}

.guarantee-item p {
    color: #64748b;
}

.certification-image img {
    width: 100%;
    border-radius: 1rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* Pricing Section */
.pricing {
    padding: 4rem 0;
    background: #f8fafc;
}

.pricing h3 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #1e293b;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: #fff;
    padding: 2.5rem 2rem;
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    position: relative;
    text-align: center;
    transition: transform 0.3s;
}

.pricing-card:hover {
    transform: translateY(-5px);
}

.pricing-card.featured {
    border: 2px solid #2563eb;
    transform: scale(1.05);
}

.popular-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: #2563eb;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.pricing-card h4 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #1e293b;
}

.price {
    margin-bottom: 2rem;
}

.price-amount {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2563eb;
}

.price-period {
    color: #64748b;
    font-size: 1rem;
}

.features-list {
    list-style: none;
    margin-bottom: 2rem;
}

.features-list li {
    padding: 0.5rem 0;
    color: #475569;
    position: relative;
    padding-left: 1.5rem;
}

.features-list li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

.btn-pricing {
    background: #2563eb;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    width: 100%;
}

.btn-pricing:hover {
    background: #1d4ed8;
}

/* Trial Section */
.trial {
    padding: 4rem 0;
    background: #1e293b;
    color: white;
}

.trial-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.trial h3 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.trial p {
    font-size: 1.1rem;
    margin-bottom: 3rem;
    color: #cbd5e1;
}

.trial-form {
    display: grid;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group input,
.form-group select {
    padding: 1rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    background: #fff;
}

.form-group input:focus,
.form-group select:focus {
    outline: 2px solid #2563eb;
}

/* Footer */
.footer {
    background: #0f172a;
    color: #cbd5e1;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    color: #fff;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.footer-section p {
    margin-bottom: 0.5rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: #cbd5e1;
    text-decoration: none;
    transition: color 0.3s;
}

.social-links a:hover {
    color: #2563eb;
}

.footer-bottom {
    border-top: 1px solid #334155;
    padding-top: 1rem;
    text-align: center;
    color: #64748b;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero .container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .preview-content,
    .certification-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .hero-content h2 {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .header .container {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .nav {
        order: 3;
        width: 100%;
        justify-content: center;
        margin-top: 1rem;
    }
    
    .hero .container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .hero-content h2 {
        font-size: 2rem;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .pricing-card.featured {
        transform: none;
    }
    
    .answers {
        grid-template-columns: 1fr;
    }
    
    .assessment-test {
        padding: 1.5rem;
    }
    
    .curriculum-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .hero-image img {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .header .container {
        flex-direction: column;
        gap: 1rem;
        padding: 0.75rem 15px;
    }
    
    .nav {
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .hero-content h2 {
        font-size: 1.8rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .curriculum-grid,
    .pricing-grid {
        grid-template-columns: 1fr;
    }
    
    .assessment h3,
    .curriculum h3,
    .testimonials h3,
    .pricing h3,
    .preview h3,
    .certification h3 {
        font-size: 2rem;
    }
    
    .assessment-test {
        padding: 1rem;
    }
    
    .question h4 {
        font-size: 1rem;
    }
    
    .question p {
        font-size: 1.1rem;
    }
    
    .hero-image img {
        height: 250px;
    }
    
    .btn-primary,
    .btn-hero,
    .btn-pricing {
        width: 100%;
        text-align: center;
    }
    
    .feature {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .testimonial {
        flex-direction: column;
        text-align: center;
    }
    
    .testimonial img {
        align-self: center;
    }
}

@media (max-width: 320px) {
    .hero-content h2 {
        font-size: 1.5rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .assessment-test,
    .curriculum-item,
    .pricing-card {
        padding: 0.75rem;
    }
}