<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate="title">GastroAI.pl - Inteligentny System Zarzdzania Restauracj</title>
    <meta name="description" content="Zwiksz obroty restauracji o 40% dziki AI - kompleksowy system zarzdzania restauracj z analityk, optymalizacj menu i automatyzacj proces�w.">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <h2>GastroAI</h2>
            <p>Aadowanie inteligentnego systemu...</p>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="logo">
                <i class="fas fa-utensils"></i>
                <span>GastroAI</span>
            </div>
            
            <nav class="nav-menu">
                <button class="nav-item active" data-section="dashboard">
                    <i class="fas fa-chart-pie"></i>
                    <span data-translate="dashboard">Panel GB�wny</span>
                </button>
                <button class="nav-item" data-section="menu-optimization">
                    <i class="fas fa-chart-line"></i>
                    <span data-translate="menuOptimization">Optymalizacja Menu</span>
                </button>
                <button class="nav-item" data-section="table-management">
                    <i class="fas fa-chair"></i>
                    <span data-translate="tableManagement">Zarzdzanie StoBami</span>
                </button>
                <button class="nav-item" data-section="analytics">
                    <i class="fas fa-analytics"></i>
                    <span data-translate="analytics">Analityka</span>
                </button>
            </nav>

            <div class="header-actions">
                <select class="language-selector" onchange="setLanguage(this.value)">
                    <option value="pl"><�<� Polski</option>
                    <option value="es"><�<� Espa�ol</option>
                    <option value="en"><�<� English</option>
                </select>
                
                <button class="notification-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>
                
                <div class="user-menu">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23667eea'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-family='Arial' font-size='16' font-weight='bold'%3EAI%3C/text%3E%3C/svg%3E" alt="User Avatar" class="user-avatar">
                    <span class="user-name">Admin</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span data-translate="tagline">Zwiksz obroty restauracji o 40% dziki AI</span>
                    <span class="hero-accent">=�</span>
                </h1>
                <p class="hero-subtitle">Zrednio 40% wzrost przychod�w w 3 miesice</p>
                <div class="hero-stats">
                    <div class="hero-stat">
                        <div class="stat-number" data-count="847">0</div>
                        <div class="stat-label">Restauracji korzysta</div>
                    </div>
                    <div class="hero-stat">
                        <div class="stat-number" data-count="40">0</div>
                        <div class="stat-label">% [redni wzrost</div>
                    </div>
                    <div class="hero-stat">
                        <div class="stat-number" data-count="98">0</div>
                        <div class="stat-label">% zadowolenia</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Dashboard Section -->
        <section id="dashboard" class="section active">
            <div class="section-header">
                <h2 data-translate="dashboard">Panel GB�wny</h2>
                <div class="section-actions">
                    <button class="btn btn-secondary" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i>
                        <span data-translate="refresh">Od[wie|</span>
                    </button>
                    <button class="btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-download"></i>
                        <span data-translate="generateReport">Generuj raport</span>
                    </button>
                </div>
            </div>

            <!-- Key Metrics -->
            <div class="metrics-grid">
                <div class="metric-card revenue">
                    <div class="metric-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="metric-content">
                        <h3 data-translate="todayRevenue">Przychody dzisiaj</h3>
                        <div class="metric-value">
                            <span class="currency">PLN</span>
                            <span class="amount" data-count="15847">0</span>
                        </div>
                        <div class="metric-trend positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12.5%</span>
                        </div>
                    </div>
                </div>

                <div class="metric-card orders">
                    <div class="metric-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="metric-content">
                        <h3 data-translate="avgOrderValue">Zrednia warto[ zam�wienia</h3>
                        <div class="metric-value">
                            <span class="currency">PLN</span>
                            <span class="amount" data-count="87">0</span>
                        </div>
                        <div class="metric-trend positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+8.3%</span>
                        </div>
                    </div>
                </div>

                <div class="metric-card occupancy">
                    <div class="metric-icon">
                        <i class="fas fa-chair"></i>
                    </div>
                    <div class="metric-content">
                        <h3 data-translate="tableOccupancy">ObBo|enie stoB�w</h3>
                        <div class="metric-value">
                            <span class="amount" data-count="78">0</span>
                            <span class="unit">%</span>
                        </div>
                        <div class="metric-progress">
                            <div class="progress-bar" style="width: 78%"></div>
                        </div>
                    </div>
                </div>

                <div class="metric-card satisfaction">
                    <div class="metric-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="metric-content">
                        <h3 data-translate="customerSatisfaction">Zadowolenie klient�w</h3>
                        <div class="metric-value">
                            <span class="amount" data-count="94">0</span>
                            <span class="unit">%</span>
                        </div>
                        <div class="rating-stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts and Analytics -->
            <div class="dashboard-grid">
                <div class="dashboard-card chart-card">
                    <div class="card-header">
                        <h3>Przychody w czasie rzeczywistym</h3>
                        <div class="card-actions">
                            <button class="btn-icon" onclick="toggleChartView('revenue')">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <canvas id="revenueChart" class="chart"></canvas>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Popularne dania dzisiaj</h3>
                        <div class="time-filter">
                            <button class="time-btn active">Dzisiaj</button>
                            <button class="time-btn">TydzieD</button>
                            <button class="time-btn">Miesic</button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="popular-dishes">
                            <div class="dish-item">
                                <div class="dish-info">
                                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48' viewBox='0 0 48 48'%3E%3Ccircle cx='24' cy='24' r='24' fill='%23ff6b6b'/%3E%3Ctext x='24' y='30' text-anchor='middle' fill='white' font-family='Arial' font-size='14'%3E<U%3C/text%3E%3C/svg%3E" alt="Pizza Margherita" class="dish-image">
                                    <div class="dish-details">
                                        <h4>Pizza Margherita</h4>
                                        <p>47 zam�wieD</p>
                                    </div>
                                </div>
                                <div class="dish-metrics">
                                    <span class="revenue">PLN 1,645</span>
                                    <span class="trend positive">+15%</span>
                                </div>
                            </div>
                            
                            <div class="dish-item">
                                <div class="dish-info">
                                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48' viewBox='0 0 48 48'%3E%3Ccircle cx='24' cy='24' r='24' fill='%234ecdc4'/%3E%3Ctext x='24' y='30' text-anchor='middle' fill='white' font-family='Arial' font-size='14'%3E>W%3C/text%3E%3C/svg%3E" alt="Caesar Salad" class="dish-image">
                                    <div class="dish-details">
                                        <h4>SaBatka Caesar</h4>
                                        <p>34 zam�wieD</p>
                                    </div>
                                </div>
                                <div class="dish-metrics">
                                    <span class="revenue">PLN 1,020</span>
                                    <span class="trend positive">+8%</span>
                                </div>
                            </div>
                            
                            <div class="dish-item">
                                <div class="dish-info">
                                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48' viewBox='0 0 48 48'%3E%3Ccircle cx='24' cy='24' r='24' fill='%23ffd93d'/%3E%3Ctext x='24' y='30' text-anchor='middle' fill='white' font-family='Arial' font-size='14'%3E<]%3C/text%3E%3C/svg%3E" alt="Spaghetti Carbonara" class="dish-image">
                                    <div class="dish-details">
                                        <h4>Spaghetti Carbonara</h4>
                                        <p>29 zam�wieD</p>
                                    </div>
                                </div>
                                <div class="dish-metrics">
                                    <span class="revenue">PLN 899</span>
                                    <span class="trend negative">-3%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-card ai-insights">
                    <div class="card-header">
                        <h3>> Rekomendacje AI</h3>
                        <div class="ai-status">
                            <span class="status-dot active"></span>
                            <span>Aktywne</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="ai-recommendations">
                            <div class="recommendation high-priority">
                                <div class="rec-icon">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <div class="rec-content">
                                    <h4>Promocja ZBotej Godziny</h4>
                                    <p>17:30-18:30: Zaoferuj 15% zni|ki na makarony - zwikszy to sprzeda| o ~23%</p>
                                    <div class="rec-impact">Potencjalny zysk: +PLN 340</div>
                                </div>
                                <button class="btn-small btn-primary">Zastosuj</button>
                            </div>
                            
                            <div class="recommendation medium-priority">
                                <div class="rec-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="rec-content">
                                    <h4>Optymalizacja menu</h4>
                                    <p>Przenie[ "Risotto z grzybami" wy|ej w menu - zwikszy to zam�wienia o 18%</p>
                                    <div class="rec-impact">Potencjalny zysk: +PLN 180/dzieD</div>
                                </div>
                                <button class="btn-small btn-secondary">Zobacz</button>
                            </div>
                            
                            <div class="recommendation">
                                <div class="rec-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="rec-content">
                                    <h4>Personalizacja dla staBych klient�w</h4>
                                    <p>3 staBych klient�w ma dzi[ urodziny - wy[lij im specjaln ofert</p>
                                    <div class="rec-impact">Potencjalny zysk: +PLN 120</div>
                                </div>
                                <button class="btn-small btn-secondary">Wy[lij</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Menu Optimization Section -->
        <section id="menu-optimization" class="section">
            <div class="section-header">
                <h2 data-translate="menuOptimization">Optymalizacja Menu</h2>
                <button class="btn btn-primary" onclick="optimizeMenu()">
                    <i class="fas fa-magic"></i>
                    <span data-translate="optimizeMenu">Optymalizuj Menu</span>
                </button>
            </div>

            <div class="optimization-grid">
                <div class="optimization-card">
                    <div class="card-header">
                        <h3 data-translate="profitMarginAnalysis">Analiza mar|y zysku</h3>
                    </div>
                    <div class="card-content">
                        <canvas id="profitMarginChart" class="chart"></canvas>
                    </div>
                </div>

                <div class="optimization-card">
                    <div class="card-header">
                        <h3>Heatmapa popularno[ci</h3>
                    </div>
                    <div class="card-content">
                        <div class="menu-heatmap">
                            <div class="heatmap-item high-heat" data-popularity="94">
                                <span class="item-name">Pizza Margherita</span>
                                <span class="popularity-score">94%</span>
                            </div>
                            <div class="heatmap-item medium-heat" data-popularity="78">
                                <span class="item-name">SaBatka Caesar</span>
                                <span class="popularity-score">78%</span>
                            </div>
                            <div class="heatmap-item medium-heat" data-popularity="65">
                                <span class="item-name">Spaghetti Carbonara</span>
                                <span class="popularity-score">65%</span>
                            </div>
                            <div class="heatmap-item low-heat" data-popularity="42">
                                <span class="item-name">Risotto z grzybami</span>
                                <span class="popularity-score">42%</span>
                            </div>
                            <div class="heatmap-item low-heat" data-popularity="31">
                                <span class="item-name">Aoso[ grillowany</span>
                                <span class="popularity-score">31%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Table Management Section -->
        <section id="table-management" class="section">
            <div class="section-header">
                <h2 data-translate="tableManagement">Zarzdzanie StoBami</h2>
                <div class="table-controls">
                    <button class="btn btn-secondary" onclick="toggleFloorPlan()">
                        <i class="fas fa-th"></i>
                        <span data-translate="floorPlan">Plan sali</span>
                    </button>
                    <button class="btn btn-primary" onclick="addReservation()">
                        <i class="fas fa-plus"></i>
                        <span data-translate="addNew">Dodaj rezerwacj</span>
                    </button>
                </div>
            </div>

            <div class="table-management-grid">
                <div class="table-card floor-plan-card">
                    <div class="card-header">
                        <h3 data-translate="floorPlan">Plan sali</h3>
                        <div class="legend">
                            <span class="legend-item">
                                <span class="status-dot available"></span>
                                <span data-translate="available">Dostpny</span>
                            </span>
                            <span class="legend-item">
                                <span class="status-dot occupied"></span>
                                <span data-translate="occupied">Zajty</span>
                            </span>
                            <span class="legend-item">
                                <span class="status-dot reserved"></span>
                                <span data-translate="reserved">Zarezerwowany</span>
                            </span>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="floor-plan">
                            <div class="table table-2 available" data-table="1" onclick="selectTable(1)">
                                <span class="table-number">1</span>
                                <span class="table-capacity">2</span>
                            </div>
                            <div class="table table-4 occupied" data-table="2" onclick="selectTable(2)">
                                <span class="table-number">2</span>
                                <span class="table-capacity">4</span>
                            </div>
                            <div class="table table-6 reserved" data-table="3" onclick="selectTable(3)">
                                <span class="table-number">3</span>
                                <span class="table-capacity">6</span>
                            </div>
                            <div class="table table-4 available" data-table="4" onclick="selectTable(4)">
                                <span class="table-number">4</span>
                                <span class="table-capacity">4</span>
                            </div>
                            <div class="table table-2 occupied" data-table="5" onclick="selectTable(5)">
                                <span class="table-number">5</span>
                                <span class="table-capacity">2</span>
                            </div>
                            <div class="table table-8 available" data-table="6" onclick="selectTable(6)">
                                <span class="table-number">6</span>
                                <span class="table-capacity">8</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-card reservations-card">
                    <div class="card-header">
                        <h3 data-translate="reservations">Rezerwacje</h3>
                        <div class="reservation-filters">
                            <button class="filter-btn active">Dzisiaj</button>
                            <button class="filter-btn">Jutro</button>
                            <button class="filter-btn">TydzieD</button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="reservations-list">
                            <div class="reservation-item">
                                <div class="reservation-time">18:30</div>
                                <div class="reservation-details">
                                    <h4>Jan Kowalski</h4>
                                    <p>St�B 3 " 4 osoby</p>
                                    <span class="reservation-note">Urodziny</span>
                                </div>
                                <div class="reservation-actions">
                                    <button class="btn-icon" onclick="editReservation(1)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon" onclick="confirmReservation(1)">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="reservation-item">
                                <div class="reservation-time">19:15</div>
                                <div class="reservation-details">
                                    <h4>Anna Nowak</h4>
                                    <p>St�B 6 " 2 osoby</p>
                                    <span class="reservation-note">Bez glutenu</span>
                                </div>
                                <div class="reservation-actions">
                                    <button class="btn-icon" onclick="editReservation(2)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon" onclick="confirmReservation(2)">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="reservation-item">
                                <div class="reservation-time">20:00</div>
                                <div class="reservation-details">
                                    <h4>MichaB Wi[niewski</h4>
                                    <p>St�B 2 " 6 os�b</p>
                                    <span class="reservation-note">Biznes</span>
                                </div>
                                <div class="reservation-actions">
                                    <button class="btn-icon" onclick="editReservation(3)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon" onclick="confirmReservation(3)">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Analytics Section -->
        <section id="analytics" class="section">
            <div class="section-header">
                <h2 data-translate="analytics">Analityka</h2>
                <div class="analytics-controls">
                    <select class="time-range-selector">
                        <option value="today">Dzisiaj</option>
                        <option value="week">Ostatni tydzieD</option>
                        <option value="month" selected>Ostatni miesic</option>
                        <option value="quarter">Ostatni kwartaB</option>
                    </select>
                    <button class="btn btn-primary" onclick="exportAnalytics()">
                        <i class="fas fa-download"></i>
                        <span data-translate="exportData">Eksportuj dane</span>
                    </button>
                </div>
            </div>

            <div class="analytics-grid">
                <div class="analytics-card revenue-forecast">
                    <div class="card-header">
                        <h3 data-translate="revenueForecasting">Prognoza przychod�w</h3>
                        <div class="forecast-accuracy">
                            <span class="accuracy-score">94.2%</span>
                            <span class="accuracy-label">dokBadno[</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <canvas id="forecastChart" class="chart"></canvas>
                        <div class="forecast-insights">
                            <div class="insight">
                                <i class="fas fa-cloud-rain"></i>
                                <span>Pogoda: -8% w pitek</span>
                            </div>
                            <div class="insight">
                                <i class="fas fa-calendar-alt"></i>
                                <span>Weekend: +15% oczekiwane</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="analytics-card customer-behavior">
                    <div class="card-header">
                        <h3 data-translate="customerBehavior">Zachowania klient�w</h3>
                    </div>
                    <div class="card-content">
                        <div class="behavior-metrics">
                            <div class="behavior-metric">
                                <div class="metric-label">Zredni czas wizyty</div>
                                <div class="metric-value">1h 23min</div>
                                <div class="metric-change positive">� 8min</div>
                            </div>
                            <div class="behavior-metric">
                                <div class="metric-label">Powracajcy klienci</div>
                                <div class="metric-value">67%</div>
                                <div class="metric-change positive">� 12%</div>
                            </div>
                            <div class="behavior-metric">
                                <div class="metric-label">NPS Score</div>
                                <div class="metric-value">8.4</div>
                                <div class="metric-change positive">� 0.3</div>
                            </div>
                        </div>
                        <canvas id="behaviorChart" class="chart"></canvas>
                    </div>
                </div>

                <div class="analytics-card peak-hours">
                    <div class="card-header">
                        <h3 data-translate="peakHours">Godziny szczytu</h3>
                    </div>
                    <div class="card-content">
                        <div class="peak-hours-chart">
                            <div class="hour-bar" style="height: 20%" data-hour="12">
                                <span class="hour-label">12</span>
                                <span class="hour-value">20%</span>
                            </div>
                            <div class="hour-bar" style="height: 45%" data-hour="13">
                                <span class="hour-label">13</span>
                                <span class="hour-value">45%</span>
                            </div>
                            <div class="hour-bar" style="height: 78%" data-hour="14">
                                <span class="hour-label">14</span>
                                <span class="hour-value">78%</span>
                            </div>
                            <div class="hour-bar" style="height: 35%" data-hour="15">
                                <span class="hour-label">15</span>
                                <span class="hour-value">35%</span>
                            </div>
                            <div class="hour-bar" style="height: 55%" data-hour="18">
                                <span class="hour-label">18</span>
                                <span class="hour-value">55%</span>
                            </div>
                            <div class="hour-bar" style="height: 92%" data-hour="19">
                                <span class="hour-label">19</span>
                                <span class="hour-value">92%</span>
                            </div>
                            <div class="hour-bar" style="height: 88%" data-hour="20">
                                <span class="hour-label">20</span>
                                <span class="hour-value">88%</span>
                            </div>
                            <div class="hour-bar" style="height: 65%" data-hour="21">
                                <span class="hour-label">21</span>
                                <span class="hour-value">65%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- AI Chatbot -->
    <div class="chatbot-container" id="chatbot">
        <div class="chatbot-header">
            <div class="chatbot-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="chatbot-info">
                <h3>GastroAI Assistant</h3>
                <span class="status online">Online</span>
            </div>
            <button class="chatbot-toggle" onclick="toggleChatbot()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="chatbot-messages" id="chatbot-messages">
            <div class="message bot-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <p>Cze[! Jestem GastroAI Assistant. Jak mog Ci pom�c w zarzdzaniu restauracj?</p>
                    <div class="quick-actions">
                        <button class="quick-action" onclick="chatbotAction('reservations')">
                            =� Rezerwacje
                        </button>
                        <button class="quick-action" onclick="chatbotAction('menu')">
                            <} Menu
                        </button>
                        <button class="quick-action" onclick="chatbotAction('analytics')">
                            =� Analityka
                        </button>
                    </div>
                    <span class="message-time">Teraz</span>
                </div>
            </div>
        </div>
        <div class="chatbot-input">
            <input type="text" placeholder="Wpisz wiadomo[..." id="chatbot-input-field" onkeypress="handleChatbotEnter(event)">
            <button onclick="sendChatbotMessage()">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>

    <!-- Chatbot Toggle Button -->
    <button class="chatbot-fab" onclick="toggleChatbot()">
        <div class="fab-icon">
            <i class="fas fa-robot"></i>
        </div>
        <div class="fab-notification">3</div>
    </button>

    <!-- QR Menu Modal -->
    <div class="qr-menu-modal" id="qr-menu-modal">
        <div class="modal-overlay" onclick="closeQRMenu()"></div>
        <div class="qr-menu-container">
            <div class="qr-menu-header">
                <h2>🍽️ Menu Cyfrowe GastroAI</h2>
                <button class="close-btn" onclick="closeQRMenu()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="qr-menu-content">
                <div class="qr-code-section">
                    <div class="qr-code">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='200' viewBox='0 0 200 200'%3E%3Crect width='200' height='200' fill='%23ffffff'/%3E%3Crect x='0' y='0' width='40' height='40' fill='%23000000'/%3E%3Crect x='40' y='0' width='40' height='40' fill='%23ffffff'/%3E%3Crect x='80' y='0' width='40' height='40' fill='%23000000'/%3E%3Crect x='120' y='0' width='40' height='40' fill='%23ffffff'/%3E%3Crect x='160' y='0' width='40' height='40' fill='%23000000'/%3E%3Crect x='0' y='40' width='40' height='40' fill='%23ffffff'/%3E%3Crect x='40' y='40' width='40' height='40' fill='%23000000'/%3E%3Crect x='80' y='40' width='40' height='40' fill='%23ffffff'/%3E%3Crect x='120' y='40' width='40' height='40' fill='%23000000'/%3E%3Crect x='160' y='40' width='40' height='40' fill='%23ffffff'/%3E%3Crect x='0' y='80' width='40' height='40' fill='%23000000'/%3E%3Crect x='40' y='80' width='40' height='40' fill='%23ffffff'/%3E%3Crect x='80' y='80' width='40' height='40' fill='%23000000'/%3E%3Crect x='120' y='80' width='40' height='40' fill='%23ffffff'/%3E%3Crect x='160' y='80' width='40' height='40' fill='%23000000'/%3E%3Crect x='0' y='120' width='40' height='40' fill='%23ffffff'/%3E%3Crect x='40' y='120' width='40' height='40' fill='%23000000'/%3E%3Crect x='80' y='120' width='40' height='40' fill='%23ffffff'/%3E%3Crect x='120' y='120' width='40' height='40' fill='%23000000'/%3E%3Crect x='160' y='120' width='40' height='40' fill='%23ffffff'/%3E%3Crect x='0' y='160' width='40' height='40' fill='%23000000'/%3E%3Crect x='40' y='160' width='40' height='40' fill='%23ffffff'/%3E%3Crect x='80' y='160' width='40' height='40' fill='%23000000'/%3E%3Crect x='120' y='160' width='40' height='40' fill='%23ffffff'/%3E%3Crect x='160' y='160' width='40' height='40' fill='%23000000'/%3E%3C/svg%3E" alt="QR Code Menu">
                    </div>
                    <p class="qr-instruction">Zeskanuj kod QR, aby otworzyć menu na telefonie</p>
                    <div class="qr-features">
                        <div class="feature">
                            <i class="fas fa-mobile-alt"></i>
                            <span>Mobilne zamówienia</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-clock"></i>
                            <span>Szacowany czas oczekiwania</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-calculator"></i>
                            <span>Podział rachunku</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-leaf"></i>
                            <span>Filtry dietetyczne</span>
                        </div>
                    </div>
                </div>
                
                <div class="digital-menu-preview">
                    <h3>Przykład menu cyfrowego</h3>
                    <div class="menu-categories">
                        <div class="menu-category">
                            <h4>🍕 Pizze</h4>
                            <div class="menu-items">
                                <div class="menu-item">
                                    <div class="item-info">
                                        <h5>Pizza Margherita</h5>
                                        <p>Sos pomidorowy, mozzarella, bazylia</p>
                                        <div class="item-tags">
                                            <span class="tag vegetarian">Wegetariańska</span>
                                            <span class="tag popular">Popularna</span>
                                        </div>
                                    </div>
                                    <div class="item-price">35 PLN</div>
                                </div>
                                
                                <div class="menu-item">
                                    <div class="item-info">
                                        <h5>Pizza Pepperoni</h5>
                                        <p>Sos pomidorowy, mozzarella, pepperoni</p>
                                        <div class="item-tags">
                                            <span class="tag spicy">Ostra</span>
                                        </div>
                                    </div>
                                    <div class="item-price">42 PLN</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="menu-category">
                            <h4>🥗 Sałatki</h4>
                            <div class="menu-items">
                                <div class="menu-item">
                                    <div class="item-info">
                                        <h5>Sałatka Caesar</h5>
                                        <p>Sałata rzymska, kurczak, parmezan, croutony</p>
                                        <div class="item-tags">
                                            <span class="tag protein">Wysokobiałkowa</span>
                                        </div>
                                    </div>
                                    <div class="item-price">28 PLN</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="menu-footer">
                        <button class="btn btn-primary" onclick="openFullMenu()">
                            <i class="fas fa-external-link-alt"></i>
                            Otwórz pełne menu
                        </button>
                        <button class="btn btn-secondary" onclick="generateQRCode()">
                            <i class="fas fa-qrcode"></i>
                            Generuj nowy kod QR
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- QR Menu FAB -->
    <button class="qr-menu-fab" onclick="showQRMenu()" title="Menu cyfrowe QR">
        <div class="fab-icon">
            <i class="fas fa-qrcode"></i>
        </div>
    </button>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="translations.js"></script>
    <script src="script.js"></script>
</body>
</html>