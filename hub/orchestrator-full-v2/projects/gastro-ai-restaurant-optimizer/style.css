/* ===== CSS VARIABLES ===== */
:root {
    /* Primary Colors */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    /* Glass Colors */
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    
    /* Dark Glass */
    --dark-glass-bg: rgba(0, 0, 0, 0.25);
    --dark-glass-border: rgba(255, 255, 255, 0.18);
    
    /* Text Colors */
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --text-white: #ffffff;
    
    /* Background */
    --bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --bg-secondary: #f7fafc;
    --bg-card: rgba(255, 255, 255, 0.95);
    
    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.25);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 20px;
    --radius-xl: 30px;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-base: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ===== RESET & BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

.loading-spinner {
    text-align: center;
    color: var(--text-white);
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.loading-spinner p {
    opacity: 0.8;
    font-size: 1rem;
}

/* ===== HEADER ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    backdrop-filter: blur(20px);
    background: var(--glass-bg);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    height: 70px;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--text-white);
}

.logo i {
    font-size: 2rem;
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    gap: 0.5rem;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: transparent;
    border: none;
    border-radius: var(--radius-md);
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: var(--transition-base);
    font-weight: 500;
}

.nav-item:hover,
.nav-item.active {
    background: var(--glass-bg);
    color: var(--text-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.nav-item i {
    font-size: 1.1rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.language-selector {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-sm);
    padding: 0.5rem 0.75rem;
    color: var(--text-white);
    cursor: pointer;
    transition: var(--transition-base);
}

.language-selector:hover {
    background: var(--dark-glass-bg);
    transform: translateY(-1px);
}

.notification-btn {
    position: relative;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    cursor: pointer;
    transition: var(--transition-base);
}

.notification-btn:hover {
    background: var(--dark-glass-bg);
    transform: translateY(-2px);
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--secondary-gradient);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-base);
    color: var(--text-white);
}

.user-menu:hover {
    background: var(--dark-glass-bg);
    transform: translateY(-1px);
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 2px solid var(--glass-border);
}

.user-name {
    font-weight: 600;
}

/* ===== MAIN CONTENT ===== */
.main-content {
    margin-top: 70px;
    padding: 2rem;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 90px;
}

/* ===== HERO SECTION ===== */
.hero-section {
    text-align: center;
    padding: 3rem 0;
    margin-bottom: 3rem;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: var(--text-white);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    line-height: 1.2;
}

.hero-accent {
    font-size: 3rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
}

.hero-stat {
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--text-white);
    display: block;
    line-height: 1;
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

/* ===== SECTIONS ===== */
.section {
    display: none;
    animation: fadeInUp 0.6s ease;
}

.section.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.section-header h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-white);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-actions {
    display: flex;
    gap: 1rem;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: var(--transition-base);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--text-white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-white);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--dark-glass-bg);
    transform: translateY(-2px);
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

.btn-icon {
    background: none;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    transition: var(--transition-base);
}

.btn-icon:hover {
    background: var(--glass-bg);
    color: var(--text-primary);
    transform: scale(1.1);
}

/* ===== METRICS GRID ===== */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
    transition: var(--transition-base);
    box-shadow: var(--shadow-md);
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.metric-card.revenue::before {
    background: var(--success-gradient);
}

.metric-card.orders::before {
    background: var(--secondary-gradient);
}

.metric-card.occupancy::before {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.metric-card.satisfaction::before {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.metric-card {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.metric-icon {
    background: var(--glass-bg);
    border-radius: var(--radius-md);
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--text-white);
    flex-shrink: 0;
}

.metric-content {
    flex: 1;
}

.metric-content h3 {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.metric-value {
    display: flex;
    align-items: baseline;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
}

.metric-value .currency {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
}

.metric-value .amount {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-white);
    line-height: 1;
}

.metric-value .unit {
    font-size: 1.5rem;
    color: rgba(255, 255, 255, 0.8);
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.85rem;
    font-weight: 600;
}

.metric-trend.positive {
    color: #68d391;
}

.metric-trend.negative {
    color: #fc8181;
}

.metric-progress {
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.progress-bar {
    height: 100%;
    background: var(--success-gradient);
    border-radius: 2px;
    transition: width 1s ease;
    animation: progressAnimation 2s ease;
}

@keyframes progressAnimation {
    from { width: 0; }
}

.rating-stars {
    display: flex;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.rating-stars i {
    color: #ffd700;
    font-size: 0.9rem;
}

/* ===== DASHBOARD GRID ===== */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-base);
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--glass-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-white);
}

.card-content {
    padding: 1.5rem;
}

.chart {
    width: 100%;
    height: 300px;
}

.chart-card .card-content {
    padding: 1rem;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.time-filter {
    display: flex;
    gap: 0.25rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    padding: 0.25rem;
}

.time-btn {
    padding: 0.5rem 1rem;
    background: transparent;
    border: none;
    border-radius: var(--radius-sm);
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.85rem;
}

.time-btn.active,
.time-btn:hover {
    background: var(--glass-bg);
    color: var(--text-white);
}

/* ===== POPULAR DISHES ===== */
.popular-dishes {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.dish-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
    transition: var(--transition-base);
}

.dish-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.dish-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.dish-image {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    object-fit: cover;
}

.dish-details h4 {
    color: var(--text-white);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.dish-details p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
}

.dish-metrics {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.dish-metrics .revenue {
    color: var(--text-white);
    font-weight: 600;
}

.dish-metrics .trend {
    font-size: 0.8rem;
    font-weight: 600;
}

.trend.positive {
    color: #68d391;
}

.trend.negative {
    color: #fc8181;
}

/* ===== AI INSIGHTS ===== */
.ai-insights {
    grid-row: span 2;
}

.ai-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #68d391;
    animation: pulse 2s infinite;
}

.ai-recommendations {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.recommendation {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
    border-left: 4px solid #4299e1;
    transition: var(--transition-base);
}

.recommendation:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.recommendation.high-priority {
    border-left-color: #fc8181;
}

.recommendation.medium-priority {
    border-left-color: #ffd700;
}

.rec-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--glass-bg);
    border-radius: var(--radius-sm);
    color: var(--text-white);
    flex-shrink: 0;
}

.rec-content {
    flex: 1;
}

.rec-content h4 {
    color: var(--text-white);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.rec-content p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.rec-impact {
    color: #68d391;
    font-size: 0.85rem;
    font-weight: 600;
}

/* ===== MENU OPTIMIZATION ===== */
.optimization-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.optimization-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.menu-heatmap {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.heatmap-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-radius: var(--radius-md);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

.heatmap-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1));
    transition: width 1s ease;
}

.heatmap-item.high-heat {
    background: rgba(252, 129, 129, 0.2);
}

.heatmap-item.high-heat::before {
    width: 94%;
    background: linear-gradient(90deg, rgba(252, 129, 129, 0.3), rgba(252, 129, 129, 0.1));
}

.heatmap-item.medium-heat {
    background: rgba(255, 215, 0, 0.2);
}

.heatmap-item.medium-heat::before {
    width: 70%;
    background: linear-gradient(90deg, rgba(255, 215, 0, 0.3), rgba(255, 215, 0, 0.1));
}

.heatmap-item.low-heat {
    background: rgba(104, 211, 145, 0.2);
}

.heatmap-item.low-heat::before {
    width: 40%;
    background: linear-gradient(90deg, rgba(104, 211, 145, 0.3), rgba(104, 211, 145, 0.1));
}

.item-name {
    color: var(--text-white);
    font-weight: 600;
    z-index: 1;
    position: relative;
}

.popularity-score {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
    z-index: 1;
    position: relative;
}

/* ===== TABLE MANAGEMENT ===== */
.table-management-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.table-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.legend {
    display: flex;
    gap: 1rem;
    font-size: 0.85rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
}

.legend-item .status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: none;
}

.legend-item .status-dot.available {
    background: #68d391;
}

.legend-item .status-dot.occupied {
    background: #fc8181;
}

.legend-item .status-dot.reserved {
    background: #ffd700;
}

/* ===== FLOOR PLAN ===== */
.floor-plan {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
    min-height: 300px;
}

.table {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition-base);
    position: relative;
    font-weight: 600;
    color: var(--text-white);
}

.table-2 {
    width: 60px;
    height: 60px;
}

.table-4 {
    width: 80px;
    height: 80px;
}

.table-6 {
    width: 90px;
    height: 90px;
}

.table-8 {
    width: 100px;
    height: 100px;
}

.table.available {
    background: rgba(104, 211, 145, 0.3);
    border: 2px solid #68d391;
}

.table.occupied {
    background: rgba(252, 129, 129, 0.3);
    border: 2px solid #fc8181;
}

.table.reserved {
    background: rgba(255, 215, 0, 0.3);
    border: 2px solid #ffd700;
}

.table:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.table-number {
    font-size: 1.2rem;
    font-weight: 700;
}

.table-capacity {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* ===== RESERVATIONS ===== */
.reservation-filters {
    display: flex;
    gap: 0.25rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    padding: 0.25rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    background: transparent;
    border: none;
    border-radius: var(--radius-sm);
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.85rem;
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--glass-bg);
    color: var(--text-white);
}

.reservations-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.reservation-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
    transition: var(--transition-base);
}

.reservation-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.reservation-time {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-white);
    min-width: 60px;
}

.reservation-details {
    flex: 1;
}

.reservation-details h4 {
    color: var(--text-white);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.reservation-details p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.reservation-note {
    display: inline-block;
    background: var(--secondary-gradient);
    color: var(--text-white);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
    margin-top: 0.5rem;
}

.reservation-actions {
    display: flex;
    gap: 0.5rem;
}

/* ===== ANALYTICS ===== */
.analytics-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    grid-template-rows: auto auto;
}

.analytics-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.revenue-forecast {
    grid-column: span 2;
}

.forecast-accuracy {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.accuracy-score {
    font-size: 1.5rem;
    font-weight: 700;
    color: #68d391;
}

.accuracy-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
}

.forecast-insights {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--glass-border);
}

.insight {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.insight i {
    color: #4299e1;
}

.behavior-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.behavior-metric {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
}

.behavior-metric .metric-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
}

.behavior-metric .metric-value {
    color: var(--text-white);
    font-size: 1.5rem;
    font-weight: 700;
    display: block;
}

.behavior-metric .metric-change {
    font-size: 0.8rem;
    font-weight: 600;
    margin-top: 0.25rem;
}

.behavior-metric .metric-change.positive {
    color: #68d391;
}

/* ===== PEAK HOURS ===== */
.peak-hours-chart {
    display: flex;
    align-items: end;
    justify-content: space-between;
    height: 200px;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
}

.hour-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    background: var(--primary-gradient);
    border-radius: var(--radius-sm);
    min-width: 30px;
    position: relative;
    cursor: pointer;
    transition: var(--transition-base);
    animation: growUp 1s ease;
}

@keyframes growUp {
    from { height: 0; }
}

.hour-bar:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.hour-label {
    position: absolute;
    bottom: -25px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.8rem;
    font-weight: 600;
}

.hour-value {
    position: absolute;
    top: -25px;
    color: var(--text-white);
    font-size: 0.8rem;
    font-weight: 600;
    opacity: 0;
    transition: var(--transition-base);
}

.hour-bar:hover .hour-value {
    opacity: 1;
}

/* ===== CHATBOT ===== */
.chatbot-fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border: none;
    border-radius: 50%;
    box-shadow: var(--shadow-xl);
    cursor: pointer;
    z-index: 1001;
    transition: var(--transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
}

.chatbot-fab:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.4);
}

.fab-icon {
    color: var(--text-white);
    font-size: 1.5rem;
}

.fab-notification {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--secondary-gradient);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
    animation: pulse 2s infinite;
}

.chatbot-container {
    position: fixed;
    bottom: 5rem;
    right: 2rem;
    width: 400px;
    height: 600px;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    transform: translateY(100px) scale(0.8);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-base);
}

.chatbot-container.active {
    transform: translateY(0) scale(1);
    opacity: 1;
    visibility: visible;
}

.chatbot-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--glass-border);
}

.chatbot-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
}

.chatbot-info {
    flex: 1;
}

.chatbot-info h3 {
    color: var(--text-white);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

.status.online::before {
    content: '';
    width: 8px;
    height: 8px;
    background: #68d391;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.chatbot-toggle {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    transition: var(--transition-base);
}

.chatbot-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-white);
}

.chatbot-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.message {
    display: flex;
    gap: 0.75rem;
    animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-avatar {
    width: 32px;
    height: 32px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: 0.9rem;
    flex-shrink: 0;
}

.message-content {
    flex: 1;
    position: relative;
}

.message-content p {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.75rem 1rem;
    border-radius: var(--radius-md);
    color: var(--text-white);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    line-height: 1.4;
}

.quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.quick-action {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-sm);
    padding: 0.5rem 0.75rem;
    color: var(--text-white);
    cursor: pointer;
    font-size: 0.8rem;
    transition: var(--transition-base);
}

.quick-action:hover {
    background: var(--primary-gradient);
    transform: translateY(-1px);
}

.message-time {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.7rem;
}

.chatbot-input {
    display: flex;
    gap: 0.5rem;
    padding: 1rem;
    border-top: 1px solid var(--glass-border);
}

.chatbot-input input {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-sm);
    padding: 0.75rem;
    color: var(--text-white);
    font-size: 0.9rem;
}

.chatbot-input input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.chatbot-input input:focus {
    outline: none;
    border-color: var(--primary-gradient);
    background: rgba(255, 255, 255, 0.15);
}

.chatbot-input button {
    background: var(--primary-gradient);
    border: none;
    border-radius: var(--radius-sm);
    padding: 0.75rem;
    color: var(--text-white);
    cursor: pointer;
    transition: var(--transition-base);
}

.chatbot-input button:hover {
    transform: scale(1.05);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    .main-content {
        padding: 1rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .analytics-grid {
        grid-template-columns: 1fr;
    }
    
    .optimization-grid {
        grid-template-columns: 1fr;
    }
    
    .table-management-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-container {
        padding: 0 1rem;
    }
    
    .nav-menu {
        display: none;
    }
    
    .hero-title {
        font-size: 2.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .section-actions {
        width: 100%;
        justify-content: flex-start;
    }
    
    .chatbot-container {
        right: 1rem;
        left: 1rem;
        width: auto;
    }
    
    .chatbot-fab {
        right: 1rem;
    }
    
    .floor-plan {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .behavior-metrics {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .main-content {
        margin-top: 80px;
        padding: 0.5rem;
    }
    
    .hero-section {
        padding: 2rem 0;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .metric-card {
        padding: 1rem;
    }
    
    .dashboard-card,
    .analytics-card,
    .optimization-card,
    .table-card {
        margin: 0 -0.5rem;
        border-radius: var(--radius-md);
    }
    
    .card-header,
    .card-content {
        padding: 1rem;
    }
    
    .chatbot-container {
        height: 500px;
        bottom: 4rem;
    }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* ===== FOCUS STYLES ===== */
button:focus,
input:focus,
select:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* ===== CUSTOM SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* ===== QR MENU ===== */
.qr-menu-fab {
    position: fixed;
    bottom: 8rem;
    right: 2rem;
    width: 56px;
    height: 56px;
    background: var(--secondary-gradient);
    border: none;
    border-radius: 50%;
    box-shadow: var(--shadow-lg);
    cursor: pointer;
    z-index: 1001;
    transition: var(--transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
}

.qr-menu-fab:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow: 0 20px 40px rgba(249, 147, 251, 0.4);
}

.qr-menu-fab .fab-icon {
    color: var(--text-white);
    font-size: 1.3rem;
}

.qr-menu-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: none;
    align-items: center;
    justify-content: center;
}

.qr-menu-modal.active {
    display: flex;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.qr-menu-container {
    position: relative;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    max-width: 900px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(50px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.qr-menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2rem;
    border-bottom: 1px solid var(--glass-border);
}

.qr-menu-header h2 {
    color: var(--text-white);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    transition: var(--transition-base);
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-white);
}

.qr-menu-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 2rem;
    max-height: 70vh;
    overflow-y: auto;
}

.qr-code-section {
    text-align: center;
}

.qr-code {
    background: white;
    padding: 1rem;
    border-radius: var(--radius-lg);
    display: inline-block;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-md);
    animation: qrPulse 2s infinite;
}

@keyframes qrPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.qr-code img {
    width: 200px;
    height: 200px;
    display: block;
}

.qr-instruction {
    color: var(--text-white);
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.qr-features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
    transition: var(--transition-base);
}

.feature:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.feature i {
    font-size: 1.5rem;
    color: var(--text-white);
    margin-bottom: 0.5rem;
}

.feature span {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    text-align: center;
}

.digital-menu-preview {
    overflow-y: auto;
}

.digital-menu-preview h3 {
    color: var(--text-white);
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.menu-categories {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.menu-category h4 {
    color: var(--text-white);
    font-size: 1.1rem;
    margin-bottom: 1rem;
    font-weight: 600;
    border-bottom: 2px solid var(--glass-border);
    padding-bottom: 0.5rem;
}

.menu-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.menu-item {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
    transition: var(--transition-base);
}

.menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.item-info {
    flex: 1;
}

.item-info h5 {
    color: var(--text-white);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.item-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.item-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tag {
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tag.vegetarian {
    background: rgba(104, 211, 145, 0.2);
    color: #68d391;
    border: 1px solid #68d391;
}

.tag.popular {
    background: rgba(255, 215, 0, 0.2);
    color: #ffd700;
    border: 1px solid #ffd700;
}

.tag.spicy {
    background: rgba(252, 129, 129, 0.2);
    color: #fc8181;
    border: 1px solid #fc8181;
}

.tag.protein {
    background: rgba(102, 126, 234, 0.2);
    color: #667eea;
    border: 1px solid #667eea;
}

.item-price {
    color: var(--text-white);
    font-size: 1.1rem;
    font-weight: 700;
    white-space: nowrap;
    margin-left: 1rem;
}

.menu-footer {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--glass-border);
}

/* ===== RESPONSIVE QR MENU ===== */
@media (max-width: 768px) {
    .qr-menu-container {
        width: 95%;
        margin: 1rem;
    }
    
    .qr-menu-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .qr-features {
        grid-template-columns: 1fr;
    }
    
    .menu-footer {
        flex-direction: column;
    }
    
    .qr-menu-fab {
        right: 1rem;
        bottom: 6rem;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .header,
    .chatbot-fab,
    .chatbot-container,
    .qr-menu-fab,
    .qr-menu-modal {
        display: none !important;
    }
    
    .main-content {
        margin-top: 0;
        background: white !important;
        color: black !important;
    }
    
    .dashboard-card,
    .metric-card,
    .analytics-card {
        background: white !important;
        border: 1px solid #ccc !important;
        box-shadow: none !important;
    }
}