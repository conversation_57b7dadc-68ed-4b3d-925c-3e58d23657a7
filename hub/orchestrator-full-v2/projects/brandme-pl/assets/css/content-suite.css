/* ===== CONTENT SUITE SPECIFIC STYLES ===== */

.content-suite-body {
    background: #0a0a0f;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== CONTENT NAVIGATION ===== */
.content-nav {
    background: rgba(10, 10, 15, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    padding: var(--space-md) 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
}

.content-nav .nav-container {
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    align-items: center;
    gap: var(--space-xl);
}

.nav-center {
    justify-self: center;
}

.page-title {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    color: white;
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin: 0;
}

.page-title i {
    color: var(--primary-600);
}

.nav-actions {
    justify-self: end;
    display: flex;
    gap: var(--space-md);
}

/* ===== CONTENT CONTAINER ===== */
.content-container {
    display: grid;
    grid-template-columns: 350px 1fr;
    height: 100vh;
    padding-top: 80px;
}

/* ===== CONTENT SIDEBAR ===== */
.content-sidebar {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-right: 1px solid var(--glass-border);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.tools-navigation {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-xl);
}

.tool-category {
    margin-bottom: var(--space-xl);
}

.category-header {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--space-md);
    color: var(--gray-200);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-base);
}

.category-header:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.category-header i {
    color: var(--primary-600);
    width: 20px;
    text-align: center;
}

.category-tools {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.tool-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md);
    border-radius: var(--border-radius-lg);
    color: var(--gray-400);
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
}

.tool-item:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--gray-200);
}

.tool-item.active {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.tool-item i {
    width: 16px;
    text-align: center;
}

.tool-item span {
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* ===== AI ASSISTANT ===== */
.ai-assistant {
    border-top: 1px solid var(--glass-border);
    padding: var(--space-lg);
    background: rgba(99, 102, 241, 0.05);
}

.assistant-header {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
}

.assistant-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-lg);
}

.assistant-info h4 {
    color: white;
    font-weight: 600;
    margin: 0 0 var(--space-xs) 0;
    font-size: var(--font-size-base);
}

.assistant-status {
    color: var(--success-400);
    font-size: var(--font-size-sm);
    margin: 0;
}

.assistant-suggestions h5 {
    color: var(--gray-200);
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin: 0 0 var(--space-md) 0;
}

.suggestion-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-md);
    color: var(--gray-300);
    cursor: pointer;
    transition: all var(--transition-base);
    font-size: var(--font-size-sm);
}

.suggestion-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.suggestion-item i {
    color: var(--primary-400);
    width: 16px;
    text-align: center;
}

/* ===== MAIN CONTENT AREA ===== */
.content-main {
    padding: var(--space-xl);
    overflow-y: auto;
    background: var(--gray-900);
}

.content-tool {
    display: none;
}

.content-tool.active {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

/* ===== TOOL HEADER ===== */
.tool-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-2xl);
    padding-bottom: var(--space-xl);
    border-bottom: 1px solid var(--glass-border);
}

.tool-info h2 {
    color: white;
    font-size: var(--font-size-3xl);
    font-weight: 800;
    margin: 0 0 var(--space-sm) 0;
}

.tool-info p {
    color: var(--gray-300);
    font-size: var(--font-size-lg);
    margin: 0;
    line-height: 1.6;
}

.tool-stats {
    display: flex;
    gap: var(--space-xl);
}

.stat {
    text-align: center;
}

.stat-value {
    color: var(--primary-400);
    font-size: var(--font-size-2xl);
    font-weight: 800;
    display: block;
}

.stat-label {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    margin-top: var(--space-xs);
}

/* ===== TOOL CONTENT ===== */
.tool-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3xl);
}

/* ===== INPUT SECTION ===== */
.content-input-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    padding: var(--space-xl);
}

.input-tabs {
    display: flex;
    gap: var(--space-xs);
    margin-bottom: var(--space-xl);
    padding: var(--space-xs);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
}

.tab-btn {
    flex: 1;
    padding: var(--space-md);
    border: none;
    background: transparent;
    color: var(--gray-400);
    border-radius: var(--border-radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-base);
}

.tab-btn.active {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: slideInUp 0.3s ease-out;
}

.input-group {
    margin-bottom: var(--space-lg);
}

.input-group label {
    display: block;
    color: var(--gray-200);
    font-weight: 500;
    margin-bottom: var(--space-sm);
}

.input-group input,
.input-group select,
.input-group textarea {
    width: 100%;
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    color: var(--gray-100);
    font-size: var(--font-size-base);
    transition: all var(--transition-base);
}

.input-group input:focus,
.input-group select:focus,
.input-group textarea:focus {
    outline: none;
    border-color: var(--primary-600);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.input-group input::placeholder,
.input-group textarea::placeholder {
    color: var(--gray-500);
}

.tone-options {
    display: flex;
    gap: var(--space-sm);
    flex-wrap: wrap;
}

.tone-btn {
    padding: var(--space-sm) var(--space-lg);
    border: 1px solid var(--glass-border);
    background: rgba(255, 255, 255, 0.05);
    color: var(--gray-300);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all var(--transition-base);
    font-size: var(--font-size-sm);
}

.tone-btn:hover,
.tone-btn.active {
    border-color: var(--primary-600);
    background: var(--primary-gradient);
    color: white;
}

.btn-generate {
    width: 100%;
    padding: var(--space-lg);
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius-xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-md);
    transition: all var(--transition-base);
    margin-top: var(--space-xl);
}

.btn-generate:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.btn-generate:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* ===== TEMPLATE GRID ===== */
.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-lg);
}

.template-card {
    padding: var(--space-lg);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-base);
}

.template-card:hover {
    border-color: var(--primary-600);
    background: rgba(99, 102, 241, 0.1);
    transform: translateY(-2px);
}

.template-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-md);
    font-size: var(--font-size-xl);
    color: white;
}

.template-card h4 {
    color: white;
    font-weight: 600;
    margin: 0 0 var(--space-sm) 0;
    font-size: var(--font-size-base);
}

.template-card p {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* ===== OUTPUT SECTION ===== */
.content-output-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
}

.output-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-xl);
    border-bottom: 1px solid var(--glass-border);
}

.output-header h3 {
    color: white;
    font-weight: 600;
    margin: 0;
}

.output-actions {
    display: flex;
    gap: var(--space-sm);
}

.btn-action {
    padding: var(--space-sm) var(--space-lg);
    background: rgba(255, 255, 255, 0.1);
    color: var(--gray-300);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: var(--font-size-sm);
    transition: all var(--transition-base);
}

.btn-action:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-1px);
}

/* ===== LINKEDIN PREVIEW ===== */
.linkedin-preview {
    padding: var(--space-xl);
}

.post-preview {
    background: white;
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    margin-bottom: var(--space-xl);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-lg);
}

.profile-info {
    display: flex;
    gap: var(--space-md);
}

.profile-avatar img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
}

.profile-name {
    color: #000;
    font-weight: 600;
    font-size: var(--font-size-base);
}

.profile-title {
    color: #666;
    font-size: var(--font-size-sm);
    margin: var(--space-xs) 0;
}

.post-time {
    color: #666;
    font-size: var(--font-size-sm);
}

.post-menu {
    color: #666;
    cursor: pointer;
}

.post-content {
    padding: 0 var(--space-lg) var(--space-lg);
}

#generated-post-text {
    color: #000;
    line-height: 1.6;
    font-size: var(--font-size-base);
    white-space: pre-wrap;
}

.post-engagement {
    border-top: 1px solid #e6e6e6;
    padding: var(--space-md) var(--space-lg);
}

.engagement-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-md);
    font-size: var(--font-size-sm);
    color: #666;
}

.engagement-actions {
    display: flex;
    justify-content: space-around;
}

.action-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm);
    border-radius: var(--border-radius-md);
    transition: background var(--transition-base);
    font-size: var(--font-size-sm);
}

.action-btn:hover {
    background: #f3f2ef;
}

/* ===== POST ANALYTICS ===== */
.post-analytics {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-xl);
    padding: var(--space-xl);
}

.post-analytics h4 {
    color: white;
    font-weight: 600;
    margin: 0 0 var(--space-lg) 0;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.metric {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-lg);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
}

.metric-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.metric-value {
    color: white;
    font-size: var(--font-size-xl);
    font-weight: 800;
    margin: 0;
}

.metric-label {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    margin: var(--space-xs) 0 0 0;
}

.optimization-tips {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: var(--border-radius-lg);
    padding: var(--space-lg);
}

.optimization-tips h5 {
    color: var(--warning-400);
    margin: 0 0 var(--space-md) 0;
}

.optimization-tips ul {
    margin: 0;
    padding-left: var(--space-lg);
    color: var(--gray-300);
}

.optimization-tips li {
    margin-bottom: var(--space-sm);
    font-size: var(--font-size-sm);
}

/* ===== BIO GENERATOR ===== */
.bio-input-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    padding: var(--space-xl);
}

.platform-selector {
    margin-bottom: var(--space-2xl);
}

.platform-selector h3 {
    color: white;
    font-weight: 600;
    margin: 0 0 var(--space-lg) 0;
}

.platform-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-lg);
}

.platform-option {
    cursor: pointer;
}

.platform-option input[type="checkbox"] {
    display: none;
}

.platform-card {
    padding: var(--space-lg);
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    text-align: center;
    transition: all var(--transition-base);
}

.platform-option input[type="checkbox"]:checked + .platform-card {
    border-color: var(--primary-600);
    background: rgba(99, 102, 241, 0.1);
}

.platform-card i {
    font-size: var(--font-size-2xl);
    color: var(--primary-400);
    margin-bottom: var(--space-md);
}

.platform-card span {
    display: block;
    color: white;
    font-weight: 600;
    margin-bottom: var(--space-sm);
}

.char-limit {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

.bio-form {
    margin-top: var(--space-xl);
}

.form-section {
    margin-bottom: var(--space-2xl);
    padding-bottom: var(--space-xl);
    border-bottom: 1px solid var(--glass-border);
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h4 {
    color: white;
    font-weight: 600;
    margin: 0 0 var(--space-lg) 0;
    font-size: var(--font-size-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-lg);
    margin-bottom: var(--space-lg);
}

.personality-options {
    display: flex;
    gap: var(--space-sm);
    flex-wrap: wrap;
}

.personality-btn {
    padding: var(--space-sm) var(--space-lg);
    border: 1px solid var(--glass-border);
    background: rgba(255, 255, 255, 0.05);
    color: var(--gray-300);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all var(--transition-base);
    font-size: var(--font-size-sm);
}

.personality-btn:hover,
.personality-btn.active {
    border-color: var(--primary-600);
    background: var(--primary-gradient);
    color: white;
}

/* ===== BIO OUTPUT ===== */
.bio-output-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
}

.generated-bios {
    max-height: 600px;
    overflow-y: auto;
}

.bio-result {
    border-bottom: 1px solid var(--glass-border);
    padding: var(--space-xl);
}

.bio-result:last-child {
    border-bottom: none;
}

.bio-header {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
}

.bio-header i {
    color: var(--primary-400);
    font-size: var(--font-size-lg);
}

.bio-header h4 {
    color: white;
    font-weight: 600;
    margin: 0;
    flex: 1;
}

.char-count {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

.copy-bio-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    color: var(--gray-300);
    padding: var(--space-sm);
    cursor: pointer;
    transition: all var(--transition-base);
}

.copy-bio-btn:hover {
    background: var(--primary-gradient);
    color: white;
}

.bio-content {
    margin-bottom: var(--space-lg);
}

.bio-text {
    width: 100%;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: var(--space-md);
    color: var(--gray-100);
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
}

.bio-tips {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: var(--border-radius-lg);
    padding: var(--space-md);
}

.bio-tips strong {
    color: var(--success-400);
}

.bio-tips ul {
    margin: var(--space-sm) 0 0 0;
    padding-left: var(--space-lg);
    color: var(--gray-300);
}

.bio-tips li {
    margin-bottom: var(--space-xs);
    font-size: var(--font-size-sm);
}

/* ===== CONTENT CALENDAR ===== */
.calendar-controls {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.calendar-controls button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    color: var(--gray-300);
    width: 32px;
    height: 32px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-base);
}

.calendar-controls button:hover {
    background: var(--primary-gradient);
    color: white;
}

.calendar-controls h3 {
    color: white;
    font-weight: 600;
    margin: 0;
    min-width: 150px;
    text-align: center;
}

.calendar-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: var(--space-xl);
    height: 70vh;
}

.calendar-sidebar {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    padding: var(--space-xl);
    overflow-y: auto;
}

.content-types-legend h4,
.quick-actions h4,
.ai-suggestions h4 {
    color: white;
    font-weight: 600;
    margin: 0 0 var(--space-lg) 0;
    font-size: var(--font-size-base);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
    color: var(--gray-300);
    font-size: var(--font-size-sm);
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.quick-actions {
    margin: var(--space-2xl) 0;
    padding: var(--space-xl) 0;
    border-top: 1px solid var(--glass-border);
    border-bottom: 1px solid var(--glass-border);
}

.action-btn {
    width: 100%;
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    color: var(--gray-300);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
    transition: all var(--transition-base);
    font-size: var(--font-size-sm);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.action-btn i {
    color: var(--primary-400);
}

.suggestion-cards {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.suggestion-card {
    display: flex;
    gap: var(--space-md);
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--glass-border);
}

.suggestion-icon {
    width: 30px;
    height: 30px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-sm);
    flex-shrink: 0;
}

.suggestion-content h5 {
    color: white;
    font-weight: 600;
    margin: 0 0 var(--space-xs) 0;
    font-size: var(--font-size-sm);
}

.suggestion-content p {
    color: var(--gray-400);
    font-size: var(--font-size-xs);
    margin: 0;
}

.calendar-main {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
}

.calendar-header {
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid var(--glass-border);
    padding: var(--space-lg);
}

.weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: var(--space-sm);
}

.weekday {
    text-align: center;
    color: var(--gray-400);
    font-weight: 600;
    font-size: var(--font-size-sm);
    padding: var(--space-sm);
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: var(--glass-border);
    padding: 1px;
}

.calendar-day {
    background: var(--glass-bg);
    padding: var(--space-md);
    min-height: 100px;
    cursor: pointer;
    transition: background var(--transition-base);
    position: relative;
}

.calendar-day:hover {
    background: rgba(255, 255, 255, 0.05);
}

.calendar-day.today {
    background: rgba(99, 102, 241, 0.1);
    border: 1px solid var(--primary-600);
}

.calendar-day.other-month {
    opacity: 0.5;
}

.day-number {
    color: var(--gray-200);
    font-weight: 600;
    margin-bottom: var(--space-sm);
}

.day-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.content-item {
    padding: var(--space-xs);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    color: white;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.content-item.linkedin {
    background: #6366f1;
}

.content-item.instagram {
    background: #ec4899;
}

.content-item.blog {
    background: #10b981;
}

.content-item.video {
    background: #f59e0b;
}

.content-item.newsletter {
    background: #8b5cf6;
}

/* ===== CHECKBOX TOGGLE ===== */
.checkbox-toggle {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.checkbox-toggle input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkbox-toggle label {
    margin: 0;
    cursor: pointer;
    color: var(--gray-300);
}

/* ===== RANGE INPUT ===== */
.input-group input[type="range"] {
    background: var(--glass-border);
    height: 6px;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.input-group input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-gradient);
    cursor: pointer;
}

.input-group input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-gradient);
    cursor: pointer;
    border: none;
}

.range-value {
    color: var(--primary-400);
    font-weight: 600;
    margin-left: var(--space-sm);
}

/* ===== MODAL STYLES ===== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: var(--z-modal);
    align-items: center;
    justify-content: center;
    padding: var(--space-lg);
}

.modal.active {
    display: flex;
}

.modal-content {
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-xl);
    border-bottom: 1px solid var(--glass-border);
}

.modal-title {
    color: white;
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--gray-400);
    font-size: var(--font-size-xl);
    cursor: pointer;
    padding: var(--space-sm);
    transition: color var(--transition-base);
}

.modal-close:hover {
    color: white;
}

.modal-body {
    padding: var(--space-xl);
}

.form-group {
    margin-bottom: var(--space-lg);
}

.form-group label {
    display: block;
    color: var(--gray-200);
    font-weight: 500;
    margin-bottom: var(--space-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    color: var(--gray-100);
    font-size: var(--font-size-base);
}

.modal-actions {
    display: flex;
    gap: var(--space-md);
    justify-content: flex-end;
    margin-top: var(--space-xl);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .content-container {
        grid-template-columns: 300px 1fr;
    }
    
    .tool-content {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
    }
    
    .calendar-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
    
    .analytics-grid {
        grid-template-columns: 1fr;
    }
    
    .tool-stats {
        display: none;
    }
}

@media (max-width: 768px) {
    .content-nav .nav-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--space-md);
    }
    
    .nav-actions {
        justify-self: center;
    }
    
    .content-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
    
    .content-sidebar {
        max-height: 50vh;
        order: 2;
    }
    
    .content-main {
        order: 1;
    }
    
    .tool-header {
        flex-direction: column;
        gap: var(--space-lg);
        text-align: center;
    }
    
    .platform-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .tone-options,
    .personality-options {
        flex-direction: column;
    }
    
    .template-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .content-main {
        padding: var(--space-lg);
    }
    
    .tool-info h2 {
        font-size: var(--font-size-2xl);
    }
    
    .content-input-section,
    .content-output-section {
        padding: var(--space-lg);
    }
    
    .modal-content {
        margin: var(--space-md);
    }
}