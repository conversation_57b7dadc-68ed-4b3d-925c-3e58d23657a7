/* ===== PROFESSIONAL TOOLS STYLES ===== */

.tools-body {
    background: #0a0a0f;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== TOOLS NAVIGATION ===== */
.tools-nav {
    background: rgba(10, 10, 15, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    padding: var(--space-md) 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
}

.tools-nav .nav-container {
    display: grid;
    grid-template-columns: 250px 1fr 350px;
    align-items: center;
    gap: var(--space-xl);
}

.nav-center {
    text-align: center;
}

.page-title {
    color: white;
    font-weight: 700;
    font-size: var(--font-size-xl);
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-md);
}

.page-title i {
    color: var(--primary-400);
}

/* ===== TOOLS CONTAINER ===== */
.tools-container {
    display: grid;
    grid-template-columns: 350px 1fr;
    height: 100vh;
    padding-top: 80px;
}

/* ===== TOOLS SIDEBAR ===== */
.tools-sidebar {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-right: 1px solid var(--glass-border);
    overflow-y: auto;
    padding: var(--space-xl);
}

.sidebar-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-2xl);
}

/* ===== TOOL CATEGORIES ===== */
.tool-categories {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.category-item {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    padding: var(--space-lg);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.category-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    transition: left var(--transition-slow);
}

.category-item:hover::before {
    left: 100%;
}

.category-item:hover,
.category-item.active {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-600);
    transform: translateY(-2px);
}

.category-item.active {
    background: var(--primary-gradient);
    box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
}

.category-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.category-item.active .category-icon {
    background: rgba(255, 255, 255, 0.2);
}

.category-info h4 {
    color: white;
    font-weight: 600;
    margin: 0 0 var(--space-xs) 0;
    font-size: var(--font-size-base);
}

.category-info p {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    margin: 0;
}

.category-item.active .category-info p {
    color: rgba(255, 255, 255, 0.8);
}

/* ===== QUICK STATS ===== */
.quick-stats h4,
.templates-library h4 {
    color: white;
    font-weight: 600;
    margin-bottom: var(--space-lg);
    font-size: var(--font-size-base);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-md);
}

.stat-label {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

.stat-value {
    color: var(--primary-400);
    font-weight: 700;
    font-size: var(--font-size-lg);
}

/* ===== TEMPLATES LIBRARY ===== */
.template-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.template-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--glass-border);
    transition: all var(--transition-base);
}

.template-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-1px);
}

.template-item i {
    color: var(--primary-400);
    font-size: var(--font-size-lg);
    width: 20px;
    text-align: center;
}

.template-item span {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
    flex: 1;
}

.template-use {
    background: var(--primary-gradient);
    border: none;
    border-radius: var(--border-radius-md);
    color: white;
    padding: var(--space-xs) var(--space-md);
    cursor: pointer;
    font-size: var(--font-size-xs);
    font-weight: 600;
    transition: all var(--transition-base);
}

.template-use:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* ===== TOOLS MAIN ===== */
.tools-main {
    padding: var(--space-xl);
    overflow-y: auto;
    background: var(--gray-900);
    display: flex;
    flex-direction: column;
    gap: var(--space-2xl);
}

/* ===== TOOL SECTIONS ===== */
.tool-section {
    display: none;
}

.tool-section.active {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-2xl);
}

.section-header h2 {
    color: white;
    font-weight: 700;
    font-size: var(--font-size-2xl);
    margin: 0 0 var(--space-md) 0;
}

.section-header p {
    color: var(--gray-400);
    font-size: var(--font-size-lg);
    margin: 0;
}

/* ===== KIT BUILDER ===== */
.kit-builder,
.speaker-builder {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-2xl);
    padding: var(--space-2xl);
}

/* ===== BUILDER TABS ===== */
.builder-tabs {
    display: flex;
    gap: var(--space-sm);
    margin-bottom: var(--space-2xl);
    padding: var(--space-xs);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
}

.tab-btn {
    flex: 1;
    padding: var(--space-md);
    background: transparent;
    border: none;
    border-radius: var(--border-radius-md);
    color: var(--gray-400);
    cursor: pointer;
    transition: all var(--transition-base);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.tab-btn.active {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

/* ===== TAB CONTENT ===== */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

/* ===== FORM STYLES ===== */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    color: var(--gray-300);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    color: white;
    padding: var(--space-md);
    font-size: var(--font-size-base);
    transition: all var(--transition-base);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-600);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--gray-500);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* ===== AI ASSIST BUTTON ===== */
.btn-ai-assist {
    background: var(--primary-gradient);
    border: none;
    border-radius: var(--border-radius-lg);
    color: white;
    padding: var(--space-sm) var(--space-lg);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-weight: 600;
    font-size: var(--font-size-sm);
    transition: all var(--transition-base);
    margin-top: var(--space-sm);
    align-self: flex-start;
}

.btn-ai-assist:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
}

.btn-ai-assist i {
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(5deg); }
}

/* ===== EXPERTISE TAGS ===== */
.expertise-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-sm);
    margin-bottom: var(--space-md);
}

.expertise-tag {
    background: var(--primary-gradient);
    color: white;
    padding: var(--space-xs) var(--space-md);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.expertise-tag .remove-tag {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    color: white;
    width: 18px;
    height: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
}

.add-expertise {
    display: flex;
    gap: var(--space-sm);
}

.add-expertise input {
    flex: 1;
}

.add-expertise button {
    background: var(--primary-gradient);
    border: none;
    border-radius: var(--border-radius-lg);
    color: white;
    padding: var(--space-md);
    cursor: pointer;
    font-weight: 600;
    transition: all var(--transition-base);
}

.add-expertise button:hover {
    transform: translateY(-1px);
}

/* ===== MEDIA UPLOAD ===== */
.media-upload-section {
    margin-bottom: var(--space-2xl);
}

.media-upload-section h4,
.media-links-section h4,
.portfolio-section h4 {
    color: white;
    font-weight: 600;
    margin-bottom: var(--space-lg);
    font-size: var(--font-size-lg);
}

.upload-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-lg);
}

.upload-item {
    position: relative;
}

.upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--space-md);
    padding: var(--space-2xl);
    background: rgba(255, 255, 255, 0.05);
    border: 2px dashed var(--glass-border);
    border-radius: var(--border-radius-xl);
    cursor: pointer;
    transition: all var(--transition-base);
    min-height: 150px;
}

.upload-placeholder:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--primary-600);
}

.upload-placeholder i {
    color: var(--primary-400);
    font-size: var(--font-size-2xl);
}

.upload-placeholder span {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
    text-align: center;
}

.upload-placeholder input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

/* ===== PORTFOLIO SECTION ===== */
.portfolio-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
    margin-bottom: var(--space-lg);
}

.portfolio-item {
    display: flex;
    gap: var(--space-lg);
    padding: var(--space-lg);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--glass-border);
}

.portfolio-info {
    flex: 1;
}

.portfolio-title {
    color: white;
    font-weight: 600;
    margin-bottom: var(--space-sm);
}

.portfolio-description {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-sm);
}

.portfolio-metrics {
    display: flex;
    gap: var(--space-lg);
    color: var(--primary-400);
    font-size: var(--font-size-sm);
}

.btn-add-portfolio {
    background: rgba(255, 255, 255, 0.1);
    border: 2px dashed var(--glass-border);
    border-radius: var(--border-radius-lg);
    color: var(--gray-300);
    padding: var(--space-lg);
    cursor: pointer;
    transition: all var(--transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-md);
    font-weight: 500;
}

.btn-add-portfolio:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-600);
    color: white;
}

/* ===== SPEAKER BUILDER ===== */
.speaker-form {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: var(--space-2xl);
}

.form-section {
    margin-bottom: var(--space-2xl);
}

.form-section h4 {
    color: white;
    font-weight: 600;
    margin-bottom: var(--space-lg);
    font-size: var(--font-size-lg);
}

/* ===== SPEECH TOPICS ===== */
.speech-topics {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
    margin-bottom: var(--space-lg);
}

.topic-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: var(--space-lg);
    padding: var(--space-lg);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--glass-border);
}

.topic-info {
    flex: 1;
}

.topic-title {
    color: white;
    font-weight: 600;
    margin-bottom: var(--space-sm);
}

.topic-details {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    display: flex;
    gap: var(--space-lg);
    margin-bottom: var(--space-sm);
}

.topic-description {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.btn-add-topic {
    background: rgba(255, 255, 255, 0.1);
    border: 2px dashed var(--glass-border);
    border-radius: var(--border-radius-lg);
    color: var(--gray-300);
    padding: var(--space-lg);
    cursor: pointer;
    transition: all var(--transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-md);
    font-weight: 500;
    width: 100%;
}

.btn-add-topic:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-600);
    color: white;
}

/* ===== REQUIREMENTS LIST ===== */
.requirements-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-md);
}

.requirement-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
}

.requirement-item:hover {
    background: rgba(255, 255, 255, 0.08);
}

.requirement-item input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--glass-border);
    border-radius: var(--border-radius-sm);
    position: relative;
    transition: all var(--transition-base);
}

.requirement-item input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-gradient);
    border-color: var(--primary-600);
}

.requirement-item input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: var(--font-size-xs);
}

.requirement-item span:not(.checkmark) {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
}

/* ===== SPEAKER PREVIEW ===== */
.speaker-preview {
    position: sticky;
    top: var(--space-xl);
}

.speaker-preview h4 {
    color: white;
    font-weight: 600;
    margin-bottom: var(--space-lg);
    text-align: center;
}

.speaker-sheet-preview {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    padding: var(--space-xl);
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-placeholder {
    text-align: center;
    color: var(--gray-400);
}

.preview-placeholder i {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--space-md);
    color: var(--primary-400);
}

/* ===== BUILDER ACTIONS ===== */
.builder-actions {
    display: flex;
    justify-content: center;
    gap: var(--space-lg);
    margin-top: var(--space-2xl);
    padding-top: var(--space-2xl);
    border-top: 1px solid var(--glass-border);
}

/* ===== MODALS ===== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    z-index: var(--z-modal);
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
    animation: fadeIn 0.3s ease-out;
}

.modal-large .modal-content {
    max-width: 90vw;
    max-height: 90vh;
    width: 1200px;
}

.modal-content {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-2xl);
    padding: var(--space-2xl);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
    padding-bottom: var(--space-lg);
    border-bottom: 1px solid var(--glass-border);
}

.modal-actions-header {
    display: flex;
    gap: var(--space-md);
    align-items: center;
}

.modal-title {
    color: white;
    font-weight: 700;
    font-size: var(--font-size-xl);
    margin: 0;
}

.modal-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    color: var(--gray-400);
    width: 40px;
    height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-base);
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--space-md);
    margin-top: var(--space-xl);
    padding-top: var(--space-lg);
    border-top: 1px solid var(--glass-border);
}

/* ===== KIT PREVIEW CONTENT ===== */
.kit-preview-content {
    background: white;
    color: #333;
    padding: var(--space-2xl);
    border-radius: var(--border-radius-lg);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    .tools-container {
        grid-template-columns: 300px 1fr;
    }
    
    .tools-nav .nav-container {
        grid-template-columns: 200px 1fr 300px;
    }
    
    .speaker-form {
        grid-template-columns: 1fr;
    }
    
    .speaker-preview {
        position: static;
    }
}

@media (max-width: 768px) {
    .tools-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
    
    .tools-sidebar {
        max-height: 50vh;
        order: 2;
    }
    
    .tools-main {
        order: 1;
    }
    
    .tools-nav .nav-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--space-md);
    }
    
    .nav-actions {
        justify-self: center;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .builder-tabs {
        flex-direction: column;
    }
    
    .requirements-list {
        grid-template-columns: 1fr;
    }
    
    .upload-grid {
        grid-template-columns: 1fr;
    }
    
    .builder-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .tools-main {
        padding: var(--space-lg);
    }
    
    .kit-builder,
    .speaker-builder {
        padding: var(--space-lg);
    }
    
    .modal-content {
        padding: var(--space-lg);
        width: 95%;
    }
    
    .category-item {
        flex-direction: column;
        text-align: center;
        gap: var(--space-md);
    }
    
    .template-item {
        flex-direction: column;
        text-align: center;
        gap: var(--space-sm);
    }
}