/* ===== RESET & BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Primary Brand Colors */
    --primary-600: #6366f1;
    --primary-700: #5b21b6;
    --primary-800: #4c1d95;
    --primary-gradient: linear-gradient(135deg, #6366f1 0%, #a855f7 50%, #ec4899 100%);
    
    /* Neutral Colors */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Success/Error */
    --success-500: #10b981;
    --error-500: #ef4444;
    --warning-500: #f59e0b;
    
    /* Glass Effect */
    --glass-bg: rgba(255, 255, 255, 0.08);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    
    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Border Radius */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-base: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1040;
    --z-popover: 1050;
    --z-tooltip: 1060;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-100);
    background: #0a0a0f;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

/* ===== UTILITY CLASSES ===== */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    border-radius: var(--border-radius-xl);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    border: none;
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-base);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.6);
}

.btn-secondary {
    background: var(--glass-bg);
    color: var(--gray-100);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(20px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.btn-large {
    padding: var(--space-lg) var(--space-2xl);
    font-size: var(--font-size-base);
    border-radius: var(--border-radius-xl);
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
    background: rgba(10, 10, 15, 0.8);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    padding: var(--space-md) 0;
}

.nav-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-weight: 800;
    font-size: var(--font-size-xl);
    color: white;
}

.brand-icon {
    color: var(--primary-600);
    font-size: var(--font-size-2xl);
}

.nav-links {
    display: flex;
    align-items: center;
    gap: var(--space-2xl);
}

.nav-link {
    color: var(--gray-300);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-base);
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: width var(--transition-base);
}

.nav-link:hover {
    color: white;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-cta {
    margin-left: var(--space-lg);
}

.mobile-menu-toggle {
    display: none;
    color: white;
    font-size: var(--font-size-xl);
    cursor: pointer;
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding-top: 80px;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.gradient-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(60px);
    opacity: 0.6;
    animation: float 6s ease-in-out infinite;
}

.orb-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.8), transparent);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.orb-2 {
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(168, 85, 247, 0.6), transparent);
    top: 50%;
    right: 10%;
    animation-delay: 2s;
}

.orb-3 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(236, 72, 153, 0.7), transparent);
    bottom: 20%;
    left: 50%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3xl);
    align-items: center;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.hero-content {
    z-index: 10;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-2xl);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-200);
    margin-bottom: var(--space-xl);
    animation: slideInUp 0.8s ease-out;
}

.hero-badge i {
    color: var(--primary-600);
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    line-height: 1.1;
    color: white;
    margin-bottom: var(--space-xl);
    animation: slideInUp 0.8s ease-out 0.2s both;
}

.highlight {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-300);
    margin-bottom: var(--space-2xl);
    line-height: 1.6;
    animation: slideInUp 0.8s ease-out 0.4s both;
}

.hero-stats {
    display: flex;
    gap: var(--space-2xl);
    margin-bottom: var(--space-2xl);
    animation: slideInUp 0.8s ease-out 0.6s both;
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: white;
    display: block;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-400);
    margin-top: var(--space-xs);
}

.hero-actions {
    display: flex;
    gap: var(--space-lg);
    margin-bottom: var(--space-2xl);
    animation: slideInUp 0.8s ease-out 0.8s both;
}

.hero-trust {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    animation: slideInUp 0.8s ease-out 1s both;
}

.trust-text {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

.trust-logos {
    display: flex;
    gap: var(--space-lg);
}

.trust-logo {
    color: var(--gray-500);
    font-size: var(--font-size-sm);
    font-weight: 600;
    padding: var(--space-xs) var(--space-sm);
    background: var(--glass-bg);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--glass-border);
}

/* ===== HERO VISUAL ===== */
.hero-visual {
    position: relative;
    animation: slideInRight 1s ease-out 0.5s both;
}

.dashboard-preview {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-2xl);
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: transform var(--transition-slow);
}

.dashboard-preview:hover {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

.dashboard-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--glass-border);
}

.dashboard-tabs {
    display: flex;
    gap: var(--space-md);
}

.tab {
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-base);
    color: var(--gray-400);
}

.tab.active {
    background: var(--primary-gradient);
    color: white;
}

.dashboard-content {
    padding: var(--space-lg);
}

.metric-card {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    padding: var(--space-lg);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--space-lg);
    transition: all var(--transition-base);
}

.metric-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.metric-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
}

.metric-value {
    font-size: var(--font-size-2xl);
    font-weight: 800;
    color: white;
}

.metric-label {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

.progress-section {
    margin-top: var(--space-xl);
}

.progress-label {
    color: var(--gray-300);
    margin-bottom: var(--space-sm);
    font-weight: 500;
}

.progress-bar {
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    margin-bottom: var(--space-sm);
}

.progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-md);
    width: 0%;
    transition: width 2s ease-out;
    animation: progressAnimation 2s ease-out 1.5s both;
}

.progress-value {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

@keyframes progressAnimation {
    from { width: 0%; }
    to { width: 87%; }
}

/* ===== FEATURES SECTION ===== */
.features {
    padding: var(--space-3xl) 0;
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-3xl);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: white;
    margin-bottom: var(--space-lg);
    line-height: 1.2;
}

.section-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-300);
    line-height: 1.6;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--space-2xl);
}

.feature-card {
    padding: var(--space-2xl);
    transition: all var(--transition-base);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    transition: left var(--transition-slow);
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(99, 102, 241, 0.3);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: white;
    margin-bottom: var(--space-lg);
}

.feature-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: white;
    margin-bottom: var(--space-sm);
}

.feature-description {
    color: var(--gray-300);
    margin-bottom: var(--space-xl);
    line-height: 1.6;
}

.feature-demo {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    padding: var(--space-lg);
    margin-bottom: var(--space-lg);
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.demo-preview {
    width: 100%;
}

.demo-screen {
    background: var(--gray-800);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    transform: scale(0.8);
}

.demo-header {
    height: 20px;
    background: var(--gray-700);
    display: flex;
    align-items: center;
    padding: 0 var(--space-sm);
}

.demo-content {
    padding: var(--space-lg);
}

.demo-section {
    height: 12px;
    background: var(--gray-600);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--space-sm);
    animation: pulse 2s ease-in-out infinite;
}

.demo-section.short {
    width: 60%;
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

.content-types {
    display: flex;
    gap: var(--space-sm);
    justify-content: center;
    flex-wrap: wrap;
}

.content-type {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--border-radius-lg);
    background: rgba(255, 255, 255, 0.1);
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    transition: all var(--transition-base);
}

.content-type.active {
    background: var(--primary-gradient);
    color: white;
}

.analytics-chart {
    display: flex;
    align-items: end;
    justify-content: center;
    height: 80px;
    gap: var(--space-sm);
}

.chart-bars {
    display: flex;
    align-items: end;
    gap: var(--space-xs);
    height: 100%;
}

.bar {
    width: 16px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
    animation: barGrow 1.5s ease-out;
}

@keyframes barGrow {
    from { height: 0; }
}

.pro-tools {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.tool-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-md);
    color: var(--gray-300);
    font-size: var(--font-size-sm);
}

.tool-item i {
    color: var(--primary-600);
}

.score-display {
    text-align: center;
}

.score-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-600) 0deg 313deg, rgba(255, 255, 255, 0.1) 313deg 360deg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-sm);
    position: relative;
}

.score-circle::after {
    content: '';
    position: absolute;
    inset: 8px;
    background: var(--gray-800);
    border-radius: 50%;
}

.score-number {
    font-size: var(--font-size-xl);
    font-weight: 800;
    color: white;
    z-index: 1;
}

.score-label {
    font-size: var(--font-size-sm);
    color: var(--gray-400);
    z-index: 1;
}

.score-level {
    color: var(--primary-600);
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.bio-preview {
    text-align: center;
}

.bio-text {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-md);
    padding: var(--space-md);
    color: var(--gray-300);
    font-size: var(--font-size-sm);
    font-style: italic;
    margin-bottom: var(--space-md);
    line-height: 1.5;
}

.bio-platforms {
    display: flex;
    gap: var(--space-xs);
    justify-content: center;
}

.platform {
    padding: var(--space-xs) var(--space-sm);
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.feature-btn {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    color: var(--gray-200);
    border: 1px solid var(--glass-border);
    padding: var(--space-md);
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-base);
    text-decoration: none;
    display: block;
    text-align: center;
}

.feature-btn:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateY(-2px);
}

/* ===== DEMO SECTION ===== */
.demo-section {
    padding: var(--space-3xl) 0;
    background: rgba(255, 255, 255, 0.02);
}

.demo-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3xl);
    align-items: center;
}

.demo-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: white;
    margin-bottom: var(--space-lg);
    line-height: 1.2;
}

.demo-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-300);
    margin-bottom: var(--space-2xl);
}

.demo-features {
    margin-bottom: var(--space-2xl);
}

.demo-feature {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
    color: var(--gray-300);
}

.demo-feature i {
    color: var(--success-500);
    font-size: var(--font-size-lg);
}

.demo-visual {
    position: relative;
}

.demo-player {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-2xl);
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.player-controls {
    padding: var(--space-lg);
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.play-button {
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all var(--transition-base);
}

.play-button:hover {
    transform: scale(1.1);
}

.progress-timeline {
    flex: 1;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.timeline-fill {
    height: 100%;
    background: var(--primary-gradient);
    width: 30%;
    border-radius: var(--border-radius-sm);
}

.time-display {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.player-screen {
    padding: var(--space-2xl);
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.demo-transformation {
    width: 100%;
}

.before-after {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-xl);
}

.before, .after {
    text-align: center;
    padding: var(--space-lg);
    border-radius: var(--border-radius-lg);
}

.before {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.after {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.before h4, .after h4 {
    color: white;
    margin-bottom: var(--space-md);
    font-size: var(--font-size-lg);
}

.profile-weak .score, .profile-strong .score {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    margin-bottom: var(--space-md);
}

.profile-weak .score {
    color: var(--error-500);
}

.profile-strong .score {
    color: var(--success-500);
}

.issues, .improvements {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.issue {
    background: rgba(239, 68, 68, 0.2);
    color: var(--error-500);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
}

.improvement {
    background: rgba(16, 185, 129, 0.2);
    color: var(--success-500);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
}

/* ===== SUCCESS STORIES ===== */
.success-stories {
    padding: var(--space-3xl) 0;
}

.stories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-2xl);
}

.story-card {
    padding: var(--space-2xl);
    transition: all var(--transition-base);
}

.story-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.story-header {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    margin-bottom: var(--space-lg);
}

.story-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-placeholder {
    color: white;
    font-weight: 700;
    font-size: var(--font-size-sm);
}

.story-info {
    flex: 1;
}

.story-name {
    color: white;
    font-weight: 700;
    margin-bottom: var(--space-xs);
}

.story-role {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

.story-rating {
    display: flex;
    gap: var(--space-xs);
    color: var(--warning-500);
}

.story-quote {
    color: var(--gray-300);
    font-size: var(--font-size-lg);
    font-style: italic;
    line-height: 1.6;
    margin-bottom: var(--space-xl);
    padding-left: var(--space-lg);
    border-left: 3px solid var(--primary-600);
}

.story-metrics {
    display: flex;
    gap: var(--space-xl);
}

.story-metrics .metric {
    text-align: center;
}

.story-metrics .metric-value {
    font-size: var(--font-size-2xl);
    font-weight: 800;
    color: var(--primary-600);
    display: block;
}

.story-metrics .metric-label {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    margin-top: var(--space-xs);
}

/* ===== PRICING ===== */
.pricing {
    padding: var(--space-3xl) 0;
    background: rgba(255, 255, 255, 0.02);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-2xl);
    margin-bottom: var(--space-3xl);
}

.pricing-card {
    padding: var(--space-2xl);
    position: relative;
    transition: all var(--transition-base);
}

.pricing-card.featured {
    border-color: var(--primary-600);
    transform: scale(1.05);
    box-shadow: 0 20px 40px rgba(99, 102, 241, 0.2);
}

.pricing-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-8px);
}

.pricing-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-gradient);
    color: white;
    padding: var(--space-xs) var(--space-lg);
    border-radius: var(--border-radius-2xl);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.pricing-header {
    text-align: center;
    margin-bottom: var(--space-2xl);
}

.pricing-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: white;
    margin-bottom: var(--space-lg);
}

.pricing-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: var(--space-xs);
    margin-bottom: var(--space-lg);
}

.price-amount {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    color: white;
}

.price-currency {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-300);
}

.price-period {
    font-size: var(--font-size-lg);
    color: var(--gray-400);
}

.pricing-description {
    color: var(--gray-300);
    text-align: center;
}

.pricing-features {
    margin-bottom: var(--space-2xl);
}

.pricing-features .feature {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
    color: var(--gray-300);
}

.pricing-features .feature i {
    color: var(--success-500);
    font-size: var(--font-size-sm);
}

.pricing-btn {
    width: 100%;
    padding: var(--space-lg);
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    font-size: var(--font-size-base);
    transition: all var(--transition-base);
}

.pricing-guarantee {
    text-align: center;
}

.guarantee-card {
    display: inline-flex;
    align-items: center;
    gap: var(--space-lg);
    padding: var(--space-lg) var(--space-2xl);
    max-width: 500px;
}

.guarantee-icon {
    width: 50px;
    height: 50px;
    background: var(--success-500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
}

.guarantee-title {
    color: white;
    font-weight: 700;
    margin-bottom: var(--space-xs);
}

.guarantee-text {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
}

/* ===== CTA SECTION ===== */
.cta-section {
    padding: var(--space-3xl) 0;
    text-align: center;
}

.cta-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: white;
    margin-bottom: var(--space-lg);
    line-height: 1.2;
}

.cta-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-300);
    margin-bottom: var(--space-2xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-actions {
    display: flex;
    gap: var(--space-lg);
    justify-content: center;
    margin-bottom: var(--space-2xl);
}

.cta-trust {
    display: flex;
    gap: var(--space-2xl);
    justify-content: center;
    flex-wrap: wrap;
}

.trust-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

.trust-item i {
    color: var(--success-500);
}

/* ===== FOOTER ===== */
.footer {
    background: rgba(0, 0, 0, 0.5);
    border-top: 1px solid var(--glass-border);
    padding: var(--space-3xl) 0 var(--space-xl);
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: var(--space-3xl);
    margin-bottom: var(--space-2xl);
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-weight: 800;
    font-size: var(--font-size-xl);
    color: white;
    margin-bottom: var(--space-lg);
}

.footer-description {
    color: var(--gray-400);
    line-height: 1.6;
    margin-bottom: var(--space-lg);
}

.footer-social {
    display: flex;
    gap: var(--space-md);
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    text-decoration: none;
    transition: all var(--transition-base);
}

.social-link:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateY(-2px);
}

.footer-title {
    color: white;
    font-weight: 700;
    margin-bottom: var(--space-lg);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--space-sm);
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    transition: color var(--transition-base);
}

.footer-links a:hover {
    color: white;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-xl);
    border-top: 1px solid var(--glass-border);
}

.footer-legal {
    color: var(--gray-500);
    font-size: var(--font-size-sm);
}

.footer-trust {
    display: flex;
    gap: var(--space-lg);
}

.trust-badge {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-xs) var(--space-sm);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    color: var(--gray-400);
    font-size: var(--font-size-xs);
}

.trust-badge i {
    color: var(--success-500);
}

/* ===== MODAL ===== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: var(--z-modal);
    align-items: center;
    justify-content: center;
    padding: var(--space-lg);
}

.modal.active {
    display: flex;
}

.modal-content {
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-xl);
    border-bottom: 1px solid var(--glass-border);
}

.modal-title {
    color: white;
    font-size: var(--font-size-xl);
    font-weight: 700;
}

.modal-close {
    background: none;
    border: none;
    color: var(--gray-400);
    font-size: var(--font-size-xl);
    cursor: pointer;
    padding: var(--space-sm);
    transition: color var(--transition-base);
}

.modal-close:hover {
    color: white;
}

.modal-body {
    padding: var(--space-xl);
}

/* ===== ANIMATIONS ===== */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    :root {
        --font-size-5xl: 2.5rem;
        --font-size-4xl: 2rem;
        --font-size-3xl: 1.75rem;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        gap: var(--space-2xl);
        text-align: center;
    }
    
    .hero-visual {
        order: -1;
    }
    
    .demo-container {
        grid-template-columns: 1fr;
        gap: var(--space-2xl);
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: var(--space-2xl);
    }
}

@media (max-width: 768px) {
    :root {
        --space-lg: 1rem;
        --space-xl: 1.5rem;
        --space-2xl: 2rem;
        --space-3xl: 2.5rem;
    }
    
    .container {
        padding: 0 var(--space-md);
    }
    
    .nav-links {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .hero {
        padding-top: 60px;
        min-height: auto;
        padding-bottom: var(--space-3xl);
    }
    
    .hero-stats {
        flex-direction: column;
        gap: var(--space-lg);
        text-align: center;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-trust {
        flex-direction: column;
        text-align: center;
        gap: var(--space-md);
    }
    
    .trust-logos {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }
    
    .feature-card {
        padding: var(--space-lg);
    }
    
    .stories-grid {
        grid-template-columns: 1fr;
    }
    
    .pricing-grid {
        grid-template-columns: 1fr;
    }
    
    .pricing-card.featured {
        transform: none;
    }
    
    .before-after {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }
    
    .cta-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .cta-trust {
        flex-direction: column;
        gap: var(--space-md);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
        text-align: center;
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: var(--space-lg);
        text-align: center;
    }
    
    .dashboard-preview {
        transform: none;
    }
    
    .dashboard-preview:hover {
        transform: none;
    }
}

@media (max-width: 480px) {
    :root {
        --font-size-5xl: 2rem;
        --font-size-4xl: 1.75rem;
        --font-size-3xl: 1.5rem;
        --font-size-2xl: 1.25rem;
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .btn-large {
        padding: var(--space-md) var(--space-lg);
        font-size: var(--font-size-sm);
    }
    
    .section-title {
        font-size: var(--font-size-3xl);
    }
    
    .section-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .story-quote {
        font-size: var(--font-size-base);
    }
    
    .modal-content {
        margin: var(--space-md);
    }
}