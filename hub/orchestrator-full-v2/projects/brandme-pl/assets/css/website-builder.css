/* ===== WEBSITE BUILDER SPECIFIC STYLES ===== */

.builder-body {
    background: #0a0a0f;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== BUILDER NAVIGATION ===== */
.builder-nav {
    background: rgba(10, 10, 15, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    padding: var(--space-md) 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
}

.builder-nav .nav-container {
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    align-items: center;
    gap: var(--space-xl);
}

.brand-link {
    text-decoration: none;
    color: inherit;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.builder-progress {
    justify-self: center;
}

.progress-steps {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-xs);
    opacity: 0.5;
    transition: all var(--transition-base);
    cursor: pointer;
}

.step.active {
    opacity: 1;
}

.step.completed {
    opacity: 0.8;
}

.step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--font-size-sm);
    color: var(--gray-400);
    transition: all var(--transition-base);
}

.step.active .step-number {
    background: var(--primary-gradient);
    border-color: var(--primary-600);
    color: white;
}

.step.completed .step-number {
    background: var(--success-500);
    border-color: var(--success-500);
    color: white;
}

.step.completed .step-number::before {
    content: '✓';
    font-size: var(--font-size-xs);
}

.step-label {
    font-size: var(--font-size-xs);
    color: var(--gray-400);
    font-weight: 500;
    white-space: nowrap;
}

.step.active .step-label {
    color: var(--gray-200);
}

.builder-actions {
    justify-self: end;
    display: flex;
    gap: var(--space-md);
}

/* ===== BUILDER CONTAINER ===== */
.builder-container {
    display: grid;
    grid-template-columns: 450px 1fr;
    height: 100vh;
    padding-top: 80px;
}

/* ===== SIDEBAR ===== */
.builder-sidebar {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-right: 1px solid var(--glass-border);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-xl);
}

.builder-step {
    display: none;
}

.builder-step.active {
    display: block;
    animation: slideInUp 0.5s ease-out;
}

.step-header {
    text-align: center;
    margin-bottom: var(--space-2xl);
}

.step-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-xl);
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-lg);
    font-size: var(--font-size-2xl);
    color: white;
}

.step-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: white;
    margin-bottom: var(--space-sm);
}

.step-description {
    color: var(--gray-300);
    line-height: 1.6;
}

/* ===== FORM STYLES ===== */
.builder-form {
    max-width: 100%;
}

.form-group {
    margin-bottom: var(--space-lg);
}

.form-group label {
    display: block;
    color: var(--gray-200);
    font-weight: 500;
    margin-bottom: var(--space-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    color: var(--gray-100);
    font-size: var(--font-size-base);
    transition: all var(--transition-base);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-600);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--gray-500);
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--border-radius-md);
    transition: background var(--transition-base);
}

.checkbox-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.checkbox-item input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--glass-border);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-base);
}

.checkbox-item input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-gradient);
    border-color: var(--primary-600);
}

.checkbox-item input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: white;
    font-size: var(--font-size-xs);
    font-weight: bold;
}

/* ===== AI SUGGESTION ===== */
.ai-suggestion {
    background: rgba(99, 102, 241, 0.1);
    border: 1px solid rgba(99, 102, 241, 0.3);
    border-radius: var(--border-radius-xl);
    padding: var(--space-lg);
    margin-top: var(--space-xl);
    display: flex;
    gap: var(--space-md);
}

.ai-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.ai-content h4 {
    color: var(--primary-400);
    font-weight: 600;
    margin-bottom: var(--space-sm);
}

.ai-content p {
    color: var(--gray-300);
    line-height: 1.6;
}

/* ===== TEMPLATE SELECTION ===== */
.ai-recommendation {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: var(--border-radius-lg);
    padding: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.recommendation-header {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    color: var(--success-400);
    font-weight: 600;
    margin-bottom: var(--space-sm);
}

.template-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-lg);
}

.template-option {
    border: 2px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    padding: var(--space-lg);
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
}

.template-option:hover {
    border-color: var(--primary-600);
    transform: translateY(-2px);
}

.template-option.selected {
    border-color: var(--primary-600);
    background: rgba(99, 102, 241, 0.05);
}

.template-option.recommended::before {
    content: 'Rekomendowane';
    position: absolute;
    top: -8px;
    left: var(--space-lg);
    background: var(--success-500);
    color: white;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.template-preview {
    margin-bottom: var(--space-lg);
}

.template-screen {
    width: 100%;
    height: 200px;
    background: var(--gray-800);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    position: relative;
}

.template-header {
    height: 40px;
    background: var(--gray-700);
    border-bottom: 1px solid var(--gray-600);
}

.template-hero {
    padding: var(--space-lg);
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.template-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-600);
    border-radius: 50%;
}

.template-info {
    flex: 1;
}

.template-name {
    height: 12px;
    background: var(--gray-600);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--space-xs);
    width: 80%;
}

.template-title {
    height: 8px;
    background: var(--gray-700);
    border-radius: var(--border-radius-sm);
    width: 60%;
}

.template-content {
    padding: 0 var(--space-lg) var(--space-lg);
}

.template-section {
    height: 10px;
    background: var(--gray-700);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--space-sm);
}

.template-section.short {
    width: 70%;
}

.template-info h4 {
    color: white;
    font-weight: 600;
    margin-bottom: var(--space-xs);
}

.template-info p {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

.template-badge {
    display: inline-block;
    background: var(--success-500);
    color: white;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    margin-top: var(--space-sm);
}

/* ===== CONTENT GENERATION ===== */
.content-sections {
    display: flex;
    flex-direction: column;
    gap: var(--space-xl);
}

.content-section {
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    padding: var(--space-lg);
    background: rgba(255, 255, 255, 0.02);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-lg);
}

.section-header h4 {
    color: white;
    font-weight: 600;
}

.btn-ai-generate {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    transition: all var(--transition-base);
}

.btn-ai-generate:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.generated-content textarea {
    width: 100%;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: var(--space-md);
    color: var(--gray-100);
    resize: vertical;
    min-height: 60px;
}

.content-actions {
    display: flex;
    gap: var(--space-sm);
    margin-top: var(--space-md);
}

.btn-regenerate,
.btn-copy,
.btn-add-skill {
    background: rgba(255, 255, 255, 0.1);
    color: var(--gray-300);
    border: 1px solid var(--glass-border);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    transition: all var(--transition-base);
}

.btn-regenerate:hover,
.btn-copy:hover,
.btn-add-skill:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
}

.skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-sm);
    min-height: 40px;
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
}

.skill-tag {
    background: var(--primary-gradient);
    color: white;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.skill-remove {
    cursor: pointer;
    opacity: 0.7;
    transition: opacity var(--transition-base);
}

.skill-remove:hover {
    opacity: 1;
}

.ai-writing-tips {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: var(--border-radius-lg);
    padding: var(--space-lg);
    margin-top: var(--space-xl);
}

.ai-writing-tips h4 {
    color: var(--warning-400);
    margin-bottom: var(--space-sm);
}

.ai-writing-tips ul {
    list-style: none;
    padding: 0;
}

.ai-writing-tips li {
    color: var(--gray-300);
    margin-bottom: var(--space-xs);
    padding-left: var(--space-lg);
    position: relative;
}

.ai-writing-tips li::before {
    content: '•';
    color: var(--warning-400);
    position: absolute;
    left: 0;
}

/* ===== STYLING OPTIONS ===== */
.styling-options {
    display: flex;
    flex-direction: column;
    gap: var(--space-2xl);
}

.style-group h4 {
    color: white;
    font-weight: 600;
    margin-bottom: var(--space-lg);
}

.color-schemes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--space-md);
}

.color-scheme {
    text-align: center;
    padding: var(--space-md);
    border: 2px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all var(--transition-base);
}

.color-scheme:hover,
.color-scheme.active {
    border-color: var(--primary-600);
}

.color-preview {
    display: flex;
    gap: var(--space-xs);
    justify-content: center;
    margin-bottom: var(--space-sm);
}

.color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
}

.color-scheme span {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
}

.font-options {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.font-option {
    padding: var(--space-md);
    border: 2px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all var(--transition-base);
}

.font-option:hover,
.font-option.active {
    border-color: var(--primary-600);
}

.font-option span {
    color: var(--gray-200);
    font-size: var(--font-size-lg);
}

.layout-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: var(--space-md);
}

.layout-option {
    text-align: center;
    padding: var(--space-md);
    border: 2px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all var(--transition-base);
}

.layout-option:hover,
.layout-option.active {
    border-color: var(--primary-600);
}

.layout-preview {
    width: 60px;
    height: 40px;
    background: var(--gray-800);
    border-radius: var(--border-radius-sm);
    margin: 0 auto var(--space-sm);
    padding: var(--space-xs);
}

.layout-content {
    width: 100%;
    height: 100%;
    display: flex;
    gap: 2px;
}

.layout-content.centered {
    flex-direction: column;
    align-items: center;
}

.layout-content.sidebar {
    flex-direction: row;
}

.layout-content.grid {
    flex-wrap: wrap;
}

.layout-sidebar {
    width: 20%;
    background: var(--gray-600);
    border-radius: 1px;
}

.layout-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1px;
}

.layout-element {
    background: var(--gray-600);
    border-radius: 1px;
    flex: 1;
}

.layout-content.centered .layout-element {
    width: 80%;
    height: 30%;
    margin-bottom: 1px;
}

.layout-content.grid .layout-element {
    width: 48%;
    height: 48%;
}

.layout-option span {
    color: var(--gray-300);
    font-size: var(--font-size-xs);
}

/* ===== PUBLICATION OPTIONS ===== */
.publication-options {
    display: flex;
    flex-direction: column;
    gap: var(--space-2xl);
}

.domain-setup h4,
.seo-settings h4,
.integrations h4 {
    color: white;
    font-weight: 600;
    margin-bottom: var(--space-lg);
}

.domain-options {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.domain-option {
    position: relative;
}

.domain-option input[type="radio"] {
    display: none;
}

.domain-option label {
    display: block;
    padding: var(--space-lg);
    border: 2px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all var(--transition-base);
}

.domain-option input[type="radio"]:checked + label {
    border-color: var(--primary-600);
    background: rgba(99, 102, 241, 0.05);
}

.domain-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.domain-type {
    color: white;
    font-weight: 600;
}

.domain-example {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

.domain-price {
    color: var(--success-400);
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.domain-input {
    margin-top: var(--space-lg);
}

.domain-input label {
    display: block;
    color: var(--gray-200);
    font-weight: 500;
    margin-bottom: var(--space-sm);
}

.domain-input-group {
    display: flex;
    align-items: center;
    gap: 0;
    margin-bottom: var(--space-sm);
}

.domain-input-group input {
    flex: 1;
    border-radius: var(--border-radius-lg) 0 0 var(--border-radius-lg);
    border-right: none;
}

.domain-suffix {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-left: none;
    border-right: none;
    padding: var(--space-md);
    color: var(--gray-400);
    display: flex;
    align-items: center;
}

.btn-check-availability {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: var(--space-md) var(--space-lg);
    border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-weight: 600;
    transition: all var(--transition-base);
}

.btn-check-availability:hover {
    background: var(--primary-700);
}

.availability-result {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    color: var(--success-400);
    font-size: var(--font-size-sm);
    opacity: 0;
    transition: opacity var(--transition-base);
}

.availability-result.show {
    opacity: 1;
}

.integration-grid {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.integration-item {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    padding: var(--space-lg);
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
}

.integration-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-lg);
    background: var(--glass-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--primary-400);
}

.integration-info {
    flex: 1;
}

.integration-info h5 {
    color: white;
    font-weight: 600;
    margin-bottom: var(--space-xs);
}

.integration-info p {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

.integration-toggle {
    position: relative;
}

.integration-toggle input[type="checkbox"] {
    display: none;
}

.integration-toggle label {
    width: 50px;
    height: 24px;
    background: var(--gray-600);
    border-radius: 12px;
    cursor: pointer;
    position: relative;
    transition: background var(--transition-base);
}

.integration-toggle label::after {
    content: '';
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    position: absolute;
    top: 2px;
    left: 2px;
    transition: transform var(--transition-base);
}

.integration-toggle input[type="checkbox"]:checked + label {
    background: var(--primary-600);
}

.integration-toggle input[type="checkbox"]:checked + label::after {
    transform: translateX(26px);
}

/* ===== NAVIGATION BUTTONS ===== */
.builder-navigation {
    padding: var(--space-xl);
    border-top: 1px solid var(--glass-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.btn-nav {
    padding: var(--space-md) var(--space-xl);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    background: var(--glass-bg);
    color: var(--gray-200);
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    transition: all var(--transition-base);
}

.btn-nav:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.btn-nav:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-publish {
    padding: var(--space-md) var(--space-xl);
    font-size: var(--font-size-base);
}

/* ===== PREVIEW PANEL ===== */
.builder-preview {
    display: flex;
    flex-direction: column;
    background: var(--gray-900);
    position: relative;
}

.preview-header {
    background: var(--glass-bg);
    border-bottom: 1px solid var(--glass-border);
    padding: var(--space-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.device-selector {
    display: flex;
    gap: var(--space-sm);
}

.device-btn {
    width: 40px;
    height: 40px;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    background: var(--glass-bg);
    color: var(--gray-400);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-base);
}

.device-btn:hover,
.device-btn.active {
    border-color: var(--primary-600);
    color: var(--primary-400);
}

.preview-url {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    background: var(--gray-800);
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--border-radius-lg);
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

.preview-content {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-xl);
}

.preview-frame {
    width: 100%;
    height: 100%;
    max-width: 1200px;
    background: white;
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transition: all var(--transition-base);
}

.preview-frame.tablet {
    max-width: 768px;
    height: 80%;
}

.preview-frame.mobile {
    max-width: 375px;
    height: 70%;
}

#website-preview {
    width: 100%;
    height: 100%;
    border: none;
}

.preview-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: var(--gray-400);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-lg);
    font-size: var(--font-size-xl);
    color: white;
}

/* ===== AI PROCESSING MODAL ===== */
.ai-processing {
    text-align: center;
    padding: var(--space-3xl);
}

.ai-brain {
    position: relative;
    margin-bottom: var(--space-2xl);
}

.brain-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: var(--font-size-3xl);
    color: white;
}

.processing-dots {
    display: flex;
    justify-content: center;
    gap: var(--space-sm);
    margin-top: var(--space-lg);
}

.processing-dots span {
    width: 8px;
    height: 8px;
    background: var(--primary-600);
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
}

.processing-dots span:nth-child(1) { animation-delay: -0.32s; }
.processing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% { 
        transform: scale(0);
    } 40% { 
        transform: scale(1.0);
    }
}

.ai-processing h3 {
    color: white;
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--space-2xl);
}

.processing-steps {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
    margin-bottom: var(--space-2xl);
    text-align: left;
}

.processing-step {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    color: var(--gray-400);
    transition: color var(--transition-base);
}

.processing-step.active {
    color: var(--primary-400);
}

.processing-step.completed {
    color: var(--success-400);
}

.processing-step i {
    width: 16px;
    text-align: center;
}

.processing-progress {
    margin-top: var(--space-xl);
}

.processing-progress .progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    margin-bottom: var(--space-sm);
}

.processing-progress .progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-sm);
    width: 0%;
    transition: width var(--transition-base);
}

.progress-text {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .builder-nav .nav-container {
        grid-template-columns: 200px 1fr 250px;
        gap: var(--space-md);
    }
    
    .builder-container {
        grid-template-columns: 400px 1fr;
    }
    
    .progress-steps {
        gap: var(--space-md);
    }
    
    .step-label {
        display: none;
    }
}

@media (max-width: 768px) {
    .builder-nav .nav-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--space-md);
    }
    
    .builder-actions {
        justify-self: center;
    }
    
    .progress-steps {
        justify-content: center;
    }
    
    .builder-container {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr auto;
    }
    
    .builder-sidebar {
        order: 2;
        max-height: 50vh;
    }
    
    .builder-preview {
        order: 1;
    }
    
    .preview-content {
        padding: var(--space-md);
    }
    
    .template-grid {
        grid-template-columns: 1fr;
    }
    
    .color-schemes {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
    
    .layout-options {
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    }
}