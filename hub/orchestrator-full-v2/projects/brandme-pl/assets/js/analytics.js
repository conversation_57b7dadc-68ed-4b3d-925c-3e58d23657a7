/**
 * Analytics Dashboard - BrandMe.pl
 * AI-Powered Brand Analytics and Insights
 */

class AnalyticsDashboard {
    constructor() {
        this.currentPeriod = '30d';
        this.brandScore = 87;
        this.charts = {};
        this.metricsData = {};
        this.isCalculating = false;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeCharts();
        this.loadAnalyticsData();
        this.startRealTimeUpdates();
    }

    bindEvents() {
        // Navigation actions
        document.getElementById('exportReport')?.addEventListener('click', () => {
            this.exportReport();
        });
        
        document.getElementById('calculateScore')?.addEventListener('click', () => {
            this.recalculateBrandScore();
        });

        // Quick actions
        document.getElementById('analyzeCompetitors')?.addEventListener('click', () => {
            this.analyzeCompetitors();
        });
        
        document.getElementById('contentGaps')?.addEventListener('click', () => {
            this.analyzeContentGaps();
        });
        
        document.getElementById('trendAnalysis')?.addEventListener('click', () => {
            this.analyzeTrends();
        });
        
        document.getElementById('optimizationTips')?.addEventListener('click', () => {
            this.generateOptimizationTips();
        });

        // Period selectors
        document.querySelectorAll('select[id$="Period"]').forEach(select => {
            select.addEventListener('change', (e) => {
                const period = e.target.value;
                const metricType = e.target.id.replace('Period', '');
                this.updateMetricData(metricType, period);
            });
        });

        // Chart controls
        document.querySelectorAll('.chart-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const period = e.target.getAttribute('data-period');
                this.updateChartPeriod(period);
                
                // Update active state
                document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
            });
        });

        // Content performance filters
        document.getElementById('contentPlatform')?.addEventListener('change', (e) => {
            this.filterContentPerformance('platform', e.target.value);
        });
        
        document.getElementById('contentPeriod')?.addEventListener('change', (e) => {
            this.filterContentPerformance('period', e.target.value);
        });

        // Insight tabs
        document.querySelectorAll('.insight-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabId = e.target.getAttribute('data-tab');
                this.switchInsightTab(tabId);
            });
        });

        // Refresh insights
        document.getElementById('refreshInsights')?.addEventListener('click', () => {
            this.refreshAIInsights();
        });

        // Modal close
        document.querySelector('.modal-close')?.addEventListener('click', () => {
            this.closeModal('scoreCalculationModal');
        });
    }

    // ===== CHART INITIALIZATION ===== //
    initializeCharts() {
        this.initializeMetricCharts();
        this.initializeGrowthChart();
        this.initializePlatformChart();
    }

    initializeMetricCharts() {
        const chartConfigs = [
            { id: 'reachChart', color: '#6366f1', data: this.generateMockData(30) },
            { id: 'engagementChart', color: '#ec4899', data: this.generateMockData(30) },
            { id: 'rateChart', color: '#10b981', data: this.generateMockData(30) },
            { id: 'followersChart', color: '#f59e0b', data: this.generateMockData(30) }
        ];

        chartConfigs.forEach(config => {
            const canvas = document.getElementById(config.id);
            if (canvas) {
                this.charts[config.id] = this.createMiniChart(canvas, config.color, config.data);
            }
        });
    }

    createMiniChart(canvas, color, data) {
        const ctx = canvas.getContext('2d');
        return new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    borderColor: color,
                    backgroundColor: `${color}20`,
                    borderWidth: 2,
                    tension: 0.4,
                    pointRadius: 0,
                    pointHoverRadius: 4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: { enabled: false }
                },
                scales: {
                    x: { display: false },
                    y: { display: false }
                },
                elements: {
                    point: { radius: 0 }
                }
            }
        });
    }

    initializeGrowthChart() {
        const canvas = document.getElementById('growthChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const data = this.generateGrowthData();

        this.charts.growthChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [
                    {
                        label: 'Zasięg',
                        data: data.reach,
                        borderColor: '#6366f1',
                        backgroundColor: '#6366f120',
                        borderWidth: 3,
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Engagement',
                        data: data.engagement,
                        borderColor: '#ec4899',
                        backgroundColor: '#ec489920',
                        borderWidth: 3,
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        labels: { color: '#e2e8f0' }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#374151',
                        borderWidth: 1
                    }
                },
                scales: {
                    x: {
                        grid: { color: '#374151' },
                        ticks: { color: '#9ca3af' }
                    },
                    y: {
                        grid: { color: '#374151' },
                        ticks: { color: '#9ca3af' }
                    }
                }
            }
        });
    }

    initializePlatformChart() {
        const canvas = document.getElementById('platformChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const data = {
            labels: ['LinkedIn', 'Instagram', 'Twitter', 'YouTube', 'Blog'],
            datasets: [{
                data: [45, 25, 15, 10, 5],
                backgroundColor: [
                    '#0077b5',
                    '#e4405f',
                    '#1da1f2',
                    '#ff0000',
                    '#6366f1'
                ],
                borderWidth: 2,
                borderColor: '#1f2937'
            }]
        };

        this.charts.platformChart = new Chart(ctx, {
            type: 'doughnut',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff'
                    }
                }
            }
        });

        // Create custom legend
        this.createPlatformLegend(data);
    }

    createPlatformLegend(data) {
        const legendContainer = document.getElementById('platformLegend');
        if (!legendContainer) return;

        legendContainer.innerHTML = '';
        
        data.labels.forEach((label, index) => {
            const legendItem = document.createElement('div');
            legendItem.className = 'legend-item';
            legendItem.innerHTML = `
                <div class="legend-color" style="background-color: ${data.datasets[0].backgroundColor[index]}"></div>
                <span>${label}</span>
            `;
            legendContainer.appendChild(legendItem);
        });
    }

    // ===== DATA MANAGEMENT ===== //
    loadAnalyticsData() {
        // Simulate loading analytics data
        this.metricsData = {
            reach: { value: 124500, change: 18.2, trend: 'up' },
            engagement: { value: 8947, change: 24.8, trend: 'up' },
            rate: { value: 7.18, change: 2.1, trend: 'up' },
            followers: { value: 1247, change: 31.5, trend: 'up' }
        };

        this.updateMetricDisplays();
        this.loadTopContent();
    }

    updateMetricDisplays() {
        Object.keys(this.metricsData).forEach(metric => {
            const data = this.metricsData[metric];
            const valueElement = document.getElementById(`total${this.capitalize(metric)}`);
            const changeElement = valueElement?.parentElement.querySelector('.metric-change span');
            
            if (valueElement) {
                if (metric === 'followers') {
                    valueElement.textContent = `+${data.value.toLocaleString()}`;
                } else if (metric === 'rate') {
                    valueElement.textContent = `${data.value}%`;
                } else {
                    valueElement.textContent = data.value > 1000 
                        ? `${(data.value / 1000).toFixed(1)}k` 
                        : data.value.toLocaleString();
                }
            }
            
            if (changeElement) {
                changeElement.textContent = `+${data.change}%`;
            }
        });
    }

    updateMetricData(metricType, period) {
        // Simulate updating metric data for different periods
        const mockMultipliers = {
            '7d': 0.3,
            '30d': 1.0,
            '90d': 2.8
        };

        const multiplier = mockMultipliers[period] || 1.0;
        const baseData = this.metricsData[metricType];
        
        if (baseData) {
            const newValue = Math.round(baseData.value * multiplier);
            const element = document.getElementById(`total${this.capitalize(metricType)}`);
            
            if (element) {
                element.textContent = newValue > 1000 
                    ? `${(newValue / 1000).toFixed(1)}k` 
                    : newValue.toLocaleString();
            }
        }

        // Update corresponding mini chart
        const chartId = `${metricType}Chart`;
        if (this.charts[chartId]) {
            const newData = this.generateMockData(period === '7d' ? 7 : period === '30d' ? 30 : 90);
            this.charts[chartId].data.datasets[0].data = newData.values;
            this.charts[chartId].update();
        }
    }

    updateChartPeriod(period) {
        this.currentPeriod = period;
        
        // Update growth chart
        if (this.charts.growthChart) {
            const newData = this.generateGrowthData(period);
            this.charts.growthChart.data.labels = newData.labels;
            this.charts.growthChart.data.datasets[0].data = newData.reach;
            this.charts.growthChart.data.datasets[1].data = newData.engagement;
            this.charts.growthChart.update();
        }
    }

    loadTopContent() {
        const contentList = document.getElementById('topContentList');
        if (!contentList) return;

        const topContent = [
            {
                rank: 1,
                title: 'Jak AI zmienia personal branding w 2025',
                platform: 'LinkedIn',
                date: '2 dni temu',
                reach: 15200,
                engagement: 1247,
                rate: 8.2
            },
            {
                rank: 2,
                title: 'Moje 5 sekretów efektywnego networkingu',
                platform: 'LinkedIn',
                date: '1 tydzień temu',
                reach: 12800,
                engagement: 982,
                rate: 7.7
            },
            {
                rank: 3,
                title: 'Behind the scenes: Dzień z życia marketera',
                platform: 'Instagram',
                date: '3 dni temu',
                reach: 8900,
                engagement: 743,
                rate: 8.3
            },
            {
                rank: 4,
                title: 'Thread o trendach w content marketingu',
                platform: 'Twitter',
                date: '5 dni temu',
                reach: 6700,
                engagement: 456,
                rate: 6.8
            },
            {
                rank: 5,
                title: 'Case study: Jak zwiększyłem zasięg o 200%',
                platform: 'LinkedIn',
                date: '1 tydzień temu',
                reach: 11300,
                engagement: 891,
                rate: 7.9
            }
        ];

        contentList.innerHTML = '';
        
        topContent.forEach(item => {
            const contentItem = document.createElement('div');
            contentItem.className = 'content-item';
            contentItem.innerHTML = `
                <div class="content-rank">${item.rank}</div>
                <div class="content-info">
                    <div class="content-title">${item.title}</div>
                    <div class="content-meta">
                        <span><i class="fab fa-${item.platform.toLowerCase()}"></i> ${item.platform}</span>
                        <span>${item.date}</span>
                    </div>
                </div>
                <div class="content-metrics">
                    <div class="content-metric">
                        <div class="content-metric-value">${item.reach.toLocaleString()}</div>
                        <div class="content-metric-label">Zasięg</div>
                    </div>
                    <div class="content-metric">
                        <div class="content-metric-value">${item.engagement}</div>
                        <div class="content-metric-label">Engagement</div>
                    </div>
                    <div class="content-metric">
                        <div class="content-metric-value">${item.rate}%</div>
                        <div class="content-metric-label">Rate</div>
                    </div>
                </div>
            `;
            contentList.appendChild(contentItem);
        });
    }

    filterContentPerformance(filterType, value) {
        // Simulate filtering content performance data
        console.log(`Filtering content by ${filterType}: ${value}`);
        this.loadTopContent(); // Reload with filters applied
    }

    // ===== BRAND SCORE CALCULATION ===== //
    async recalculateBrandScore() {
        if (this.isCalculating) return;
        
        this.isCalculating = true;
        this.showModal('scoreCalculationModal');
        
        try {
            await this.performBrandScoreCalculation();
            this.showNotification('Brand Score został zaktualizowany!', 'success');
        } catch (error) {
            this.showNotification('Błąd podczas kalkulacji Brand Score', 'error');
        } finally {
            this.isCalculating = false;
            this.closeModal('scoreCalculationModal');
        }
    }

    async performBrandScoreCalculation() {
        const steps = [
            { text: 'Analizuję Twoje profile w social media...', duration: 2000 },
            { text: 'Obliczam metryki engagement...', duration: 1500 },
            { text: 'AI analizuje jakość contentu...', duration: 2000 },
            { text: 'Finalizuję wynik Brand Score...', duration: 1000 }
        ];

        const progressBar = document.getElementById('calculationProgress');
        const progressText = document.getElementById('calculationText');
        const progressSteps = document.querySelectorAll('.progress-step');

        for (let i = 0; i < steps.length; i++) {
            // Update progress text
            if (progressText) {
                progressText.textContent = steps[i].text;
            }

            // Update progress bar
            const progress = ((i + 1) / steps.length) * 100;
            if (progressBar) {
                progressBar.style.width = `${progress}%`;
            }

            // Update step indicators
            if (progressSteps[i]) {
                progressSteps[i].classList.add('active');
                if (i > 0) {
                    progressSteps[i - 1].classList.remove('active');
                    progressSteps[i - 1].classList.add('completed');
                }
            }

            // Simulate AI analysis with actual AI service
            if (window.aiService) {
                try {
                    const analysisPrompt = `Analyze personal brand metrics: reach ${this.metricsData.reach.value}, engagement ${this.metricsData.engagement.value}, rate ${this.metricsData.rate.value}%. Provide score out of 100.`;
                    const aiResponse = await window.aiService.generateText(analysisPrompt, {
                        maxLength: 200,
                        temperature: 0.3
                    });
                    
                    // Extract score from AI response
                    const scoreMatch = aiResponse.match(/(\d+)(?:\/100|%|\s*points?)/i);
                    if (scoreMatch) {
                        this.brandScore = Math.min(100, Math.max(0, parseInt(scoreMatch[1])));
                    }
                } catch (error) {
                    console.log('AI analysis unavailable, using calculated score');
                }
            }

            await new Promise(resolve => setTimeout(resolve, steps[i].duration));
        }

        // Update brand score display
        this.updateBrandScoreDisplay();
    }

    updateBrandScoreDisplay() {
        const scoreValue = document.getElementById('brandScoreValue');
        const scoreLevel = document.getElementById('scoreLevel');
        const scoreChange = document.getElementById('scoreChange');
        const scoreCircle = document.getElementById('brandScoreCircle');

        if (scoreValue) {
            scoreValue.textContent = this.brandScore;
        }

        // Determine score level
        let level = 'Początkujący';
        if (this.brandScore >= 80) level = 'Expert';
        else if (this.brandScore >= 60) level = 'Zaawansowany';
        else if (this.brandScore >= 40) level = 'Średniozaawansowany';

        if (scoreLevel) {
            scoreLevel.textContent = level;
        }

        // Update score circle (conic gradient)
        if (scoreCircle) {
            const angle = (this.brandScore / 100) * 360;
            scoreCircle.style.background = `conic-gradient(var(--primary-600) 0deg ${angle}deg, rgba(255, 255, 255, 0.1) ${angle}deg 360deg)`;
        }

        // Simulate score change
        const change = Math.floor(Math.random() * 15) + 5;
        if (scoreChange) {
            scoreChange.innerHTML = `<i class="fas fa-arrow-up"></i> +${change} this month`;
        }
    }

    // ===== AI INSIGHTS ===== //
    switchInsightTab(tabId) {
        // Update active tab
        document.querySelectorAll('.insight-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

        // Update active panel
        document.querySelectorAll('.insight-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        document.getElementById(`${tabId}-panel`).classList.add('active');
    }

    async refreshAIInsights() {
        this.showNotification('AI generuje nowe insights...', 'info');
        
        try {
            if (window.aiService) {
                const profileData = {
                    reach: this.metricsData.reach.value,
                    engagement: this.metricsData.engagement.value,
                    rate: this.metricsData.rate.value,
                    followers: this.metricsData.followers.value,
                    brandScore: this.brandScore
                };

                const insights = await window.aiService.analyzeProfile(profileData);
                this.updateInsightsPanels(insights);
                this.showNotification('AI insights zostały zaktualizowane!', 'success');
            } else {
                this.showNotification('AI service niedostępny, używam przykładowych danych', 'warning');
                this.updateInsightsPanels(this.getMockInsights());
            }
        } catch (error) {
            console.error('Error refreshing insights:', error);
            this.showNotification('Błąd podczas generowania insights', 'error');
        }
    }

    updateInsightsPanels(insights) {
        // Update opportunities panel with AI insights
        if (insights.recommendations) {
            this.updateOpportunitiesPanel(insights.recommendations);
        }
        
        // Update optimization suggestions
        if (insights.improvements) {
            this.updateOptimizationPanel(insights.improvements);
        }
    }

    getMockInsights() {
        return {
            recommendations: [
                'Publikuj częściej w optymalnych godzinach',
                'Zwiększ interakcję z komentarzami',
                'Dodaj więcej video content'
            ],
            improvements: [
                'Zwiększ częstotliwość publikacji o 50%',
                'Dodaj więcej pytań w postach',
                'Wykorzystaj trending hashtagi'
            ]
        };
    }

    // ===== COMPETITIVE ANALYSIS ===== //
    async analyzeCompetitors() {
        this.showNotification('AI analizuje konkurencję...', 'info');
        
        try {
            if (window.aiService) {
                const prompt = `Analyze competitors in personal branding space for someone with ${this.brandScore} brand score in technology industry. Provide 3 key insights.`;
                const analysis = await window.aiService.generateText(prompt, {
                    maxLength: 500,
                    temperature: 0.6
                });
                
                console.log('Competitor analysis:', analysis);
                this.showNotification('Analiza konkurencji została zakończona!', 'success');
            }
        } catch (error) {
            this.showNotification('Błąd podczas analizy konkurencji', 'error');
        }
    }

    async analyzeContentGaps() {
        this.showNotification('AI analizuje luki w contencie...', 'info');
        
        try {
            if (window.aiService) {
                const prompt = `Analyze content gaps for personal brand with current performance: ${this.metricsData.engagement.value} engagement, ${this.metricsData.reach.value} reach. Suggest content improvements.`;
                const analysis = await window.aiService.generateText(prompt, {
                    maxLength: 400,
                    temperature: 0.7
                });
                
                console.log('Content gap analysis:', analysis);
                this.showNotification('Analiza luk w contencie została zakończona!', 'success');
            }
        } catch (error) {
            this.showNotification('Błąd podczas analizy contentu', 'error');
        }
    }

    async analyzeTrends() {
        this.showNotification('AI analizuje trendy branżowe...', 'info');
        
        try {
            if (window.aiService) {
                const prompt = "Analyze current trends in personal branding and content marketing for 2025. Provide 5 key trends with growth percentages.";
                const trends = await window.aiService.generateText(prompt, {
                    maxLength: 600,
                    temperature: 0.8
                });
                
                console.log('Trend analysis:', trends);
                this.showNotification('Analiza trendów została zakończona!', 'success');
            }
        } catch (error) {
            this.showNotification('Błąd podczas analizy trendów', 'error');
        }
    }

    async generateOptimizationTips() {
        this.showNotification('AI generuje rekomendacje optymalizacji...', 'info');
        
        try {
            if (window.aiService) {
                const currentPerformance = {
                    brandScore: this.brandScore,
                    engagement: this.metricsData.engagement.value,
                    reach: this.metricsData.reach.value
                };
                
                const tips = await window.aiService.optimizeContent(
                    JSON.stringify(currentPerformance), 
                    'engagement'
                );
                
                console.log('Optimization tips:', tips);
                this.showNotification('Rekomendacje optymalizacji zostały wygenerowane!', 'success');
            }
        } catch (error) {
            this.showNotification('Błąd podczas generowania rekomendacji', 'error');
        }
    }

    // ===== EXPORT FUNCTIONALITY ===== //
    exportReport() {
        const reportData = {
            brandScore: this.brandScore,
            metrics: this.metricsData,
            period: this.currentPeriod,
            exportDate: new Date().toISOString(),
            charts: {
                growth: this.charts.growthChart?.data,
                platform: this.charts.platformChart?.data
            }
        };

        // Create downloadable report
        const dataStr = JSON.stringify(reportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `brandme-analytics-report-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        URL.revokeObjectURL(url);
        this.showNotification('Raport został wyeksportowany!', 'success');
    }

    // ===== REAL-TIME UPDATES ===== //
    startRealTimeUpdates() {
        // Simulate real-time data updates every 30 seconds
        setInterval(() => {
            this.updateRealtimeMetrics();
        }, 30000);
    }

    updateRealtimeMetrics() {
        // Simulate small changes in metrics
        Object.keys(this.metricsData).forEach(metric => {
            const currentValue = this.metricsData[metric].value;
            const variation = currentValue * 0.001; // 0.1% variation
            const change = (Math.random() - 0.5) * variation;
            
            this.metricsData[metric].value = Math.round(currentValue + change);
        });

        this.updateMetricDisplays();
    }

    // ===== UTILITY METHODS ===== //
    generateMockData(days) {
        const labels = [];
        const values = [];
        
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            labels.push(date.toLocaleDateString('pl-PL', { day: '2-digit', month: '2-digit' }));
            
            // Generate realistic growth pattern
            const baseValue = 1000 + (Math.random() * 500);
            const trend = (days - i) / days; // Upward trend
            const noise = (Math.random() - 0.5) * 200;
            values.push(Math.round(baseValue + (trend * 300) + noise));
        }
        
        return { labels, values };
    }

    generateGrowthData(period = '30d') {
        const days = period === '7d' ? 7 : period === '30d' ? 30 : 365;
        const labels = [];
        const reach = [];
        const engagement = [];
        
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            
            if (days <= 30) {
                labels.push(date.toLocaleDateString('pl-PL', { day: '2-digit', month: '2-digit' }));
            } else {
                labels.push(date.toLocaleDateString('pl-PL', { month: 'short' }));
            }
            
            // Generate realistic data with growth trend
            const reachBase = 5000 + (Math.random() * 2000);
            const engagementBase = 400 + (Math.random() * 200);
            const growthFactor = (days - i) / days;
            
            reach.push(Math.round(reachBase + (growthFactor * 3000)));
            engagement.push(Math.round(engagementBase + (growthFactor * 300)));
        }
        
        return { labels, reach, engagement };
    }

    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.classList.add('modal-open');
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('active');
            document.body.classList.remove('modal-open');
        }
    }

    capitalize(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    showNotification(message, type = 'info') {
        if (window.brandMePlatform) {
            window.brandMePlatform.showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Initialize Analytics Dashboard
const analyticsDashboard = new AnalyticsDashboard();

// Export for global use
window.AnalyticsDashboard = AnalyticsDashboard;