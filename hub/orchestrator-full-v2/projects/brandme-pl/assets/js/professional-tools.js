/**
 * Professional Tools - BrandMe.pl
 * Media Kit, Speaker <PERSON>, <PERSON> Kit, <PERSON> Kit, Proposals Generator
 */

class ProfessionalTools {
    constructor() {
        this.currentCategory = 'media-kit';
        this.activeTab = 'info';
        this.formData = {};
        this.speechTopics = [];
        this.portfolioItems = [];
        this.expertiseAreas = [];
        this.isGenerating = false;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeDefaultData();
        this.updateStats();
    }

    bindEvents() {
        // Category navigation
        document.querySelectorAll('.category-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const category = e.currentTarget.getAttribute('data-category');
                this.switchCategory(category);
            });
        });

        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabName = e.target.getAttribute('data-tab');
                this.switchTab(tabName);
            });
        });

        // Navigation actions
        document.getElementById('exportAll')?.addEventListener('click', () => {
            this.exportAllKits();
        });

        document.getElementById('generateKit')?.addEventListener('click', () => {
            this.openAIGenerator();
        });

        // Media Kit form handlers
        this.bindMediaKitEvents();
        
        // Speaker Kit form handlers
        this.bindSpeakerKitEvents();

        // Template library
        document.querySelectorAll('.template-use').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const template = e.target.closest('.template-item').querySelector('span').textContent;
                this.useTemplate(template);
            });
        });

        // Modal handlers
        this.bindModalEvents();
    }

    bindMediaKitEvents() {
        // AI Bio Generator
        document.querySelector('.btn-ai-assist[data-target="media-bio"]')?.addEventListener('click', () => {
            this.generateAIBio();
        });

        // Expertise management
        document.getElementById('add-expertise-btn')?.addEventListener('click', () => {
            this.addExpertiseArea();
        });

        document.getElementById('expertise-input')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addExpertiseArea();
            }
        });

        // Portfolio management
        document.getElementById('add-portfolio')?.addEventListener('click', () => {
            this.addPortfolioItem();
        });

        // File uploads
        document.getElementById('highres-photo')?.addEventListener('change', (e) => {
            this.handleFileUpload(e, 'highres');
        });

        document.getElementById('profile-photo')?.addEventListener('change', (e) => {
            this.handleFileUpload(e, 'profile');
        });

        // Generate Media Kit
        document.getElementById('generate-media-kit')?.addEventListener('click', () => {
            this.generateMediaKit();
        });

        document.getElementById('preview-media-kit')?.addEventListener('click', () => {
            this.previewMediaKit();
        });
    }

    bindSpeakerKitEvents() {
        // Add speech topic
        document.getElementById('add-topic')?.addEventListener('click', () => {
            this.openTopicModal();
        });

        // Topic form submission
        document.getElementById('topic-form')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.addSpeechTopic();
        });

        // Generate Speaker Kit
        document.getElementById('generate-speaker-kit')?.addEventListener('click', () => {
            this.generateSpeakerKit();
        });

        document.getElementById('preview-speaker-kit')?.addEventListener('click', () => {
            this.previewSpeakerKit();
        });

        // Real-time speaker preview
        document.querySelectorAll('#speaker-kit input, #speaker-kit textarea, #speaker-kit select').forEach(input => {
            input.addEventListener('input', () => {
                this.updateSpeakerPreview();
            });
        });
    }

    bindModalEvents() {
        // Close modals
        document.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                this.closeModal(modal.id);
            });
        });

        // Download and share actions
        document.getElementById('downloadKit')?.addEventListener('click', () => {
            this.downloadKit();
        });

        document.getElementById('shareKit')?.addEventListener('click', () => {
            this.shareKit();
        });

        // Click outside to close
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal(modal.id);
                }
            });
        });
    }

    // ===== CATEGORY MANAGEMENT ===== //
    switchCategory(category) {
        // Update active category
        document.querySelectorAll('.category-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`).classList.add('active');

        // Update active section
        document.querySelectorAll('.tool-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(category).classList.add('active');

        this.currentCategory = category;
        this.updateStats();
    }

    switchTab(tabName) {
        // Update active tab
        document.querySelectorAll('.tab-btn').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update active content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        this.activeTab = tabName;
    }

    // ===== MEDIA KIT FUNCTIONALITY ===== //
    async generateAIBio() {
        const bioTextarea = document.getElementById('media-bio');
        const assistBtn = document.querySelector('.btn-ai-assist[data-target="media-bio"]');
        
        if (!bioTextarea || this.isGenerating) return;

        this.isGenerating = true;
        const originalText = assistBtn.innerHTML;
        assistBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generuję...';
        assistBtn.disabled = true;

        try {
            // Collect form data for bio generation
            const bioData = {
                name: document.getElementById('media-name')?.value || '',
                title: document.getElementById('media-title')?.value || '',
                company: document.getElementById('media-company')?.value || '',
                industry: document.getElementById('media-industry')?.value || '',
                tagline: document.getElementById('media-tagline')?.value || '',
                achievements: document.getElementById('media-achievements')?.value || '',
                credentials: document.getElementById('media-credentials')?.value || ''
            };

            if (window.aiService) {
                const generatedBio = await window.aiService.generateBio(bioData, 'media');
                bioTextarea.value = generatedBio.text;
                this.showNotification('Bio zostało wygenerowane przez AI!', 'success');
            } else {
                // Fallback bio generation
                const fallbackBio = this.generateFallbackBio(bioData);
                bioTextarea.value = fallbackBio;
                this.showNotification('Bio zostało wygenerowane (tryb offline)', 'info');
            }
        } catch (error) {
            console.error('Error generating bio:', error);
            this.showNotification('Błąd podczas generowania bio', 'error');
        } finally {
            assistBtn.innerHTML = originalText;
            assistBtn.disabled = false;
            this.isGenerating = false;
        }
    }

    generateFallbackBio(bioData) {
        const { name, title, company, industry, tagline, achievements } = bioData;
        
        return `${name} jest ${title}${company ? ` w ${company}` : ''} z wieloletnim doświadczeniem w branży ${industry}. ${tagline ? tagline + ' ' : ''}Specjalizuje się w dostarczaniu wartościowych rozwiązań i budowaniu silnych relacji biznesowych. ${achievements ? 'Kluczowe osiągnięcia obejmują: ' + achievements + '.' : ''} Jako ekspert w swojej dziedzinie, ${name} regularnie dzieli się wiedzą i wspiera rozwój branży poprzez udział w konferencjach i publikacjach branżowych.`;
    }

    addExpertiseArea() {
        const input = document.getElementById('expertise-input');
        const tagsContainer = document.getElementById('expertise-tags');
        
        if (!input || !tagsContainer) return;

        const expertise = input.value.trim();
        if (expertise && !this.expertiseAreas.includes(expertise)) {
            this.expertiseAreas.push(expertise);
            this.renderExpertiseTags();
            input.value = '';
        }
    }

    renderExpertiseTags() {
        const container = document.getElementById('expertise-tags');
        if (!container) return;

        container.innerHTML = '';
        
        this.expertiseAreas.forEach((area, index) => {
            const tag = document.createElement('div');
            tag.className = 'expertise-tag';
            tag.innerHTML = `
                <span>${area}</span>
                <button type="button" class="remove-tag" onclick="professionalTools.removeExpertiseArea(${index})">
                    <i class="fas fa-times"></i>
                </button>
            `;
            container.appendChild(tag);
        });
    }

    removeExpertiseArea(index) {
        this.expertiseAreas.splice(index, 1);
        this.renderExpertiseTags();
    }

    handleFileUpload(event, type) {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file type
        if (!file.type.startsWith('image/')) {
            this.showNotification('Proszę wybrać plik obrazu', 'error');
            return;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            this.showNotification('Plik jest zbyt duży (max 5MB)', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            // Store image data
            this.formData[`${type}Photo`] = {
                name: file.name,
                data: e.target.result,
                size: file.size
            };

            // Update upload placeholder
            const placeholder = event.target.closest('.upload-placeholder');
            placeholder.innerHTML = `
                <i class="fas fa-check-circle" style="color: var(--success-400);"></i>
                <span style="color: var(--success-400);">${file.name}</span>
            `;

            this.showNotification(`Zdjęcie ${type} zostało przesłane`, 'success');
        };
        reader.readAsDataURL(file);
    }

    addPortfolioItem() {
        const portfolioData = {
            id: Date.now(),
            title: 'Nowy case study',
            description: 'Opis projektu...',
            metrics: {
                impact: '100%',
                duration: '3 miesiące',
                budget: '50k PLN'
            }
        };

        this.portfolioItems.push(portfolioData);
        this.renderPortfolioItems();
    }

    renderPortfolioItems() {
        const container = document.getElementById('portfolio-list');
        if (!container) return;

        container.innerHTML = '';
        
        this.portfolioItems.forEach((item, index) => {
            const portfolioElement = document.createElement('div');
            portfolioElement.className = 'portfolio-item';
            portfolioElement.innerHTML = `
                <div class="portfolio-info">
                    <div class="portfolio-title" contenteditable="true" data-field="title" data-index="${index}">
                        ${item.title}
                    </div>
                    <div class="portfolio-description" contenteditable="true" data-field="description" data-index="${index}">
                        ${item.description}
                    </div>
                    <div class="portfolio-metrics">
                        <span>Impact: ${item.metrics.impact}</span>
                        <span>Czas: ${item.metrics.duration}</span>
                        <span>Budżet: ${item.metrics.budget}</span>
                    </div>
                </div>
                <button class="btn-secondary" onclick="professionalTools.removePortfolioItem(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            container.appendChild(portfolioElement);
        });

        // Bind content editable events
        container.querySelectorAll('[contenteditable="true"]').forEach(element => {
            element.addEventListener('blur', (e) => {
                const field = e.target.getAttribute('data-field');
                const index = parseInt(e.target.getAttribute('data-index'));
                this.portfolioItems[index][field] = e.target.textContent;
            });
        });
    }

    removePortfolioItem(index) {
        this.portfolioItems.splice(index, 1);
        this.renderPortfolioItems();
    }

    async generateMediaKit() {
        if (this.isGenerating) return;

        this.isGenerating = true;
        this.showNotification('Generuję Media Kit...', 'info');

        try {
            // Collect all form data
            const mediaKitData = this.collectMediaKitData();
            
            // Validate required fields
            if (!this.validateMediaKitData(mediaKitData)) {
                this.showNotification('Proszę wypełnić wszystkie wymagane pola', 'error');
                return;
            }

            // Generate kit content
            const kitContent = await this.buildMediaKitContent(mediaKitData);
            
            // Show preview modal
            this.showKitPreview(kitContent, 'Media Kit');
            this.showNotification('Media Kit został wygenerowany!', 'success');

        } catch (error) {
            console.error('Error generating media kit:', error);
            this.showNotification('Błąd podczas generowania Media Kit', 'error');
        } finally {
            this.isGenerating = false;
        }
    }

    collectMediaKitData() {
        return {
            name: document.getElementById('media-name')?.value || '',
            title: document.getElementById('media-title')?.value || '',
            company: document.getElementById('media-company')?.value || '',
            industry: document.getElementById('media-industry')?.value || '',
            tagline: document.getElementById('media-tagline')?.value || '',
            bio: document.getElementById('media-bio')?.value || '',
            expertise: this.expertiseAreas,
            achievements: document.getElementById('media-achievements')?.value || '',
            credentials: document.getElementById('media-credentials')?.value || '',
            contact: {
                email: document.getElementById('contact-email')?.value || '',
                phone: document.getElementById('contact-phone')?.value || '',
                location: document.getElementById('contact-location')?.value || '',
                availability: document.getElementById('contact-availability')?.value || '',
                topics: document.getElementById('preferred-topics')?.value || '',
                fees: document.getElementById('speaking-fees')?.value || ''
            },
            media: {
                website: document.getElementById('website-url')?.value || '',
                linkedin: document.getElementById('linkedin-url')?.value || '',
                twitter: document.getElementById('twitter-url')?.value || '',
                youtube: document.getElementById('youtube-url')?.value || ''
            },
            portfolio: this.portfolioItems,
            photos: {
                highres: this.formData.highresPhoto,
                profile: this.formData.profilePhoto
            }
        };
    }

    validateMediaKitData(data) {
        const required = ['name', 'title', 'industry'];
        return required.every(field => data[field] && data[field].trim().length > 0);
    }

    async buildMediaKitContent(data) {
        const currentDate = new Date().toLocaleDateString('pl-PL');
        
        return `
            <div class="media-kit-header">
                <h1>Media Kit</h1>
                <h2>${data.name}</h2>
                <p class="subtitle">${data.title}${data.company ? ` | ${data.company}` : ''}</p>
                ${data.tagline ? `<p class="tagline">"${data.tagline}"</p>` : ''}
            </div>

            <div class="media-kit-section">
                <h3>O mnie</h3>
                <p>${data.bio}</p>
            </div>

            ${data.expertise.length > 0 ? `
            <div class="media-kit-section">
                <h3>Obszary ekspertyzy</h3>
                <ul>
                    ${data.expertise.map(area => `<li>${area}</li>`).join('')}
                </ul>
            </div>
            ` : ''}

            ${data.achievements ? `
            <div class="media-kit-section">
                <h3>Kluczowe osiągnięcia</h3>
                <div class="achievements">
                    ${data.achievements.split('\n').map(achievement => 
                        achievement.trim() ? `<p>${achievement}</p>` : ''
                    ).join('')}
                </div>
            </div>
            ` : ''}

            ${data.portfolio.length > 0 ? `
            <div class="media-kit-section">
                <h3>Portfolio i case studies</h3>
                ${data.portfolio.map(item => `
                    <div class="portfolio-case">
                        <h4>${item.title}</h4>
                        <p>${item.description}</p>
                        <div class="case-metrics">
                            <span>Impact: ${item.metrics.impact}</span>
                            <span>Czas: ${item.metrics.duration}</span>
                            <span>Budżet: ${item.metrics.budget}</span>
                        </div>
                    </div>
                `).join('')}
            </div>
            ` : ''}

            <div class="media-kit-section">
                <h3>Kontakt</h3>
                <div class="contact-info">
                    <p><strong>Email:</strong> ${data.contact.email}</p>
                    ${data.contact.phone ? `<p><strong>Telefon:</strong> ${data.contact.phone}</p>` : ''}
                    ${data.contact.location ? `<p><strong>Lokalizacja:</strong> ${data.contact.location}</p>` : ''}
                    <p><strong>Dostępność:</strong> ${this.getAvailabilityText(data.contact.availability)}</p>
                </div>
            </div>

            ${Object.values(data.media).some(url => url) ? `
            <div class="media-kit-section">
                <h3>Media społecznościowe</h3>
                <div class="social-links">
                    ${data.media.website ? `<p><strong>Website:</strong> ${data.media.website}</p>` : ''}
                    ${data.media.linkedin ? `<p><strong>LinkedIn:</strong> ${data.media.linkedin}</p>` : ''}
                    ${data.media.twitter ? `<p><strong>Twitter:</strong> ${data.media.twitter}</p>` : ''}
                    ${data.media.youtube ? `<p><strong>YouTube:</strong> ${data.media.youtube}</p>` : ''}
                </div>
            </div>
            ` : ''}

            <div class="media-kit-footer">
                <p><small>Media Kit wygenerowany ${currentDate} | BrandMe.pl</small></p>
            </div>

            <style>
                .media-kit-header { text-align: center; margin-bottom: 2rem; }
                .media-kit-header h1 { color: #6366f1; font-size: 2rem; margin-bottom: 0.5rem; }
                .media-kit-header h2 { font-size: 1.8rem; margin-bottom: 0.5rem; }
                .subtitle { font-size: 1.2rem; color: #666; margin-bottom: 1rem; }
                .tagline { font-style: italic; color: #333; font-size: 1.1rem; }
                .media-kit-section { margin-bottom: 2rem; }
                .media-kit-section h3 { color: #333; border-bottom: 2px solid #6366f1; padding-bottom: 0.5rem; }
                .achievements p { margin: 0.5rem 0; }
                .portfolio-case { margin-bottom: 1.5rem; padding: 1rem; background: #f8f9fa; border-radius: 8px; }
                .case-metrics { display: flex; gap: 1rem; margin-top: 0.5rem; font-size: 0.9rem; color: #666; }
                .contact-info p { margin: 0.5rem 0; }
                .social-links p { margin: 0.5rem 0; }
                .media-kit-footer { text-align: center; margin-top: 3rem; padding-top: 2rem; border-top: 1px solid #eee; }
            </style>
        `;
    }

    // ===== SPEAKER KIT FUNCTIONALITY ===== //
    openTopicModal() {
        this.showModal('addTopicModal');
    }

    addSpeechTopic() {
        const formData = {
            title: document.getElementById('topic-title')?.value || '',
            description: document.getElementById('topic-description')?.value || '',
            duration: document.getElementById('topic-duration')?.value || '',
            audience: document.getElementById('topic-audience')?.value || '',
            takeaways: document.getElementById('topic-takeaways')?.value || ''
        };

        if (!formData.title.trim()) {
            this.showNotification('Proszę podać tytuł wystąpienia', 'error');
            return;
        }

        this.speechTopics.push({
            id: Date.now(),
            ...formData
        });

        this.renderSpeechTopics();
        this.closeModal('addTopicModal');
        this.clearTopicForm();
        this.showNotification('Temat wystąpienia został dodany', 'success');
    }

    renderSpeechTopics() {
        const container = document.getElementById('speech-topics');
        if (!container) return;

        container.innerHTML = '';
        
        this.speechTopics.forEach((topic, index) => {
            const topicElement = document.createElement('div');
            topicElement.className = 'topic-item';
            topicElement.innerHTML = `
                <div class="topic-info">
                    <div class="topic-title">${topic.title}</div>
                    <div class="topic-details">
                        <span>Czas: ${topic.duration} min</span>
                        <span>Grupa: ${topic.audience}</span>
                    </div>
                    <div class="topic-description">${topic.description}</div>
                </div>
                <button class="btn-secondary" onclick="professionalTools.removeSpeechTopic(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            container.appendChild(topicElement);
        });
    }

    removeSpeechTopic(index) {
        this.speechTopics.splice(index, 1);
        this.renderSpeechTopics();
    }

    clearTopicForm() {
        document.getElementById('topic-form')?.reset();
    }

    updateSpeakerPreview() {
        const preview = document.getElementById('speaker-sheet-preview');
        if (!preview) return;

        const name = document.getElementById('speaker-name')?.value || '';
        const credentials = document.getElementById('speaker-credentials')?.value || '';
        const bio = document.getElementById('speaker-bio-short')?.value || '';

        if (!name && !bio) {
            preview.innerHTML = `
                <div class="preview-placeholder">
                    <i class="fas fa-file-alt"></i>
                    <p>Wypełnij formularz aby zobaczyć podgląd</p>
                </div>
            `;
            return;
        }

        preview.innerHTML = `
            <div class="speaker-preview-content">
                <h4>${name}${credentials ? `, ${credentials}` : ''}</h4>
                <p>${bio}</p>
                <div class="topics-count">
                    <small>${this.speechTopics.length} dostępnych tematów</small>
                </div>
            </div>
        `;
    }

    async generateSpeakerKit() {
        if (this.isGenerating) return;

        this.isGenerating = true;
        this.showNotification('Generuję Speaker Kit...', 'info');

        try {
            const speakerData = this.collectSpeakerKitData();
            
            if (!this.validateSpeakerKitData(speakerData)) {
                this.showNotification('Proszę wypełnić podstawowe informacje o prelegencie', 'error');
                return;
            }

            const kitContent = await this.buildSpeakerKitContent(speakerData);
            this.showKitPreview(kitContent, 'Speaker Kit');
            this.showNotification('Speaker Kit został wygenerowany!', 'success');

        } catch (error) {
            console.error('Error generating speaker kit:', error);
            this.showNotification('Błąd podczas generowania Speaker Kit', 'error');
        } finally {
            this.isGenerating = false;
        }
    }

    collectSpeakerKitData() {
        const requirements = [];
        document.querySelectorAll('input[name="requirements"]:checked').forEach(checkbox => {
            requirements.push(checkbox.value);
        });

        return {
            name: document.getElementById('speaker-name')?.value || '',
            credentials: document.getElementById('speaker-credentials')?.value || '',
            bio: document.getElementById('speaker-bio-short')?.value || '',
            experience: document.getElementById('speaking-experience')?.value || '',
            eventsSpoken: document.getElementById('events-spoken')?.value || '',
            notableEvents: document.getElementById('notable-events')?.value || '',
            topics: this.speechTopics,
            requirements: requirements
        };
    }

    validateSpeakerKitData(data) {
        return data.name.trim() && data.bio.trim();
    }

    async buildSpeakerKitContent(data) {
        const currentDate = new Date().toLocaleDateString('pl-PL');
        
        return `
            <div class="speaker-kit-header">
                <h1>Speaker One-Sheet</h1>
                <h2>${data.name}${data.credentials ? `, ${data.credentials}` : ''}</h2>
                <p class="speaker-bio">${data.bio}</p>
            </div>

            <div class="speaker-kit-section">
                <h3>Doświadczenie w wystąpieniach</h3>
                <div class="experience-info">
                    <p><strong>Poziom doświadczenia:</strong> ${this.getExperienceText(data.experience)}</p>
                    ${data.eventsSpoken ? `<p><strong>Liczba wystąpień:</strong> ${data.eventsSpoken}</p>` : ''}
                    ${data.notableEvents ? `<p><strong>Ważne wydarzenia:</strong> ${data.notableEvents}</p>` : ''}
                </div>
            </div>

            ${data.topics.length > 0 ? `
            <div class="speaker-kit-section">
                <h3>Dostępne tematy wystąpień</h3>
                ${data.topics.map(topic => `
                    <div class="topic-card">
                        <h4>${topic.title}</h4>
                        <div class="topic-meta">
                            <span>Czas: ${topic.duration} min</span>
                            <span>Grupa docelowa: ${topic.audience}</span>
                        </div>
                        <p>${topic.description}</p>
                        ${topic.takeaways ? `
                        <div class="takeaways">
                            <strong>Kluczowe wnioski:</strong>
                            <div class="takeaways-list">
                                ${topic.takeaways.split('\n').map(takeaway => 
                                    takeaway.trim() ? `<p>• ${takeaway}</p>` : ''
                                ).join('')}
                            </div>
                        </div>
                        ` : ''}
                    </div>
                `).join('')}
            </div>
            ` : ''}

            ${data.requirements.length > 0 ? `
            <div class="speaker-kit-section">
                <h3>Wymagania techniczne</h3>
                <ul class="requirements-list">
                    ${data.requirements.map(req => `<li>${this.getRequirementText(req)}</li>`).join('')}
                </ul>
            </div>
            ` : ''}

            <div class="speaker-kit-footer">
                <p><small>Speaker Kit wygenerowany ${currentDate} | BrandMe.pl</small></p>
            </div>

            <style>
                .speaker-kit-header { text-align: center; margin-bottom: 2rem; }
                .speaker-kit-header h1 { color: #6366f1; font-size: 2rem; margin-bottom: 0.5rem; }
                .speaker-kit-header h2 { font-size: 1.8rem; margin-bottom: 1rem; }
                .speaker-bio { font-size: 1.1rem; line-height: 1.6; margin-bottom: 1rem; }
                .speaker-kit-section { margin-bottom: 2rem; }
                .speaker-kit-section h3 { color: #333; border-bottom: 2px solid #6366f1; padding-bottom: 0.5rem; }
                .experience-info p { margin: 0.5rem 0; }
                .topic-card { margin-bottom: 1.5rem; padding: 1rem; background: #f8f9fa; border-radius: 8px; }
                .topic-meta { display: flex; gap: 1rem; margin: 0.5rem 0; font-size: 0.9rem; color: #666; }
                .takeaways { margin-top: 1rem; }
                .takeaways-list p { margin: 0.25rem 0; }
                .requirements-list { columns: 2; list-style-type: none; padding: 0; }
                .requirements-list li { margin: 0.5rem 0; padding-left: 1rem; position: relative; }
                .requirements-list li::before { content: '✓'; position: absolute; left: 0; color: #6366f1; }
                .speaker-kit-footer { text-align: center; margin-top: 3rem; padding-top: 2rem; border-top: 1px solid #eee; }
            </style>
        `;
    }

    // ===== UTILITY METHODS ===== //
    initializeDefaultData() {
        // Initialize with some sample data for demo
        this.expertiseAreas = ['Digital Marketing', 'AI w biznesie', 'Personal Branding'];
        this.renderExpertiseTags();
        
        // Add sample portfolio item
        this.addPortfolioItem();
    }

    updateStats() {
        // Update sidebar stats based on current work
        const stats = {
            generated: Math.floor(Math.random() * 30) + 20,
            downloaded: Math.floor(Math.random() * 200) + 100,
            shared: Math.floor(Math.random() * 100) + 50
        };

        document.querySelectorAll('.stat-value').forEach((element, index) => {
            const values = [stats.generated, stats.downloaded, stats.shared];
            element.textContent = values[index] || 0;
        });
    }

    previewMediaKit() {
        const data = this.collectMediaKitData();
        if (!this.validateMediaKitData(data)) {
            this.showNotification('Proszę wypełnić podstawowe informacje', 'error');
            return;
        }
        this.generateMediaKit();
    }

    previewSpeakerKit() {
        const data = this.collectSpeakerKitData();
        if (!this.validateSpeakerKitData(data)) {
            this.showNotification('Proszę wypełnić podstawowe informacje', 'error');
            return;
        }
        this.generateSpeakerKit();
    }

    showKitPreview(content, title) {
        const modal = document.getElementById('kitPreviewModal');
        const titleElement = modal.querySelector('.modal-title');
        const contentElement = document.getElementById('kit-preview-content');
        
        if (titleElement) titleElement.textContent = `Podgląd - ${title}`;
        if (contentElement) contentElement.innerHTML = content;
        
        this.showModal('kitPreviewModal');
    }

    downloadKit() {
        const content = document.getElementById('kit-preview-content')?.innerHTML || '';
        const title = document.querySelector('#kitPreviewModal .modal-title')?.textContent || 'Professional Kit';
        
        // Create downloadable HTML file
        const fullHTML = `
            <!DOCTYPE html>
            <html lang="pl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${title}</title>
                <style>
                    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; margin: 2rem; color: #333; }
                    ${content.includes('<style>') ? '' : '/* Styles are embedded in content */'}
                </style>
            </head>
            <body>
                ${content}
            </body>
            </html>
        `;

        const blob = new Blob([fullHTML], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${title.replace(/\s+/g, '-').toLowerCase()}-${new Date().toISOString().split('T')[0]}.html`;
        link.click();
        URL.revokeObjectURL(url);

        this.showNotification('Kit został pobrany!', 'success');
    }

    shareKit() {
        if (navigator.share) {
            navigator.share({
                title: 'Professional Kit - BrandMe.pl',
                text: 'Sprawdź mój profesjonalny kit wygenerowany przez BrandMe.pl',
                url: window.location.href
            }).then(() => {
                this.showNotification('Kit został udostępniony!', 'success');
            }).catch(err => {
                console.log('Error sharing:', err);
                this.copyShareLink();
            });
        } else {
            this.copyShareLink();
        }
    }

    copyShareLink() {
        const url = window.location.href;
        navigator.clipboard.writeText(url).then(() => {
            this.showNotification('Link został skopiowany do schowka!', 'success');
        }).catch(() => {
            this.showNotification('Nie udało się skopiować linku', 'error');
        });
    }

    exportAllKits() {
        const exportData = {
            mediaKit: this.collectMediaKitData(),
            speakerKit: this.collectSpeakerKitData(),
            expertiseAreas: this.expertiseAreas,
            portfolioItems: this.portfolioItems,
            speechTopics: this.speechTopics,
            exportDate: new Date().toISOString()
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `brandme-professional-tools-export-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);

        this.showNotification('Wszystkie dane zostały wyeksportowane!', 'success');
    }

    openAIGenerator() {
        this.showNotification('AI Generator zostanie wkrótce uruchomiony!', 'info');
        // Future implementation for AI-powered kit generation
    }

    useTemplate(templateName) {
        this.showNotification(`Szablon "${templateName}" został załadowany!`, 'success');
        // Future implementation for template loading
    }

    // ===== HELPER METHODS ===== //
    getAvailabilityText(value) {
        const options = {
            'immediate': 'Natychmiast',
            '1week': 'W ciągu tygodnia',
            '2weeks': 'W ciągu 2 tygodni',
            '1month': 'W ciągu miesiąca',
            'seasonal': 'Sezonowo'
        };
        return options[value] || value;
    }

    getExperienceText(value) {
        const options = {
            'beginner': 'Początkujący (0-2 lata)',
            'intermediate': 'Średniozaawansowany (2-5 lat)',
            'experienced': 'Doświadczony (5-10 lat)',
            'expert': 'Ekspert (10+ lat)'
        };
        return options[value] || value;
    }

    getRequirementText(value) {
        const options = {
            'projector': 'Projektor/Ekran',
            'microphone': 'Mikrofon',
            'clicker': 'Wskaźnik laserowy',
            'internet': 'Połączenie internetowe',
            'flipchart': 'Flipchart',
            'recording': 'Zgoda na nagrywanie'
        };
        return options[value] || value;
    }

    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.classList.add('modal-open');
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('active');
            document.body.classList.remove('modal-open');
        }
    }

    showNotification(message, type = 'info') {
        if (window.brandMePlatform) {
            window.brandMePlatform.showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Initialize Professional Tools
const professionalTools = new ProfessionalTools();

// Export for global use
window.ProfessionalTools = ProfessionalTools;
window.professionalTools = professionalTools;