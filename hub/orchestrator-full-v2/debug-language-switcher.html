<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Language Switcher - PixelGarage</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
            color: white;
        }
        
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .navbar-test {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px 30px;
            border-radius: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo {
            color: white;
            font-size: 24px;
            font-weight: 700;
        }
        
        .nav-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .debug-buttons {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .debug-btn {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .debug-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .debug-output {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .test-layer {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            position: relative;
        }
        
        .z-index-high {
            z-index: 5000;
        }
        
        .z-index-medium {
            z-index: 1000;
        }
        
        .z-index-low {
            z-index: 100;
        }
    </style>
</head>
<body data-page="debug">
    <!-- Test Navbar -->
    <nav class="navbar-test">
        <div class="logo">
            🎯 PixelGarage Debug
        </div>
        <div class="nav-actions">
            <!-- Language switcher will be added here automatically -->
        </div>
    </nav>

    <div class="debug-container">
        <h1>🐛 Language Switcher Debug</h1>
        <p>Ta strona pomoże zdiagnozować problemy z dropdown'em language switcher'a.</p>
        
        <div class="debug-buttons">
            <button class="debug-btn" onclick="testLanguageSwitcher()">
                🧪 Test Dropdown
            </button>
            <button class="debug-btn" onclick="debugDropdownElements()">
                🔍 Debug Elements
            </button>
            <button class="debug-btn" onclick="forceShowDropdown()">
                👁️ Force Show Dropdown
            </button>
            <button class="debug-btn" onclick="checkZIndex()">
                📊 Check Z-Index
            </button>
            <button class="debug-btn" onclick="clearConsole()">
                🧹 Clear Console
            </button>
        </div>

        <div class="debug-output" id="debug-output">
            <div>🔧 Debug output will appear here...</div>
        </div>

        <div class="test-layer z-index-low">
            <h3>Layer 1 (z-index: 100)</h3>
            <p>Dropdown powinien być widoczny nad tą warstwą.</p>
        </div>

        <div class="test-layer z-index-medium">
            <h3>Layer 2 (z-index: 1000)</h3>
            <p>Dropdown powinien być widoczny nad tą warstwą.</p>
        </div>

        <div class="test-layer z-index-high">
            <h3>Layer 3 (z-index: 5000)</h3>
            <p>Dropdown powinien być widoczny nad tą warstwą.</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/translations.js"></script>
    <script src="assets/js/language-switcher.js"></script>
    
    <script>
        // Initialize translation system
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌐 Initializing debug page...');
            initializeLanguage();
        });
        
        function debugDropdownElements() {
            const output = document.getElementById('debug-output');
            const switcher = document.querySelector('.language-switcher');
            const dropdown = document.getElementById('main-lang-dropdown') || document.querySelector('.lang-dropdown');
            const overlay = document.querySelector('.lang-dropdown-overlay');
            
            const info = {
                switcher: {
                    exists: !!switcher,
                    element: switcher,
                    classes: switcher?.className,
                    innerHTML: switcher?.innerHTML
                },
                dropdown: {
                    exists: !!dropdown,
                    element: dropdown,
                    id: dropdown?.id,
                    classes: dropdown?.className,
                    style: dropdown?.style.cssText,
                    parent: dropdown?.parentElement?.tagName
                },
                overlay: {
                    exists: !!overlay,
                    element: overlay,
                    classes: overlay?.className
                }
            };
            
            output.innerHTML = '<pre>' + JSON.stringify(info, null, 2) + '</pre>';
            console.log('🔍 Debug info:', info);
        }
        
        function forceShowDropdown() {
            const dropdown = document.getElementById('main-lang-dropdown') || document.querySelector('.lang-dropdown');
            if (dropdown) {
                dropdown.style.position = 'fixed';
                dropdown.style.top = '100px';
                dropdown.style.left = '100px';
                dropdown.style.opacity = '1';
                dropdown.style.visibility = 'visible';
                dropdown.style.transform = 'translateY(0) scale(1)';
                dropdown.style.display = 'block';
                dropdown.style.zIndex = '9999999';
                dropdown.style.background = 'rgba(255, 0, 0, 0.9)';
                dropdown.style.border = '3px solid yellow';
                dropdown.classList.add('active');
                
                console.log('🔴 Forced dropdown visible at top-left with red background');
                document.getElementById('debug-output').innerHTML = '🔴 Dropdown forced visible with red background and yellow border';
            } else {
                console.error('❌ No dropdown found to force show');
                document.getElementById('debug-output').innerHTML = '❌ No dropdown found to force show';
            }
        }
        
        function checkZIndex() {
            const elements = [
                { name: 'navbar', element: document.querySelector('.navbar-test') },
                { name: 'switcher', element: document.querySelector('.language-switcher') },
                { name: 'dropdown', element: document.getElementById('main-lang-dropdown') || document.querySelector('.lang-dropdown') },
                { name: 'overlay', element: document.querySelector('.lang-dropdown-overlay') },
                { name: 'layer1', element: document.querySelector('.z-index-low') },
                { name: 'layer2', element: document.querySelector('.z-index-medium') },
                { name: 'layer3', element: document.querySelector('.z-index-high') }
            ];
            
            const zIndexInfo = elements.map(item => ({
                name: item.name,
                exists: !!item.element,
                zIndex: item.element ? getComputedStyle(item.element).zIndex : 'N/A',
                position: item.element ? getComputedStyle(item.element).position : 'N/A'
            }));
            
            document.getElementById('debug-output').innerHTML = '<pre>' + JSON.stringify(zIndexInfo, null, 2) + '</pre>';
            console.log('📊 Z-Index info:', zIndexInfo);
        }
        
        function clearConsole() {
            console.clear();
            document.getElementById('debug-output').innerHTML = '🧹 Console cleared';
        }
    </script>
</body>
</html>
