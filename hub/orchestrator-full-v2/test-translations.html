<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Translations - PixelGarage</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
            color: white;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .navbar-test {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px 30px;
            border-radius: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo {
            color: white;
            font-size: 24px;
            font-weight: 700;
        }
        
        .nav-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-buttons {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .test-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .translation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .translation-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .translation-key {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #ffd700;
            margin-bottom: 5px;
        }
        
        .translation-text {
            font-size: 14px;
            line-height: 1.4;
        }
        
        .missing-translation {
            background: rgba(255, 0, 0, 0.2);
            border-color: rgba(255, 0, 0, 0.5);
        }
        
        .stats {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            min-width: 120px;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #ffd700;
        }
        
        .stat-label {
            font-size: 12px;
            margin-top: 5px;
        }
    </style>
</head>
<body data-page="test">
    <!-- Test Navbar -->
    <nav class="navbar-test">
        <div class="logo">
            🌐 Translation Test
        </div>
        <div class="nav-actions">
            <!-- Language switcher will be added here automatically -->
        </div>
    </nav>

    <div class="test-container">
        <h1>🌐 Translation Coverage Test</h1>
        <p>Ta strona testuje pokrycie tłumaczeń dla wszystkich elementów PixelGarage.</p>
        
        <div class="test-buttons">
            <button class="test-btn" onclick="testAllTranslations()">
                🧪 Test All Translations
            </button>
            <button class="test-btn" onclick="addMissingAttributes()">
                ➕ Add Missing Attributes
            </button>
            <button class="test-btn" onclick="showMissingTranslations()">
                ❌ Show Missing
            </button>
            <button class="test-btn" onclick="exportTranslationReport()">
                📊 Export Report
            </button>
        </div>

        <div class="stats" id="translation-stats">
            <div class="stat-item">
                <div class="stat-number" id="total-elements">0</div>
                <div class="stat-label">Total Elements</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="translated-elements">0</div>
                <div class="stat-label">Translated</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="missing-elements">0</div>
                <div class="stat-label">Missing</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="coverage-percentage">0%</div>
                <div class="stat-label">Coverage</div>
            </div>
        </div>

        <div class="test-section">
            <h2>Sample Translations</h2>
            <div class="translation-grid" id="sample-translations">
                <!-- Sample translations will be populated here -->
            </div>
        </div>

        <div class="test-section">
            <h2>Missing Translations</h2>
            <div class="translation-grid" id="missing-translations">
                <!-- Missing translations will be populated here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/translations.js"></script>
    <script src="auto-translate-attributes.js"></script>
    <script src="assets/js/language-switcher.js"></script>
    
    <script>
        // Initialize translation system
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌐 Initializing translation test...');
            
            // Add missing attributes first
            setTimeout(() => {
                autoAddTranslationAttributes();
                initializeLanguage();
                testAllTranslations();
            }, 100);
        });
        
        function testAllTranslations() {
            console.log('🧪 Testing all translations...');
            
            const elements = document.querySelectorAll('[data-translate]');
            const textElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, div, button, a');
            
            let totalElements = 0;
            let translatedElements = 0;
            let missingElements = 0;
            const missingTranslations = [];
            const sampleTranslations = [];
            
            // Count all text elements
            textElements.forEach(element => {
                const text = element.textContent.trim();
                if (text && text.length > 2 && !text.match(/^[0-9\s\+\-\%\.\,\(\)]+$/)) {
                    totalElements++;
                    
                    if (element.hasAttribute('data-translate')) {
                        translatedElements++;
                        const key = element.getAttribute('data-translate');
                        const translation = t(key);
                        
                        if (sampleTranslations.length < 10) {
                            sampleTranslations.push({
                                key: key,
                                original: text,
                                translation: translation,
                                element: element
                            });
                        }
                    } else {
                        missingElements++;
                        missingTranslations.push({
                            text: text,
                            element: element,
                            tag: element.tagName.toLowerCase(),
                            classes: element.className
                        });
                    }
                }
            });
            
            // Update stats
            document.getElementById('total-elements').textContent = totalElements;
            document.getElementById('translated-elements').textContent = translatedElements;
            document.getElementById('missing-elements').textContent = missingElements;
            document.getElementById('coverage-percentage').textContent = 
                Math.round((translatedElements / totalElements) * 100) + '%';
            
            // Show sample translations
            const sampleContainer = document.getElementById('sample-translations');
            sampleContainer.innerHTML = sampleTranslations.map(item => `
                <div class="translation-item">
                    <div class="translation-key">${item.key}</div>
                    <div class="translation-text"><strong>PL:</strong> ${item.original}</div>
                    <div class="translation-text"><strong>EN:</strong> ${item.translation}</div>
                </div>
            `).join('');
            
            // Show missing translations
            const missingContainer = document.getElementById('missing-translations');
            missingContainer.innerHTML = missingTranslations.slice(0, 20).map(item => `
                <div class="translation-item missing-translation">
                    <div class="translation-key">&lt;${item.tag}&gt; ${item.classes}</div>
                    <div class="translation-text">${item.text}</div>
                </div>
            `).join('');
            
            console.log(`📊 Translation coverage: ${translatedElements}/${totalElements} (${Math.round((translatedElements / totalElements) * 100)}%)`);
            console.log('❌ Missing translations:', missingTranslations);
            
            return {
                total: totalElements,
                translated: translatedElements,
                missing: missingElements,
                coverage: (translatedElements / totalElements) * 100,
                missingList: missingTranslations
            };
        }
        
        function addMissingAttributes() {
            const result = autoAddTranslationAttributes();
            alert(`Added ${result} translation attributes. Refreshing test...`);
            setTimeout(testAllTranslations, 500);
        }
        
        function showMissingTranslations() {
            const result = testAllTranslations();
            console.table(result.missingList);
            alert(`Found ${result.missing} missing translations. Check console for details.`);
        }
        
        function exportTranslationReport() {
            const result = testAllTranslations();
            const report = {
                timestamp: new Date().toISOString(),
                coverage: result.coverage,
                stats: {
                    total: result.total,
                    translated: result.translated,
                    missing: result.missing
                },
                missingTranslations: result.missingList
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'translation-report.json';
            a.click();
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
