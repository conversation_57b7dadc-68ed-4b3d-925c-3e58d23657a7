<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Projektów - PixelGarage | 12 Zrealizowanych Projektów AI</title>
    <meta name="description" content="Odkryj portfolio PixelGarage - 12 innowacyjnych projektów AI. SmartCard, BrandMe, FitGenius i więcej. Animowana galeria z pełną prezentacją.">
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/gallery.css">
    <link rel="stylesheet" href="../assets/css/responsive-universal.css">
    <link rel="stylesheet" href="../assets/css/responsive-components.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body data-page="portfolio">
    <!-- Navigation -->
    <nav class="navbar glass-effect">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../" class="logo-container">
                    <span class="logo-icon">🎯</span>
                    <span class="logo-text">Pixel<span class="logo-accent">Garage</span></span>
                </a>
            </div>
            <ul class="nav-menu">
                <li class="nav-item"><a href="../" class="nav-link" data-translate="nav.home">Strona Główna</a></li>
                <li class="nav-item"><a href="../services/" class="nav-link" data-translate="nav.services">Katalog Usług</a></li>
                <li class="nav-item"><a href="#" class="nav-link active" data-translate="nav.portfolio">Portfolio</a></li>
                <li class="nav-item"><a href="../academy/" class="nav-link" data-translate="nav.academy">Tech Academy</a></li>
                <li class="nav-item"><a href="../communication/" class="nav-link" data-translate="nav.contact">Kontakt Live</a></li>
            </ul>
            <div class="nav-actions">
                <button class="btn-secondary" onclick="openCommunication()">
                    <i class="fab fa-telegram"></i> <span data-translate="common.liveChat">Live Chat</span>
                </button>
                <button class="btn-primary" onclick="scrollToContact()">
                    <i class="fas fa-rocket"></i> <span data-translate="landing.hero.cta1">Rozpocznij Projekt</span>
                </button>
            </div>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="gallery-hero">
        <div class="hero-background">
            <div class="gradient-orb orb-1"></div>
            <div class="gradient-orb orb-2"></div>
            <div class="floating-elements"></div>
        </div>
        
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-trophy"></i>
                    <span>Portfolio Realizacji • Innowacyjne Rozwiązania • Sprawdzone Technologie</span>
                </div>

                <h1 class="hero-title">
                    Portfolio
                    <span class="gradient-text">PixelGarage</span>
                </h1>

                <p class="hero-description">
                    Prezentujemy nasze najnowsze realizacje - od zaawansowanych aplikacji biznesowych po innowacyjne platformy cyfrowe.
                    Każdy projekt to dowód naszego doświadczenia w tworzeniu nowoczesnych rozwiązań technologicznych.
                </p>

                <div class="hero-stats">
                    <div class="stat-item">
                        <div class="stat-number" id="projectsCount">12</div>
                        <div class="stat-label">Projektów</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="technologiesCount">25+</div>
                        <div class="stat-label">Technologii</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="experienceCount">5+</div>
                        <div class="stat-label">Lat Doświadczenia</div>
                    </div>
                </div>

                <div class="hero-actions">
                    <button class="btn-primary large" onclick="scrollToGallery()">
                        <i class="fas fa-eye"></i>
                        <span>Przejrzyj Realizacje</span>
                    </button>
                    <button class="btn-secondary large" onclick="openCommunication()">
                        <i class="fas fa-handshake"></i>
                        <span>Rozpocznij Współpracę</span>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Premium Filter Section -->
    <section class="premium-filter-section">
        <div class="container">
            <div class="premium-filter-container">
                <!-- Filter Header with Stats -->
                <div class="filter-header-premium">
                    <div class="filter-title-section">
                        <h3 class="filter-title">
                            <span class="filter-icon">🎯</span>
                            Filtruj projekty
                        </h3>
                        <div class="filter-stats">
                            <span class="projects-count" id="filteredCount">12</span>
                            <span class="projects-label">projektów</span>
                        </div>
                    </div>

                    <!-- Advanced Controls -->
                    <div class="filter-controls">
                        <div class="view-controls-premium">
                            <button class="view-btn-premium active" data-view="grid" onclick="toggleView('grid')" title="Widok siatki">
                                <i class="fas fa-th"></i>
                                <span class="view-label">Grid</span>
                            </button>
                            <button class="view-btn-premium" data-view="masonry" onclick="toggleView('masonry')" title="Widok masonry">
                                <i class="fas fa-th-large"></i>
                                <span class="view-label">Masonry</span>
                            </button>
                            <button class="view-btn-premium" data-view="list" onclick="toggleView('list')" title="Widok listy">
                                <i class="fas fa-list"></i>
                                <span class="view-label">List</span>
                            </button>
                        </div>

                        <div class="sort-controls">
                            <select class="sort-select" id="sortSelect" onchange="sortProjects(this.value)">
                                <option value="default">Sortuj według</option>
                                <option value="name">Nazwa A-Z</option>
                                <option value="featured">Polecane</option>
                                <option value="newest">Najnowsze</option>
                                <option value="popular">Popularne</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Premium Filter Tabs -->
                <div class="filter-tabs-container">
                    <div class="filter-tabs">
                        <button class="filter-tab active" data-filter="all" onclick="filterProjects('all')">
                            <div class="tab-content">
                                <i class="fas fa-globe"></i>
                                <span class="tab-label">Wszystkie</span>
                                <span class="tab-count" data-count="all">12</span>
                            </div>
                            <div class="tab-indicator"></div>
                        </button>

                        <button class="filter-tab" data-filter="web" onclick="filterProjects('web')">
                            <div class="tab-content">
                                <i class="fas fa-desktop"></i>
                                <span class="tab-label">Web Apps</span>
                                <span class="tab-count" data-count="web">6</span>
                            </div>
                            <div class="tab-indicator"></div>
                        </button>

                        <button class="filter-tab" data-filter="mobile" onclick="filterProjects('mobile')">
                            <div class="tab-content">
                                <i class="fas fa-mobile-alt"></i>
                                <span class="tab-label">Mobile</span>
                                <span class="tab-count" data-count="mobile">5</span>
                            </div>
                            <div class="tab-indicator"></div>
                        </button>

                        <button class="filter-tab" data-filter="ai" onclick="filterProjects('ai')">
                            <div class="tab-content">
                                <i class="fas fa-robot"></i>
                                <span class="tab-label">AI Tools</span>
                                <span class="tab-count" data-count="ai">4</span>
                            </div>
                            <div class="tab-indicator"></div>
                        </button>

                        <button class="filter-tab" data-filter="business" onclick="filterProjects('business')">
                            <div class="tab-content">
                                <i class="fas fa-briefcase"></i>
                                <span class="tab-label">Business</span>
                                <span class="tab-count" data-count="business">7</span>
                            </div>
                            <div class="tab-indicator"></div>
                        </button>

                        <button class="filter-tab" data-filter="health" onclick="filterProjects('health')">
                            <div class="tab-content">
                                <i class="fas fa-heartbeat"></i>
                                <span class="tab-label">Health & Fitness</span>
                                <span class="tab-count" data-count="health">3</span>
                            </div>
                            <div class="tab-indicator"></div>
                        </button>

                        <button class="filter-tab" data-filter="education" onclick="filterProjects('education')">
                            <div class="tab-content">
                                <i class="fas fa-graduation-cap"></i>
                                <span class="tab-label">Education</span>
                                <span class="tab-count" data-count="education">1</span>
                            </div>
                            <div class="tab-indicator"></div>
                        </button>
                    </div>

                    <!-- Filter Animation Background -->
                    <div class="filter-background-animation">
                        <div class="animation-orb orb-1"></div>
                        <div class="animation-orb orb-2"></div>
                        <div class="animation-orb orb-3"></div>
                    </div>
                </div>

                <!-- Search and Advanced Filters -->
                <div class="advanced-filters">
                    <div class="search-container">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="Szukaj projektów..." id="searchInput" oninput="searchProjects(this.value)">
                        <button class="search-clear" onclick="clearSearch()" style="display: none;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="quick-filters">
                        <button class="quick-filter" data-filter="featured" onclick="quickFilter('featured')">
                            <i class="fas fa-star"></i>
                            Polecane
                        </button>
                        <button class="quick-filter" data-filter="new" onclick="quickFilter('new')">
                            <i class="fas fa-sparkles"></i>
                            Nowe
                        </button>
                        <button class="quick-filter" data-filter="popular" onclick="quickFilter('popular')">
                            <i class="fas fa-fire"></i>
                            Popularne
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Gallery -->
    <section class="projects-gallery" id="projectsGallery">
        <div class="container">
            <div class="gallery-grid" id="galleryGrid">
                <!-- Projects will be dynamically loaded here -->
            </div>
        </div>
    </section>

    <!-- Featured Project Modal -->
    <div class="project-modal" id="projectModal">
        <div class="modal-overlay" onclick="closeProjectModal()"></div>
        <div class="modal-content glass-effect">
            <div class="modal-header">
                <h2 id="modalTitle"></h2>
                <button class="modal-close" onclick="closeProjectModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="modal-image">
                    <img id="modalImage" src="" alt="">
                    <div class="modal-badges" id="modalBadges"></div>
                </div>
                <div class="modal-info">
                    <div class="modal-description" id="modalDescription"></div>
                    <div class="modal-tech" id="modalTech"></div>
                    <div class="modal-features" id="modalFeatures"></div>
                    <div class="modal-stats" id="modalStats"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-modal-primary" id="modalViewBtn">
                    <i class="fas fa-external-link-alt"></i>
                    Zobacz Live Demo
                </button>
                <button class="btn-modal-secondary" onclick="openContactModal()">
                    <i class="fas fa-clone"></i>
                    Zamów Podobny
                </button>
            </div>
        </div>
    </div>

    <!-- Contact Choice Modal -->
    <div class="contact-modal" id="contactModal">
        <div class="modal-overlay" onclick="closeContactModal()"></div>
        <div class="contact-modal-content glass-effect">
            <div class="contact-modal-header">
                <h3>Wybierz sposób kontaktu</h3>
                <button class="modal-close" onclick="closeContactModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="contact-modal-body">
                <p class="contact-description">
                    Skontaktuj się z nami, aby omówić szczegóły podobnego projektu.
                    Wybierz preferowany sposób komunikacji:
                </p>

                <div class="contact-options">
                    <button class="contact-option telegram" onclick="contactViaTelegram()">
                        <div class="contact-icon">
                            <i class="fab fa-telegram"></i>
                        </div>
                        <div class="contact-info">
                            <h4>Telegram</h4>
                            <p>Szybka komunikacja w czasie rzeczywistym</p>
                            <span class="contact-badge">Polecane</span>
                        </div>
                        <div class="contact-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </button>

                    <button class="contact-option whatsapp" onclick="contactViaWhatsApp()">
                        <div class="contact-icon">
                            <i class="fab fa-whatsapp"></i>
                        </div>
                        <div class="contact-info">
                            <h4>WhatsApp</h4>
                            <p>Wygodne rozmowy i wymiana plików</p>
                            <span class="contact-badge">Popularne</span>
                        </div>
                        <div class="contact-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </button>
                </div>

                <div class="contact-footer">
                    <p class="contact-note">
                        <i class="fas fa-info-circle"></i>
                        Odpowiadamy zwykle w ciągu 15 minut w godzinach pracy
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Technologies Section -->
    <section class="technologies-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Technologie</h2>
                <p class="section-subtitle">Najnowsze narzędzia i frameworki używane w naszych projektach</p>
            </div>
            
            <div class="tech-grid">
                <div class="tech-item">
                    <div class="tech-icon">
                        <i class="fab fa-js-square"></i>
                    </div>
                    <h4>JavaScript</h4>
                    <p>ES6+, TypeScript</p>
                </div>
                
                <div class="tech-item">
                    <div class="tech-icon">
                        <i class="fab fa-react"></i>
                    </div>
                    <h4>React</h4>
                    <p>Next.js, Hooks</p>
                </div>
                
                <div class="tech-item">
                    <div class="tech-icon">
                        <i class="fab fa-python"></i>
                    </div>
                    <h4>Python</h4>
                    <p>FastAPI, Django</p>
                </div>
                
                <div class="tech-item">
                    <div class="tech-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h4>AI/ML</h4>
                    <p>OpenAI, Claude</p>
                </div>
                
                <div class="tech-item">
                    <div class="tech-icon">
                        <i class="fab fa-node-js"></i>
                    </div>
                    <h4>Node.js</h4>
                    <p>Express, APIs</p>
                </div>
                
                <div class="tech-item">
                    <div class="tech-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h4>Databases</h4>
                    <p>MongoDB, PostgreSQL</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-container glass-effect">
                <div class="cta-content">
                    <h2>Gotowy na swój projekt?</h2>
                    <p>Stwórzmy razem coś niesamowitego. Skontaktuj się z nami i omówmy Twoje pomysły.</p>
                </div>
                <div class="cta-actions">
                    <button class="btn-cta telegram" onclick="openTelegram()">
                        <i class="fab fa-telegram"></i>
                        <span>Telegram</span>
                    </button>
                    <button class="btn-cta whatsapp" onclick="openWhatsApp()">
                        <i class="fab fa-whatsapp"></i>
                        <span>WhatsApp</span>
                    </button>
                    <button class="btn-cta services" onclick="window.location.href='../services/'">
                        <i class="fas fa-list"></i>
                        <span>Zobacz Usługi</span>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div class="logo-container">
                        <span class="logo-icon">🎯</span>
                        <span class="logo-text">Pixel<span class="logo-accent">Garage</span></span>
                    </div>
                    <p>Tworzymy przyszłość cyfrową z technologią AI</p>
                </div>
                <div class="footer-links">
                    <div class="footer-section">
                        <h4>Portfolio</h4>
                        <a href="#web">Web Applications</a>
                        <a href="#mobile">Mobile Apps</a>
                        <a href="#ai">AI Solutions</a>
                    </div>
                    <div class="footer-section">
                        <h4>Kontakt</h4>
                        <a href="../communication/">Live Chat</a>
                        <a href="#" onclick="openTelegram()">Telegram</a>
                        <a href="#" onclick="openWhatsApp()">WhatsApp</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 PixelGarage. Wszystkie prawa zastrzeżone.</p>
            </div>
        </div>
    </footer>

    <script src="../assets/js/translations.js"></script>
    <script src="../assets/js/language-switcher.js"></script>
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/gallery.js"></script>

    <script>
        // Initialize translation system
        document.addEventListener('DOMContentLoaded', function() {
            initializeLanguage();
        });
    </script>
</body>
</html>
