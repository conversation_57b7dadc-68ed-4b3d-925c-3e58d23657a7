#!/bin/bash

# ===== PIXELGARAGE.SPACE MANAGEMENT SCRIPT =====
# Post-deployment management and monitoring
# Author: <PERSON> <PERSON> <PERSON>xelGarage Founder

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔧 PixelGarage.space Management Console"
echo "======================================"

# Configuration
DOMAIN="pixelgarage.space"
read -p "Enter your mikr.us server hostname: " SERVER_HOST
read -p "Enter your mikr.us username: " SERVER_USER
SERVER_PATH="/var/www/pixelgarage.space"

# Check server status
check_server_status() {
    log_info "Checking server status..."
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        echo '=== System Status ==='
        uptime
        echo ''
        
        echo '=== Disk Usage ==='
        df -h $SERVER_PATH
        echo ''
        
        echo '=== Nginx Status ==='
        sudo systemctl status nginx --no-pager -l
        echo ''
        
        echo '=== SSL Certificate Status ==='
        if [ -f /etc/letsencrypt/live/$DOMAIN/fullchain.pem ]; then
            sudo openssl x509 -in /etc/letsencrypt/live/$DOMAIN/fullchain.pem -text -noout | grep 'Not After'
        else
            echo 'No SSL certificate found'
        fi
        echo ''
        
        echo '=== Recent Nginx Errors ==='
        sudo tail -n 10 /var/log/nginx/pixelgarage.space.error.log 2>/dev/null || echo 'No error log found'
    "
}

# View logs
view_logs() {
    echo "Choose log type:"
    echo "1. Nginx access log"
    echo "2. Nginx error log"
    echo "3. System log"
    read -p "Enter choice (1-3): " log_choice
    
    case $log_choice in
        1)
            ssh "$SERVER_USER@$SERVER_HOST" "sudo tail -f /var/log/nginx/pixelgarage.space.access.log"
            ;;
        2)
            ssh "$SERVER_USER@$SERVER_HOST" "sudo tail -f /var/log/nginx/pixelgarage.space.error.log"
            ;;
        3)
            ssh "$SERVER_USER@$SERVER_HOST" "sudo journalctl -f"
            ;;
        *)
            log_error "Invalid choice"
            ;;
    esac
}

# Update website
update_website() {
    log_info "Updating website from local files..."
    
    # Backup current version
    BACKUP_DIR="/var/backups/pixelgarage-update-$(date +%Y%m%d-%H%M%S)"
    ssh "$SERVER_USER@$SERVER_HOST" "
        sudo mkdir -p /var/backups
        sudo cp -r '$SERVER_PATH' '$BACKUP_DIR'
        echo 'Backup created at: $BACKUP_DIR'
    "
    
    # Deploy updated files
    rsync -avz --delete \
        --exclude='.git*' \
        --exclude='node_modules' \
        --exclude='*.log' \
        --exclude='deploy*.sh' \
        --exclude='auto-deploy*.sh' \
        --exclude='manage*.sh' \
        --exclude='setup*.sh' \
        --exclude='README.md' \
        --progress \
        "./orchestrator-full-v2/" "$SERVER_USER@$SERVER_HOST:$SERVER_PATH/"
    
    # Set permissions
    ssh "$SERVER_USER@$SERVER_HOST" "
        sudo chown -R www-data:www-data '$SERVER_PATH'
        sudo find '$SERVER_PATH' -type d -exec chmod 755 {} \;
        sudo find '$SERVER_PATH' -type f -exec chmod 644 {} \;
    "
    
    log_success "Website updated successfully!"
}

# Test website functionality
test_website() {
    log_info "Testing website functionality..."
    
    # Test main pages
    pages=("" "/services/" "/gallery/" "/academy/" "/communication/")
    
    for page in "${pages[@]}"; do
        url="http://$DOMAIN$page"
        if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "200"; then
            log_success "✓ $url"
        else
            log_error "✗ $url"
        fi
    done
    
    # Test translation system
    log_info "Testing translation system..."
    if curl -s "http://$DOMAIN/assets/js/translations.js" | grep -q "translations"; then
        log_success "✓ Translation system loaded"
    else
        log_error "✗ Translation system not working"
    fi
}

# SSL management
manage_ssl() {
    echo "SSL Management Options:"
    echo "1. Check SSL certificate status"
    echo "2. Renew SSL certificate"
    echo "3. Install SSL certificate"
    read -p "Enter choice (1-3): " ssl_choice
    
    case $ssl_choice in
        1)
            ssh "$SERVER_USER@$SERVER_HOST" "
                if [ -f /etc/letsencrypt/live/$DOMAIN/fullchain.pem ]; then
                    echo 'SSL Certificate Information:'
                    sudo openssl x509 -in /etc/letsencrypt/live/$DOMAIN/fullchain.pem -text -noout | grep -E 'Subject:|Not After'
                    echo ''
                    echo 'Certificate files:'
                    sudo ls -la /etc/letsencrypt/live/$DOMAIN/
                else
                    echo 'No SSL certificate found for $DOMAIN'
                fi
            "
            ;;
        2)
            ssh "$SERVER_USER@$SERVER_HOST" "
                sudo certbot renew
                sudo systemctl reload nginx
            "
            log_success "SSL certificate renewal completed"
            ;;
        3)
            ssh "$SERVER_USER@$SERVER_HOST" "
                sudo certbot --nginx -d $DOMAIN -d www.$DOMAIN
                sudo systemctl reload nginx
            "
            log_success "SSL certificate installation completed"
            ;;
        *)
            log_error "Invalid choice"
            ;;
    esac
}

# Performance optimization
optimize_performance() {
    log_info "Applying performance optimizations..."
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        # Enable nginx gzip compression
        sudo tee -a /etc/nginx/nginx.conf > /dev/null << 'EOF'

# Performance optimizations
client_max_body_size 50M;
client_body_timeout 60s;
client_header_timeout 60s;
keepalive_timeout 65s;
send_timeout 60s;

# Buffer sizes
client_body_buffer_size 128k;
client_header_buffer_size 1k;
large_client_header_buffers 4 4k;
output_buffers 1 32k;
postpone_output 1460;
EOF
        
        # Test and reload nginx
        sudo nginx -t && sudo systemctl reload nginx
        
        echo 'Performance optimizations applied!'
    "
    
    log_success "Performance optimization completed"
}

# Backup management
manage_backups() {
    echo "Backup Management:"
    echo "1. Create manual backup"
    echo "2. List existing backups"
    echo "3. Restore from backup"
    read -p "Enter choice (1-3): " backup_choice
    
    case $backup_choice in
        1)
            BACKUP_NAME="pixelgarage-manual-$(date +%Y%m%d-%H%M%S)"
            ssh "$SERVER_USER@$SERVER_HOST" "
                sudo mkdir -p /var/backups
                sudo tar -czf /var/backups/$BACKUP_NAME.tar.gz -C '$SERVER_PATH' .
                echo 'Backup created: /var/backups/$BACKUP_NAME.tar.gz'
                sudo ls -lh /var/backups/$BACKUP_NAME.tar.gz
            "
            log_success "Manual backup created"
            ;;
        2)
            ssh "$SERVER_USER@$SERVER_HOST" "
                echo 'Available backups:'
                sudo ls -lh /var/backups/pixelgarage-* 2>/dev/null || echo 'No backups found'
            "
            ;;
        3)
            ssh "$SERVER_USER@$SERVER_HOST" "
                echo 'Available backups:'
                sudo ls -1 /var/backups/pixelgarage-* 2>/dev/null || echo 'No backups found'
            "
            read -p "Enter backup filename to restore: " backup_file
            ssh "$SERVER_USER@$SERVER_HOST" "
                if [ -f '/var/backups/$backup_file' ]; then
                    sudo rm -rf '$SERVER_PATH.old' 2>/dev/null || true
                    sudo mv '$SERVER_PATH' '$SERVER_PATH.old'
                    sudo mkdir -p '$SERVER_PATH'
                    sudo tar -xzf '/var/backups/$backup_file' -C '$SERVER_PATH'
                    sudo chown -R www-data:www-data '$SERVER_PATH'
                    echo 'Backup restored successfully'
                else
                    echo 'Backup file not found'
                fi
            "
            ;;
        *)
            log_error "Invalid choice"
            ;;
    esac
}

# Main menu
main_menu() {
    while true; do
        echo ""
        echo "🔧 Management Options:"
        echo "1. Check server status"
        echo "2. View logs"
        echo "3. Update website"
        echo "4. Test website functionality"
        echo "5. Manage SSL certificates"
        echo "6. Optimize performance"
        echo "7. Manage backups"
        echo "8. Exit"
        echo ""
        read -p "Enter your choice (1-8): " choice
        
        case $choice in
            1) check_server_status ;;
            2) view_logs ;;
            3) update_website ;;
            4) test_website ;;
            5) manage_ssl ;;
            6) optimize_performance ;;
            7) manage_backups ;;
            8) echo "Goodbye!"; exit 0 ;;
            *) log_error "Invalid choice. Please enter 1-8." ;;
        esac
    done
}

# Run main menu
main_menu
