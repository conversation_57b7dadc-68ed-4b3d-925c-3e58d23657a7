// Script to automatically add translation attributes to all pages
// Run this in browser console on each page to add missing translation attributes

function addTranslationAttributes() {
    console.log('🌐 Adding translation attributes to PixelGarage pages...');
    
    // Common navigation elements
    const navElements = {
        'Strona Główna': 'nav.home',
        'Home': 'nav.home',
        'Inicio': 'nav.home',
        'Katalog Usług': 'nav.services',
        'Services Catalog': 'nav.services',
        'Catálogo de Servicios': 'nav.services',
        'Portfolio': 'nav.portfolio',
        'Portafolio': 'nav.portfolio',
        'Tech Academy': 'nav.academy',
        'Kontakt Live': 'nav.contact',
        'Live Contact': 'nav.contact',
        'Contacto en Vivo': 'nav.contact',
        'Live Chat': 'nav.liveChat',
        'Chat en Vivo': 'nav.liveChat',
        'Zapisz się': 'nav.register',
        'Register': 'nav.register',
        'Registrarse': 'nav.register'
    };
    
    // Common buttons and actions
    const buttonElements = {
        'Czytaj więcej': 'common.readMore',
        'Read more': 'common.readMore',
        'Leer más': 'common.readMore',
        'Dowiedz się więcej': 'common.learnMore',
        'Learn more': 'common.learnMore',
        'Saber más': 'common.learnMore',
        'Rozpocznij': 'common.getStarted',
        'Get started': 'common.getStarted',
        'Comenzar': 'common.getStarted',
        'Kontakt': 'common.contact',
        'Contact': 'common.contact',
        'Contacto': 'common.contact',
        'Zamknij': 'common.close',
        'Close': 'common.close',
        'Cerrar': 'common.close',
        'Wyślij': 'common.submit',
        'Submit': 'common.submit',
        'Enviar': 'common.submit',
        'Anuluj': 'common.cancel',
        'Cancel': 'common.cancel',
        'Cancelar': 'common.cancel'
    };
    
    // Service categories
    const serviceCategories = {
        'Aplikacje Web': 'serviceCategories.web',
        'Web Applications': 'serviceCategories.web',
        'Aplicaciones Web': 'serviceCategories.web',
        'Mobile & PWA': 'serviceCategories.mobile',
        'Móvil y PWA': 'serviceCategories.mobile',
        'Aplikacje Desktop': 'serviceCategories.desktop',
        'Desktop Applications': 'serviceCategories.desktop',
        'Aplicaciones de Escritorio': 'serviceCategories.desktop',
        'Sztuczna Inteligencja': 'serviceCategories.ai',
        'Artificial Intelligence': 'serviceCategories.ai',
        'Inteligencia Artificial': 'serviceCategories.ai',
        'Automatyzacja Biznesowa': 'serviceCategories.automation',
        'Business Automation': 'serviceCategories.automation',
        'Automatización Empresarial': 'serviceCategories.automation',
        'Tworzenie Treści': 'serviceCategories.content',
        'Content Creation': 'serviceCategories.content',
        'Creación de Contenido': 'serviceCategories.content'
    };
    
    // Footer elements
    const footerElements = {
        'Tworzymy przyszłość cyfrową z technologią AI': 'footer.brand',
        'Creating digital future with AI technology': 'footer.brand',
        'Creando futuro digital con tecnología IA': 'footer.brand',
        'Usługi': 'footer.services',
        'Services': 'footer.services',
        'Servicios': 'footer.services',
        'Wszystkie prawa zastrzeżone.': 'footer.copyright',
        'All rights reserved.': 'footer.copyright',
        'Todos los derechos reservados.': 'footer.copyright'
    };
    
    // Combine all translation mappings
    const allTranslations = {
        ...navElements,
        ...buttonElements,
        ...serviceCategories,
        ...footerElements
    };
    
    // Function to add translation attribute to element
    function addTranslationToElement(element, translationKey) {
        if (!element.hasAttribute('data-translate')) {
            element.setAttribute('data-translate', translationKey);
            console.log(`✅ Added translation: "${element.textContent.trim()}" -> ${translationKey}`);
        }
    }
    
    // Process all text elements
    let addedCount = 0;
    
    // Find elements by text content
    Object.entries(allTranslations).forEach(([text, translationKey]) => {
        // Find all elements containing this text
        const xpath = `//text()[normalize-space(.)='${text}']/parent::*`;
        const result = document.evaluate(xpath, document, null, XPathResult.UNORDERED_NODE_SNAPSHOT_TYPE, null);
        
        for (let i = 0; i < result.snapshotLength; i++) {
            const element = result.snapshotItem(i);
            if (element && element.textContent.trim() === text) {
                addTranslationToElement(element, translationKey);
                addedCount++;
            }
        }
        
        // Also check for partial matches in buttons and links
        const buttons = document.querySelectorAll('button, a, span, div, h1, h2, h3, h4, h5, h6, p');
        buttons.forEach(button => {
            if (button.textContent.trim() === text && !button.hasAttribute('data-translate')) {
                addTranslationToElement(button, translationKey);
                addedCount++;
            }
        });
    });
    
    // Add specific page-based translations
    const currentPage = document.body.getAttribute('data-page') || 'unknown';
    
    switch (currentPage) {
        case 'landing':
            addLandingPageTranslations();
            break;
        case 'services':
            addServicesPageTranslations();
            break;
        case 'portfolio':
            addPortfolioPageTranslations();
            break;
        case 'academy':
            addAcademyPageTranslations();
            break;
        case 'communication':
            addCommunicationPageTranslations();
            break;
    }
    
    console.log(`🎉 Added ${addedCount} translation attributes!`);
    console.log('🔄 Run updatePageContent() to apply translations immediately.');
}

function addLandingPageTranslations() {
    // Hero section specific translations
    const heroElements = document.querySelectorAll('.hero-section *');
    heroElements.forEach(el => {
        const text = el.textContent.trim();
        if (text.includes('Rozpoczynamy przygodę z') && !el.hasAttribute('data-translate')) {
            el.setAttribute('data-translate', 'landing.hero.title');
        }
        if (text.includes('technologią AI') && !el.hasAttribute('data-translate')) {
            el.setAttribute('data-translate', 'landing.hero.titleHighlight');
        }
        if (text.includes('Twoja cyfrowa prawa ręka') && !el.hasAttribute('data-translate')) {
            el.setAttribute('data-translate', 'landing.hero.subtitle');
        }
    });
}

function addServicesPageTranslations() {
    // Services page specific translations
    const serviceElements = document.querySelectorAll('.services-section *');
    serviceElements.forEach(el => {
        const text = el.textContent.trim();
        if (text.includes('Katalog Usług') && !el.hasAttribute('data-translate')) {
            el.setAttribute('data-translate', 'services.hero.title');
        }
    });
}

function addPortfolioPageTranslations() {
    // Portfolio page specific translations
    const portfolioElements = document.querySelectorAll('.portfolio-section *');
    portfolioElements.forEach(el => {
        const text = el.textContent.trim();
        if (text.includes('Portfolio') && !el.hasAttribute('data-translate')) {
            el.setAttribute('data-translate', 'portfolio.hero.title');
        }
    });
}

function addAcademyPageTranslations() {
    // Academy page specific translations
    const academyElements = document.querySelectorAll('.academy-section *');
    academyElements.forEach(el => {
        const text = el.textContent.trim();
        if (text.includes('Tech Academy') && !el.hasAttribute('data-translate')) {
            el.setAttribute('data-translate', 'academy.hero.title');
        }
    });
}

function addCommunicationPageTranslations() {
    // Communication page specific translations
    const commElements = document.querySelectorAll('.communication-section *');
    commElements.forEach(el => {
        const text = el.textContent.trim();
        if (text.includes('Komunikacja Live') && !el.hasAttribute('data-translate')) {
            el.setAttribute('data-translate', 'communication.hero.title');
        }
    });
}

// Auto-run when script is loaded
if (typeof document !== 'undefined') {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', addTranslationAttributes);
    } else {
        addTranslationAttributes();
    }
}

// Export for manual use
if (typeof window !== 'undefined') {
    window.addTranslationAttributes = addTranslationAttributes;
}

console.log('🌐 Translation attribute script loaded. Run addTranslationAttributes() to add missing translations.');
