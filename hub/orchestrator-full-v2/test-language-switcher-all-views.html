<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Switcher Test - All Views</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .test-section h3 {
            color: white;
            margin-top: 0;
        }
        .test-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            color: white;
        }
        .translation-key {
            font-family: monospace;
            color: #ffd700;
            font-size: 12px;
            margin-top: 5px;
        }
        .view-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .view-link {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            text-decoration: none;
            color: white;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .view-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .view-link i {
            font-size: 2em;
            margin-bottom: 10px;
            display: block;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 10px;
        }
        .status-working { background: #4CAF50; }
        .status-error { background: #f44336; }
        .status-warning { background: #ff9800; }
    </style>
</head>
<body data-page="test">
    <!-- Test Navbar -->
    <nav class="navbar glass-effect">
        <div class="nav-container">
            <div class="nav-brand">
                <div class="logo-container">
                    <span class="logo-icon">🎯</span>
                    <span class="logo-text">Pixel<span class="logo-accent">Garage</span></span>
                </div>
            </div>
            <ul class="nav-menu">
                <li class="nav-item"><a href="#" class="nav-link" data-translate="nav.home">Strona Główna</a></li>
                <li class="nav-item"><a href="#" class="nav-link" data-translate="nav.services">Katalog Usług</a></li>
                <li class="nav-item"><a href="#" class="nav-link" data-translate="nav.portfolio">Portfolio</a></li>
                <li class="nav-item"><a href="#" class="nav-link" data-translate="nav.academy">Tech Academy</a></li>
                <li class="nav-item"><a href="#" class="nav-link" data-translate="nav.contact">Kontakt Live</a></li>
            </ul>
            <div class="nav-actions">
                <!-- Language switcher will be automatically added here -->
                <button class="btn-secondary">
                    <i class="fab fa-telegram"></i> <span data-translate="common.liveChat">Live Chat</span>
                </button>
                <button class="btn-primary">
                    <i class="fas fa-rocket"></i> <span data-translate="landing.hero.cta1">Rozpocznij Projekt</span>
                </button>
            </div>
        </div>
    </nav>

    <div class="test-container">
        <div class="test-header">
            <h1>🌐 Language Switcher Test - All Views</h1>
            <p>Sprawdź działanie przełącznika języków we wszystkich widokach aplikacji PixelGarage</p>
        </div>

        <div class="test-section">
            <h3>🔍 Status Systemu Tłumaczeń</h3>
            <div class="test-item">
                <strong>Translations.js loaded:</strong> 
                <span id="translationsStatus">Checking...</span>
                <span class="status-indicator" id="translationsIndicator"></span>
            </div>
            <div class="test-item">
                <strong>Language Switcher loaded:</strong> 
                <span id="switcherStatus">Checking...</span>
                <span class="status-indicator" id="switcherIndicator"></span>
            </div>
            <div class="test-item">
                <strong>Current Language:</strong> 
                <span id="currentLang">Detecting...</span>
            </div>
            <div class="test-item">
                <strong>Language Switcher in Navbar:</strong> 
                <span id="navbarSwitcher">Checking...</span>
                <span class="status-indicator" id="navbarIndicator"></span>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Test Translations</h3>
            <div class="test-item">
                <strong data-translate="nav.home">Strona Główna</strong>
                <div class="translation-key">nav.home</div>
            </div>
            <div class="test-item">
                <strong data-translate="common.telegram">Telegram</strong>
                <div class="translation-key">common.telegram</div>
            </div>
            <div class="test-item">
                <strong data-translate="landing.hero.title">Rozpoczynamy przygodę z</strong>
                <div class="translation-key">landing.hero.title</div>
            </div>
            <div class="test-item">
                <strong data-translate="brand.name">PixelGarage</strong>
                <div class="translation-key">brand.name</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔗 Test All Application Views</h3>
            <p>Kliknij poniższe linki, aby przetestować przełącznik języków w każdym widoku:</p>
            
            <div class="view-links">
                <a href="index.html" class="view-link">
                    <i class="fas fa-home"></i>
                    <strong>Landing Page</strong>
                    <div>Strona główna</div>
                </a>
                
                <a href="services/index.html" class="view-link">
                    <i class="fas fa-cogs"></i>
                    <strong>Services</strong>
                    <div>Katalog usług</div>
                </a>
                
                <a href="gallery/index.html" class="view-link">
                    <i class="fas fa-images"></i>
                    <strong>Portfolio</strong>
                    <div>Galeria projektów</div>
                </a>
                
                <a href="academy/index.html" class="view-link">
                    <i class="fas fa-graduation-cap"></i>
                    <strong>Tech Academy</strong>
                    <div>Akademia technologiczna</div>
                </a>
                
                <a href="communication/index.html" class="view-link">
                    <i class="fas fa-comments"></i>
                    <strong>Communication</strong>
                    <div>Kontakt live</div>
                </a>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 Instrukcje Testowania</h3>
            <div class="test-item">
                <strong>1.</strong> Sprawdź czy przełącznik języków jest widoczny w prawym górnym rogu nawigacji
            </div>
            <div class="test-item">
                <strong>2.</strong> Kliknij na przełącznik i wybierz różne języki (PL/EN/ES)
            </div>
            <div class="test-item">
                <strong>3.</strong> Sprawdź czy treść strony zmienia się natychmiast
            </div>
            <div class="test-item">
                <strong>4.</strong> Przejdź do innych widoków i powtórz test
            </div>
            <div class="test-item">
                <strong>5.</strong> Sprawdź czy wybór języka jest zapamiętywany między stronami
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/translations.js"></script>
    <script src="assets/js/language-switcher.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Starting language switcher test...');
            
            // Initialize translation system
            initializeLanguage();
            
            // Test system status
            setTimeout(() => {
                testSystemStatus();
            }, 1000);
        });

        function testSystemStatus() {
            // Test translations.js
            const translationsLoaded = typeof translations !== 'undefined';
            document.getElementById('translationsStatus').textContent = translationsLoaded ? 'Loaded ✓' : 'Not loaded ✗';
            document.getElementById('translationsIndicator').className = 'status-indicator ' + (translationsLoaded ? 'status-working' : 'status-error');
            
            // Test language switcher
            const switcherLoaded = typeof LanguageSwitcher !== 'undefined';
            document.getElementById('switcherStatus').textContent = switcherLoaded ? 'Loaded ✓' : 'Not loaded ✗';
            document.getElementById('switcherIndicator').className = 'status-indicator ' + (switcherLoaded ? 'status-working' : 'status-error');
            
            // Test current language
            const currentLang = getCurrentLanguage ? getCurrentLanguage() : 'Unknown';
            document.getElementById('currentLang').textContent = currentLang;
            
            // Test navbar switcher
            const navbarSwitcher = document.querySelector('.navbar .language-switcher');
            const hasNavbarSwitcher = navbarSwitcher !== null;
            document.getElementById('navbarSwitcher').textContent = hasNavbarSwitcher ? 'Found ✓' : 'Not found ✗';
            document.getElementById('navbarIndicator').className = 'status-indicator ' + (hasNavbarSwitcher ? 'status-working' : 'status-error');
            
            console.log('🔍 Test Results:', {
                translationsLoaded,
                switcherLoaded,
                currentLang,
                hasNavbarSwitcher
            });
        }
    </script>
</body>
</html>
