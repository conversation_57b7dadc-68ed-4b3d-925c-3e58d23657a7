/* ===== PREMIUM SEARCH PAGE STYLES ===== */

/* ===== PREMIUM NAVBAR FOR SEARCH PAGE ===== */
.premium-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.1) 100%);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 0;
}

.premium-navbar.scrolled {
    background: linear-gradient(135deg,
        rgba(10, 10, 10, 0.95) 0%,
        rgba(15, 15, 15, 0.95) 100%);
    border-bottom-color: rgba(102, 126, 234, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.navbar-content {
    display: grid;
    grid-template-columns: 1fr 3fr 2fr;
    align-items: center;
    padding: 16px 0;
    gap: 24px;
}

/* Logo Section (1/6) */
.navbar-logo {
    display: flex;
    align-items: center;
}

.logo-link {
    text-decoration: none;
    transition: transform 0.3s ease;
}

.logo-link:hover {
    transform: scale(1.05);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    font-size: 2rem;
    animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-3px); }
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--text-primary);
    letter-spacing: -0.5px;
}

.logo-accent {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Navigation Menu (3/6) */
.navbar-menu {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 32px;
}

.nav-link {
    position: relative;
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.95rem;
    padding: 8px 0;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.nav-link:hover {
    color: var(--text-primary);
    transform: translateY(-2px);
}

.nav-link.active {
    color: #667eea;
}

.nav-link-underline {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover .nav-link-underline,
.nav-link.active .nav-link-underline {
    width: 100%;
}

/* Action Buttons (2/6) */
.navbar-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 16px;
}

.btn-nav-primary,
.btn-nav-secondary {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    white-space: nowrap;
}

.btn-nav-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.btn-nav-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.btn-nav-secondary {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
}

.btn-nav-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    color: var(--text-primary);
    transform: translateY(-2px);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
}

.hamburger-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    transition: transform 0.3s ease;
}

.hamburger-btn:hover {
    transform: scale(1.1);
}

.hamburger-line {
    width: 24px;
    height: 2px;
    background: var(--text-primary);
    transition: all 0.3s ease;
    border-radius: 1px;
}

.hamburger-btn.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-btn.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.hamburger-btn.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Menu */
.mobile-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg,
        rgba(10, 10, 10, 0.98) 0%,
        rgba(15, 15, 15, 0.98) 100%);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

.mobile-menu-content {
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
    background: rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.3);
    color: #667eea;
    transform: translateX(8px);
}

.mobile-menu-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-mobile-primary,
.btn-mobile-secondary {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 16px;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-mobile-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-mobile-secondary {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
}

/* Navbar Responsive */
@media (max-width: 1024px) {
    .navbar-content {
        grid-template-columns: 1fr 2fr 1fr;
        gap: 16px;
    }

    .navbar-menu {
        gap: 24px;
    }

    .nav-link {
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    .navbar-menu,
    .navbar-actions {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
        justify-content: flex-end;
    }

    .navbar-content {
        grid-template-columns: 1fr auto;
        gap: 16px;
    }
}

@media (max-width: 480px) {
    .logo-text {
        font-size: 1.3rem;
    }

    .logo-icon {
        font-size: 1.8rem;
    }
}

/* Global Search Page Background */
.search-page-body {
    background: linear-gradient(180deg,
        #0a0a0a 0%,
        #0f0f0f 25%,
        #0a0a0a 50%,
        #0f0f0f 75%,
        #0a0a0a 100%);
    background-attachment: fixed;
    position: relative;
}

.search-page-body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(245, 158, 11, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.02) 0%, transparent 50%);
    z-index: -2;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: translateX(0) translateY(0) scale(1); }
    25% { transform: translateX(-2%) translateY(-1%) scale(1.02); }
    50% { transform: translateX(1%) translateY(2%) scale(0.98); }
    75% { transform: translateX(2%) translateY(-1%) scale(1.01); }
}

/* Premium Floating Search Section */
.floating-search-section {
    padding: var(--spacing-xl) 0;
    background: transparent;
    position: sticky;
    top: 80px;
    z-index: 100;
}

.floating-search-container {
    padding: var(--spacing-xl);
    border-radius: 32px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.12) 0%,
        rgba(255, 255, 255, 0.04) 50%,
        rgba(255, 255, 255, 0.12) 100%);
    backdrop-filter: blur(30px);
    border: 2px solid rgba(255, 255, 255, 0.15);
    box-shadow:
        0 32px 64px rgba(0, 0, 0, 0.4),
        0 16px 32px rgba(102, 126, 234, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-search-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.05) 0%,
        transparent 30%,
        transparent 70%,
        rgba(245, 158, 11, 0.05) 100%);
    border-radius: 32px;
    z-index: -1;
}

.floating-search-container:hover {
    transform: translateY(-4px);
    border-color: rgba(102, 126, 234, 0.3);
    box-shadow:
        0 40px 80px rgba(0, 0, 0, 0.5),
        0 20px 40px rgba(102, 126, 234, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.search-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.search-header h3 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.search-header p {
    color: var(--text-secondary);
    font-size: 1rem;
}

.search-form {
    max-width: 1000px;
    margin: 0 auto;
}

.search-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.search-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.search-group label {
    font-size: 0.9rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
}

.search-group select {
    padding: 16px 20px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.04) 100%);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    cursor: pointer;
    position: relative;
}

.search-group select:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.6);
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.12) 0%,
        rgba(255, 255, 255, 0.08) 100%);
    box-shadow:
        0 8px 32px rgba(102, 126, 234, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.search-group select:hover {
    border-color: rgba(255, 255, 255, 0.25);
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.06) 100%);
}

.search-input-row {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.search-input-row input {
    flex: 1;
    padding: 20px 24px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.04) 100%);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

.search-input-row input:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.6);
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.12) 0%,
        rgba(255, 255, 255, 0.08) 100%);
    box-shadow:
        0 8px 32px rgba(102, 126, 234, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.search-input-row input::placeholder {
    color: rgba(255, 255, 255, 0.5);
    font-weight: 400;
}

.search-btn {
    padding: 20px 32px;
    background: linear-gradient(135deg,
        #667eea 0%,
        #764ba2 50%,
        #667eea 100%);
    background-size: 200% 200%;
    color: white;
    border: none;
    border-radius: 20px;
    font-weight: 700;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 12px;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow:
        0 8px 32px rgba(102, 126, 234, 0.4),
        0 4px 16px rgba(102, 126, 234, 0.2);
    animation: buttonGlow 3s ease-in-out infinite;
}

@keyframes buttonGlow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.search-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent);
    transition: left 0.6s ease;
}

.search-btn:hover::before {
    left: 100%;
}

.search-btn:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
        0 16px 48px rgba(102, 126, 234, 0.5),
        0 8px 24px rgba(102, 126, 234, 0.3);
}

.search-btn:active {
    transform: translateY(-2px) scale(0.98);
}

.search-actions {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-md);
}

.filter-reset {
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.filter-reset:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

/* Search Hero Section */
.search-hero {
    padding: 120px 0 var(--spacing-xl);
    background: transparent;
    position: relative;
}

.search-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.05) 0%,
        rgba(118, 75, 162, 0.05) 100%);
    z-index: -1;
}

.search-hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

.breadcrumb a:hover {
    color: var(--secondary-color);
}

.search-hero-title {
    font-size: 3rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-md);
    line-height: 1.2;
}

.search-hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.search-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
}

.search-stat {
    text-align: center;
}

.search-count {
    display: block;
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
}

.search-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Advanced Search Section */
.advanced-search-section {
    padding: var(--spacing-xl) 0;
}

.advanced-search-container {
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
}

.search-toggle {
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.search-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
}

.advanced-search-form {
    margin-top: var(--spacing-lg);
}

.search-tags {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.search-tags-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

.search-tag {
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.8rem;
}

.search-tag:hover {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
}

/* Premium Search Results Section */
.search-results-section {
    padding: var(--spacing-2xl) 0;
    background: transparent;
    position: relative;
}

.search-results-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg,
        transparent 0%,
        rgba(102, 126, 234, 0.02) 30%,
        rgba(245, 158, 11, 0.01) 70%,
        transparent 100%);
    z-index: -1;
}

.search-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-xl);
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.04) 100%);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    backdrop-filter: blur(20px);
    box-shadow:
        0 16px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.results-info h2 {
    font-size: 2rem;
    font-weight: 800;
    background: linear-gradient(135deg,
        #ffffff 0%,
        #b8c5d6 50%,
        #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-sm);
}

.results-info p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 500;
}

.results-controls {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.view-toggle {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 4px;
    backdrop-filter: blur(10px);
}

.view-btn {
    padding: 12px 16px;
    background: transparent;
    border: none;
    border-radius: 8px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.view-btn.active,
.view-btn:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.search-results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.search-results-grid.list-view {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
}

/* Premium Search Result Cards */
.search-result-card {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.04) 50%,
        rgba(255, 255, 255, 0.08) 100%);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: var(--spacing-xl);
    backdrop-filter: blur(20px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.search-result-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(102, 126, 234, 0.1),
        transparent);
    transition: left 0.6s ease;
}

.search-result-card:hover::before {
    left: 100%;
}

.search-result-card:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(102, 126, 234, 0.3);
    box-shadow:
        0 24px 48px rgba(0, 0, 0, 0.4),
        0 12px 24px rgba(102, 126, 234, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.search-result-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.search-result-category {
    display: inline-block;
    padding: 6px 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.search-result-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #4ade80;
    text-shadow: 0 0 10px rgba(74, 222, 128, 0.3);
}

.search-result-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    line-height: 1.3;
}

.search-result-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
    font-size: 1rem;
}

.search-result-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.search-result-time {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.search-result-rating {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #fbbf24;
}

.search-result-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.search-result-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.search-result-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    flex: 1;
}

.search-result-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
}

.search-result-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.search-result-btn.primary:hover {
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

/* List View Styling */
.search-results-grid.list-view .search-result-card {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: var(--spacing-lg);
    align-items: center;
    padding: var(--spacing-lg);
}

.search-results-grid.list-view .search-result-header {
    margin-bottom: 0;
}

.search-results-grid.list-view .search-result-actions {
    flex-direction: column;
    min-width: 150px;
}

/* Search Result Tags */
.search-result-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-bottom: var(--spacing-md);
}

.search-result-tag {
    padding: 4px 12px;
    background: rgba(102, 126, 234, 0.2);
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 16px;
    color: #667eea;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.search-result-tag:hover {
    background: rgba(102, 126, 234, 0.3);
    border-color: rgba(102, 126, 234, 0.5);
    transform: translateY(-1px);
}

/* Premium Search Pagination */
.search-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    margin-top: var(--spacing-2xl);
    padding: var(--spacing-xl);
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.04) 100%);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    backdrop-filter: blur(20px);
}

.pagination-btn {
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    min-width: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(102, 126, 234, 0.3);
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.pagination-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
    transform: translateY(-2px);
}

.pagination-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.pagination-btn:disabled:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    transform: none;
}

.pagination-dots {
    color: var(--text-muted);
    font-weight: 600;
    padding: 0 8px;
}

/* Premium No Results Section */
.no-results-section {
    padding: var(--spacing-3xl) 0;
    text-align: center;
    background: transparent;
    position: relative;
}

.no-results-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg,
        transparent 0%,
        rgba(255, 255, 255, 0.02) 50%,
        transparent 100%);
    z-index: -1;
}

.no-results-content {
    max-width: 600px;
    margin: 0 auto;
    padding: var(--spacing-2xl);
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.04) 100%);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 32px;
    backdrop-filter: blur(20px);
}

.no-results-icon {
    font-size: 5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-xl);
    animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.no-results-content h3 {
    font-size: 2rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.no-results-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.7;
    font-size: 1.1rem;
}

.no-results-actions {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
}

.no-results-actions .btn-primary,
.no-results-actions .btn-secondary {
    padding: 16px 32px;
    border-radius: 16px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
}

.no-results-actions .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.no-results-actions .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

.no-results-actions .btn-primary:hover,
.no-results-actions .btn-secondary:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
}

/* Quick Contact Section */
.quick-contact-section {
    padding: var(--spacing-xl) 0;
    background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.02) 100%);
}

.quick-contact-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
}

.contact-content h3 {
    font-size: 1.3rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.contact-content p {
    color: var(--text-secondary);
}

.contact-actions {
    display: flex;
    gap: var(--spacing-md);
}

.btn-contact {
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
}

.btn-contact.telegram:hover {
    background: #0088cc;
    border-color: #0088cc;
    color: white;
}

.btn-contact.whatsapp:hover {
    background: #25d366;
    border-color: #25d366;
    color: white;
}

.btn-contact.email:hover {
    background: #ea4335;
    border-color: #ea4335;
    color: white;
}

/* Premium Responsive Design */
@media (max-width: 1024px) {
    .search-results-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: var(--spacing-lg);
    }

    .floating-search-container {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .search-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .search-input-row {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .search-btn {
        padding: 16px 24px;
        font-size: 1rem;
    }

    .search-hero-title {
        font-size: 2.5rem;
    }

    .search-results-header {
        flex-direction: column;
        gap: var(--spacing-lg);
        align-items: flex-start;
        padding: var(--spacing-lg);
    }

    .search-results-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .search-result-card {
        padding: var(--spacing-lg);
    }

    .search-result-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .search-result-btn {
        width: 100%;
        justify-content: center;
    }

    .search-pagination {
        padding: var(--spacing-lg);
        gap: 8px;
    }

    .pagination-btn {
        padding: 10px 12px;
        min-width: 40px;
    }

    .quick-contact-container {
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
        padding: var(--spacing-lg);
    }

    .contact-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .no-results-actions {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .no-results-content {
        padding: var(--spacing-lg);
    }

    .search-tags {
        justify-content: center;
    }

    .floating-search-container {
        margin: 0 var(--spacing-md);
        padding: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .search-hero-title {
        font-size: 2rem;
    }

    .search-group select,
    .search-input-row input {
        padding: 14px 16px;
        font-size: 0.9rem;
    }

    .search-btn {
        padding: 14px 20px;
        font-size: 0.9rem;
    }

    .search-result-title {
        font-size: 1.2rem;
    }

    .search-result-description {
        font-size: 0.9rem;
    }

    .search-result-btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    .no-results-icon {
        font-size: 3.5rem;
    }

    .no-results-content h3 {
        font-size: 1.5rem;
    }

    .floating-search-container {
        border-radius: 20px;
    }

    .search-result-card {
        border-radius: 16px;
    }
}
