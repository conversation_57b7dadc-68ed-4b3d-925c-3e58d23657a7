/* Premium Gallery Page Styles */

/* Premium Gallery Hero */
.gallery-hero {
    padding: 120px 0 80px 0;
    position: relative;
    overflow: hidden;
    text-align: center;
    background: radial-gradient(ellipse at center, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
}

.gallery-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(10px, 10px); }
}

.gallery-hero .hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.gallery-hero .hero-title {
    font-size: 3rem;
    font-weight: var(--font-weight-black);
    margin-bottom: var(--spacing-md);
}

.gallery-hero .hero-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.7;
}

.gallery-hero .hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    max-width: 500px;
    margin: 0 auto var(--spacing-xl);
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

/* Premium Filter Section */
.premium-filter-section {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.02) 50%, transparent 100%);
    position: relative;
    overflow: hidden;
}

.premium-filter-container {
    position: relative;
    z-index: 10;
}

/* Filter Header Premium */
.filter-header-premium {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-xl);
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.filter-title-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.filter-title {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

.filter-icon {
    font-size: 1.2rem;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.filter-stats {
    display: flex;
    align-items: baseline;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--primary-gradient);
    border-radius: var(--radius-lg);
    color: white;
    font-weight: var(--font-weight-medium);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.projects-count {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    animation: countUp 0.6s ease-out;
}

@keyframes countUp {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.projects-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Filter Controls */
.filter-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.view-controls-premium {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    padding: 4px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.view-btn-premium {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: var(--spacing-sm) var(--spacing-md);
    background: transparent;
    border: none;
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.view-btn-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: var(--radius-md);
}

.view-btn-premium.active::before,
.view-btn-premium:hover::before {
    opacity: 1;
}

.view-btn-premium.active,
.view-btn-premium:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.view-btn-premium i,
.view-btn-premium .view-label {
    position: relative;
    z-index: 1;
}

.view-label {
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
}

.sort-controls {
    position: relative;
}

.sort-select {
    padding: var(--spacing-sm) var(--spacing-lg);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

.sort-select:hover,
.sort-select:focus {
    border-color: var(--primary-color);
    background-color: rgba(255, 255, 255, 0.1);
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Premium Filter Tabs */
.filter-tabs-container {
    position: relative;
    margin-bottom: var(--spacing-2xl);
}

.filter-tabs {
    display: flex;
    gap: var(--spacing-xs);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.filter-tabs::-webkit-scrollbar {
    display: none;
}

.filter-tab {
    position: relative;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: var(--radius-lg);
    overflow: hidden;
    min-width: 120px;
    flex-shrink: 0;
}

.tab-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md) var(--spacing-lg);
    position: relative;
    z-index: 2;
}

.filter-tab i {
    font-size: 1.2rem;
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.tab-label {
    font-size: 0.9rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-count {
    font-size: 0.75rem;
    padding: 2px 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    color: var(--text-muted);
    transition: all 0.3s ease;
    min-width: 20px;
    text-align: center;
}

.tab-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 2px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
}

.filter-tab.active .tab-indicator {
    width: 80%;
}

.filter-tab.active i,
.filter-tab:hover i {
    color: var(--primary-color);
    transform: scale(1.1);
}

.filter-tab.active .tab-label,
.filter-tab:hover .tab-label {
    color: var(--text-primary);
}

.filter-tab.active .tab-count,
.filter-tab:hover .tab-count {
    background: var(--primary-gradient);
    color: white;
    transform: scale(1.05);
}

.filter-tab:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
}

.filter-tab.active {
    background: rgba(102, 126, 234, 0.1);
}

/* Filter Background Animation */
.filter-background-animation {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;
    border-radius: var(--radius-xl);
}

.animation-orb {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

.animation-orb.orb-1 {
    width: 100px;
    height: 100px;
    top: -50px;
    left: 10%;
    animation-delay: 0s;
}

.animation-orb.orb-2 {
    width: 80px;
    height: 80px;
    top: -40px;
    right: 20%;
    animation-delay: 2s;
}

.animation-orb.orb-3 {
    width: 60px;
    height: 60px;
    bottom: -30px;
    left: 50%;
    animation-delay: 4s;
}

/* Advanced Filters */
.advanced-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-xl);
}

.search-container {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-icon {
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 0.9rem;
    z-index: 2;
}

.search-input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 40px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: all 0.3s ease;
    outline: none;
}

.search-input::placeholder {
    color: var(--text-muted);
}

.search-input:focus {
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-clear {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.search-clear:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.quick-filters {
    display: flex;
    gap: var(--spacing-sm);
}

.quick-filter {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.quick-filter:hover {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.quick-filter.active {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
}

/* Projects Gallery */
.projects-gallery {
    padding: var(--spacing-2xl) 0;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.gallery-grid.masonry {
    columns: 3;
    column-gap: var(--spacing-lg);
}

.gallery-grid.list-view {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.gallery-grid.list-view .project-card {
    display: flex;
    flex-direction: row;
    height: 200px;
    max-width: none;
}

.gallery-grid.list-view .project-image {
    width: 300px;
    height: 100%;
    flex-shrink: 0;
}

.gallery-grid.list-view .project-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.gallery-grid.list-view .project-stats {
    grid-template-columns: repeat(6, 1fr);
}

.gallery-grid.list-view .project-actions {
    margin-top: auto;
}

.project-card {
    background: var(--background-card);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
}

.gallery-grid.masonry .project-card {
    break-inside: avoid;
    margin-bottom: var(--spacing-lg);
}

.project-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(255, 255, 255, 0.2);
}

.project-image {
    position: relative;
    height: 200px;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
    display: flex;
    align-items: flex-end;
    padding: var(--spacing-md);
    opacity: 0;
    transition: var(--transition-normal);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-badges {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    display: flex;
    gap: var(--spacing-xs);
}

.project-badge {
    padding: 4px 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
    backdrop-filter: blur(10px);
}

.project-badge.featured {
    background: var(--secondary-gradient);
}

.project-badge.new {
    background: var(--accent-gradient);
}

.project-content {
    padding: var(--spacing-xl);
}

.project-title {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.project-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
    font-size: 0.95rem;
}

.project-tech {
    display: flex;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.tech-tag {
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.project-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.stat-mini {
    text-align: center;
}

.stat-mini-number {
    font-size: 1rem;
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    display: block;
}

.stat-mini-label {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.project-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.btn-project {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    text-decoration: none;
    font-size: 0.9rem;
}

.btn-project.primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-project.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-project:hover {
    transform: translateY(-1px);
}

/* Project Modal */
.project-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2000;
    display: none;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
}

.project-modal.active {
    display: flex;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.modal-content {
    position: relative;
    max-width: 900px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    border-radius: var(--radius-lg);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
}

.modal-close {
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: var(--spacing-xl);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.modal-image {
    position: relative;
}

.modal-image img {
    width: 100%;
    border-radius: var(--radius-md);
}

.modal-badges {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    display: flex;
    gap: var(--spacing-xs);
}

.modal-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.modal-tech,
.modal-features {
    margin-bottom: var(--spacing-md);
}

.modal-tech h4,
.modal-features h4 {
    font-size: 1rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.modal-tech-list {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.modal-features-list {
    list-style: none;
}

.modal-features-list li {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    position: relative;
    padding-left: var(--spacing-md);
}

.modal-features-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: var(--font-weight-bold);
}

.modal-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.modal-footer {
    padding: var(--spacing-xl);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    gap: var(--spacing-md);
}

.btn-modal-primary,
.btn-modal-secondary {
    flex: 1;
    padding: var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    text-decoration: none;
}

.btn-modal-primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-modal-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-modal-primary:hover,
.btn-modal-secondary:hover {
    transform: translateY(-2px);
}

/* Contact Choice Modal */
.contact-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 3000;
    display: none;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
    animation: fadeIn 0.3s ease-out;
}

.contact-modal.active {
    display: flex;
}

.contact-modal-content {
    position: relative;
    max-width: 500px;
    width: 100%;
    border-radius: var(--radius-xl);
    overflow: hidden;
    animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.contact-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.contact-modal-header h3 {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
}

.contact-modal-body {
    padding: var(--spacing-xl);
}

.contact-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
    text-align: center;
}

.contact-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.contact-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.contact-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.contact-option:hover::before {
    left: 100%;
}

.contact-option:hover {
    transform: translateY(-4px);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.contact-option.telegram:hover {
    border-color: #0088cc;
    box-shadow: 0 12px 40px rgba(0, 136, 204, 0.3);
}

.contact-option.whatsapp:hover {
    border-color: #25d366;
    box-shadow: 0 12px 40px rgba(37, 211, 102, 0.3);
}

.contact-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.contact-option.telegram .contact-icon {
    background: linear-gradient(135deg, #0088cc 0%, #005f8a 100%);
}

.contact-option.whatsapp .contact-icon {
    background: linear-gradient(135deg, #25d366 0%, #1da851 100%);
}

.contact-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.contact-option:hover .contact-icon::before {
    opacity: 1;
}

.contact-info {
    flex: 1;
    text-align: left;
}

.contact-info h4 {
    font-size: 1.1rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.contact-info p {
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-xs) 0;
    font-size: 0.9rem;
}

.contact-badge {
    display: inline-block;
    padding: 2px 8px;
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
}

.contact-option.whatsapp .contact-badge {
    background: linear-gradient(135deg, #25d366 0%, #1da851 100%);
}

.contact-arrow {
    color: var(--text-muted);
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.contact-option:hover .contact-arrow {
    color: var(--text-primary);
    transform: translateX(4px);
}

.contact-footer {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.contact-note {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    color: var(--text-muted);
    font-size: 0.85rem;
    margin: 0;
}

.contact-note i {
    color: var(--primary-color);
}

/* Technologies Section */
.technologies-section {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.02) 100%);
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-lg);
}

.tech-item {
    text-align: center;
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    transition: var(--transition-normal);
}

.tech-item:hover {
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.1);
}

.tech-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.tech-item h4 {
    font-size: 1rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-xs);
}

.tech-item p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
    padding: var(--spacing-2xl) 0;
}

.cta-container {
    padding: var(--spacing-2xl);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xl);
}

.cta-content h2 {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
}

.cta-content p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.cta-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
    justify-content: center;
}

.btn-cta {
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    font-size: 1rem;
    min-width: 160px;
    justify-content: center;
}

.btn-cta.telegram {
    background: linear-gradient(135deg, #0088cc 0%, #229ed9 100%);
    color: white;
}

.btn-cta.whatsapp {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
}

.btn-cta.services {
    background: var(--primary-gradient);
    color: white;
}

.btn-cta:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Animations */
@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fade-in-up 0.6s ease-out forwards;
    opacity: 0;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .gallery-hero .hero-title {
        font-size: 2.5rem;
    }
    
    .gallery-hero .hero-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .filter-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }
    
    .filter-options {
        justify-content: flex-start;
    }
    
    .gallery-grid {
        grid-template-columns: 1fr;
    }
    
    .gallery-grid.masonry {
        columns: 1;
    }
    
    .modal-body {
        grid-template-columns: 1fr;
    }
    
    .tech-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .cta-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-cta {
        width: 100%;
    }
}
