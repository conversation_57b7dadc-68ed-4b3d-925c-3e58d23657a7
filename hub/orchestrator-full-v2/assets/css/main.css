/* PixelGarage - Premium CSS Framework */

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Premium Color Palette */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --dark-gradient: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);

    /* Premium Background System */
    --bg-primary: #0a0a0a;
    --bg-secondary: #0f0f0f;
    --bg-section-overlay: rgba(255, 255, 255, 0.01);
    --bg-hero-overlay: rgba(102, 126, 234, 0.02);
    --bg-portfolio-overlay: rgba(245, 158, 11, 0.01);
    --bg-academy-overlay: rgba(255, 215, 0, 0.02);
    --bg-contact-overlay: rgba(102, 126, 234, 0.02);

    /* Colors */
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #4facfe;
    --text-primary: #ffffff;
    --text-secondary: #b8c5d6;
    --text-muted: #8892a6;
    --background-dark: #0a0a0a;
    --background-card: rgba(255, 255, 255, 0.05);
    --border-color: rgba(255, 255, 255, 0.1);
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-black: 900;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    
    /* Shadows */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.25);
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

body {
    font-family: var(--font-primary);
    background: linear-gradient(180deg,
        #0a0a0a 0%,
        #0f0f0f 25%,
        #0a0a0a 50%,
        #0f0f0f 75%,
        #0a0a0a 100%);
    background-attachment: fixed;
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
}

/* Premium Global Background Effects */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(245, 158, 11, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.02) 0%, transparent 50%);
    z-index: -2;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: translateX(0) translateY(0) scale(1); }
    25% { transform: translateX(-2%) translateY(-1%) scale(1.02); }
    50% { transform: translateX(1%) translateY(2%) scale(0.98); }
    75% { transform: translateX(2%) translateY(-1%) scale(1.01); }
}

/* Glass Effect */
.glass-effect {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Premium Navigation */
/* ===== PREMIUM NAVBAR - PERFECT LAYOUT ===== */
.navbar {
    position: fixed !important;
    top: 0 !important;
    left: 0;
    right: 0;
    z-index: 1000 !important;
    height: 80px;
    background: rgba(10, 10, 10, 0.85);
    backdrop-filter: blur(30px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.15);
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.05) 0%,
        rgba(245, 158, 11, 0.03) 50%,
        rgba(102, 126, 234, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.navbar:hover::before {
    opacity: 1;
}

.navbar.scrolled {
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(40px);
    box-shadow: 0 12px 60px rgba(0, 0, 0, 0.25);
    height: 70px;
}

/* ===== PERFECT 3-COLUMN LAYOUT - 1/6 + 3/6 + 2/6 ===== */
.nav-container {
    display: grid;
    grid-template-columns: 1fr 3fr 2fr;
    align-items: center;
    height: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    gap: var(--spacing-md);
}

/* ===== LOGO SECTION - MAKSYMALNIE W LEWO ===== */
.nav-brand {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.nav-brand .logo-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) 0;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    text-decoration: none;
    color: inherit;
}

.nav-brand .logo-container:hover {
    transform: translateY(-2px) scale(1.05);
}

.logo-icon {
    font-size: 1.4rem;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    animation: logoFloat 8s ease-in-out infinite;
    filter: drop-shadow(0 0 8px rgba(102, 126, 234, 0.3));
}

.nav-brand:hover .logo-icon {
    transform: rotate(360deg) scale(1.2);
    filter: drop-shadow(0 0 15px rgba(102, 126, 234, 0.8));
}

@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-2px) rotate(90deg);
    }
    50% {
        transform: translateY(-4px) rotate(180deg);
    }
    75% {
        transform: translateY(-2px) rotate(270deg);
    }
}

.logo-text {
    font-size: 1.2rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    transition: all 0.5s ease;
    letter-spacing: -0.3px;
}

.logo-accent {
    background: var(--primary-gradient);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* ===== MENU NAWIGACYJNE - KOMPAKTOWE I ESTETYCZNE ===== */
.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-md);
    align-items: center;
    justify-content: center;
    height: 100%;
    margin: 0;
    padding: 0;
    background: rgba(255, 255, 255, 0.03);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(15px);
    padding: var(--spacing-xs) var(--spacing-sm);
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.nav-menu:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 6px 30px rgba(0, 0, 0, 0.12);
}

.nav-item {
    position: relative;
    display: flex;
    align-items: center;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    overflow: hidden;
    white-space: nowrap;
    font-size: 0.85rem;
    border: 1px solid transparent;
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(10px);
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(245, 158, 11, 0.2));
    transition: left 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: -1;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover {
    color: var(--text-primary);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(245, 158, 11, 0.08));
    transform: translateY(-3px) scale(1.08);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    border-color: rgba(102, 126, 234, 0.4);
    filter: brightness(1.2);
}

.nav-link.active {
    color: var(--text-primary);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.25), rgba(245, 158, 11, 0.15));
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.5);
    border-color: rgba(102, 126, 234, 0.5);
    transform: translateY(-2px) scale(1.05);
    animation: activeGlow 3s ease-in-out infinite;
}

@keyframes activeGlow {
    0%, 100% {
        box-shadow: 0 8px 30px rgba(102, 126, 234, 0.5);
    }
    50% {
        box-shadow: 0 12px 40px rgba(102, 126, 234, 0.7);
    }
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 2px;
    animation: activeIndicator 0.5s ease-out;
}

@keyframes activeIndicator {
    from {
        width: 0%;
        opacity: 0;
    }
    to {
        width: 80%;
        opacity: 1;
    }
}

/* ===== PRZYCISKI AKCJI - PERFEKCYJNIE ROZŁOŻONE W 2/6 ===== */
.nav-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    justify-content: space-evenly;
    background: rgba(255, 255, 255, 0.05);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    width: 100%;
}

.nav-actions::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 70%;
    background: linear-gradient(to bottom,
        transparent,
        rgba(102, 126, 234, 0.8),
        rgba(245, 158, 11, 0.6),
        rgba(102, 126, 234, 0.8),
        transparent);
    border-radius: 2px;
    animation: separatorGlow 4s ease-in-out infinite;
}

@keyframes separatorGlow {
    0%, 100% {
        opacity: 0.6;
        transform: translateY(-50%) scaleY(1);
    }
    50% {
        opacity: 1;
        transform: translateY(-50%) scaleY(1.1);
    }
}

.nav-actions:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

/* ===== PREMIUM PRZYCISKI AKCJI ===== */
.nav-actions .btn-secondary,
.nav-actions .btn-primary {
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    font-weight: var(--font-weight-medium);
    letter-spacing: 0.1px;
    padding: 10px 16px;
    border-radius: var(--radius-lg);
    font-size: 0.85rem;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 40px;
    line-height: 1.3;
    justify-content: center;
}

/* Live Chat - krótszy tekst, mniejszy przycisk */
.nav-actions .btn-secondary {
    flex: 0.8;
    max-width: 110px;
}

/* Zamów Usługę - dłuższy tekst, większy przycisk */
.nav-actions .btn-primary {
    flex: 1.2;
    max-width: 160px;
}

.nav-actions .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border-color: rgba(255, 255, 255, 0.3);
}

.nav-actions .btn-primary {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(102, 126, 234, 0.7));
    color: white;
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.nav-actions .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

.nav-actions .btn-primary:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 1), rgba(102, 126, 234, 0.9));
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.6);
    border-color: rgba(102, 126, 234, 0.8);
}

.nav-actions .btn-secondary:hover::before,
.nav-actions .btn-primary:hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: premiumShimmer 1s ease-out;
}

@keyframes premiumShimmer {
    0% { left: -100%; opacity: 0; }
    50% { opacity: 1; }
    100% { left: 100%; opacity: 0; }
}

/* Premium Buttons */
.btn-primary,
.btn-secondary {
    padding: var(--spacing-xs) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    text-decoration: none;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--primary-gradient);
    background-size: 200% 200%;
    color: var(--text-primary);
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    animation: gradientShift 3s ease-in-out infinite;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.5);
    filter: brightness(1.1);
}

.btn-primary:active {
    transform: translateY(-1px) scale(0.98);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-secondary:hover::before {
    left: 100%;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px) scale(1.02);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);
}

.btn-secondary:active {
    transform: translateY(0) scale(0.98);
}

.btn-primary.large,
.btn-secondary.large {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1rem;
    border-radius: var(--radius-lg);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
}

.btn-primary.large:hover {
    transform: translateY(-4px) scale(1.03);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.6);
}

.btn-secondary.large:hover {
    transform: translateY(-3px) scale(1.03);
    box-shadow: 0 8px 30px rgba(255, 255, 255, 0.15);
}

/* Premium Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: transparent;
    padding-top: 120px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(10px, 10px); }
}

.gradient-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(60px);
    opacity: 0.4;
    animation: premiumFloat 8s ease-in-out infinite;
}

.orb-1 {
    width: 400px;
    height: 400px;
    background: var(--primary-gradient);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.orb-2 {
    width: 300px;
    height: 300px;
    background: var(--secondary-gradient);
    top: 60%;
    right: 20%;
    animation-delay: 2.5s;
}

.orb-3 {
    width: 350px;
    height: 350px;
    background: var(--accent-gradient);
    bottom: 20%;
    left: 60%;
    animation-delay: 5s;
}

.orb-4 {
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    top: 30%;
    right: 10%;
    animation-delay: 1.5s;
}

.orb-5 {
    width: 150px;
    height: 150px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    bottom: 40%;
    left: 20%;
    animation-delay: 3.5s;
}

@keyframes premiumFloat {
    0%, 100% {
        transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
    }
    25% {
        transform: translateY(-30px) translateX(20px) rotate(90deg) scale(1.1);
    }
    50% {
        transform: translateY(-20px) translateX(-15px) rotate(180deg) scale(0.9);
    }
    75% {
        transform: translateY(10px) translateX(-25px) rotate(270deg) scale(1.05);
    }
}

.hero-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    font-size: 0.85rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: var(--font-weight-black);
    line-height: 1.1;
    margin-bottom: var(--spacing-md);
}

.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle-text {
    font-size: 2rem;
    font-weight: var(--font-weight-normal);
    color: var(--text-secondary);
    line-height: 1.2;
}

.hero-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.7;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    justify-content: center;
    align-items: center;
}

.hero-trust {
    text-align: center;
}

.credentials-text {
    color: var(--text-muted);
    margin-bottom: var(--spacing-sm);
    font-size: 0.9rem;
}

.credentials-badges {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
    flex-wrap: wrap;
}

.badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* Profile Showcase */
.profile-showcase {
    display: flex;
    justify-content: center;
}

.profile-card {
    width: 100%;
    max-width: 400px;
    padding: var(--spacing-xl);
}

.profile-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.profile-avatar {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    overflow: hidden;
}

.profile-avatar .avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.status-indicator {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid var(--background-dark);
}

.status-indicator.online {
    background: #10b981;
}

.profile-info h3 {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-xs);
}

.profile-info p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.profile-badges {
    display: flex;
    gap: var(--spacing-xs);
}

.badge-small {
    padding: 2px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    font-size: 0.7rem;
    color: var(--text-secondary);
}

.profile-skills {
    margin-bottom: var(--spacing-lg);
}

.skill-item {
    margin-bottom: var(--spacing-md);
}

.skill-name {
    display: block;
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.skill-bar {
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.skill-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 3px;
    transition: width 1s ease;
}

.profile-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

/* Premium Split Contact Button */
.split-contact-btn {
    display: flex;
    border-radius: 12px;
    overflow: hidden;
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.split-contact-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 12px 32px rgba(0, 0, 0, 0.2),
        0 4px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}

/* Premium glow effect on hover */
.split-contact-btn::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
        rgba(0, 136, 204, 0.3) 0%,
        rgba(37, 211, 102, 0.3) 100%);
    border-radius: 14px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.split-contact-btn:hover::after {
    opacity: 1;
}

.btn-contact-telegram,
.btn-contact-whatsapp {
    flex: 1;
    padding: 12px 16px;
    border: none;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

/* Telegram Button */
.btn-contact-telegram {
    background: linear-gradient(135deg, #0088cc 0%, #005580 100%);
    color: white;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-contact-telegram::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-contact-telegram:hover {
    background: linear-gradient(135deg, #0099dd 0%, #006699 100%);
    transform: scale(1.02);
}

.btn-contact-telegram:hover::before {
    left: 100%;
}

/* WhatsApp Button */
.btn-contact-whatsapp {
    background: linear-gradient(135deg, #25d366 0%, #1da851 100%);
    color: white;
}

.btn-contact-whatsapp::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-contact-whatsapp:hover {
    background: linear-gradient(135deg, #2ee673 0%, #20b85c 100%);
    transform: scale(1.02);
}

.btn-contact-whatsapp:hover::before {
    left: 100%;
}

/* Icons styling */
.btn-contact-telegram i,
.btn-contact-whatsapp i {
    font-size: 1.1rem;
}

/* Active states */
.btn-contact-telegram:active {
    transform: scale(0.98);
}

.btn-contact-whatsapp:active {
    transform: scale(0.98);
}

/* Responsive styles for split contact button */
@media (max-width: 768px) {
    .split-contact-btn {
        border-radius: 10px;
    }

    .btn-contact-telegram,
    .btn-contact-whatsapp {
        padding: 10px 12px;
        font-size: 0.85rem;
        gap: 6px;
    }

    .btn-contact-telegram i,
    .btn-contact-whatsapp i {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .split-contact-btn {
        border-radius: 8px;
    }

    .btn-contact-telegram,
    .btn-contact-whatsapp {
        padding: 8px 10px;
        font-size: 0.8rem;
        gap: 4px;
    }

    .btn-contact-telegram i,
    .btn-contact-whatsapp i {
        font-size: 0.9rem;
    }
}

.btn-profile-primary,
.btn-profile-secondary {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    text-decoration: none;
}

.btn-profile-primary {
    background: var(--primary-gradient);
    color: var(--text-primary);
}

/* Premium Tech Academy Button with Orange Theme */
.btn-profile-secondary {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border: 1px solid rgba(245, 158, 11, 0.3);
    position: relative;
    overflow: hidden;
    box-shadow:
        0 4px 16px rgba(245, 158, 11, 0.2),
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    animation: academyFloat 4s ease-in-out infinite;
}

/* Premium shimmer effect for Tech Academy button */
.btn-profile-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

/* Premium glow effect */
.btn-profile-secondary::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
    filter: blur(4px);
}

/* Hover effects */
.btn-profile-secondary:hover {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    border-color: rgba(245, 158, 11, 0.5);
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 8px 24px rgba(245, 158, 11, 0.3),
        0 4px 16px rgba(0, 0, 0, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
    animation-play-state: paused;
}

.btn-profile-secondary:hover::before {
    left: 100%;
}

.btn-profile-secondary:hover::after {
    opacity: 0.6;
}

/* Active state */
.btn-profile-secondary:active {
    transform: translateY(-1px) scale(0.98);
}

/* Floating animation for Tech Academy button */
@keyframes academyFloat {
    0%, 100% { transform: translateY(0px); }
    25% { transform: translateY(-2px); }
    50% { transform: translateY(-1px); }
    75% { transform: translateY(-3px); }
}

/* Responsive styles for Tech Academy button */
@media (max-width: 768px) {
    .btn-profile-secondary {
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    .btn-profile-secondary i {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .btn-profile-secondary {
        padding: 8px 12px;
        font-size: 0.85rem;
    }

    .btn-profile-secondary i {
        font-size: 0.9rem;
    }
}

/* Premium Hamburger Menu - moved to premium section */

/* Services Preview Section */
.services-preview {
    padding: var(--spacing-2xl) 0;
    background: transparent;
    position: relative;
}

.services-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg,
        transparent 0%,
        rgba(255, 255, 255, 0.01) 50%,
        transparent 100%);
    z-index: -1;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.section-title {
    font-size: 2.5rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-md);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Premium Services Grid - 5 Cards in One Row */
.services-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1.5rem;
    max-width: 1800px;
    margin: 0 auto;
    padding: 3rem 0;
    position: relative;
    justify-items: center;
    align-items: start;
}

/* Ensure all cards have equal height */
.service-card {
    width: 100%;
    max-width: 320px;
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Premium floating background effect for 5 cards */
.services-grid::before {
    content: '';
    position: absolute;
    top: -60px;
    left: -60px;
    right: -60px;
    bottom: -60px;
    background:
        radial-gradient(ellipse at 10% 50%, rgba(102, 126, 234, 0.08) 0%, transparent 40%),
        radial-gradient(ellipse at 30% 50%, rgba(16, 185, 129, 0.06) 0%, transparent 40%),
        radial-gradient(ellipse at 50% 50%, rgba(245, 158, 11, 0.06) 0%, transparent 40%),
        radial-gradient(ellipse at 70% 50%, rgba(139, 92, 246, 0.06) 0%, transparent 40%),
        radial-gradient(ellipse at 90% 50%, rgba(239, 68, 68, 0.06) 0%, transparent 40%);
    border-radius: 60px;
    z-index: -1;
    animation: breathe 8s ease-in-out infinite;
}

/* Premium grid pattern overlay */
.services-grid::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
    background-size: 30px 30px;
    z-index: -1;
    opacity: 0.5;
    animation: gridMove 20s linear infinite;
}

@keyframes breathe {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.6; }
    33% { transform: scale(1.03) rotate(1deg); opacity: 0.8; }
    66% { transform: scale(1.01) rotate(-0.5deg); opacity: 0.7; }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(30px, 30px); }
}

/* Premium Service Card */
.service-card {
    padding: 2.5rem;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 24px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 2px 8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Premium glass morphism overlay */
.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 30%,
        transparent 70%,
        rgba(102, 126, 234, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    border-radius: inherit;
    pointer-events: none;
}

/* Premium shimmer effect */
.service-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%);
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    transition: transform 0.6s ease;
    pointer-events: none;
}

/* Premium hover effects */
.service-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow:
        0 20px 60px rgba(102, 126, 234, 0.15),
        0 8px 32px rgba(0, 0, 0, 0.2),
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(102, 126, 234, 0.3);
    background: rgba(255, 255, 255, 0.08);
}

.service-card:hover::before {
    opacity: 1;
}

.service-card:hover::after {
    transform: translateX(100%) translateY(100%) rotate(45deg);
}

/* Premium Card Variants */

/* Premium Card (Darmowa Konsultacja) */
.service-card.premium-card {
    background: linear-gradient(135deg,
        rgba(16, 185, 129, 0.1) 0%,
        rgba(5, 150, 105, 0.05) 50%,
        rgba(16, 185, 129, 0.1) 100%);
    border: 2px solid rgba(16, 185, 129, 0.2);
    position: relative;
    overflow: hidden;
}

.service-card.premium-card::before {
    background: linear-gradient(135deg,
        rgba(16, 185, 129, 0.15) 0%,
        transparent 30%,
        transparent 70%,
        rgba(16, 185, 129, 0.1) 100%);
}

.service-card.premium-card:hover {
    border-color: rgba(16, 185, 129, 0.4);
    box-shadow:
        0 20px 60px rgba(16, 185, 129, 0.2),
        0 8px 32px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(16, 185, 129, 0.3);
}

.premium-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 50%, #10b981 100%) !important;
    box-shadow:
        0 8px 24px rgba(16, 185, 129, 0.4),
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.service-card.premium-card:hover .premium-icon {
    box-shadow:
        0 16px 40px rgba(16, 185, 129, 0.5),
        0 4px 16px rgba(0, 0, 0, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.3) !important;
}

.feature-tag.premium {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.3));
    border: 1px solid rgba(16, 185, 129, 0.3);
    color: #10b981;
    font-weight: 600;
}

.premium-cta {
    color: #10b981 !important;
    font-weight: 600;
}

/* Triangle Card (Trójkąt Przełamany) */
.service-card.triangle-card {
    background: linear-gradient(135deg,
        rgba(245, 158, 11, 0.1) 0%,
        rgba(217, 119, 6, 0.05) 50%,
        rgba(245, 158, 11, 0.1) 100%);
    border: 2px solid rgba(245, 158, 11, 0.2);
    position: relative;
}

.service-card.triangle-card::before {
    background: linear-gradient(135deg,
        rgba(245, 158, 11, 0.15) 0%,
        transparent 30%,
        transparent 70%,
        rgba(245, 158, 11, 0.1) 100%);
}

.service-card.triangle-card:hover {
    border-color: rgba(245, 158, 11, 0.4);
    box-shadow:
        0 20px 60px rgba(245, 158, 11, 0.2),
        0 8px 32px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(245, 158, 11, 0.3);
}

.triangle-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #f59e0b 100%) !important;
    box-shadow:
        0 8px 24px rgba(245, 158, 11, 0.4),
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.service-card.triangle-card:hover .triangle-icon {
    box-shadow:
        0 16px 40px rgba(245, 158, 11, 0.5),
        0 4px 16px rgba(0, 0, 0, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.3) !important;
}

.triangle-features {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.feature-tag.triangle {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(217, 119, 6, 0.3));
    border: 1px solid rgba(245, 158, 11, 0.3);
    color: #f59e0b;
    font-weight: 600;
    font-size: 0.8rem;
}

.triangle-cta {
    color: #f59e0b !important;
    font-weight: 600;
}

/* Academy Card (Tech Academy Exclusive) */
.service-card.academy-card {
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.1) 0%,
        rgba(124, 58, 237, 0.05) 50%,
        rgba(139, 92, 246, 0.1) 100%);
    border: 2px solid rgba(139, 92, 246, 0.2);
    position: relative;
}

.service-card.academy-card::before {
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.15) 0%,
        transparent 30%,
        transparent 70%,
        rgba(139, 92, 246, 0.1) 100%);
}

.service-card.academy-card:hover {
    border-color: rgba(139, 92, 246, 0.4);
    box-shadow:
        0 20px 60px rgba(139, 92, 246, 0.2),
        0 8px 32px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(139, 92, 246, 0.3);
}

.academy-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #8b5cf6 100%) !important;
    box-shadow:
        0 8px 24px rgba(139, 92, 246, 0.4),
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.service-card.academy-card:hover .academy-icon {
    box-shadow:
        0 16px 40px rgba(139, 92, 246, 0.5),
        0 4px 16px rgba(0, 0, 0, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.3) !important;
}

.academy-features {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.feature-tag.academy {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(124, 58, 237, 0.3));
    border: 1px solid rgba(139, 92, 246, 0.3);
    color: #8b5cf6;
    font-weight: 600;
    font-size: 0.8rem;
}

.academy-cta {
    color: #8b5cf6 !important;
    font-weight: 600;
}

/* Premium Animations for All 5 Cards */
.service-card {
    animation: cardFloat 10s ease-in-out infinite;
}

/* Staggered animation delays for 5 cards */
.service-card:nth-child(1) {
    animation-delay: 0s;
}

.service-card:nth-child(2) {
    animation-delay: 2s;
}

.service-card:nth-child(3) {
    animation-delay: 4s;
}

.service-card:nth-child(4) {
    animation-delay: 6s;
}

.service-card:nth-child(5) {
    animation-delay: 8s;
}

@keyframes cardFloat {
    0%, 100% { transform: translateY(0px); }
    25% { transform: translateY(-5px); }
    50% { transform: translateY(-2px); }
    75% { transform: translateY(-8px); }
}

/* Hover pause animation for all cards */
.service-card:hover {
    animation-play-state: paused;
}

/* Premium Particle Effects */
.services-grid {
    position: relative;
}

.services-grid::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 40% 60%, rgba(245, 158, 11, 0.1) 1px, transparent 1px);
    background-size: 50px 50px, 70px 70px, 60px 60px;
    animation: particleFloat 15s linear infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes particleFloat {
    0% { transform: translate(0, 0); }
    100% { transform: translate(-50px, -50px); }
}

/* Premium Glow Effects on Hover */
.service-card:hover {
    position: relative;
}

.service-card:hover::before {
    box-shadow:
        0 0 30px rgba(102, 126, 234, 0.3),
        inset 0 0 30px rgba(255, 255, 255, 0.1);
}

.service-card.premium-card:hover::before {
    box-shadow:
        0 0 30px rgba(16, 185, 129, 0.4),
        inset 0 0 30px rgba(16, 185, 129, 0.1);
}

.service-card.triangle-card:hover::before {
    box-shadow:
        0 0 30px rgba(245, 158, 11, 0.4),
        inset 0 0 30px rgba(245, 158, 11, 0.1);
}

.service-card.academy-card:hover::before {
    box-shadow:
        0 0 30px rgba(139, 92, 246, 0.4),
        inset 0 0 30px rgba(139, 92, 246, 0.1);
}

/* Premium Text Effects */
.service-title {
    position: relative;
    overflow: hidden;
}

.service-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.8), transparent);
    transition: left 0.5s ease;
}

.service-card:hover .service-title::after {
    left: 100%;
}

.service-card.premium-card:hover .service-title::after {
    background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.8), transparent);
}

.service-card.triangle-card:hover .service-title::after {
    background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.8), transparent);
}

.service-card.academy-card:hover .service-title::after {
    background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.8), transparent);
}

.service-card.premium-card {
    border: 2px solid transparent;
    background: linear-gradient(var(--background-card), var(--background-card)) padding-box,
                var(--primary-gradient) border-box;
}

.premium-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--secondary-gradient);
    color: white;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: var(--font-weight-bold);
}

/* Premium Service Icon */
.service-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
    background-size: 200% 200%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto 1.5rem;
    position: relative;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow:
        0 8px 24px rgba(102, 126, 234, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    animation: iconFloat 6s ease-in-out infinite;
}

/* Premium icon floating animation */
@keyframes iconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-8px) rotate(2deg); }
    50% { transform: translateY(-4px) rotate(0deg); }
    75% { transform: translateY(-12px) rotate(-2deg); }
}

/* Premium icon glow effect */
.service-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #667eea, #764ba2, #667eea);
    background-size: 200% 200%;
    border-radius: 22px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Premium hover effects for icon */
.service-card:hover .service-icon {
    transform: translateY(-8px) scale(1.1) rotate(5deg);
    box-shadow:
        0 16px 40px rgba(102, 126, 234, 0.4),
        0 4px 16px rgba(0, 0, 0, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
    background-position: 100% 50%;
    animation: none;
}

.service-card:hover .service-icon::before {
    opacity: 1;
}

/* Premium Service Title */
.service-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.service-card:hover .service-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transform: translateY(-2px);
}

/* Premium Service Description */
.service-description {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.5rem;
    line-height: 1.7;
    font-size: 1rem;
    text-align: center;
    transition: all 0.3s ease;
}

.service-card:hover .service-description {
    color: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
}

.service-features {
    display: flex;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.feature-tag {
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.service-cta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--primary-color);
    font-weight: var(--font-weight-medium);
}

.service-cta i {
    transition: var(--transition-fast);
}

.service-card:hover .service-cta i {
    transform: translateX(4px);
}

/* Quick Contact Section */
.quick-contact {
    padding: var(--spacing-2xl) 0;
    background: transparent;
    position: relative;
}

.quick-contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg,
        transparent 0%,
        rgba(102, 126, 234, 0.02) 50%,
        transparent 100%);
    z-index: -1;
}

.contact-container {
    padding: var(--spacing-2xl);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xl);
}

.contact-content h2 {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
}

.contact-content p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.contact-actions {
    display: flex;
    gap: var(--spacing-md);
}

.btn-contact {
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    font-size: 1rem;
    min-width: 150px;
    justify-content: center;
}

.btn-contact.telegram {
    background: linear-gradient(135deg, #0088cc 0%, #229ed9 100%);
    color: white;
}

.btn-contact.whatsapp {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
}

.btn-contact:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Footer */
.footer {
    background: transparent;
    padding: var(--spacing-2xl) 0 var(--spacing-lg) 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg,
        transparent 0%,
        rgba(0, 0, 0, 0.2) 100%);
    z-index: -1;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-xl);
}

.footer-brand p {
    color: var(--text-secondary);
    margin-top: var(--spacing-sm);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-xl);
}

.footer-section h4 {
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.footer-section a {
    display: block;
    color: var(--text-secondary);
    text-decoration: none;
    margin-bottom: var(--spacing-sm);
    transition: var(--transition-fast);
}

.footer-section a:hover {
    color: var(--text-primary);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Responsive Design */

/* Premium Services Grid Responsive Breakpoints - 5 Cards */

/* Extra Large screens - 5 columns in one row */
@media (min-width: 1400px) {
    .services-grid {
        grid-template-columns: repeat(5, 1fr);
        max-width: 1800px;
        gap: 2rem;
    }

    .service-card {
        max-width: 320px;
    }
}

/* Large screens - 5 columns in one row (smaller cards) */
@media (max-width: 1399px) and (min-width: 1200px) {
    .services-grid {
        grid-template-columns: repeat(5, 1fr);
        max-width: 1600px;
        gap: 1.5rem;
    }

    .service-card {
        max-width: 280px;
        padding: 2rem;
    }

    .service-icon {
        width: 70px;
        height: 70px;
        font-size: 1.75rem;
    }

    .service-title {
        font-size: 1.25rem;
    }

    .service-description {
        font-size: 0.9rem;
    }
}

/* Medium Large screens - 3 columns (2 rows) */
@media (max-width: 1199px) and (min-width: 900px) {
    .services-grid {
        grid-template-columns: repeat(3, 1fr);
        max-width: 1200px;
        gap: 2rem;
    }

    .service-card {
        max-width: 350px;
    }
}

/* Medium screens - 2 columns */
@media (max-width: 899px) and (min-width: 600px) {
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        max-width: 800px;
        gap: 1.5rem;
    }

    .service-card {
        max-width: 380px;
    }
}

/* Small screens - 1 column */
@media (max-width: 599px) {
    .services-grid {
        grid-template-columns: 1fr;
        max-width: 400px;
        gap: 1.5rem;
        padding: 2rem 0;
    }

    .services-grid::before {
        top: -30px;
        left: -30px;
        right: -30px;
        bottom: -30px;
        border-radius: 30px;
    }

    .service-card {
        padding: 2rem;
        max-width: 100%;
    }

    .service-icon {
        width: 70px;
        height: 70px;
        font-size: 1.75rem;
        margin-bottom: 1.25rem;
    }

    .service-title {
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }

    .service-description {
        font-size: 0.9rem;
        margin-bottom: 1.25rem;
    }
}

@media (max-width: 768px) {
    .navbar {
        left: 5px;
        right: 5px;
        border-radius: 0 0 15px 15px;
        position: fixed !important;
        top: 0 !important;
        z-index: 1000 !important;
    }

    .nav-menu {
        display: none;
        position: fixed;
        top: 70px;
        left: 5px;
        right: 5px;
        background: rgba(10, 10, 10, 0.98);
        backdrop-filter: blur(30px);
        flex-direction: column;
        padding: var(--spacing-lg);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        transform: translateY(-100%);
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        opacity: 0;
        z-index: 999;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    }

    .nav-menu.active {
        display: flex;
        transform: translateY(0);
        opacity: 1;
    }

    .nav-item {
        margin: var(--spacing-sm) 0;
        transform: translateX(-30px);
        opacity: 0;
        animation: slideInLeft 0.3s ease-out forwards;
    }

    .nav-menu.active .nav-item:nth-child(1) { animation-delay: 0.1s; }
    .nav-menu.active .nav-item:nth-child(2) { animation-delay: 0.2s; }
    .nav-menu.active .nav-item:nth-child(3) { animation-delay: 0.3s; }
    .nav-menu.active .nav-item:nth-child(4) { animation-delay: 0.4s; }
    .nav-menu.active .nav-item:nth-child(5) { animation-delay: 0.5s; }

    @keyframes slideInLeft {
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .hamburger {
        display: flex;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-xl);
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .subtitle-text {
        font-size: 1.6rem;
        line-height: 1.1;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .credentials-badges {
        justify-content: center;
    }

    .contact-actions {
        flex-direction: column;
        width: 100%;
    }

    .btn-contact {
        width: 100%;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-links {
        grid-template-columns: repeat(2, 1fr);
    }

    .vip-academy-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .vip-features {
        grid-template-columns: 1fr;
    }

    .vip-badges {
        flex-direction: column;
        align-items: center;
    }

    .vip-academy-title {
        font-size: 2.5rem;
    }

    .premium-projects-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }

    .premium-project-card {
        max-width: 100%;
    }

    .project-actions-premium {
        flex-direction: column;
    }

    .project-metrics-premium {
        grid-template-columns: repeat(2, 1fr);
    }

    .cta-actions {
        flex-direction: column;
        align-items: center;
    }

    .cta-actions .btn-primary.large,
    .cta-actions .btn-secondary.large {
        width: 100%;
        max-width: 300px;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease forwards;
}

/* Premium Floating Particles System */
.floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;
}

.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: particleFloat 6s ease-in-out infinite;
}

.particle:nth-child(odd) {
    background: rgba(102, 126, 234, 0.4);
}

.particle:nth-child(3n) {
    background: rgba(245, 158, 11, 0.3);
}

@keyframes particleFloat {
    0%, 100% {
        transform: translateY(0px) translateX(0px) scale(1);
        opacity: 0.3;
    }
    25% {
        transform: translateY(-100px) translateX(50px) scale(1.2);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-200px) translateX(-30px) scale(0.8);
        opacity: 0.4;
    }
    75% {
        transform: translateY(-150px) translateX(-80px) scale(1.1);
        opacity: 0.7;
    }
}

/* Premium Glass Morphism */
.glass-effect {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    position: relative;
    overflow: hidden;
}

.glass-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
}

.glass-effect:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

/* Premium Mobile Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-lg);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 2px;
}

.hamburger:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    border-color: rgba(102, 126, 234, 0.3);
}

.hamburger.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
    background: var(--primary-color);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
    background: var(--primary-color);
}

/* Premium Floating Contact Widget */
.floating-contact-widget {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.contact-toggle {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    position: relative;
    animation: pulse 2s infinite;
}

.contact-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.6);
}

.contact-toggle i {
    font-size: 1.5rem;
    color: white;
}

.contact-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

.contact-options {
    position: absolute;
    bottom: 70px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    opacity: 0;
    transform: translateY(20px) scale(0.8);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    pointer-events: none;
}

.floating-contact-widget.expanded .contact-options {
    opacity: 1;
    transform: translateY(0) scale(1);
    pointer-events: all;
}

.contact-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: var(--font-weight-medium);
    white-space: nowrap;
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.contact-option.telegram {
    background: linear-gradient(135deg, #0088cc 0%, #229ed9 100%);
    color: white;
}

.contact-option.whatsapp {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
}

.contact-option:hover {
    transform: translateX(-5px) scale(1.05);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

/* ===== PREMIUM VIP TECH ACADEMY SECTION ===== */
.vip-academy-section-premium {
    position: relative;
    padding: 120px 0;
    background: transparent;
    overflow: hidden;
}

.vip-academy-section-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg,
        transparent 0%,
        rgba(245, 158, 11, 0.03) 30%,
        rgba(255, 215, 0, 0.02) 70%,
        transparent 100%);
    z-index: -1;
}

/* Premium Background Effects */
.academy-background-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255, 215, 0, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(245, 158, 11, 0.4), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.2), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 215, 0, 0.2), transparent);
    background-repeat: repeat;
    background-size: 200px 200px;
    animation: floatingParticles 20s linear infinite;
}

@keyframes floatingParticles {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-200px) rotate(360deg); }
}

.gradient-orbs {
    position: absolute;
    width: 100%;
    height: 100%;
}

.orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    opacity: 0.6;
    animation: orbFloat 8s ease-in-out infinite;
}

.orb-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(245, 158, 11, 0.4) 0%, transparent 70%);
    top: 10%;
    left: -10%;
    animation-delay: 0s;
}

.orb-2 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
    top: 60%;
    right: -5%;
    animation-delay: 2s;
}

.orb-3 {
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(217, 119, 6, 0.4) 0%, transparent 70%);
    bottom: 20%;
    left: 50%;
    animation-delay: 4s;
}

@keyframes orbFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-30px) scale(1.1); }
}

/* Premium Container */
.vip-academy-premium-container {
    position: relative;
    z-index: 2;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.02) 50%,
        rgba(255, 255, 255, 0.08) 100%);
    border: 2px solid rgba(255, 215, 0, 0.2);
    border-radius: 32px;
    padding: 60px;
    backdrop-filter: blur(20px);
    box-shadow:
        0 32px 64px rgba(0, 0, 0, 0.4),
        0 16px 32px rgba(245, 158, 11, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.vip-academy-premium-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(245, 158, 11, 0.05) 0%,
        transparent 30%,
        transparent 70%,
        rgba(255, 215, 0, 0.05) 100%);
    border-radius: 32px;
    z-index: -1;
}

/* Premium Header */
.vip-academy-premium-header {
    text-align: center;
    margin-bottom: 80px;
}

.premium-badges-container {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.premium-badge {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 16px 32px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.exclusive-badge {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%);
    color: #1a1a1a;
    box-shadow:
        0 8px 32px rgba(255, 215, 0, 0.4),
        0 4px 16px rgba(255, 215, 0, 0.2);
}

.limited-badge {
    background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 50%, #ff6b6b 100%);
    color: white;
    box-shadow:
        0 8px 32px rgba(255, 107, 107, 0.4),
        0 4px 16px rgba(255, 107, 107, 0.2);
}

.badge-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50px;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.6), rgba(255, 107, 107, 0.6));
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.premium-badge:hover .badge-glow {
    opacity: 1;
    animation: glowPulse 2s ease-in-out infinite;
}

@keyframes glowPulse {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.05); opacity: 1; }
}

/* Badge Sparkles Effect */
.badge-sparkles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.sparkle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: white;
    border-radius: 50%;
    animation: sparkleFloat 3s ease-in-out infinite;
}

.sparkle-1 {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.sparkle-2 {
    top: 30%;
    right: 25%;
    animation-delay: 1s;
}

.sparkle-3 {
    bottom: 25%;
    left: 70%;
    animation-delay: 2s;
}

@keyframes sparkleFloat {
    0%, 100% { opacity: 0; transform: translateY(0px) scale(0); }
    50% { opacity: 1; transform: translateY(-10px) scale(1); }
}

.flame-effect {
    position: absolute;
    top: -5px;
    right: 10px;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
    border-radius: 50% 50% 50% 0;
    animation: flameFlicker 1.5s ease-in-out infinite;
}

@keyframes flameFlicker {
    0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.8; }
    50% { transform: rotate(10deg) scale(1.1); opacity: 1; }
}

/* Premium Title */
.premium-title-container {
    position: relative;
    margin-bottom: 40px;
}

.vip-academy-premium-title {
    font-size: 4rem;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 16px;
    text-align: center;
}

.title-main {
    display: block;
    background: linear-gradient(135deg,
        #ffd700 0%,
        #ffed4e 25%,
        #ffffff 50%,
        #ffed4e 75%,
        #ffd700 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleShimmer 4s ease-in-out infinite;
}

.title-subtitle {
    display: block;
    font-size: 1.8rem;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 8px;
}

@keyframes titleShimmer {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.title-underline {
    position: relative;
    width: 200px;
    height: 4px;
    background: linear-gradient(90deg, transparent, #ffd700, transparent);
    margin: 24px auto 0;
    border-radius: 2px;
}

.underline-glow {
    position: absolute;
    top: -2px;
    left: -10px;
    right: -10px;
    bottom: -2px;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.6), transparent);
    border-radius: 4px;
    filter: blur(4px);
    animation: underlineGlow 3s ease-in-out infinite;
}

@keyframes underlineGlow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.vip-academy-premium-description {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.85);
    max-width: 900px;
    margin: 0 auto;
    line-height: 1.8;
    text-align: center;
}

/* Premium Features Grid */
.premium-features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 32px;
    margin: 80px 0;
}

.premium-feature-card {
    position: relative;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 24px;
    padding: 40px 32px;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.premium-feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 215, 0, 0.1),
        transparent);
    transition: left 0.6s ease;
}

.premium-feature-card:hover::before {
    left: 100%;
}

.premium-feature-card:hover {
    transform: translateY(-12px) scale(1.02);
    border-color: rgba(255, 215, 0, 0.4);
    box-shadow:
        0 24px 48px rgba(0, 0, 0, 0.3),
        0 12px 24px rgba(255, 215, 0, 0.2);
}

.feature-card-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
        rgba(255, 215, 0, 0.3),
        rgba(245, 158, 11, 0.3));
    border-radius: 24px;
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.premium-feature-card:hover .feature-card-glow {
    opacity: 1;
}

/* Premium Feature Icons */
.feature-icon-premium {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #ffd700;
    z-index: 2;
}

.icon-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 215, 0, 0.2) 0%,
        rgba(245, 158, 11, 0.2) 100%);
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 50%;
    transition: all 0.4s ease;
}

.premium-feature-card:hover .icon-background {
    background: linear-gradient(135deg,
        rgba(255, 215, 0, 0.3) 0%,
        rgba(245, 158, 11, 0.3) 100%);
    border-color: rgba(255, 215, 0, 0.6);
    transform: scale(1.1);
}

.icon-pulse {
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid rgba(255, 215, 0, 0.4);
    border-radius: 50%;
    animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.3; }
}

.feature-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 16px;
    transition: color 0.3s ease;
}

.premium-feature-card:hover .feature-title {
    color: #ffd700;
}

.feature-description {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.feature-highlight {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
        transparent,
        #ffd700,
        transparent);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.premium-feature-card:hover .feature-highlight {
    transform: scaleX(1);
}

/* Premium CTA Section */
.premium-cta-section {
    position: relative;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.02) 100%);
    border: 2px solid rgba(255, 215, 0, 0.2);
    border-radius: 32px;
    padding: 48px;
    text-align: center;
    overflow: hidden;
}

.cta-background-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle,
        rgba(255, 215, 0, 0.1) 0%,
        transparent 50%);
    animation: ctaGlow 8s ease-in-out infinite;
    z-index: -1;
}

@keyframes ctaGlow {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.5; }
    50% { transform: scale(1.1) rotate(180deg); opacity: 0.8; }
}

.premium-status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    margin-bottom: 32px;
    position: relative;
}

.status-pulse-ring {
    position: absolute;
    width: 24px;
    height: 24px;
    border: 2px solid rgba(255, 215, 0, 0.6);
    border-radius: 50%;
    animation: pulseRing 2s ease-out infinite;
}

@keyframes pulseRing {
    0% { transform: scale(0.8); opacity: 1; }
    100% { transform: scale(2); opacity: 0; }
}

.status-dot-premium {
    width: 12px;
    height: 12px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    border-radius: 50%;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
    animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.status-text-premium {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.premium-benefits-showcase {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin: 32px 0;
    flex-wrap: wrap;
}

.benefit-premium-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg,
        rgba(255, 215, 0, 0.2) 0%,
        rgba(245, 158, 11, 0.2) 100%);
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    color: #ffd700;
    transition: all 0.3s ease;
}

.benefit-premium-badge:hover {
    background: linear-gradient(135deg,
        rgba(255, 215, 0, 0.3) 0%,
        rgba(245, 158, 11, 0.3) 100%);
    border-color: rgba(255, 215, 0, 0.5);
    transform: translateY(-2px);
}

.premium-benefits-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin: 32px 0;
    padding: 32px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.05) 0%,
        rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.benefit-item-premium {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    padding: 8px 0;
}

.benefit-item-premium:hover {
    color: #ffd700;
    transform: translateX(8px);
}

.benefit-check-icon {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 255, 136, 0.3);
    transition: all 0.3s ease;
}

.benefit-item-premium:hover .benefit-check-icon {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
    transform: scale(1.1);
}

/* Premium Action Buttons */
.premium-action-buttons {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 40px;
}

.btn-premium-primary,
.btn-premium-secondary {
    position: relative;
    padding: 20px 40px;
    border: none;
    border-radius: 16px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-premium-primary {
    background: linear-gradient(135deg,
        #ffd700 0%,
        #ffed4e 25%,
        #ffd700 50%,
        #ffed4e 75%,
        #ffd700 100%);
    background-size: 200% 200%;
    color: #1a1a1a;
    box-shadow:
        0 12px 32px rgba(255, 215, 0, 0.4),
        0 6px 16px rgba(255, 215, 0, 0.2);
    animation: primaryButtonGlow 3s ease-in-out infinite;
}

@keyframes primaryButtonGlow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.btn-premium-secondary {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    color: #ffffff;
    border: 2px solid rgba(255, 215, 0, 0.3);
    box-shadow:
        0 8px 24px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.btn-background-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 215, 0, 0.1) 50%,
        rgba(255, 255, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 1;
}

.btn-premium-primary:hover .btn-background-effect,
.btn-premium-secondary:hover .btn-background-effect {
    opacity: 1;
}

.btn-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.btn-shine-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent);
    transition: left 0.6s ease;
    z-index: 3;
}

.btn-premium-primary:hover .btn-shine-effect {
    left: 100%;
}

.btn-premium-primary:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
        0 20px 40px rgba(255, 215, 0, 0.5),
        0 10px 20px rgba(255, 215, 0, 0.3);
}

.btn-premium-secondary:hover {
    transform: translateY(-4px) scale(1.02);
    border-color: rgba(255, 215, 0, 0.6);
    box-shadow:
        0 16px 32px rgba(0, 0, 0, 0.3),
        0 8px 16px rgba(255, 215, 0, 0.2);
}

/* ===== PREMIUM VIP ACADEMY RESPONSIVE ===== */

/* Tablet and smaller */
@media (max-width: 1024px) {
    .vip-academy-premium-container {
        padding: 40px;
    }

    .premium-features-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .vip-academy-premium-title {
        font-size: 3rem;
    }

    .premium-badges-container {
        gap: 16px;
    }

    .premium-badge {
        padding: 12px 24px;
        font-size: 0.8rem;
    }
}

/* Mobile */
@media (max-width: 768px) {
    .vip-academy-section-premium {
        padding: 80px 0;
    }

    .vip-academy-premium-container {
        padding: 32px 24px;
        border-radius: 24px;
    }

    .vip-academy-premium-title {
        font-size: 2.5rem;
    }

    .title-subtitle {
        font-size: 1.4rem;
    }

    .vip-academy-premium-description {
        font-size: 1.1rem;
    }

    .premium-badges-container {
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }

    .premium-feature-card {
        padding: 32px 24px;
    }

    .feature-icon-premium {
        width: 64px;
        height: 64px;
        font-size: 1.6rem;
    }

    .premium-cta-section {
        padding: 32px 24px;
    }

    .premium-benefits-showcase {
        flex-direction: column;
        align-items: center;
    }

    .premium-benefits-list {
        padding: 24px;
    }
}

/* Small mobile */
@media (max-width: 480px) {
    .vip-academy-premium-title {
        font-size: 2rem;
    }

    .title-subtitle {
        font-size: 1.2rem;
    }

    .premium-badge {
        padding: 10px 20px;
        font-size: 0.75rem;
    }

    .premium-feature-card {
        padding: 24px 20px;
    }

    .feature-title {
        font-size: 1.2rem;
    }

    .feature-description {
        font-size: 0.9rem;
    }

    .btn-premium-primary,
    .btn-premium-secondary {
        padding: 16px 32px;
        font-size: 1rem;
    }
}

.progress-mini {
    text-align: center;
}

.progress-bar-mini {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: var(--spacing-xs);
}

.progress-fill-mini {
    height: 100%;
    background: linear-gradient(90deg, #f59e0b 0%, #10b981 100%);
    border-radius: 3px;
    transition: width 1s ease;
}

.progress-text-mini {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.vip-pricing {
    margin-bottom: var(--spacing-lg);
}

.price-comparison-mini {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
}

.old-price {
    font-size: 1rem;
    color: var(--text-muted);
    text-decoration: line-through;
}

.new-price {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
}

.discount-badge {
    padding: 4px 8px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: var(--font-weight-bold);
}

.vip-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.btn-vip-primary,
.btn-vip-secondary {
    width: 100%;
    padding: var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    position: relative;
    overflow: hidden;
}

.btn-vip-primary {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(245, 158, 11, 0.4);
}

.btn-vip-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-vip-primary:hover,
.btn-vip-secondary:hover {
    transform: translateY(-2px);
}

.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-vip-primary:hover .btn-shine {
    left: 100%;
}

/* Premium Portfolio Showcase */
.premium-portfolio-section {
    padding: var(--spacing-3xl) 0;
    background: transparent;
    position: relative;
    overflow: hidden;
}

.premium-portfolio-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg,
        transparent 0%,
        rgba(102, 126, 234, 0.02) 30%,
        rgba(245, 158, 11, 0.01) 70%,
        transparent 100%);
    z-index: -1;
}

.premium-portfolio-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(168, 85, 247, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.premium-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border-radius: var(--radius-full);
    color: white;
    font-size: 0.875rem;
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-md);
    box-shadow: 0 4px 20px rgba(245, 158, 11, 0.3);
}

.premium-projects-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.premium-project-card {
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    border-radius: var(--radius-xl);
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    position: relative;
}

.premium-project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    border-radius: var(--radius-xl);
}

.premium-project-card:hover::before {
    opacity: 1;
}

.premium-project-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.15);
}

.project-image-premium {
    position: relative;
    height: 240px;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.project-image-premium img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    display: block;
}

.premium-project-card:hover .project-image-premium img {
    transform: scale(1.08);
}

.project-overlay-premium {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    right: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.project-badge-premium {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: var(--font-weight-bold);
    color: white;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.project-badge-premium.revenue {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(21, 128, 61, 0.9) 100%);
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.project-badge-premium.featured {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.9) 0%, rgba(124, 58, 237, 0.9) 100%);
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.project-badge-premium.innovative {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(37, 99, 235, 0.9) 100%);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.project-badge-premium.perfection {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.9) 0%, rgba(217, 119, 6, 0.9) 100%);
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.project-status-premium {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(0, 0, 0, 0.7);
    border-radius: var(--radius-md);
    backdrop-filter: blur(10px);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.active {
    background: #22c55e;
}

.status-dot.beta {
    background: #f59e0b;
}

.status-dot.development {
    background: #3b82f6;
}

.status-dot.completed {
    background: #10b981;
}

.status-text {
    font-size: 0.75rem;
    color: white;
    font-weight: var(--font-weight-medium);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.project-content-premium {
    padding: var(--spacing-xl);
}

.project-header-premium {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.project-header-premium h3 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
}

.project-rating {
    display: flex;
    align-items: center;
    gap: 2px;
}

.project-rating .fas.fa-star {
    color: #fbbf24;
    font-size: 0.875rem;
}

.rating-text {
    margin-left: var(--spacing-xs);
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

.project-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
    font-size: 0.95rem;
}

.project-tech-premium {
    display: flex;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.tech-tag-premium {
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.08);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
}

.tech-tag-premium:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.2);
}

.tech-tag-premium.ai {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.2) 0%, rgba(168, 85, 247, 0.2) 100%);
    border-color: rgba(99, 102, 241, 0.3);
    color: #a5b4fc;
}

.project-metrics-premium {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.03);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.metric-premium {
    text-align: center;
}

.metric-number {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    display: block;
    margin-bottom: 2px;
}

.metric-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-weight: var(--font-weight-medium);
}

.project-actions-premium {
    display: flex;
    gap: var(--spacing-sm);
}

.btn-primary-premium {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-color) 0%, #4338ca 100%);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-semibold);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

.btn-primary-premium:hover {
    background: linear-gradient(135deg, #4338ca 0%, #3730a3 100%);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.btn-secondary-premium {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-secondary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-semibold);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

.btn-secondary-premium:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.premium-portfolio-cta {
    text-align: center;
    padding: var(--spacing-2xl);
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.05);
    max-width: 800px;
    margin: 0 auto;
}

.cta-content {
    margin-bottom: var(--spacing-xl);
}

.cta-content h3 {
    font-size: 1.75rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.cta-content p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.6;
}

.cta-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

.cta-actions .btn-primary.large,
.cta-actions .btn-secondary.large {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1rem;
    min-width: 200px;
}

/* Additional Responsive Styles for Premium Portfolio */
@media (max-width: 1200px) {
    .premium-projects-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xl);
    }
}

/* ===== RESPONSIVE DESIGN - PREMIUM NAVBAR ===== */
@media (max-width: 1200px) {
    .nav-container {
        grid-template-columns: 1fr 2.5fr 1.8fr;
        padding: 0 var(--spacing-md);
        gap: var(--spacing-sm);
    }

    .nav-menu {
        gap: var(--spacing-sm);
        padding: 6px var(--spacing-xs);
    }

    .nav-link {
        padding: 6px 10px;
        font-size: 0.8rem;
    }

    .nav-actions .btn-secondary {
        padding: 8px 10px;
        font-size: 0.8rem;
        flex: 0.8;
        max-width: 90px;
    }

    .nav-actions .btn-primary {
        padding: 8px 14px;
        font-size: 0.8rem;
        flex: 1.2;
        max-width: 130px;
    }
}

@media (max-width: 1024px) {
    .nav-container {
        grid-template-columns: 1fr 2fr 1.5fr;
        gap: var(--spacing-xs);
    }

    .nav-menu {
        gap: 6px;
        padding: 4px 6px;
    }

    .nav-link {
        padding: 4px 8px;
        font-size: 0.75rem;
    }

    .logo-text {
        font-size: 1rem;
    }

    .logo-icon {
        font-size: 1.2rem;
    }

    .nav-actions {
        gap: var(--spacing-xs);
        padding: 4px 6px;
    }

    .nav-actions .btn-secondary {
        padding: 6px 10px;
        font-size: 0.75rem;
        flex: 0.8;
        max-width: 70px;
        gap: 4px;
    }

    .nav-actions .btn-primary {
        padding: 6px 12px;
        font-size: 0.75rem;
        flex: 1.2;
        max-width: 100px;
        gap: 4px;
    }
}

@media (max-width: 900px) {
    .nav-container {
        grid-template-columns: 1fr 1.8fr 1.2fr;
    }

    .nav-menu {
        gap: 4px;
        padding: 2px 4px;
    }

    .nav-link {
        padding: 4px 6px;
        font-size: 0.7rem;
    }

    .nav-actions {
        gap: 4px;
        padding: 2px 4px;
    }

    .nav-actions .btn-secondary {
        padding: 6px 8px;
        font-size: 0.7rem;
        gap: 2px;
        min-height: 30px;
        flex: 0.8;
        max-width: 60px;
    }

    .nav-actions .btn-primary {
        padding: 6px 10px;
        font-size: 0.7rem;
        gap: 2px;
        min-height: 30px;
        flex: 1.2;
        max-width: 85px;
    }
}

@media (max-width: 768px) {
    .nav-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 var(--spacing-md);
    }

    .nav-menu {
        display: none;
        position: fixed;
        top: 80px;
        left: 10px;
        right: 10px;
        background: rgba(10, 10, 10, 0.98);
        backdrop-filter: blur(40px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-xl);
        padding: var(--spacing-lg);
        flex-direction: column;
        gap: var(--spacing-md);
        box-shadow: 0 20px 80px rgba(0, 0, 0, 0.5);
        z-index: 999;
        transform: translateY(-20px);
        opacity: 0;
        transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .nav-menu.active {
        display: flex;
        transform: translateY(0);
        opacity: 1;
    }

    .nav-actions {
        gap: var(--spacing-xs);
        padding: var(--spacing-xs);
        background: rgba(255, 255, 255, 0.05);
    }

    .nav-actions::before {
        display: none;
    }

    .logo-text {
        font-size: 1rem;
    }

    .logo-icon {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .navbar {
        height: 70px;
    }

    .nav-container {
        padding: 0 var(--spacing-sm);
    }

    .nav-brand .logo-container {
        gap: 4px;
        padding: 4px 8px;
    }

    .logo-text {
        font-size: 0.9rem;
    }

    .logo-icon {
        font-size: 1.1rem;
    }

    .nav-actions {
        gap: 4px;
        padding: 4px 6px;
    }

    .nav-actions .btn-secondary,
    .nav-actions .btn-primary {
        padding: 8px 12px;
        font-size: 0.8rem;
        min-width: auto;
        gap: 4px;
    }

    .nav-actions .btn-secondary span,
    .nav-actions .btn-primary span {
        display: none;
    }

    .nav-actions .btn-secondary i,
    .nav-actions .btn-primary i {
        margin: 0;
        font-size: 0.9rem;
    }

    .nav-menu {
        top: 70px;
        padding: var(--spacing-md);
        gap: var(--spacing-sm);
    }

    .premium-projects-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .project-header-premium {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }

    .project-rating {
        align-self: flex-start;
    }

    .hero-title {
        font-size: 2rem;
    }

    .subtitle-text {
        font-size: 1.3rem;
        line-height: 1.1;
    }
}

@media (max-width: 640px) {
    .premium-portfolio-section {
        padding: var(--spacing-xl) 0;
    }

    .premium-projects-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .project-image-premium {
        height: 200px;
    }

    .project-content-premium {
        padding: var(--spacing-lg);
    }

    .project-header-premium h3 {
        font-size: 1.25rem;
    }

    .project-metrics-premium {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .premium-portfolio-cta {
        padding: var(--spacing-xl);
        margin: 0 var(--spacing-md);
    }

    .cta-content h3 {
        font-size: 1.5rem;
    }

    .cta-content p {
        font-size: 1rem;
    }

    .floating-contact-widget {
        bottom: 15px;
        right: 15px;
    }

    .contact-toggle {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .contact-option {
        min-width: 100px;
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
    }
}

/* Floating Contact Widget */
.floating-contact-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    transition: var(--transition-normal);
}

.contact-toggle {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: var(--shadow-xl);
    transition: var(--transition-fast);
    position: relative;
    animation: pulse-glow 3s infinite;
}

.contact-toggle:hover {
    transform: scale(1.1);
}

.contact-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
    animation: bounce 2s infinite;
}

.contact-options {
    position: absolute;
    bottom: 70px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: var(--transition-normal);
}

.floating-contact-widget.expanded .contact-options {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.contact-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-lg);
    color: white;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: var(--transition-fast);
    backdrop-filter: blur(20px);
    min-width: 120px;
    justify-content: flex-start;
}

.contact-option.telegram {
    background: linear-gradient(135deg, #0088cc 0%, #229ed9 100%);
}

.contact-option.whatsapp {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
}

.contact-option:hover {
    transform: translateX(-5px) scale(1.05);
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}

/* Premium Navbar Animations */
@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

@keyframes navbarGlow {
    0%, 100% {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    50% {
        box-shadow: 0 8px 40px rgba(102, 126, 234, 0.2);
    }
}

.navbar.scrolled {
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(30px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    animation: navbarGlow 3s ease-in-out infinite;
}

/* Premium Nav Link Active State */
.nav-link.active {
    color: var(--text-primary);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(245, 158, 11, 0.2));
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    border-color: rgba(102, 126, 234, 0.5);
    transform: translateY(-2px);
}

/* Premium Logo Animation */
.logo-icon {
    animation: logoFloat 6s ease-in-out infinite, logoRotate 12s linear infinite;
}

@keyframes logoRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
