/* Tech Academy Page Specific Styles */

/* Premium Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 20px rgba(245, 158, 11, 0.3); }
    50% { box-shadow: 0 0 40px rgba(245, 158, 11, 0.6); }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Premium Academy Hero */
.academy-hero {
    padding: 120px 0 80px 0;
    position: relative;
    overflow: hidden;
    text-align: center;
    animation: slideInUp 1s ease-out;
    background: radial-gradient(ellipse at center, rgba(245, 158, 11, 0.1) 0%, transparent 70%);
}

.academy-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    animation: gridMove 20s linear infinite;
    z-index: -1;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(10px, 10px); }
}

.academy-hero .hero-content {
    max-width: 900px;
    margin: 0 auto;
}

.vip-badge {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border: none;
    animation: glow 2s ease-in-out infinite;
    position: relative;
    overflow: hidden;
}

.vip-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 3s ease-in-out infinite;
}

.academy-hero .hero-title {
    font-size: 3.5rem;
    font-weight: var(--font-weight-black);
    margin-bottom: var(--spacing-md);
    line-height: 1.1;
    animation: slideInUp 1s ease-out 0.3s both;
}

.academy-hero .hero-title .gradient-text {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #f59e0b 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shimmer 3s ease-in-out infinite;
}

.academy-hero .hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.7;
}

.academy-hero .hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    max-width: 600px;
    margin: 0 auto var(--spacing-xl);
    animation: slideInUp 1s ease-out 0.6s both;
}

.stat-item {
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
    animation: float 6s ease-in-out infinite;
}

.stat-item:nth-child(2) {
    animation-delay: 2s;
}

.stat-item:nth-child(3) {
    animation-delay: 4s;
}

.stat-item:hover {
    transform: translateY(-10px) scale(1.05);
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.3);
}

.launch-status {
    margin-bottom: var(--spacing-xl);
}

/* Launch Announcement Styling */
.launch-announcement {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(20px);
    animation: glow 3s ease-in-out infinite;
}

.announcement-icon {
    font-size: 3rem;
    color: var(--primary-color);
    animation: float 4s ease-in-out infinite;
}

.announcement-content {
    text-align: center;
}

.announcement-content h3 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.announcement-content p {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.5;
}

.urgency-indicator {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.urgency-badge,
.benefit-highlight {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    color: var(--text-secondary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.urgency-badge:hover,
.benefit-highlight:hover {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.3);
    transform: translateY(-2px);
}

.urgency-badge i,
.benefit-highlight i {
    color: var(--primary-color);
}

.progress-container {
    max-width: 400px;
    margin: var(--spacing-md) auto 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #f59e0b 0%, #10b981 100%);
    border-radius: 4px;
    transition: width 1s ease;
    position: relative;
    overflow: hidden;
}

.progress-fill::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s ease-in-out infinite;
}

.progress-text {
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.progress-text #currentRegistrations {
    color: var(--primary-color);
    font-weight: var(--font-weight-bold);
}

.vip-btn {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    position: relative;
    overflow: hidden;
    animation: pulse 2s ease-in-out infinite;
    box-shadow: 0 10px 30px rgba(245, 158, 11, 0.3);
}

.vip-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.vip-btn:hover {
    animation: none;
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(245, 158, 11, 0.5);
}

.vip-btn:hover::before {
    left: 100%;
}

.floating-icons {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-icons::before,
.floating-icons::after {
    content: '💻';
    position: absolute;
    font-size: 2rem;
    opacity: 0.1;
    animation: float 8s ease-in-out infinite;
}

.floating-icons::before {
    top: 20%;
    left: 10%;
    animation-delay: 1s;
}

.floating-icons::after {
    content: '🚀';
    top: 60%;
    right: 15%;
    animation-delay: 3s;
}

/* Academy Features */
.academy-features {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.02) 100%);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.feature-card {
    padding: var(--spacing-2xl);
    text-align: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    opacity: 0;
    transform: translateY(30px);
    animation: slideInUp 0.8s ease-out forwards;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }

.feature-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 20px 60px rgba(245, 158, 11, 0.2);
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: inherit;
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-card.premium-card {
    border: 2px solid transparent;
    background: linear-gradient(var(--background-card), var(--background-card)) padding-box,
                linear-gradient(135deg, #f59e0b 0%, #d97706 100%) border-box;
}

.feature-highlight {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    padding: 4px 8px;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: var(--font-weight-bold);
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-lg);
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto var(--spacing-md);
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotateY(360deg);
    box-shadow: 0 10px 30px rgba(245, 158, 11, 0.4);
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Curriculum Section */
.curriculum-section {
    padding: var(--spacing-2xl) 0;
}

.curriculum-container {
    max-width: 800px;
    margin: 0 auto;
}

.curriculum-timeline {
    position: relative;
    padding-left: 40px;
}

.curriculum-timeline::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--primary-gradient);
}

.timeline-item {
    position: relative;
    margin-bottom: var(--spacing-2xl);
    padding-left: var(--spacing-xl);
    opacity: 0;
    transform: translateX(-30px);
    animation: slideInUp 0.8s ease-out forwards;
}

.timeline-item:nth-child(1) { animation-delay: 0.2s; }
.timeline-item:nth-child(2) { animation-delay: 0.4s; }
.timeline-item:nth-child(3) { animation-delay: 0.6s; }
.timeline-item:nth-child(4) { animation-delay: 0.8s; }
.timeline-item:nth-child(5) { animation-delay: 1.0s; }
.timeline-item:nth-child(6) { animation-delay: 1.2s; }

.timeline-marker {
    position: absolute;
    left: -30px;
    top: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(245, 158, 11, 0.3);
}

.timeline-item:hover .timeline-marker {
    transform: scale(1.2);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.5);
    animation: pulse 1s ease-in-out;
}

.timeline-content {
    background: var(--background-card);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.timeline-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.1), transparent);
    transition: left 0.6s ease;
}

.timeline-item:hover .timeline-content {
    transform: translateX(10px);
    border-color: rgba(245, 158, 11, 0.3);
    box-shadow: 0 10px 30px rgba(245, 158, 11, 0.1);
}

.timeline-item:hover .timeline-content::before {
    left: 100%;
}

.timeline-content h4 {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.timeline-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    line-height: 1.6;
}

.timeline-duration {
    font-size: 0.9rem;
    color: var(--primary-color);
    font-weight: var(--font-weight-medium);
}

/* Instructor Section */
.instructor-section {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.02) 100%);
}

.instructor-container {
    padding: var(--spacing-2xl);
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
    position: relative;
    overflow: hidden;
}

.instructor-container::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(245, 158, 11, 0.1) 0%, transparent 70%);
    animation: float 8s ease-in-out infinite;
}

.instructor-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-md);
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border-radius: var(--radius-xl);
    font-size: 0.85rem;
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-md);
}

.instructor-container h2 {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-xs);
}

.instructor-container h3 {
    font-size: 1.25rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.instructor-credentials {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.credential {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.credential i {
    color: var(--primary-color);
}

.instructor-bio {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: var(--spacing-lg);
}

.instructor-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.instructor-visual {
    text-align: center;
}

.instructor-avatar {
    position: relative;
    width: 150px;
    height: 150px;
    margin: 0 auto var(--spacing-lg);
    animation: float 6s ease-in-out infinite;
    overflow: hidden;
    border-radius: 50%;
}

.instructor-avatar .avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(245, 158, 11, 0.3);
}

.instructor-avatar:hover .avatar-image {
    transform: scale(1.05);
    box-shadow: 0 15px 40px rgba(245, 158, 11, 0.5);
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(245, 158, 11, 0.3);
}

.instructor-avatar:hover .avatar-placeholder {
    transform: scale(1.05);
    box-shadow: 0 15px 40px rgba(245, 158, 11, 0.5);
}

.avatar-status {
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 3px solid var(--background-dark);
}

.avatar-status.online {
    background: #10b981;
}

.instructor-achievements {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.achievement {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.achievement i {
    color: var(--primary-color);
}

/* Registration Section */
.registration-section {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.registration-container {
    max-width: 700px;
    margin: 0 auto;
    padding: var(--spacing-2xl);
    position: relative;
    overflow: hidden;
}

.registration-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
    border-radius: var(--radius-xl);
    pointer-events: none;
}

.registration-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    position: relative;
    z-index: 2;
}

.premium-header {
    padding: var(--spacing-xl) 0;
}

.form-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-lg);
    box-shadow: 0 4px 20px rgba(245, 158, 11, 0.3);
    animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 4px 20px rgba(245, 158, 11, 0.3); }
    50% { box-shadow: 0 8px 30px rgba(245, 158, 11, 0.5); }
}

.registration-header h2 {
    font-size: 2.5rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.registration-header p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Premium Benefits */
.premium-benefits {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.premium-benefit {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(245, 158, 11, 0.2);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.premium-benefit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.1), transparent);
    transition: left 0.6s ease;
}

.premium-benefit:hover::before {
    left: 100%;
}

.premium-benefit:hover {
    transform: translateY(-4px);
    border-color: rgba(245, 158, 11, 0.4);
    box-shadow: 0 10px 30px rgba(245, 158, 11, 0.2);
}

.benefit-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-lg);
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.benefit-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.benefit-title {
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    font-size: 1rem;
}

.benefit-desc {
    color: var(--text-secondary);
    font-size: 0.85rem;
    line-height: 1.4;
}

/* Premium Form */
.premium-form {
    display: grid;
    gap: var(--spacing-xl);
    position: relative;
    z-index: 2;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-group label {
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    font-size: 0.95rem;
    margin-bottom: var(--spacing-xs);
}

/* Premium Input Styling */
.premium-input .input-wrapper,
.premium-select .select-wrapper,
.premium-textarea .textarea-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: var(--spacing-md);
    color: rgba(245, 158, 11, 0.7);
    z-index: 2;
    font-size: 1rem;
}

.premium-input input,
.premium-select select,
.premium-textarea textarea {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 3rem;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
}

.premium-input input:focus,
.premium-select select:focus,
.premium-textarea textarea:focus {
    outline: none;
    border-color: rgba(245, 158, 11, 0.6);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.1);
}

.input-highlight {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    width: 0;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    transition: width 0.3s ease;
}

.premium-input input:focus + .input-highlight,
.premium-textarea textarea:focus + .input-highlight {
    width: 100%;
}

/* Premium Select Styling */
.select-wrapper {
    position: relative;
}

.select-arrow {
    position: absolute;
    right: var(--spacing-md);
    color: rgba(245, 158, 11, 0.7);
    pointer-events: none;
    z-index: 2;
}

.premium-select select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
}

.premium-select select option {
    background: var(--background-primary);
    color: var(--text-primary);
    padding: var(--spacing-sm);
}

/* Premium Textarea */
.premium-textarea textarea {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
    line-height: 1.5;
}

/* Premium Checkbox */
.premium-checkbox {
    margin: var(--spacing-lg) 0;
}

.premium-checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    cursor: pointer;
    font-size: 0.95rem;
    line-height: 1.5;
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
}

.premium-checkbox-label:hover {
    background: rgba(255, 255, 255, 0.03);
}

.premium-checkbox-label input[type="checkbox"] {
    display: none;
}

.premium-checkmark {
    width: 24px;
    height: 24px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 2px;
}

.premium-checkmark i {
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
    color: white;
    font-size: 0.8rem;
}

.premium-checkbox-label input[type="checkbox"]:checked + .premium-checkmark {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border-color: #f59e0b;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.premium-checkbox-label input[type="checkbox"]:checked + .premium-checkmark i {
    opacity: 1;
    transform: scale(1);
}

.checkbox-text {
    color: var(--text-secondary);
}

.terms-link {
    color: #f59e0b;
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: color 0.3s ease;
}

.terms-link:hover {
    color: #d97706;
    text-decoration: underline;
}

/* Premium Submit Button */
.premium-submit {
    position: relative;
    padding: var(--spacing-lg) var(--spacing-2xl);
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    overflow: hidden;
    margin-top: var(--spacing-lg);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

.premium-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(245, 158, 11, 0.4);
}

.premium-submit:active {
    transform: translateY(-1px);
}

.btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    position: relative;
    z-index: 2;
}

.btn-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.premium-submit:hover .btn-icon {
    transform: translateX(5px);
}

.btn-text {
    font-weight: var(--font-weight-bold);
}

.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.premium-submit:hover .btn-shine {
    left: 100%;
}

.btn-submit {
    padding: var(--spacing-md) var(--spacing-xl);
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    font-size: 1rem;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.registration-success {
    text-align: center;
    padding: var(--spacing-2xl);
}

.success-icon {
    font-size: 4rem;
    color: #10b981;
    margin-bottom: var(--spacing-md);
}

.registration-success h3 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    color: #10b981;
}

.registration-success p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.success-actions {
    display: flex;
    justify-content: center;
}

/* FAQ Section */
.faq-section {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.02) 100%);
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: var(--background-card);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.6s ease-out forwards;
}

.faq-item:nth-child(1) { animation-delay: 0.1s; }
.faq-item:nth-child(2) { animation-delay: 0.2s; }
.faq-item:nth-child(3) { animation-delay: 0.3s; }
.faq-item:nth-child(4) { animation-delay: 0.4s; }
.faq-item:nth-child(5) { animation-delay: 0.5s; }
.faq-item:nth-child(6) { animation-delay: 0.6s; }
.faq-item:nth-child(7) { animation-delay: 0.7s; }
.faq-item:nth-child(8) { animation-delay: 0.8s; }
.faq-item:nth-child(9) { animation-delay: 0.9s; }
.faq-item:nth-child(10) { animation-delay: 1.0s; }
.faq-item:nth-child(11) { animation-delay: 1.1s; }
.faq-item:nth-child(12) { animation-delay: 1.2s; }
.faq-item:nth-child(13) { animation-delay: 1.3s; }

.faq-item:hover {
    transform: translateY(-2px);
    border-color: rgba(245, 158, 11, 0.3);
    box-shadow: 0 10px 30px rgba(245, 158, 11, 0.1);
}

.faq-question {
    padding: var(--spacing-lg);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition-fast);
}

.faq-question:hover {
    background: rgba(255, 255, 255, 0.05);
}

.faq-question h4 {
    font-size: 1.1rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
}

.faq-question i {
    color: var(--primary-color);
    transition: var(--transition-fast);
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    opacity: 0;
}

.faq-item.active .faq-answer {
    max-height: 300px;
    opacity: 1;
}

/* Fallback for FAQ visibility */
.faq-item.active .faq-answer p {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.3s ease 0.1s;
}

.faq-answer p {
    padding: 0 var(--spacing-lg) var(--spacing-lg);
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Premium Interactive Effects */
.instructor-badge {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    animation: glow 3s ease-in-out infinite;
}

.instructor-container h2 {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #f59e0b 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shimmer 4s ease-in-out infinite;
}

.instructor-stats .stat {
    transition: all 0.3s ease;
    cursor: pointer;
}

.instructor-stats .stat:hover {
    transform: scale(1.1);
    background: rgba(245, 158, 11, 0.1);
    border-radius: var(--radius-md);
}

.instructor-stats .stat-number {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.instructor-stats .stat:hover .stat-number {
    transform: scale(1.2);
    text-shadow: 0 0 20px rgba(245, 158, 11, 0.5);
}

/* Enhanced Form Styling */
.registration-form {
    position: relative;
}

.registration-form::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #f59e0b, #d97706, #f59e0b);
    background-size: 200% 200%;
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    animation: shimmer 3s ease-in-out infinite;
    transition: opacity 0.3s ease;
}

.registration-form:hover::before {
    opacity: 0.3;
}

/* Responsive Design */
@media (max-width: 768px) {
    .academy-hero .hero-title {
        font-size: 2.5rem;
    }

    .academy-hero .hero-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .instructor-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .instructor-credentials {
        grid-template-columns: 1fr;
    }

    .instructor-stats {
        grid-template-columns: 1fr;
    }

    .premium-benefits {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .registration-header h2 {
        font-size: 2rem;
    }

    .premium-submit {
        padding: var(--spacing-md) var(--spacing-xl);
        font-size: 1rem;
    }

    .timeline-item {
        padding-left: var(--spacing-md);
    }

    .timeline-marker {
        left: -20px;
        width: 30px;
        height: 30px;
        font-size: 0.9rem;
    }

    .curriculum-timeline {
        padding-left: 30px;
    }

    .curriculum-timeline::before {
        left: 15px;
    }
}

/* New exclusive elements styles for main page */
.status-dot-mini.exclusive {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
    animation: pulse-gold 2s infinite;
}

@keyframes pulse-gold {
    0%, 100% {
        box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 25px rgba(255, 215, 0, 0.8);
        transform: scale(1.1);
    }
}

.exclusive-benefits {
    display: flex;
    gap: 0.75rem;
    margin-top: 0.75rem;
    flex-wrap: wrap;
    justify-content: center;
}

.benefit-badge {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 237, 78, 0.1));
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    color: #ffd700;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.benefit-badge:hover {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 237, 78, 0.2));
    transform: translateY(-2px);
}

.benefit-badge i {
    font-size: 0.875rem;
}

.vip-benefits-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin: 1rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.benefit-item-mini {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.benefit-item-mini:hover {
    color: #00ff88;
    transform: translateX(5px);
}

.benefit-item-mini i {
    color: #00ff88;
    font-size: 1rem;
}
