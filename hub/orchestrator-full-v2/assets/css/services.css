/* Premium Services Page Styles */

/* Premium Services Hero */
.services-hero {
    padding: 120px 0 80px 0;
    position: relative;
    overflow: hidden;
    background: radial-gradient(ellipse at center, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
}

.services-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(10px, 10px); }
}

.services-hero .hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.services-hero .hero-title {
    font-size: 3rem;
    font-weight: var(--font-weight-black);
    margin-bottom: var(--spacing-md);
}

.services-hero .hero-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.7;
}

.services-hero .hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    max-width: 600px;
    margin: 0 auto;
}

/* Filter Section */
.filter-section {
    padding: var(--spacing-xl) 0;
    background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.02) 100%);
}

.filter-container {
    padding: var(--spacing-xl);
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.filter-header h3 {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
}

.filter-reset {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.filter-reset:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
    color: var(--text-primary);
    border-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.filter-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.filter-group label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

.filter-group select,
.filter-group input {
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.1);
}

/* Services Catalog */
.services-catalog {
    padding: var(--spacing-2xl) 0;
}

.catalog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

.catalog-header h2 {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
}

.view-toggle {
    display: flex;
    gap: var(--spacing-xs);
}

.view-btn {
    width: 40px;
    height: 40px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn.active,
.view-btn:hover {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.services-grid.list-view {
    grid-template-columns: 1fr;
}

.service-item {
    background: var(--background-card);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.service-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.6s ease;
}

.service-item:hover::before {
    left: 100%;
}

.service-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.3);
    background: rgba(255, 255, 255, 0.08);
}

.service-item.list-view {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: var(--spacing-lg);
    align-items: center;
    padding: var(--spacing-lg);
}

.service-category {
    display: inline-block;
    padding: 4px 8px;
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-sm);
}

.service-title {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.service-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.service-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.service-price {
    font-size: 1.1rem;
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
}

.service-time {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.service-tags {
    display: flex;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.service-tag {
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.service-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.btn-service {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    text-decoration: none;
    font-size: 0.9rem;
}

.btn-service.primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-service.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-service:hover {
    transform: translateY(-1px);
}

.load-more-container {
    text-align: center;
    margin-top: var(--spacing-xl);
}

/* Featured Services */
.featured-services {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.02) 100%);
}

.featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.featured-card {
    padding: var(--spacing-xl);
    position: relative;
    text-align: center;
    transition: var(--transition-normal);
}

.featured-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.featured-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--secondary-gradient);
    color: white;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: var(--font-weight-bold);
}

.featured-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-lg);
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto var(--spacing-md);
}

.featured-card h3 {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
}

.featured-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.featured-price {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.featured-features {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.featured-features .feature {
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.btn-featured {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

.btn-featured:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Quick Order */
.quick-order {
    padding: var(--spacing-2xl) 0;
}

.order-container {
    padding: var(--spacing-2xl);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xl);
}

.order-content h2 {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
}

.order-content p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.order-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
    justify-content: center;
}

.btn-order {
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    font-size: 1rem;
    min-width: 180px;
    justify-content: center;
}

.btn-order.telegram {
    background: linear-gradient(135deg, #0088cc 0%, #229ed9 100%);
    color: white;
}

.btn-order.whatsapp {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
}

.btn-order.custom {
    background: var(--primary-gradient);
    color: white;
}

.btn-order:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Quick Solutions Section */
.quick-solutions-section {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.02) 0%, transparent 100%);
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    font-size: 0.85rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.section-badge.premium {
    background: var(--secondary-gradient);
    color: white;
    border-color: transparent;
}

.quick-solutions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.quick-solution-card {
    background: var(--background-card);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.quick-solution-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--accent-gradient);
}

.quick-solution-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: rgba(255, 255, 255, 0.2);
}

.solution-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.solution-category {
    padding: 4px 8px;
    background: var(--accent-gradient);
    color: white;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
}

.solution-time {
    font-size: 0.8rem;
    color: var(--text-muted);
    background: rgba(255, 255, 255, 0.05);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
}

.solution-title {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.solution-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
    font-size: 0.95rem;
}

.solution-tech {
    display: flex;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.tech-tag {
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.solution-pricing {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
}

.price-range {
    font-size: 1.1rem;
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
}

.solution-includes {
    font-size: 0.85rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-md);
}

.quick-solutions-cta {
    text-align: center;
    margin-top: var(--spacing-xl);
}

/* Services Categories */
.services-categories {
    padding: var(--spacing-2xl) 0;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.category-card {
    padding: var(--spacing-xl);
    text-align: center;
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
}

.category-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-lg);
}

.category-card.active {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.category-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-lg);
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto var(--spacing-md);
    transition: var(--transition-normal);
}

.category-card:hover .category-icon {
    transform: scale(1.1);
}

.category-card h3 {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
}

.category-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.category-count {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

/* Enhanced Catalog Header */
.catalog-controls {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.sort-controls select {
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.sort-controls select:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.1);
}

/* Wishlist Section */
.wishlist-section {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.02) 100%);
}

.wishlist-categories {
    margin-top: var(--spacing-xl);
}

.wishlist-nav {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
    overflow-x: auto;
    padding-bottom: var(--spacing-sm);
}

.wishlist-tab {
    padding: var(--spacing-md) var(--spacing-lg);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    white-space: nowrap;
    font-weight: var(--font-weight-medium);
}

.wishlist-tab:hover,
.wishlist-tab.active {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
}

.wishlist-content {
    min-height: 400px;
}

.wishlist-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-lg);
}

.wishlist-item {
    background: var(--background-card);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.wishlist-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--secondary-gradient);
}

.wishlist-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(255, 255, 255, 0.2);
}

.wishlist-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.wishlist-title {
    font-size: 1.4rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.wishlist-price {
    font-size: 1.2rem;
    font-weight: var(--font-weight-bold);
    color: var(--secondary-color);
    background: rgba(118, 75, 162, 0.1);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
}

.wishlist-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.wishlist-tech {
    margin-bottom: var(--spacing-md);
}

.wishlist-tech-label {
    font-size: 0.85rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-xs);
    font-weight: var(--font-weight-medium);
}

.wishlist-tech-tags {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.wishlist-tech-tag {
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.wishlist-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.wishlist-time {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.btn-wishlist {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--secondary-gradient);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    text-decoration: none;
    font-size: 0.9rem;
}

.btn-wishlist:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Dry List Section */
.dry-list-section {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.02) 0%, transparent 100%);
}

.dry-list-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-md);
}

.dry-list-category {
    background: var(--background-card);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: var(--transition-normal);
}

.dry-list-category:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: rgba(255, 255, 255, 0.2);
}

.dry-list-category h3 {
    font-size: 1.1rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.dry-list-category h3 i {
    color: var(--primary-color);
}

.dry-list-items {
    list-style: none;
}

.dry-list-items li {
    padding: var(--spacing-xs) 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    transition: var(--transition-fast);
    cursor: pointer;
}

.dry-list-items li:hover {
    color: var(--text-primary);
    padding-left: var(--spacing-sm);
}

.dry-list-items li:last-child {
    border-bottom: none;
}

/* Service Modal */
.service-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.modal-content {
    position: relative;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    padding: var(--spacing-2xl);
    margin: var(--spacing-md);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.modal-body .service-category {
    display: inline-block;
    padding: 4px 8px;
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-md);
}

.modal-body .service-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
    font-size: 1.1rem;
}

.service-details {
    margin-bottom: var(--spacing-lg);
}

.detail-item {
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
}

.detail-item strong {
    color: var(--text-primary);
    margin-right: var(--spacing-sm);
}

.modal-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.modal-actions .btn-primary,
.modal-actions .btn-secondary {
    flex: 1;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    text-decoration: none;
    border: none;
}

.modal-actions .btn-primary {
    background: var(--primary-gradient);
    color: white;
}

.modal-actions .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-actions .btn-primary:hover,
.modal-actions .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Contact Options Modal */
.contact-item-info {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
}

.contact-item-info h3 {
    font-size: 1.2rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.contact-price {
    font-size: 1.1rem;
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
}

.contact-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.contact-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-fast);
    text-align: left;
    width: 100%;
}

.contact-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.contact-btn i {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.contact-btn.telegram i {
    color: #0088cc;
}

.contact-btn.whatsapp i {
    color: #25d366;
}

.contact-btn.email i {
    color: #ea4335;
}

.contact-btn div {
    flex: 1;
}

.contact-btn strong {
    display: block;
    font-size: 1rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 2px;
}

.contact-btn span {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.contact-note {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.contact-note i {
    color: var(--primary-color);
}

/* Enhanced Floating Search */
.floating-search-section {
    padding: var(--spacing-xl) 0;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.02) 0%, transparent 100%);
}

.floating-search-container {
    padding: var(--spacing-2xl);
    border-radius: var(--radius-2xl);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 8px 32px rgba(102, 126, 234, 0.2);
    backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    max-width: 1000px;
    margin: 0 auto;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    position: relative;
    overflow: hidden;
}

.floating-search-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.floating-search-container:hover::before {
    opacity: 1;
}

.search-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    position: relative;
}

.search-header h3 {
    font-size: 2rem;
    font-weight: var(--font-weight-black);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #4facfe 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    margin-bottom: var(--spacing-md);
    line-height: 1.2;
}

.search-header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.search-form {
    max-width: 100%;
}

.search-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.search-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.search-group label {
    font-size: 0.9rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
}

.search-group select {
    padding: var(--spacing-md) var(--spacing-lg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: var(--font-weight-medium);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    backdrop-filter: blur(10px);
    cursor: pointer;
    position: relative;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.search-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(255, 255, 255, 0.08) 100%);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.3);
    transform: translateY(-2px);
}

.search-group select:hover {
    border-color: rgba(255, 255, 255, 0.25);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
    transform: translateY(-1px);
}

.search-input-row {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    align-items: stretch;
}

.search-input-row input {
    flex: 1;
    padding: var(--spacing-md) var(--spacing-lg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: var(--font-weight-medium);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.search-input-row input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(255, 255, 255, 0.08) 100%);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.3);
    transform: translateY(-2px);
}

.search-input-row input:hover {
    border-color: rgba(255, 255, 255, 0.25);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
    transform: translateY(-1px);
}

.search-input-row input::placeholder {
    color: var(--text-muted);
    font-weight: var(--font-weight-normal);
}

.search-btn {
    padding: var(--spacing-md) var(--spacing-xl);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: 200% 200%;
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-bold);
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    white-space: nowrap;
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
    position: relative;
    overflow: hidden;
    animation: gradientShift 3s ease-in-out infinite;
}

.search-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.search-btn:hover::before {
    left: 100%;
}

.search-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.6);
    filter: brightness(1.1);
}

.search-btn:active {
    transform: translateY(-1px) scale(0.98);
}

.search-actions {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-md);
}

/* Responsive Design */
@media (max-width: 768px) {
    .services-hero .hero-title {
        font-size: 2.5rem;
    }

    .services-hero .hero-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .filter-options {
        grid-template-columns: 1fr;
    }

    .catalog-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }

    .catalog-controls {
        width: 100%;
        justify-content: space-between;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .quick-solutions-grid {
        grid-template-columns: 1fr;
    }

    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
}

/* Additional responsive breakpoints for categories */
@media (max-width: 1024px) {
    .categories-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-md);
    }
}

@media (max-width: 640px) {
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }

    .category-card {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .categories-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .wishlist-nav {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .wishlist-tab {
        justify-content: center;
    }

    .wishlist-grid {
        grid-template-columns: 1fr;
    }

    .dry-list-grid {
        grid-template-columns: 1fr;
    }

    .featured-grid {
        grid-template-columns: 1fr;
    }

    .order-actions {
        flex-direction: column;
        width: 100%;
    }

    .btn-order {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .quick-solution-card,
    .wishlist-item {
        padding: var(--spacing-md);
    }

    .solution-header,
    .wishlist-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .solution-pricing {
        flex-direction: column;
        gap: var(--spacing-xs);
        align-items: flex-start;
    }

    .wishlist-meta {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .modal-content {
        margin: var(--spacing-sm);
        padding: var(--spacing-lg);
        max-height: 95vh;
    }

    .modal-actions {
        flex-direction: column;
    }

    .modal-actions .btn-primary,
    .modal-actions .btn-secondary {
        width: 100%;
    }

    .contact-options {
        gap: var(--spacing-sm);
    }

    .contact-btn {
        padding: var(--spacing-sm);
    }

    .contact-btn i {
        font-size: 1.2rem;
        width: 30px;
    }

    .search-row {
        grid-template-columns: 1fr;
    }

    .search-input-row {
        flex-direction: column;
    }
}

/* Service Badges */
.service-badge {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
    color: white;
    z-index: 2;
}

.service-badge.express {
    background: var(--accent-gradient);
}

.service-badge.premium {
    background: var(--secondary-gradient);
}

.service-badge.standard {
    background: rgba(255, 255, 255, 0.2);
}

/* Enhanced Service Items */
.service-item {
    position: relative;
    overflow: hidden;
}

.search-result {
    border-left: 3px solid transparent;
    transition: var(--transition-normal);
}

.search-result.express {
    border-left-color: var(--accent-color);
}

.search-result.premium {
    border-left-color: var(--secondary-color);
}

.search-result:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* Enhanced Featured Services */
.featured-badge.bestseller {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.featured-badge.express {
    background: var(--accent-gradient);
}

.featured-badge.premium {
    background: var(--secondary-gradient);
}

.featured-time {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    text-align: center;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
}

.featured-wow {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin: var(--spacing-md) 0;
    padding: var(--spacing-sm);
    background: rgba(255, 215, 0, 0.1);
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: var(--radius-md);
    font-size: 0.85rem;
    color: #ffd700;
}

.featured-wow i {
    color: #ffd700;
    font-size: 0.8rem;
}

.featured-card {
    position: relative;
    overflow: hidden;
}

.featured-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.featured-card.premium::before {
    background: var(--secondary-gradient);
}

.featured-card.express::before {
    background: var(--accent-gradient);
}
