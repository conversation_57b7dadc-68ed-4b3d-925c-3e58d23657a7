/* Premium Communication Page Styles */

/* Premium Communication Hero */
.communication-hero {
    padding: 120px var(--spacing-md) 80px;
    position: relative;
    overflow-x: hidden;
    text-align: center;
    display: flex;
    align-items: center;
    min-height: 100vh;
    width: 100%;
    box-sizing: border-box;
    background: radial-gradient(ellipse at center, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
}

.communication-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(10px, 10px); }
}

.communication-hero .hero-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    width: 100%;
    padding: 0 var(--spacing-md);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.communication-hero .hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    font-size: 0.85rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    margin: 0 auto var(--spacing-md);
}

.communication-hero .hero-title {
    font-size: 3rem;
    font-weight: var(--font-weight-black);
    margin-bottom: var(--spacing-md);
}

.communication-hero .hero-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.7;
}

.communication-hero .hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    max-width: 500px;
    margin: 0 auto var(--spacing-xl);
}

.communication-hero .hero-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-lg);
    margin: 0 auto var(--spacing-xl);
    max-width: 600px;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    margin: var(--spacing-lg) auto 0;
    padding: var(--spacing-md) var(--spacing-lg);
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(34, 197, 94, 0.1) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(16, 185, 129, 0.4);
    border-radius: var(--radius-xl);
    color: #10b981;
    font-weight: var(--font-weight-semibold);
    font-size: 0.95rem;
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.2);
    position: relative;
    overflow: hidden;
    max-width: calc(100vw - 2rem);
    width: fit-content;
    text-align: center;
    animation: statusGlow 3s ease-in-out infinite;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    position: relative;
    background: linear-gradient(135deg, #10b981 0%, #22c55e 100%);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.6);
    animation: statusPulse 2s infinite;
}

.status-dot.online::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: linear-gradient(135deg, #10b981 0%, #22c55e 100%);
    opacity: 0.3;
    animation: statusRipple 2s infinite;
}

.status-dot.online::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 4px;
    height: 4px;
    background: white;
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s;
}

.status-indicator:hover::before {
    left: 100%;
}

@keyframes statusPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.6);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(16, 185, 129, 0.8);
    }
}

@keyframes statusRipple {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

@keyframes statusGlow {
    0%, 100% {
        box-shadow: 0 8px 32px rgba(16, 185, 129, 0.2);
    }
    50% {
        box-shadow: 0 8px 32px rgba(16, 185, 129, 0.4);
    }
}

.chat-bubbles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

/* Communication Options */
.communication-options {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.02) 100%);
}

.options-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xl);
    max-width: 800px;
    margin: 0 auto;
}

.option-card {
    padding: var(--spacing-2xl);
    cursor: pointer;
    transition: var(--transition-normal);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.option-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.option-card.telegram-card:hover {
    border-color: #0088cc;
    box-shadow: 0 16px 64px rgba(0, 136, 204, 0.3);
}

.option-card.whatsapp-card:hover {
    border-color: #25d366;
    box-shadow: 0 16px 64px rgba(37, 211, 102, 0.3);
}



.option-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    margin: 0 auto var(--spacing-md);
    transition: var(--transition-normal);
}

.telegram-card .option-icon {
    background: linear-gradient(135deg, #0088cc 0%, #229ed9 100%);
    color: white;
}

.whatsapp-card .option-icon {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
}



.option-card h3 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
}

.option-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.option-features {
    text-align: left;
    margin-bottom: var(--spacing-lg);
}

.feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.feature i {
    color: #10b981;
    font-size: 0.9rem;
}

.option-cta {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    color: var(--primary-color);
    font-weight: var(--font-weight-medium);
    transition: var(--transition-fast);
}

.option-card:hover .option-cta i {
    transform: translateX(4px);
}

/* Quick Actions */
.quick-actions {
    padding: var(--spacing-2xl) 0;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.action-card {
    padding: var(--spacing-xl);
    cursor: pointer;
    transition: var(--transition-normal);
    text-align: center;
}

.action-card:hover {
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.1);
}

.action-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin: 0 auto var(--spacing-md);
}

.action-card h4 {
    font-size: 1.1rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
}

.action-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}



/* Contact Info */
.contact-info {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.02) 100%);
}

.info-container {
    padding: var(--spacing-2xl);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.info-content h2 {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-lg);
}

.contact-details {
    display: grid;
    gap: var(--spacing-lg);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.contact-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-md);
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
}

.contact-text h4 {
    font-size: 1rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 4px;
}

.contact-text p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Communication Preview */
.communication-preview {
    display: flex;
    justify-content: center;
}

.phone-mockup {
    width: 250px;
    height: 400px;
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border-radius: 25px;
    padding: 20px;
    position: relative;
}

.phone-screen {
    width: 100%;
    height: 100%;
    background: var(--background-dark);
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

.chat-preview {
    padding: var(--spacing-md);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    gap: var(--spacing-sm);
}

.chat-message {
    display: flex;
    flex-direction: column;
}

.chat-message.received {
    align-items: flex-start;
}

.chat-message.sent {
    align-items: flex-end;
}

.message-bubble {
    max-width: 80%;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.8rem;
    line-height: 1.4;
}

.chat-message.received .message-bubble {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.chat-message.sent .message-bubble {
    background: var(--primary-gradient);
    color: white;
}

.chat-message .message-time {
    font-size: 0.7rem;
    color: var(--text-muted);
    margin-top: 4px;
}

.typing-indicator {
    align-self: flex-start;
    padding: var(--spacing-sm);
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: var(--text-muted);
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .communication-hero .hero-title {
        font-size: 2.5rem;
    }

    .communication-hero .hero-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .options-grid {
        grid-template-columns: 1fr;
        max-width: none;
    }

    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .info-container {
        grid-template-columns: 1fr;
        text-align: center;
    }



    .phone-mockup {
        width: 200px;
        height: 320px;
    }

    /* Mobile Navigation */
    .mobile-menu-toggle {
        display: flex;
    }

    .nav-content {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(10, 10, 10, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-lg);
        margin: var(--spacing-sm);
        padding: var(--spacing-lg);
        flex-direction: column;
        gap: var(--spacing-lg);
        opacity: 0;
        visibility: hidden;
        transform: translateY(-20px);
        transition: var(--transition-normal);
        z-index: 1000;
    }

    .nav-content.active {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .nav-menu {
        flex-direction: column;
        gap: var(--spacing-md);
        width: 100%;
        text-align: center;
    }

    .hamburger {
        display: flex;
    }

    .nav-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
        width: 100%;
        padding: var(--spacing-xs);
    }

    .contact-buttons {
        width: 100%;
        justify-content: center;
        gap: var(--spacing-xs);
        border-right: none;
        padding-right: 0;
        margin-right: 0;
    }

    .contact-buttons::after {
        display: none;
    }

    .btn-contact {
        flex: 1;
        justify-content: center;
        padding: var(--spacing-xs) var(--spacing-sm);
        min-width: auto;
        font-size: 0.8rem;
        max-width: none;
    }

    .services-btn {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
        max-width: none;
        flex: none;
    }

    .communication-hero .hero-actions {
        flex-direction: column;
        gap: var(--spacing-md);
        width: 100%;
        max-width: 320px;
        margin: 0 auto var(--spacing-xl);
    }

    .hero-actions .btn-primary.large,
    .hero-actions .btn-secondary.large {
        width: 100%;
        min-height: 70px;
        min-width: auto;
        padding: var(--spacing-md);
        justify-content: center;
    }

    .status-indicator {
        margin: var(--spacing-lg) auto 0;
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.85rem;
        max-width: calc(100vw - 3rem);
        word-wrap: break-word;
        text-align: center;
    }

    .status-indicator span {
        display: block;
        line-height: 1.4;
    }
}

/* Floating Contact Widget */
.floating-contact-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    transition: var(--transition-normal);
}

.contact-toggle {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: var(--shadow-xl);
    transition: var(--transition-fast);
    position: relative;
    animation: pulse-glow 3s infinite;
}

.contact-toggle:hover {
    transform: scale(1.1);
}

.contact-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
    animation: bounce 2s infinite;
}

.contact-options {
    position: absolute;
    bottom: 70px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: var(--transition-normal);
}

.floating-contact-widget.expanded .contact-options {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.contact-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-lg);
    color: white;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: var(--transition-fast);
    backdrop-filter: blur(20px);
    min-width: 120px;
    justify-content: flex-start;
}

.contact-option.telegram {
    background: linear-gradient(135deg, #0088cc 0%, #229ed9 100%);
}

.contact-option.whatsapp {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
}

.contact-option:hover {
    transform: translateX(-5px) scale(1.05);
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}

/* ===== PREMIUM COMMUNICATION NAVBAR - ULTRA PREMIUM REDESIGN ===== */
.premium-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(135deg,
        rgba(5, 5, 10, 0.98) 0%,
        rgba(10, 10, 20, 0.99) 30%,
        rgba(15, 15, 25, 0.98) 70%,
        rgba(8, 8, 15, 0.98) 100%);
    backdrop-filter: blur(40px) saturate(180%);
    border: none;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 8px 32px rgba(102, 126, 234, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.08),
        inset 0 -1px 0 rgba(102, 126, 234, 0.1);
    animation: navbarSlideDown 1.2s cubic-bezier(0.23, 1, 0.32, 1);
    overflow: hidden;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
}

.premium-navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(102, 126, 234, 0.15),
        rgba(139, 92, 246, 0.1),
        rgba(59, 130, 246, 0.15),
        transparent);
    animation: navbarShimmer 4s infinite ease-in-out;
}

.premium-navbar::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        rgba(102, 126, 234, 0.6),
        rgba(139, 92, 246, 0.4),
        rgba(59, 130, 246, 0.6),
        transparent);
    animation: borderGlow 3s ease-in-out infinite;
}

/* Use standard nav-container with premium styling */
.premium-navbar .nav-container {
    display: grid;
    grid-template-columns: 1fr 3fr 2fr;
    align-items: center;
    height: 80px;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    gap: var(--spacing-sm);
    position: relative;
    width: 100%;
}

/* ===== PREMIUM LOGO SECTION (1/6) - ULTRA PREMIUM ===== */
.premium-brand {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    width: 100%;
    max-width: 100%;
    padding: var(--spacing-sm);
}

.premium-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    position: relative;
    padding: var(--spacing-sm);
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    overflow: hidden;
    width: fit-content;
    max-width: 100%;
    background: transparent;
    border-radius: 0;
    border: none;
    backdrop-filter: none;
    cursor: pointer;
    box-shadow: none;
}

.logo-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
    pointer-events: none;
}

.premium-icon {
    font-size: 1.6rem;
    filter: drop-shadow(0 0 15px rgba(102, 126, 234, 0.6))
            drop-shadow(0 0 30px rgba(139, 92, 246, 0.4));
    animation: iconFloat 4s ease-in-out infinite;
    flex-shrink: 0;
    background: transparent !important;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 2;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
}

.premium-text {
    font-size: 1.2rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    text-shadow:
        0 0 20px rgba(255, 255, 255, 0.4),
        0 0 40px rgba(102, 126, 234, 0.2);
    white-space: nowrap;
    letter-spacing: 0.5px;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
}

.premium-accent {
    background: linear-gradient(135deg,
        #667eea 0%,
        #764ba2 25%,
        #f093fb 50%,
        #f5576c 75%,
        #4facfe 100%);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    animation: gradientShift 6s ease-in-out infinite;
}

.logo-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.premium-logo:hover {
    transform: translateY(-2px) scale(1.05);
    background: transparent;
    box-shadow: none;
}

.premium-logo:hover .premium-icon {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 0 25px rgba(102, 126, 234, 0.8))
            drop-shadow(0 0 40px rgba(139, 92, 246, 0.6));
}

.premium-logo:hover .premium-text {
    text-shadow:
        0 0 30px rgba(255, 255, 255, 0.6),
        0 0 60px rgba(102, 126, 234, 0.4);
}

.logo-particles::before,
.logo-particles::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    opacity: 0;
    animation: particleFloat 4s infinite;
}

.logo-particles::before {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.logo-particles::after {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.premium-logo:hover .premium-icon {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 0 15px rgba(102, 126, 234, 0.8));
}

.premium-logo:hover .premium-text {
    text-shadow: 0 0 25px rgba(255, 255, 255, 0.5);
}

.premium-logo:hover .premium-accent {
    filter: brightness(1.2);
}

/* ===== PREMIUM CTA SECTION (3/6) ===== */
.premium-cta {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(15px);
    overflow: hidden;
    width: 100%;
    max-width: 100%;
    height: 100%;
    min-height: 60px;
}

/* ===== PREMIUM NAVIGATION MENU (3/6) - ULTRA PREMIUM ===== */
.premium-nav-menu {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    list-style: none;
    margin: 0;
    padding: var(--spacing-sm);
    background: transparent;
    border-radius: 0;
    border: none;
    backdrop-filter: none;
    position: relative;
    overflow: visible;
    width: 100%;
    max-width: 100%;
    box-shadow: none;
}

.premium-nav-item {
    margin: 0;
    padding: 0;
}

.premium-nav-link {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 12px 18px;
    border: none;
    border-radius: var(--radius-xl);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    font-size: 0.85rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    justify-content: center;
    min-width: 100px;
    max-width: 140px;
    backdrop-filter: blur(15px) saturate(150%);
    min-height: 44px;
    flex-shrink: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.04) 100%);
    color: var(--text-primary);
    letter-spacing: 0.3px;
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.premium-nav-link:hover {
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.25) 0%,
        rgba(139, 92, 246, 0.2) 50%,
        rgba(59, 130, 246, 0.25) 100%);
    transform: translateY(-4px) scale(1.05);
    box-shadow:
        0 12px 40px rgba(102, 126, 234, 0.4),
        0 8px 25px rgba(139, 92, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    color: #ffffff;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

.premium-nav-link:hover .nav-btn-glow {
    opacity: 1;
}

.nav-btn-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
    pointer-events: none;
}

.nav-btn-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
    pointer-events: none;
}

.premium-nav-link:active .nav-btn-ripple {
    width: 200px;
    height: 200px;
}

.cta-background-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
        rgba(102, 126, 234, 0.05) 0%,
        rgba(139, 92, 246, 0.05) 50%,
        rgba(59, 130, 246, 0.05) 100%);
    animation: ctaBackgroundShift 6s ease-in-out infinite;
    pointer-events: none;
}

.premium-content {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 100%;
    height: 100%;
    display: grid;
    grid-template-areas:
        "badge center buttons"
        ". center buttons";
    grid-template-columns: auto 1fr auto;
    grid-template-rows: auto 1fr;
    gap: 0 var(--spacing-sm);
    align-items: start;
    padding: var(--spacing-xs);
}

/* ===== LEWY GÓRNY RÓG: BADGE (PODNIESIONY) ===== */
.premium-badge {
    grid-area: badge;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.2) 0%,
        rgba(139, 92, 246, 0.2) 100%);
    padding: 3px 6px;
    border-radius: var(--radius-md);
    border: 1px solid rgba(102, 126, 234, 0.3);
    font-size: 0.6rem;
    color: var(--text-primary);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    animation: badgeGlow 3s ease-in-out infinite;
    white-space: nowrap;
    align-self: start;
    justify-self: start;
    margin-top: -8px;
    margin-left: 0;
}

.badge-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
    animation: badgeGlowPulse 2s ease-in-out infinite;
    pointer-events: none;
}

.badge-sparkle {
    position: absolute;
    top: 50%;
    right: 10px;
    width: 6px;
    height: 6px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: sparkle 2s infinite;
}

/* ===== ŚRODEK CENTRALNEJ CZĘŚCI: GŁÓWNY TEKST CTA (OBNIŻONY O 70PX) ===== */
.cta-main-text {
    grid-area: center;
    align-self: center;
    justify-self: center;
    text-align: center;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    margin-top: 70px;
}

.premium-title {
    font-size: 1.2rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
    line-height: 1.1;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 2px;
    align-items: center;
}

.premium-gradient {
    background: linear-gradient(135deg,
        #667eea 0%,
        #764ba2 25%,
        #f093fb 50%,
        #f5576c 75%,
        #4facfe 100%);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
    font-size: 1.4rem;
    font-weight: var(--font-weight-black);
    white-space: nowrap;
    display: block;
}

.title-subtitle {
    display: block;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    white-space: nowrap;
}

/* ===== SKRAJNIE NA PRAWO: BUTTONY (PODCIĄGNIĘTE O 15PX) ===== */
.cta-buttons-container {
    grid-area: buttons;
    align-self: center;
    justify-self: end;
    display: flex;
    align-items: center;
    height: 100%;
    margin-top: 55px;
}

.premium-buttons {
    display: flex;
    flex-direction: row;
    gap: var(--spacing-xs);
    align-items: center;
    justify-content: flex-end;
}

/* ===== PREMIUM CTA BUTTONS ===== */
.premium-btn {
    display: flex;
    align-items: center;
    gap: 3px;
    padding: 6px 8px;
    border: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    font-size: 0.65rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    justify-content: center;
    min-width: 70px;
    max-width: 85px;
    backdrop-filter: blur(10px);
    min-height: 30px;
    flex-shrink: 0;
}

.btn-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
    pointer-events: none;
}

.btn-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
    pointer-events: none;
}

.services-btn {
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.9) 0%,
        rgba(139, 92, 246, 0.9) 100%);
    color: white;
    border: 1px solid rgba(102, 126, 234, 0.5);
    box-shadow:
        0 4px 20px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.portfolio-btn {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.12) 100%);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.premium-btn:hover {
    transform: translateY(-3px) scale(1.05);
    filter: brightness(1.2);
}

.premium-btn:hover .btn-glow {
    opacity: 1;
}

.premium-btn:active .btn-ripple {
    width: 300px;
    height: 300px;
}

.services-btn:hover {
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 1) 0%,
        rgba(139, 92, 246, 1) 100%);
    box-shadow:
        0 8px 40px rgba(102, 126, 234, 0.5),
        0 0 0 1px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.portfolio-btn:hover {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(255, 255, 255, 0.2) 100%);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow:
        0 8px 40px rgba(255, 255, 255, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* ===== PREMIUM CONTACT ACTIONS (2/6) - UNIFIED WITH NAVBAR ===== */
.premium-navbar .nav-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: 0;
    background: transparent;
    border: none;
    box-shadow: none;
    backdrop-filter: none;
}



.premium-contact {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 18px;
    border: none;
    border-radius: var(--radius-xl);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    font-size: 0.85rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    justify-content: center;
    min-width: 110px;
    max-width: 130px;
    backdrop-filter: blur(20px) saturate(150%);
    min-height: 44px;
    letter-spacing: 0.3px;
    box-shadow: none;
}

.contact-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    opacity: 0;
    transition: opacity 0.5s ease;
    pointer-events: none;
}

.telegram-glow {
    background: radial-gradient(circle, rgba(0, 136, 204, 0.4) 0%, transparent 70%);
}

.whatsapp-glow {
    background: radial-gradient(circle, rgba(37, 211, 102, 0.4) 0%, transparent 70%);
}

.contact-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.premium-contact.telegram {
    background: linear-gradient(135deg,
        rgba(0, 136, 204, 0.9) 0%,
        rgba(34, 158, 217, 0.9) 50%,
        rgba(0, 136, 204, 0.9) 100%);
    color: white;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 20px rgba(0, 136, 204, 0.3);
}

.premium-contact.whatsapp {
    background: linear-gradient(135deg,
        rgba(37, 211, 102, 0.9) 0%,
        rgba(18, 140, 126, 0.9) 50%,
        rgba(37, 211, 102, 0.9) 100%);
    color: white;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
}

.premium-contact:hover {
    transform: translateY(-6px) scale(1.08);
    filter: brightness(1.15) saturate(120%);
}

.premium-contact:hover .contact-glow {
    opacity: 1;
}

.premium-contact.telegram:hover {
    background: linear-gradient(135deg,
        rgba(0, 136, 204, 1) 0%,
        rgba(34, 158, 217, 1) 50%,
        rgba(0, 136, 204, 1) 100%);
    box-shadow: 0 8px 40px rgba(0, 136, 204, 0.5);
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.6);
}

.premium-contact.whatsapp:hover {
    background: linear-gradient(135deg,
        rgba(37, 211, 102, 1) 0%,
        rgba(18, 140, 126, 1) 50%,
        rgba(37, 211, 102, 1) 100%);
    box-shadow: 0 8px 40px rgba(37, 211, 102, 0.5);
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.6);
}

/* ===== PREMIUM ANIMATIONS & EFFECTS ===== */
@keyframes navbarSlideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes navbarShimmer {
    0% {
        left: -100%;
        opacity: 0;
    }
    25% {
        opacity: 0.3;
    }
    50% {
        left: 50%;
        opacity: 0.8;
    }
    75% {
        opacity: 0.3;
    }
    100% {
        left: 100%;
        opacity: 0;
    }
}

@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0) rotate(0deg) scale(1);
        filter: drop-shadow(0 0 15px rgba(102, 126, 234, 0.6));
    }
    25% {
        transform: translateY(-2px) rotate(1deg) scale(1.05);
        filter: drop-shadow(0 0 20px rgba(139, 92, 246, 0.7));
    }
    50% {
        transform: translateY(-4px) rotate(2deg) scale(1.1);
        filter: drop-shadow(0 0 25px rgba(59, 130, 246, 0.8));
    }
    75% {
        transform: translateY(-2px) rotate(1deg) scale(1.05);
        filter: drop-shadow(0 0 20px rgba(139, 92, 246, 0.7));
    }
}

@keyframes borderGlow {
    0%, 100% {
        opacity: 0.3;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.6), transparent);
    }
    50% {
        opacity: 0.8;
        background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.8), rgba(59, 130, 246, 0.8), transparent);
    }
}

@keyframes particleFloat {
    0% {
        opacity: 0;
        transform: translateY(0) scale(0);
    }
    50% {
        opacity: 1;
        transform: translateY(-10px) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-20px) scale(0);
    }
}

@keyframes ctaBackgroundShift {
    0%, 100% {
        background-position: 0% 50%;
        opacity: 0.5;
    }
    50% {
        background-position: 100% 50%;
        opacity: 0.8;
    }
}

@keyframes badgeGlow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
    }
}

@keyframes badgeGlowPulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.7; }
}

@keyframes sparkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0) rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: scale(1) rotate(180deg);
    }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes contactsShimmer {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

/* ===== PREMIUM BACKGROUND EFFECTS ===== */
.navbar-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-particles::before,
.floating-particles::after {
    content: '';
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: floatParticles 8s infinite linear;
}

.floating-particles::before {
    left: 20%;
    animation-delay: 0s;
}

.floating-particles::after {
    left: 80%;
    animation-delay: 4s;
}

.gradient-waves {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--primary-color) 50%,
        transparent 100%);
    animation: waveMove 3s ease-in-out infinite;
}

.light-rays {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg,
        transparent 0deg,
        rgba(102, 126, 234, 0.1) 45deg,
        transparent 90deg,
        rgba(139, 92, 246, 0.1) 135deg,
        transparent 180deg);
    animation: rayRotate 20s linear infinite;
    opacity: 0.3;
}

@keyframes floatParticles {
    0% {
        transform: translateY(100px) translateX(0);
        opacity: 0;
    }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% {
        transform: translateY(-100px) translateX(50px);
        opacity: 0;
    }
}

@keyframes waveMove {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

@keyframes rayRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ===== PREMIUM MOBILE DESIGN ===== */
.premium-toggle {
    display: none;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    cursor: pointer;
    padding: var(--spacing-xs);
    gap: 3px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.hamburger-line {
    width: 18px;
    height: 2px;
    background: var(--text-primary);
    border-radius: 1px;
    transition: all 0.3s ease;
}

.premium-hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 6px;
    border-radius: var(--radius-sm);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    width: 32px;
    height: 32px;
    justify-content: center;
    align-items: center;
    gap: 3px;
}

.premium-hamburger span {
    width: 16px;
    height: 2px;
    background: var(--text-primary);
    border-radius: 1px;
    transition: all 0.3s ease;
    display: block;
}

.premium-hamburger:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(102, 126, 234, 0.3);
    transform: scale(1.05);
}

.premium-hamburger:hover span {
    background: rgba(102, 126, 234, 0.8);
}

.premium-hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(3px, 3px);
    background: var(--primary-color);
}

.premium-hamburger.active span:nth-child(2) {
    opacity: 0;
}

.premium-hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(3px, -3px);
    background: var(--primary-color);
}

/* ===== PREMIUM RESPONSIVE DESIGN ===== */
@media (min-width: 1200px) {
    .premium-navbar .nav-container {
        padding: 0 var(--spacing-xl);
        gap: var(--spacing-md);
        max-width: 1600px;
    }

    .premium-text {
        font-size: 1.2rem;
    }

    .premium-icon {
        font-size: 1.5rem;
    }

    .premium-title {
        font-size: 0.9rem;
    }

    .premium-gradient {
        font-size: 0.95rem;
    }

    .title-subtitle {
        font-size: 0.7rem;
    }

    .premium-btn {
        padding: 7px 12px;
        font-size: 0.7rem;
        min-width: 75px;
        max-width: 95px;
    }

    .premium-contact {
        padding: 10px 14px;
        font-size: 0.8rem;
        min-width: 90px;
    }
}

@media (max-width: 1024px) {
    .premium-navbar .nav-container {
        gap: var(--spacing-sm);
        padding: 0 var(--spacing-md);
    }

    .premium-text {
        font-size: 1rem;
    }

    .premium-icon {
        font-size: 1.3rem;
    }

    .premium-title {
        font-size: 0.8rem;
    }

    .title-line {
        font-size: 0.7rem;
    }

    .premium-gradient {
        font-size: 0.85rem;
    }

    .premium-btn {
        padding: 6px 10px;
        font-size: 0.7rem;
        max-width: 90px;
    }

    .premium-contact {
        padding: 7px 10px;
        font-size: 0.7rem;
        min-width: 75px;
    }
}

@media (max-width: 768px) {
    .premium-navbar {
        height: auto;
        min-height: 70px;
    }

    .premium-navbar .nav-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
        height: auto;
    }

    .premium-nav-menu {
        order: 2;
        margin: var(--spacing-sm) 0;
        flex-wrap: wrap;
        justify-content: center;
    }

    .premium-nav-link {
        min-width: 70px;
        max-width: 100px;
        padding: 6px 8px;
        font-size: 0.7rem;
    }

    .premium-toggle {
        display: flex;
        position: absolute;
        top: var(--spacing-md);
        right: var(--spacing-md);
        z-index: 10;
    }

    .premium-brand {
        justify-content: center;
        order: 1;
    }

    .premium-cta {
        order: 2;
        margin: var(--spacing-sm) 0;
    }

    .premium-content {
        flex-direction: column;
        gap: var(--spacing-xs);
        text-align: center;
    }

    .cta-left {
        align-items: center;
        width: 100%;
    }

    .premium-title {
        text-align: center;
        font-size: 0.75rem;
    }

    .premium-gradient {
        font-size: 0.8rem;
    }

    .title-subtitle {
        font-size: 0.6rem;
    }

    .cta-right {
        justify-content: center;
        width: 100%;
    }

    .premium-buttons {
        justify-content: center;
        gap: var(--spacing-xs);
    }

    .premium-btn {
        min-width: 80px;
        max-width: 100px;
        padding: 8px 10px;
        font-size: 0.7rem;
    }

    .premium-navbar .nav-actions {
        order: 3;
        justify-content: center;
    }

    .premium-contact {
        flex: 1;
        min-width: auto;
        padding: 10px 14px;
    }

    .premium-hamburger {
        display: flex;
        position: absolute;
        top: var(--spacing-md);
        right: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .premium-navbar {
        min-height: 60px;
    }

    .premium-navbar .nav-container {
        padding: var(--spacing-sm);
        gap: var(--spacing-xs);
    }

    .premium-nav-menu {
        padding: var(--spacing-xs);
        gap: var(--spacing-xs);
    }

    .premium-nav-link {
        padding: 6px 8px;
        font-size: 0.65rem;
        gap: 2px;
    }

    .premium-logo {
        padding: var(--spacing-xs) var(--spacing-sm);
        gap: var(--spacing-xs);
    }

    .premium-text {
        font-size: 1.2rem;
    }

    .premium-icon {
        font-size: 1.5rem;
    }

    .premium-cta {
        padding: var(--spacing-sm);
    }

    .premium-badge {
        padding: 4px 8px;
        font-size: 0.7rem;
    }

    .premium-title {
        font-size: 0.8rem;
    }

    .title-line {
        font-size: 0.7rem;
    }

    .premium-gradient {
        font-size: 0.9rem;
    }

    .premium-btn {
        padding: 8px 12px;
        font-size: 0.75rem;
        gap: 4px;
    }



    .premium-contact {
        padding: 8px 10px;
        font-size: 0.75rem;
        gap: 4px;
    }

    .premium-contact span {
        display: none;
    }

    .premium-btn span {
        font-size: 0.7rem;
    }

    /* Reduce animation intensity on mobile */
    .premium-logo:hover {
        transform: translateY(-1px) scale(1.01);
    }

    .premium-btn:hover,
    .premium-contact:hover {
        transform: translateY(-1px) scale(1.02);
    }

    /* Disable some heavy animations on mobile */
    .navbar-effects {
        display: none;
    }

    .floating-particles,
    .gradient-waves,
    .light-rays {
        display: none;
    }
}

/* ===== PREMIUM ACCESSIBILITY & PERFORMANCE ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .navbar-effects,
    .floating-particles,
    .gradient-waves,
    .light-rays {
        display: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .premium-navbar {
        background: rgba(0, 0, 0, 0.95);
        border-bottom: 2px solid #ffffff;
    }

    .premium-logo {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid #ffffff;
    }

    .premium-text,
    .premium-title,
    .premium-btn,
    .premium-contact {
        color: #ffffff;
    }
}

/* Old styles removed - replaced with premium design above */

/* ===== PERFECT GRID ALIGNMENT FIXES ===== */
@media (min-width: 769px) {
    .premium-grid {
        align-items: stretch;
        min-height: 70px;
    }

    .premium-brand,
    .premium-navbar .nav-actions {
        display: flex;
        align-items: center;
        height: 100%;
    }

    .premium-logo {
        height: fit-content;
        align-self: center;
    }
}

/* Extra fine-tuning for perfect alignment */
@media (min-width: 1400px) {
    .premium-grid {
        max-width: 1800px;
        padding: 0 var(--spacing-2xl);
    }

    .premium-btn {
        max-width: 130px;
        padding: 8px 12px;
        font-size: 0.75rem;
    }

    .premium-contact {
        min-width: 95px;
        padding: 9px 12px;
        font-size: 0.75rem;
    }

    .premium-title {
        font-size: 0.95rem;
    }

    .premium-gradient {
        font-size: 1rem;
    }
}

/* Old navigation animations removed - replaced with premium animations above */

/* Hero Contact Buttons */
.hero-actions .telegram-btn {
    background: linear-gradient(135deg, #0088cc 0%, #229ed9 100%);
    border: none;
    color: white;
    flex-direction: column;
    gap: 4px;
    position: relative;
    overflow: hidden;
}

.hero-actions .telegram-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.hero-actions .telegram-btn:hover::before {
    left: 100%;
}

.hero-actions .whatsapp-btn {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    border: 1px solid rgba(37, 211, 102, 0.3);
    color: white;
    flex-direction: column;
    gap: 4px;
    position: relative;
    overflow: hidden;
}

.hero-actions .whatsapp-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.hero-actions .whatsapp-btn:hover::before {
    left: 100%;
}

.hero-actions .btn-primary.large,
.hero-actions .btn-secondary.large {
    min-height: 80px;
    min-width: 180px;
    padding: var(--spacing-md) var(--spacing-xl);
    justify-content: center;
    text-align: center;
}

.hero-actions .btn-primary.large small,
.hero-actions .btn-secondary.large small {
    font-size: 0.75rem;
    opacity: 0.8;
    font-weight: normal;
    margin-top: 2px;
}

/* Hero buttons hover effects */
.hero-actions .telegram-btn:hover,
.hero-actions .whatsapp-btn:hover {
    transform: translateY(-3px) scale(1.02);
    transition: all 0.3s ease;
}

.hero-actions .telegram-btn:active,
.hero-actions .whatsapp-btn:active {
    transform: translateY(-1px) scale(0.98);
    transition: all 0.1s ease;
}

/* Contact Button Animations */
.hero-actions .telegram-btn {
    animation: telegram-pulse 3s infinite;
}

.hero-actions .whatsapp-btn {
    animation: whatsapp-pulse 3s infinite 1.5s;
}

@keyframes telegram-pulse {
    0%, 100% {
        box-shadow: 0 8px 32px rgba(0, 136, 204, 0.3);
    }
    50% {
        box-shadow: 0 8px 32px rgba(0, 136, 204, 0.6), 0 0 0 10px rgba(0, 136, 204, 0.1);
    }
}

@keyframes whatsapp-pulse {
    0%, 100% {
        box-shadow: 0 8px 32px rgba(37, 211, 102, 0.3);
    }
    50% {
        box-shadow: 0 8px 32px rgba(37, 211, 102, 0.6), 0 0 0 10px rgba(37, 211, 102, 0.1);
    }
}

/* Static stat numbers for communication page */
.stat-number-text {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    display: block;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
}

/* Prevent animation on static stats */
.stat-number-text.no-animation {
    animation: none !important;
    transition: none !important;
}

/* Extra small screens */
@media (max-width: 480px) {
    .communication-hero {
        padding: 100px var(--spacing-sm) 60px;
    }

    .communication-hero .hero-content {
        padding: 0 var(--spacing-sm);
    }

    .contact-buttons {
        flex-direction: column;
        gap: var(--spacing-xs);
        padding: var(--spacing-xs);
    }

    .btn-contact {
        min-width: auto;
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
    }

    .services-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
    }

    .hamburger {
        width: 24px;
        height: 24px;
        padding: 2px;
    }

    .hamburger span {
        width: 14px;
        height: 1px;
    }

    .status-indicator {
        margin: var(--spacing-md) auto 0;
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
        max-width: calc(100vw - 2rem);
        border-radius: var(--radius-lg);
        word-break: break-word;
        hyphens: auto;
    }

    .status-indicator span {
        font-size: 0.8rem;
        line-height: 1.3;
        word-break: break-word;
    }

    .status-dot {
        width: 10px;
        height: 10px;
        flex-shrink: 0;
    }

    .communication-hero .hero-title {
        font-size: 2rem;
        line-height: 1.2;
    }

    .communication-hero .hero-subtitle {
        font-size: 1rem;
        line-height: 1.4;
    }

    .communication-hero .hero-badge {
        font-size: 0.75rem;
        padding: var(--spacing-xs) var(--spacing-sm);
        max-width: calc(100vw - 3rem);
        word-break: break-word;
    }
}
