/* ===================================
   RESPONSIVE COMPONENTS OPTIMIZATION
   Specific fixes for complex components
   =================================== */

/* ===== NAVBAR RESPONSIVE FIXES ===== */

/* Ultra small screens - navbar optimization */
@media (max-width: 374px) {
    .navbar {
        padding: 0 var(--spacing-xs);
    }
    
    .nav-container {
        gap: var(--spacing-xs);
    }
    
    .nav-brand .logo-text {
        font-size: 0.9rem;
    }
    
    .nav-actions {
        gap: var(--spacing-xs);
    }
    
    .nav-actions .btn-primary,
    .nav-actions .btn-secondary {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.7rem;
        min-width: auto;
    }
    
    .nav-actions .btn-primary span,
    .nav-actions .btn-secondary span {
        display: none;
    }
    
    .nav-actions .btn-primary i,
    .nav-actions .btn-secondary i {
        margin: 0;
    }
}

/* ===== HERO SECTION RESPONSIVE ===== */

/* Ultra small screens */
@media (max-width: 374px) {
    .hero {
        padding: 80px 0 var(--spacing-2xl);
        min-height: auto;
    }
    
    .hero-container {
        gap: var(--spacing-lg);
    }
    
    .hero-badge {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.7rem;
    }
    
    .hero-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
        text-align: center;
    }
    
    .hero-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
        width: 100%;
    }
    
    .hero-actions .btn-primary,
    .hero-actions .btn-secondary {
        width: 100%;
        justify-content: center;
    }
}

/* Small screens */
@media (min-width: 375px) and (max-width: 479px) {
    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
    
    .hero-actions {
        flex-direction: column;
        gap: var(--spacing-md);
    }
}

/* ===== CARDS AND GRIDS RESPONSIVE ===== */

/* Ultra small screens - single column everything */
@media (max-width: 374px) {
    .services-grid,
    .gallery-grid,
    .features-grid,
    .premium-projects-grid,
    .categories-grid,
    .quick-solutions-grid,
    .wishlist-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-md);
    }
    
    .project-card,
    .service-card,
    .feature-card,
    .category-card {
        margin: 0;
        padding: var(--spacing-md);
    }
}

/* Small screens - 2 columns for some grids */
@media (min-width: 375px) and (max-width: 479px) {
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }
    
    .quick-solutions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
}

/* ===== MODAL RESPONSIVE ===== */

/* Ultra small screens */
@media (max-width: 374px) {
    .modal-content {
        margin: var(--spacing-xs);
        padding: var(--spacing-sm);
        max-height: 95vh;
        border-radius: 8px;
    }
    
    .modal-header {
        padding: var(--spacing-sm);
        flex-direction: column;
        gap: var(--spacing-xs);
        text-align: center;
    }
    
    .modal-body {
        padding: var(--spacing-sm);
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .modal-footer {
        padding: var(--spacing-sm);
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .modal-footer .btn-primary,
    .modal-footer .btn-secondary {
        width: 100%;
    }
}

/* ===== FORM RESPONSIVE ===== */

/* Ultra small screens */
@media (max-width: 374px) {
    .form-group {
        margin-bottom: var(--spacing-sm);
    }
    
    .form-group label {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-xs);
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    .form-row {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}

/* ===== FILTER TABS RESPONSIVE ===== */

/* Ultra small screens */
@media (max-width: 374px) {
    .filter-tabs {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .filter-tab {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-sm);
    }
    
    .tab-label {
        font-size: var(--font-size-xs);
    }
    
    .tab-count {
        font-size: var(--font-size-xs);
    }
}

/* Small screens */
@media (min-width: 375px) and (max-width: 479px) {
    .filter-tabs {
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }
    
    .filter-tab {
        flex: 1;
        min-width: calc(50% - var(--spacing-xs));
    }
}

/* ===== STATS AND METRICS RESPONSIVE ===== */

/* Ultra small screens */
@media (max-width: 374px) {
    .stat-item,
    .metric-item {
        text-align: center;
        padding: var(--spacing-sm);
    }
    
    .stat-number,
    .metric-number {
        font-size: var(--font-size-2xl);
    }
    
    .stat-label,
    .metric-label {
        font-size: var(--font-size-xs);
    }
}

/* ===== TECH ACADEMY RESPONSIVE ===== */

/* Ultra small screens */
@media (max-width: 374px) {
    .vip-academy-container {
        padding: var(--spacing-md);
        margin: 0 -var(--spacing-xs);
    }
    
    .vip-badges {
        flex-direction: column;
        gap: var(--spacing-xs);
        align-items: center;
    }
    
    .vip-badge {
        font-size: 0.7rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .vip-features {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .timeline-item {
        padding-left: var(--spacing-sm);
    }
    
    .timeline-marker {
        left: -15px;
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }
}

/* ===== COMMUNICATION PAGE RESPONSIVE ===== */

/* Ultra small screens */
@media (max-width: 374px) {
    .premium-navbar {
        min-height: 60px;
        padding: 0 var(--spacing-xs);
    }
    
    .premium-navbar .nav-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
        text-align: center;
    }

    .premium-nav-menu {
        order: 2;
        justify-content: center;
    }

    .nav-actions {
        order: 3;
        justify-content: center;
    }
    
    .cta-title {
        font-size: var(--font-size-lg);
    }
    
    .cta-buttons {
        gap: var(--spacing-xs);
    }
    
    .premium-nav-link {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.65rem;
        min-width: 60px;
        max-width: 80px;
    }

    .premium-contact {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.7rem;
    }
}

/* ===== PORTFOLIO GALLERY RESPONSIVE ===== */

/* Ultra small screens */
@media (max-width: 374px) {
    .premium-filter-container {
        padding: var(--spacing-sm);
    }
    
    .filter-header-premium {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
    
    .view-controls-premium {
        justify-content: center;
    }
    
    .view-btn-premium {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
    }
    
    .search-container {
        margin: var(--spacing-sm) 0;
    }
    
    .search-input {
        font-size: var(--font-size-sm);
        padding: var(--spacing-sm);
    }
}

/* ===== LARGE SCREENS OPTIMIZATION ===== */

/* 8K and above - ensure content doesn't become too spread out */
@media (min-width: 7680px) {
    .container {
        max-width: 7200px; /* Limit max width even on 8K */
    }
    
    .hero-container,
    .section {
        max-width: 6400px;
        margin: 0 auto;
    }
    
    /* Prevent text from becoming unreadable on huge screens */
    .hero-title {
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .hero-description,
    .section-subtitle {
        max-width: 800px;
        margin: 0 auto;
    }
    
    /* Limit grid columns even on 8K */
    .services-grid {
        grid-template-columns: repeat(8, 1fr) !important;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(6, 1fr) !important;
    }
    
    .features-grid {
        grid-template-columns: repeat(4, 1fr) !important;
    }
}
