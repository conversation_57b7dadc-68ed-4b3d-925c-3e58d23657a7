/* ===================================
   UNIVERSAL RESPONSIVE DESIGN SYSTEM
   Complete coverage: 320px to 8K (7680px)
   =================================== */

/* ===== CSS VARIABLES FOR RESPONSIVE SCALING ===== */
:root {
    /* Base spacing that scales with screen size */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Base font sizes that scale */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
    
    /* Container max-widths */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;
    --container-3xl: 1920px;
    --container-4xl: 2560px;
    --container-5xl: 3840px;
    --container-8k: 7680px;
}

/* ===== ULTRA SMALL DEVICES (320px - 374px) ===== */
@media (max-width: 374px) {
    :root {
        --spacing-xs: 0.125rem;
        --spacing-sm: 0.25rem;
        --spacing-md: 0.5rem;
        --spacing-lg: 0.75rem;
        --spacing-xl: 1rem;
        --spacing-2xl: 1.5rem;
        --spacing-3xl: 2rem;
        
        --font-size-xs: 0.625rem;
        --font-size-sm: 0.75rem;
        --font-size-base: 0.875rem;
        --font-size-lg: 1rem;
        --font-size-xl: 1.125rem;
        --font-size-2xl: 1.25rem;
        --font-size-3xl: 1.5rem;
        --font-size-4xl: 1.875rem;
        --font-size-5xl: 2.25rem;
        --font-size-6xl: 2.75rem;
    }
    
    .container {
        padding: 0 var(--spacing-sm);
        max-width: 100%;
    }
    
    .navbar {
        height: 60px;
        padding: 0 var(--spacing-sm);
    }
    
    .nav-container {
        gap: var(--spacing-xs);
    }
    
    .nav-link {
        font-size: var(--font-size-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .btn-primary,
    .btn-secondary {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
        min-height: 40px;
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: var(--font-size-base);
    }
    
    .section-title {
        font-size: var(--font-size-3xl);
    }
    
    /* Grid adjustments for ultra small screens */
    .premium-projects-grid,
    .services-grid,
    .gallery-grid,
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    /* Card adjustments */
    .project-card,
    .service-card,
    .feature-card {
        padding: var(--spacing-md);
        margin: 0 -var(--spacing-xs);
    }
    
    /* Modal adjustments */
    .modal-content {
        margin: var(--spacing-xs);
        padding: var(--spacing-md);
        max-height: 95vh;
        border-radius: 8px;
    }
    
    /* Navigation menu for ultra small */
    .nav-menu {
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }
    
    .nav-menu.active {
        top: 60px;
    }
}

/* ===== SMALL DEVICES (375px - 413px) ===== */
@media (min-width: 375px) and (max-width: 413px) {
    :root {
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.375rem;
        --spacing-md: 0.75rem;
        --spacing-lg: 1rem;
        --spacing-xl: 1.25rem;
        --spacing-2xl: 2rem;
        --spacing-3xl: 2.5rem;
        
        --font-size-xs: 0.6875rem;
        --font-size-sm: 0.8125rem;
        --font-size-base: 0.9375rem;
        --font-size-lg: 1.0625rem;
        --font-size-xl: 1.1875rem;
        --font-size-2xl: 1.375rem;
        --font-size-3xl: 1.625rem;
        --font-size-4xl: 2rem;
        --font-size-5xl: 2.5rem;
        --font-size-6xl: 3rem;
    }
    
    .container {
        padding: 0 var(--spacing-md);
    }
    
    .navbar {
        height: 65px;
    }
    
    .hero-title {
        font-size: var(--font-size-5xl);
    }
    
    .btn-primary,
    .btn-secondary {
        min-height: 42px;
        padding: var(--spacing-sm) var(--spacing-lg);
    }
}

/* ===== MEDIUM SMALL DEVICES (414px - 479px) ===== */
@media (min-width: 414px) and (max-width: 479px) {
    :root {
        --font-size-5xl: 2.75rem;
        --font-size-6xl: 3.25rem;
    }
    
    .navbar {
        height: 70px;
    }
    
    .hero-title {
        font-size: var(--font-size-5xl);
    }
    
    .btn-primary,
    .btn-secondary {
        min-height: 44px;
    }
    
    /* Allow 2 columns for some grids on larger phones */
    .services-grid.quick-solutions,
    .features-grid.compact {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }
}

/* ===== LARGE PHONES / SMALL TABLETS (480px - 767px) ===== */
@media (min-width: 480px) and (max-width: 767px) {
    .container {
        max-width: var(--container-sm);
        padding: 0 var(--spacing-lg);
    }
    
    .navbar {
        height: 75px;
    }
    
    /* 2-column layouts for better space usage */
    .services-grid,
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
    
    /* Larger touch targets */
    .btn-primary,
    .btn-secondary,
    .nav-link {
        min-height: 48px;
        padding: var(--spacing-md) var(--spacing-lg);
    }
    
    .hero-title {
        font-size: var(--font-size-6xl);
    }
}

/* ===== TABLETS PORTRAIT (768px - 1023px) ===== */
@media (min-width: 768px) and (max-width: 1023px) {
    .container {
        max-width: var(--container-md);
        padding: 0 var(--spacing-xl);
    }
    
    .navbar {
        height: 80px;
    }
    
    /* 3-column layouts for tablets */
    .services-grid,
    .gallery-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-lg);
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xl);
    }
    
    .premium-projects-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xl);
    }
    
    /* Show desktop navigation */
    .nav-menu {
        display: flex;
        position: static;
        background: none;
        backdrop-filter: none;
        flex-direction: row;
        padding: 0;
        border: none;
        border-radius: 0;
        transform: none;
        opacity: 1;
        box-shadow: none;
    }
    
    .mobile-menu-toggle {
        display: none;
    }
}

/* ===== TABLETS LANDSCAPE / SMALL DESKTOP (1024px - 1199px) ===== */
@media (min-width: 1024px) and (max-width: 1199px) {
    .container {
        max-width: var(--container-lg);
        padding: 0 var(--spacing-2xl);
    }
    
    .navbar {
        height: 85px;
    }
    
    /* 4-column layouts */
    .services-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-lg);
    }
    
    .gallery-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-xl);
    }
    
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-xl);
    }
    
    .premium-projects-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-2xl);
    }
}

/* ===== MEDIUM DESKTOP (1200px - 1439px) ===== */
@media (min-width: 1200px) and (max-width: 1439px) {
    .container {
        max-width: var(--container-xl);
        padding: 0 var(--spacing-2xl);
    }

    .navbar {
        height: 90px;
    }

    /* Optimal layouts for medium desktop */
    .services-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-xl);
    }

    .gallery-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-xl);
    }

    .features-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-2xl);
    }

    .premium-projects-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-3xl);
    }

    /* Larger font sizes for better readability */
    .hero-title {
        font-size: 4rem;
    }

    .section-title {
        font-size: 2.5rem;
    }
}

/* ===== LARGE DESKTOP (1440px - 1919px) ===== */
@media (min-width: 1440px) and (max-width: 1919px) {
    .container {
        max-width: var(--container-2xl);
        padding: 0 var(--spacing-3xl);
    }

    .navbar {
        height: 95px;
    }

    /* Enhanced layouts for large desktop */
    .services-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: var(--spacing-xl);
    }

    .gallery-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-2xl);
    }

    .features-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-2xl);
    }

    .premium-projects-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-3xl);
    }

    /* Enhanced typography */
    .hero-title {
        font-size: 4.5rem;
        line-height: 1.1;
    }

    .hero-subtitle {
        font-size: 1.5rem;
    }

    .section-title {
        font-size: 3rem;
    }

    /* Enhanced spacing */
    .section {
        padding: var(--spacing-3xl) 0;
    }
}

/* ===== FULL HD (1920px - 2559px) ===== */
@media (min-width: 1920px) and (max-width: 2559px) {
    .container {
        max-width: var(--container-3xl);
        padding: 0 var(--spacing-3xl);
    }

    .navbar {
        height: 100px;
    }

    /* Full HD optimized layouts */
    .services-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: var(--spacing-xl);
    }

    .gallery-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: var(--spacing-2xl);
    }

    .features-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-3xl);
    }

    .premium-projects-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 4rem;
    }

    /* Full HD typography */
    .hero-title {
        font-size: 5rem;
        line-height: 1.05;
    }

    .hero-subtitle {
        font-size: 1.75rem;
    }

    .section-title {
        font-size: 3.5rem;
    }

    /* Enhanced components for Full HD */
    .project-card,
    .service-card,
    .feature-card {
        padding: var(--spacing-2xl);
        border-radius: 20px;
    }

    .btn-primary,
    .btn-secondary {
        padding: var(--spacing-lg) var(--spacing-2xl);
        font-size: var(--font-size-lg);
        border-radius: 12px;
    }

    .modal-content {
        max-width: 1200px;
        padding: var(--spacing-3xl);
        border-radius: 24px;
    }
}

/* ===== 2K/QHD (2560px - 3839px) ===== */
@media (min-width: 2560px) and (max-width: 3839px) {
    :root {
        /* Enhanced spacing for 2K */
        --spacing-xs: 0.375rem;
        --spacing-sm: 0.75rem;
        --spacing-md: 1.5rem;
        --spacing-lg: 2.25rem;
        --spacing-xl: 3rem;
        --spacing-2xl: 4.5rem;
        --spacing-3xl: 6rem;

        /* Enhanced typography for 2K */
        --font-size-xs: 0.9375rem;
        --font-size-sm: 1.0625rem;
        --font-size-base: 1.25rem;
        --font-size-lg: 1.4375rem;
        --font-size-xl: 1.625rem;
        --font-size-2xl: 1.875rem;
        --font-size-3xl: 2.375rem;
        --font-size-4xl: 2.875rem;
        --font-size-5xl: 3.75rem;
        --font-size-6xl: 4.75rem;
    }

    .container {
        max-width: var(--container-4xl);
        padding: 0 4rem;
    }

    .navbar {
        height: 120px;
    }

    /* 2K optimized layouts */
    .services-grid {
        grid-template-columns: repeat(8, 1fr);
        gap: var(--spacing-2xl);
    }

    .gallery-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: var(--spacing-2xl);
    }

    .features-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: var(--spacing-3xl);
    }

    .premium-projects-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 5rem;
    }

    /* 2K typography */
    .hero-title {
        font-size: 6rem;
        line-height: 1;
    }

    .hero-subtitle {
        font-size: 2.25rem;
    }

    .section-title {
        font-size: 4.5rem;
    }

    /* Enhanced components for 2K */
    .project-card,
    .service-card,
    .feature-card {
        padding: var(--spacing-3xl);
        border-radius: 28px;
    }

    .btn-primary,
    .btn-secondary {
        padding: var(--spacing-xl) var(--spacing-3xl);
        font-size: var(--font-size-xl);
        border-radius: 16px;
        min-height: 60px;
    }

    .modal-content {
        max-width: 1600px;
        padding: 4rem;
        border-radius: 32px;
    }

    /* Enhanced navigation for 2K */
    .nav-link {
        padding: var(--spacing-lg) var(--spacing-xl);
        font-size: var(--font-size-lg);
    }

    .logo {
        font-size: var(--font-size-2xl);
    }
}

/* ===== 4K UHD (3840px - 7679px) ===== */
@media (min-width: 3840px) and (max-width: 7679px) {
    :root {
        /* 4K spacing */
        --spacing-xs: 0.5rem;
        --spacing-sm: 1rem;
        --spacing-md: 2rem;
        --spacing-lg: 3rem;
        --spacing-xl: 4rem;
        --spacing-2xl: 6rem;
        --spacing-3xl: 8rem;

        /* 4K typography */
        --font-size-xs: 1.125rem;
        --font-size-sm: 1.25rem;
        --font-size-base: 1.5rem;
        --font-size-lg: 1.75rem;
        --font-size-xl: 2rem;
        --font-size-2xl: 2.25rem;
        --font-size-3xl: 2.875rem;
        --font-size-4xl: 3.5rem;
        --font-size-5xl: 4.5rem;
        --font-size-6xl: 5.75rem;
    }

    .container {
        max-width: var(--container-5xl);
        padding: 0 6rem;
    }

    .navbar {
        height: 160px;
    }

    /* 4K optimized layouts */
    .services-grid {
        grid-template-columns: repeat(10, 1fr);
        gap: var(--spacing-2xl);
    }

    .gallery-grid {
        grid-template-columns: repeat(8, 1fr);
        gap: var(--spacing-3xl);
    }

    .features-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: var(--spacing-3xl);
    }

    .premium-projects-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 8rem;
    }

    /* 4K typography */
    .hero-title {
        font-size: 8rem;
        line-height: 0.95;
    }

    .hero-subtitle {
        font-size: 3rem;
    }

    .section-title {
        font-size: 6rem;
    }

    /* Enhanced components for 4K */
    .project-card,
    .service-card,
    .feature-card {
        padding: 4rem;
        border-radius: 40px;
    }

    .btn-primary,
    .btn-secondary {
        padding: var(--spacing-2xl) 4rem;
        font-size: var(--font-size-2xl);
        border-radius: 24px;
        min-height: 80px;
    }

    .modal-content {
        max-width: 2400px;
        padding: 6rem;
        border-radius: 48px;
    }

    /* Enhanced navigation for 4K */
    .nav-link {
        padding: var(--spacing-xl) var(--spacing-2xl);
        font-size: var(--font-size-xl);
    }

    .logo {
        font-size: var(--font-size-3xl);
    }
}

/* ===== 8K (7680px and above) ===== */
@media (min-width: 7680px) {
    :root {
        /* 8K spacing */
        --spacing-xs: 0.75rem;
        --spacing-sm: 1.5rem;
        --spacing-md: 3rem;
        --spacing-lg: 4.5rem;
        --spacing-xl: 6rem;
        --spacing-2xl: 9rem;
        --spacing-3xl: 12rem;

        /* 8K typography */
        --font-size-xs: 1.5rem;
        --font-size-sm: 1.75rem;
        --font-size-base: 2rem;
        --font-size-lg: 2.5rem;
        --font-size-xl: 3rem;
        --font-size-2xl: 3.5rem;
        --font-size-3xl: 4.5rem;
        --font-size-4xl: 5.5rem;
        --font-size-5xl: 7rem;
        --font-size-6xl: 9rem;
    }

    .container {
        max-width: var(--container-8k);
        padding: 0 8rem;
    }

    .navbar {
        height: 200px;
    }

    /* 8K optimized layouts */
    .services-grid {
        grid-template-columns: repeat(12, 1fr);
        gap: var(--spacing-3xl);
    }

    .gallery-grid {
        grid-template-columns: repeat(10, 1fr);
        gap: var(--spacing-3xl);
    }

    .features-grid {
        grid-template-columns: repeat(8, 1fr);
        gap: 4rem;
    }

    .premium-projects-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 12rem;
    }

    /* 8K typography */
    .hero-title {
        font-size: 12rem;
        line-height: 0.9;
    }

    .hero-subtitle {
        font-size: 4.5rem;
    }

    .section-title {
        font-size: 9rem;
    }

    /* Enhanced components for 8K */
    .project-card,
    .service-card,
    .feature-card {
        padding: 6rem;
        border-radius: 60px;
    }

    .btn-primary,
    .btn-secondary {
        padding: var(--spacing-3xl) 6rem;
        font-size: var(--font-size-3xl);
        border-radius: 36px;
        min-height: 120px;
    }

    .modal-content {
        max-width: 4000px;
        padding: 8rem;
        border-radius: 72px;
    }

    /* Enhanced navigation for 8K */
    .nav-link {
        padding: var(--spacing-2xl) var(--spacing-3xl);
        font-size: var(--font-size-2xl);
    }

    .logo {
        font-size: var(--font-size-4xl);
    }
}

/* ===== ACCESSIBILITY & PERFORMANCE OPTIMIZATIONS ===== */

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .navbar-effects,
    .floating-particles,
    .gradient-waves,
    .light-rays,
    .premium-animations {
        display: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --glass-bg: rgba(255, 255, 255, 0.3);
        --glass-border: rgba(255, 255, 255, 0.6);
        --backdrop-blur: none;
    }

    .glass-effect {
        background: var(--glass-bg);
        border: 2px solid var(--glass-border);
        backdrop-filter: none;
    }
}

/* Dark mode preferences */
@media (prefers-color-scheme: dark) {
    :root {
        --background-main: #0a0a0a;
        --background-secondary: #1a1a1a;
        --text-primary: #ffffff;
        --text-secondary: #e0e0e0;
        --text-muted: #a0a0a0;
    }
}

/* Print styles */
@media print {
    .navbar,
    .mobile-menu-toggle,
    .floating-elements,
    .background-animations,
    .video-backgrounds,
    .interactive-elements {
        display: none !important;
    }

    .container {
        max-width: 100% !important;
        padding: 0 !important;
    }

    .hero,
    .section {
        page-break-inside: avoid;
        padding: 1rem 0 !important;
    }

    .project-card,
    .service-card,
    .feature-card {
        background: white !important;
        color: black !important;
        border: 1px solid #ccc !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }

    .btn-primary,
    .btn-secondary {
        background: white !important;
        color: black !important;
        border: 2px solid black !important;
    }
}
