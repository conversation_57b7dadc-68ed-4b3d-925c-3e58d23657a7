// Gallery Page JavaScript

// Projects data based on the existing projects folder
const projectsData = [
    {
        id: 'smartcard',
        title: 'SmartCard.pl',
        description: 'AI Generator Wizytówek z avatarem AI, kodem QR, NFC i zaawansowanymi analytics. Rewolucyjna wizytówka digital.',
        category: ['mobile', 'ai', 'business'],
        tech: ['JavaScript', 'AI API', 'QR Generator', 'Analytics'],
        image: '../projects/card/preview.jpg',
        liveUrl: '../projects/card/index.html',
        featured: true,
        stats: {
            users: '50K+',
            conversion: '99.9%',
            features: '15+'
        },
        features: [
            'Generator Avatarów AI',
            'Dynamiczny kod QR',
            'Symulacja NFC',
            'Zaawansowane Analytics',
            'Nagranie głosowe',
            'Zarządzanie zespołem'
        ]
    },
    {
        id: 'brandme',
        title: 'BrandMe.pl',
        description: 'Kompletny ekosystem budowania osobistej marki z AI-powered narzędziami, analityką wzrostu i profesjonalnymi funkcjami.',
        category: ['web', 'ai', 'business'],
        tech: ['React', 'AI Analytics', '<PERSON> Tools', 'Social Media'],
        image: '../projects/brandme-pl/preview.jpg',
        liveUrl: '../projects/brandme-pl/index.html',
        featured: true,
        stats: {
            growth: '+247%',
            tools: '500+',
            success: '95%'
        },
        features: [
            'AI Website Builder',
            'Kreator Treści',
            'Zaawansowana Analityka',
            'Narzędzia Pro',
            'Brand Score Calculator',
            'AI Bio Generator'
        ]
    },
    {
        id: 'fitgenius',
        title: 'FitGenius.pl',
        description: 'Personalizowany trener AI dostępny 24/7. Spersonalizowane treningi, plany żywieniowe i analiza postępów.',
        category: ['mobile', 'ai', 'health'],
        tech: ['JavaScript', 'AI Coaching', 'Health APIs', 'Analytics'],
        image: '../projects/fitgenius-ai-coach/preview.jpg',
        liveUrl: '../projects/fitgenius-ai-coach/index.html',
        featured: true,
        stats: {
            users: '50K',
            success: '92%',
            workouts: '1M+'
        },
        features: [
            'Spersonalizowane Treningi',
            'Inteligentne Odżywianie',
            'Zaawansowane Analizy',
            'Gamifikacja & Społeczność',
            'Coaching AI',
            'AI Form Checker'
        ]
    },
    {
        id: 'autoexpert',
        title: 'AutoExpert Kraków',
        description: 'Profesjonalna platforma dla ekspertów motoryzacyjnych z zaawansowanymi narzędziami diagnostycznymi.',
        category: ['web', 'business'],
        tech: ['HTML5', 'CSS3', 'JavaScript', 'Automotive APIs'],
        image: '../projects/autoexpert-krakow/preview.jpg',
        liveUrl: '../projects/autoexpert-krakow/index.html',
        featured: false,
        stats: {
            experts: '100+',
            diagnostics: '5K+',
            accuracy: '98%'
        },
        features: [
            'Diagnostyka pojazdów',
            'Baza ekspertów',
            'System rezerwacji',
            'Raporty techniczne',
            'Galeria usług',
            'Kontakt z ekspertami'
        ]
    },
    {
        id: 'designbymichal',
        title: 'Design by Michał',
        description: 'Portfolio designerskie z nowoczesnym interfejsem i prezentacją projektów graficznych.',
        category: ['web', 'business'],
        tech: ['HTML5', 'CSS3', 'JavaScript', 'Design Tools'],
        image: '../projects/design-by-michal/preview.jpg',
        liveUrl: '../projects/design-by-michal/index.html',
        featured: false,
        stats: {
            projects: '50+',
            clients: '30+',
            satisfaction: '100%'
        },
        features: [
            'Portfolio projektów',
            'Galeria prac',
            'Proces projektowy',
            'Kontakt z klientem',
            'Responsive design',
            'Nowoczesny UI'
        ]
    },
    {
        id: 'englishexpress',
        title: 'English Express',
        description: 'Interaktywna platforma do nauki języka angielskiego z zaawansowanymi metodami edukacyjnymi.',
        category: ['mobile', 'education'],
        tech: ['JavaScript', 'Education APIs', 'Interactive UI', 'Progress Tracking'],
        image: '../projects/english-express/preview.jpg',
        liveUrl: '../projects/english-express/index.html',
        featured: false,
        stats: {
            students: '1K+',
            lessons: '200+',
            progress: '85%'
        },
        features: [
            'Interaktywne lekcje',
            'Śledzenie postępów',
            'Testy i quizy',
            'Materiały edukacyjne',
            'System poziomów',
            'Certyfikaty'
        ]
    },
    {
        id: 'fitlifepro',
        title: 'FitLife Pro',
        description: 'Zaawansowana aplikacja fitness z personalizowanymi planami treningowymi i monitoringiem zdrowia.',
        category: ['mobile', 'health'],
        tech: ['JavaScript', 'Health APIs', 'Fitness Tracking', 'Charts'],
        image: '../projects/fitlife-pro/preview.jpg',
        liveUrl: '../projects/fitlife-pro/index.html',
        featured: false,
        stats: {
            workouts: '10K+',
            users: '2K+',
            goals: '90%'
        },
        features: [
            'Plany treningowe',
            'Monitoring zdrowia',
            'Śledzenie kalorii',
            'Statystyki postępów',
            'Cele fitness',
            'Społeczność'
        ]
    },
    {
        id: 'gastroai',
        title: 'Gastro AI Restaurant Optimizer',
        description: 'Inteligentny system optymalizacji restauracji z AI analizą menu, zarządzaniem zapasami i prognozowaniem sprzedaży.',
        category: ['ai', 'business'],
        tech: ['JavaScript', 'AI Analytics', 'Restaurant APIs', 'Optimization'],
        image: '../projects/gastro-ai-restaurant-optimizer/preview.jpg',
        liveUrl: '../projects/gastro-ai-restaurant-optimizer/index.html',
        featured: false,
        stats: {
            restaurants: '50+',
            optimization: '30%',
            revenue: '+25%'
        },
        features: [
            'AI analiza menu',
            'Zarządzanie zapasami',
            'Prognozowanie sprzedaży',
            'Optymalizacja kosztów',
            'Raporty biznesowe',
            'Integracje POS'
        ]
    },
    {
        id: 'smartevent',
        title: 'Smart Event Planner',
        description: 'Inteligentny planer wydarzeń z AI asystentem, automatycznym planowaniem i zarządzaniem gośćmi.',
        category: ['mobile', 'ai', 'business'],
        tech: ['JavaScript', 'AI Planning', 'Event APIs', 'Calendar Integration'],
        image: '../projects/smart-event-planner/preview.jpg',
        liveUrl: '../projects/smart-event-planner/index.html',
        featured: false,
        stats: {
            events: '1K+',
            planning: '80%',
            satisfaction: '95%'
        },
        features: [
            'AI planowanie wydarzeń',
            'Zarządzanie gośćmi',
            'Integracja kalendarza',
            'Budżetowanie',
            'Vendor management',
            'Real-time updates'
        ]
    },
    {
        id: 'stylestudio',
        title: 'Style Studio Warszawa',
        description: 'Elegancka strona studia stylizacji z galerią prac, systemem rezerwacji i prezentacją usług.',
        category: ['web', 'business'],
        tech: ['HTML5', 'CSS3', 'JavaScript', 'Booking System'],
        image: '../projects/style-studio-warszawa/preview.jpg',
        liveUrl: '../projects/style-studio-warszawa/index.html',
        featured: false,
        stats: {
            clients: '500+',
            bookings: '2K+',
            satisfaction: '98%'
        },
        features: [
            'Galeria stylizacji',
            'System rezerwacji',
            'Prezentacja usług',
            'Portfolio stylistów',
            'Cennik usług',
            'Kontakt online'
        ]
    },
    {
        id: 'technomax',
        title: 'TechnoMax',
        description: 'Nowoczesna platforma technologiczna z prezentacją najnowszych rozwiązań IT i usług technicznych.',
        category: ['web', 'business'],
        tech: ['JavaScript', 'Tech APIs', 'Modern UI', 'Responsive'],
        image: '../projects/technomax/preview.jpg',
        liveUrl: '../projects/technomax/index.html',
        featured: false,
        stats: {
            solutions: '100+',
            clients: '200+',
            uptime: '99.9%'
        },
        features: [
            'Rozwiązania IT',
            'Usługi techniczne',
            'Wsparcie 24/7',
            'Konsultacje',
            'Implementacja',
            'Maintenance'
        ]
    },
    {
        id: 'terapiadostepna',
        title: 'Terapia Dostępna',
        description: 'Platforma łącząca pacjentów z terapeutami, oferująca dostępną pomoc psychologiczną online.',
        category: ['mobile', 'health'],
        tech: ['JavaScript', 'Health APIs', 'Video Chat', 'Booking'],
        image: '../projects/terapia-dostepna/preview.jpg',
        liveUrl: '../projects/terapia-dostepna/index.html',
        featured: false,
        stats: {
            therapists: '100+',
            sessions: '5K+',
            satisfaction: '96%'
        },
        features: [
            'Baza terapeutów',
            'Sesje online',
            'System rezerwacji',
            'Bezpieczna komunikacja',
            'Śledzenie postępów',
            'Dostępne ceny'
        ]
    }
];

let currentProjects = [];
let currentFilter = 'all';
let currentView = 'grid';
let currentSort = 'default';
let currentSearch = '';
let quickFilterActive = null;

// Initialize gallery
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing gallery...');
    initializeGallery();

    // Add intersection observer for stats animation
    const heroStats = document.querySelector('.hero-stats');
    if (heroStats) {
        const statsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    updateHeroStats();
                    statsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        statsObserver.observe(heroStats);
    }
});

function initializeGallery() {
    console.log('Initializing gallery with', projectsData.length, 'projects');
    currentProjects = [...projectsData];
    updateFilterCounts();
    loadProjects();
    // createFloatingElements(); // Temporarily disabled
}

// Update hero statistics dynamically
function updateHeroStats() {
    const projectsCount = projectsData.length;

    // Calculate unique technologies
    const allTechnologies = new Set();
    projectsData.forEach(project => {
        project.tech.forEach(tech => allTechnologies.add(tech));
    });
    const technologiesCount = allTechnologies.size;

    // Calculate years of experience (starting from 2020 when Robert began as Java trainer)
    const startYear = 2020;
    const currentYear = new Date().getFullYear();
    const yearsOfExperience = currentYear - startYear;

    // Update DOM elements with animation
    animateCounter(document.getElementById('projectsCount'), projectsCount);
    animateCounter(document.getElementById('technologiesCount'), technologiesCount, '+');
    animateCounter(document.getElementById('experienceCount'), yearsOfExperience, '+');
}

// Animate counter with optional suffix
function animateCounter(element, target, suffix = '') {
    if (!element) return;

    const start = 0;
    const duration = 2000;
    const startTime = performance.now();

    function updateCounter(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function for smooth animation
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = Math.floor(start + (target - start) * easeOutQuart);

        element.textContent = current + suffix;

        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        } else {
            element.textContent = target + suffix;
        }
    }

    requestAnimationFrame(updateCounter);
}

// Load and display projects
function loadProjects() {
    const galleryGrid = document.getElementById('galleryGrid');
    console.log('Gallery grid element:', galleryGrid);
    console.log('Current projects:', currentProjects.length);

    if (!galleryGrid) {
        console.error('Gallery grid element not found!');
        return;
    }

    galleryGrid.innerHTML = '';

    currentProjects.forEach(project => {
        const projectElement = createProjectElement(project);
        galleryGrid.appendChild(projectElement);
    });

    // Add animation delay
    const projectCards = document.querySelectorAll('.project-card');
    projectCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });

    console.log('Projects loaded:', projectCards.length);
}

// Create project element
function createProjectElement(project) {
    const projectDiv = document.createElement('div');
    projectDiv.className = 'project-card';
    projectDiv.setAttribute('data-categories', project.category.join(' '));
    
    const badges = project.featured ? '<div class="project-badge featured">Featured</div>' : '';
    
    projectDiv.innerHTML = `
        <div class="project-image">
            <img src="${project.image}" alt="${project.title}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNjY3ZWVhIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj4ke3Byb2plY3QudGl0bGV9PC90ZXh0Pjwvc3ZnPg=='">
            <div class="project-badges">
                ${badges}
            </div>
            <div class="project-overlay">
                <button class="btn-project primary" onclick="openProjectModal('${project.id}')">
                    <i class="fas fa-eye"></i>
                    Zobacz szczegóły
                </button>
            </div>
        </div>
        <div class="project-content">
            <h3 class="project-title">${project.title}</h3>
            <p class="project-description">${project.description}</p>
            <div class="project-tech">
                ${project.tech.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
            </div>
            <div class="project-stats">
                ${Object.entries(project.stats).map(([key, value]) => `
                    <div class="stat-mini">
                        <span class="stat-mini-number">${value}</span>
                        <span class="stat-mini-label">${key}</span>
                    </div>
                `).join('')}
            </div>
            <div class="project-actions">
                <a href="${project.liveUrl}" target="_blank" class="btn-project primary">
                    <i class="fas fa-external-link-alt"></i>
                    Live Demo
                </a>
                <button class="btn-project secondary" onclick="openProjectModal('${project.id}')">
                    <i class="fas fa-info-circle"></i>
                    Szczegóły
                </button>
            </div>
        </div>
    `;
    
    return projectDiv;
}

// Premium Filter Functions
function updateFilterCounts() {
    const categories = ['all', 'web', 'mobile', 'ai', 'business', 'health', 'education'];

    categories.forEach(category => {
        let count;
        if (category === 'all') {
            count = projectsData.length;
        } else {
            count = projectsData.filter(project => project.category.includes(category)).length;
        }

        const countElement = document.querySelector(`[data-count="${category}"]`);
        if (countElement) {
            countElement.textContent = count;
        }
    });
}

function filterProjects(filter) {
    currentFilter = filter;
    quickFilterActive = null;

    // Update filter tabs
    document.querySelectorAll('.filter-tab').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

    // Clear quick filters
    document.querySelectorAll('.quick-filter').forEach(btn => {
        btn.classList.remove('active');
    });

    applyFilters();
}

function applyFilters() {
    let filteredProjects = [...projectsData];

    // Apply category filter
    if (currentFilter !== 'all') {
        filteredProjects = filteredProjects.filter(project =>
            project.category.includes(currentFilter)
        );
    }

    // Apply search filter
    if (currentSearch) {
        filteredProjects = filteredProjects.filter(project =>
            project.title.toLowerCase().includes(currentSearch.toLowerCase()) ||
            project.description.toLowerCase().includes(currentSearch.toLowerCase()) ||
            project.tech.some(tech => tech.toLowerCase().includes(currentSearch.toLowerCase()))
        );
    }

    // Apply quick filter
    if (quickFilterActive) {
        switch (quickFilterActive) {
            case 'featured':
                filteredProjects = filteredProjects.filter(project => project.featured);
                break;
            case 'new':
                // Assuming newer projects have higher IDs or you can add a date field
                filteredProjects = filteredProjects.slice(-6);
                break;
            case 'popular':
                // Sort by a popularity metric (you can customize this)
                filteredProjects = filteredProjects.sort((a, b) => {
                    const aPopularity = parseInt(a.stats.users?.replace(/[^\d]/g, '') || '0');
                    const bPopularity = parseInt(b.stats.users?.replace(/[^\d]/g, '') || '0');
                    return bPopularity - aPopularity;
                });
                break;
        }
    }

    // Apply sorting
    if (currentSort !== 'default') {
        filteredProjects = sortProjectsArray(filteredProjects, currentSort);
    }

    currentProjects = filteredProjects;
    updateFilteredCount();
    loadProjects();
}

function sortProjectsArray(projects, sortType) {
    switch (sortType) {
        case 'name':
            return projects.sort((a, b) => a.title.localeCompare(b.title));
        case 'featured':
            return projects.sort((a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0));
        case 'newest':
            return projects.reverse(); // Assuming array order represents creation order
        case 'popular':
            return projects.sort((a, b) => {
                const aPopularity = parseInt(a.stats.users?.replace(/[^\d]/g, '') || '0');
                const bPopularity = parseInt(b.stats.users?.replace(/[^\d]/g, '') || '0');
                return bPopularity - aPopularity;
            });
        default:
            return projects;
    }
}

function updateFilteredCount() {
    const countElement = document.getElementById('filteredCount');
    if (countElement) {
        countElement.textContent = currentProjects.length;
        countElement.style.animation = 'none';
        countElement.offsetHeight; // Trigger reflow
        countElement.style.animation = 'countUp 0.6s ease-out';
    }
}

function sortProjects(sortType) {
    currentSort = sortType;
    applyFilters();
}

function searchProjects(searchTerm) {
    currentSearch = searchTerm;

    const clearBtn = document.querySelector('.search-clear');
    if (searchTerm) {
        clearBtn.style.display = 'block';
    } else {
        clearBtn.style.display = 'none';
    }

    applyFilters();
}

function clearSearch() {
    const searchInput = document.getElementById('searchInput');
    searchInput.value = '';
    currentSearch = '';
    document.querySelector('.search-clear').style.display = 'none';
    applyFilters();
}

function quickFilter(filterType) {
    // Toggle quick filter
    if (quickFilterActive === filterType) {
        quickFilterActive = null;
    } else {
        quickFilterActive = filterType;
    }

    // Update quick filter buttons
    document.querySelectorAll('.quick-filter').forEach(btn => {
        btn.classList.remove('active');
    });

    if (quickFilterActive) {
        document.querySelector(`[data-filter="${filterType}"]`).classList.add('active');
    }

    applyFilters();
}

// Premium Toggle View
function toggleView(view) {
    currentView = view;

    // Update view buttons
    document.querySelectorAll('.view-btn-premium').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');

    // Update grid class
    const galleryGrid = document.getElementById('galleryGrid');
    galleryGrid.className = 'gallery-grid'; // Reset classes

    switch (view) {
        case 'masonry':
            galleryGrid.classList.add('masonry');
            break;
        case 'list':
            galleryGrid.classList.add('list-view');
            break;
        default:
            // Grid view is default
            break;
    }

    // Add smooth transition
    galleryGrid.style.opacity = '0';
    setTimeout(() => {
        galleryGrid.style.opacity = '1';
    }, 150);
}

// Open project modal
function openProjectModal(projectId) {
    const project = projectsData.find(p => p.id === projectId);
    if (!project) return;
    
    const modal = document.getElementById('projectModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalImage = document.getElementById('modalImage');
    const modalBadges = document.getElementById('modalBadges');
    const modalDescription = document.getElementById('modalDescription');
    const modalTech = document.getElementById('modalTech');
    const modalFeatures = document.getElementById('modalFeatures');
    const modalStats = document.getElementById('modalStats');
    const modalViewBtn = document.getElementById('modalViewBtn');
    
    // Populate modal content
    modalTitle.textContent = project.title;
    modalImage.src = project.image;
    modalImage.alt = project.title;
    
    modalBadges.innerHTML = project.featured ? 
        '<div class="project-badge featured">Featured</div>' : '';
    
    modalDescription.textContent = project.description;
    
    modalTech.innerHTML = `
        <h4>Technologie:</h4>
        <div class="modal-tech-list">
            ${project.tech.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
        </div>
    `;
    
    modalFeatures.innerHTML = `
        <h4>Funkcje:</h4>
        <ul class="modal-features-list">
            ${project.features.map(feature => `<li>${feature}</li>`).join('')}
        </ul>
    `;
    
    modalStats.innerHTML = Object.entries(project.stats).map(([key, value]) => `
        <div class="stat-mini">
            <span class="stat-mini-number">${value}</span>
            <span class="stat-mini-label">${key}</span>
        </div>
    `).join('');
    
    modalViewBtn.onclick = () => window.open(project.liveUrl, '_blank');
    
    // Show modal
    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

// Close project modal
function closeProjectModal() {
    const modal = document.getElementById('projectModal');
    modal.classList.remove('active');
    document.body.style.overflow = 'auto';
}

// Contact Modal Functions
let currentProjectForContact = null;

function openContactModal() {
    // Get current project from modal
    const modalTitle = document.getElementById('modalTitle');
    if (modalTitle) {
        currentProjectForContact = modalTitle.textContent;
    }

    const contactModal = document.getElementById('contactModal');
    contactModal.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Add animation delay for options
    const options = document.querySelectorAll('.contact-option');
    options.forEach((option, index) => {
        option.style.opacity = '0';
        option.style.transform = 'translateY(20px)';
        setTimeout(() => {
            option.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            option.style.opacity = '1';
            option.style.transform = 'translateY(0)';
        }, 100 + (index * 100));
    });
}

function closeContactModal() {
    const contactModal = document.getElementById('contactModal');
    contactModal.classList.remove('active');
    document.body.style.overflow = 'auto';
    currentProjectForContact = null;
}

function contactViaTelegram() {
    const projectName = currentProjectForContact || 'podobnego projektu';
    const message = `Cześć! 🎯

Jestem zainteresowany stworzeniem projektu podobnego do "${projectName}".

Czy możemy omówić szczegóły? Interesuje mnie:
• Zakres funkcjonalności
• Technologie i rozwiązania
• Harmonogram realizacji
• Wycena projektu

Dziękuję za kontakt!`;

    const encodedMessage = encodeURIComponent(message);
    window.open(`https://t.me/pixel_garage?text=${encodedMessage}`, '_blank');
    closeContactModal();
}

function contactViaWhatsApp() {
    const projectName = currentProjectForContact || 'podobnego projektu';
    const message = `Cześć! 🎯

Jestem zainteresowany stworzeniem projektu podobnego do "${projectName}".

Czy możemy omówić szczegóły? Interesuje mnie:
• Zakres funkcjonalności
• Technologie i rozwiązania
• Harmonogram realizacji
• Wycena projektu

Dziękuję za kontakt!`;

    const encodedMessage = encodeURIComponent(message);
    window.open(`https://wa.me/34645577385?text=${encodedMessage}`, '_blank');
    closeContactModal();
}

// Legacy function for backward compatibility
function requestSimilarProject() {
    openContactModal();
}

// Create floating elements animation
function createFloatingElements() {
    const container = document.querySelector('.floating-elements');
    if (!container) return;
    
    for (let i = 0; i < 20; i++) {
        const element = document.createElement('div');
        element.className = 'floating-element';
        element.style.cssText = `
            position: absolute;
            width: ${Math.random() * 6 + 2}px;
            height: ${Math.random() * 6 + 2}px;
            background: rgba(102, 126, 234, ${Math.random() * 0.5 + 0.2});
            border-radius: 50%;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: float ${Math.random() * 10 + 5}s ease-in-out infinite;
            animation-delay: ${Math.random() * 5}s;
        `;
        container.appendChild(element);
    }
}

// Scroll functions
function scrollToGallery() {
    const gallerySection = document.getElementById('projectsGallery');
    if (gallerySection) {
        gallerySection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

function scrollToContact() {
    const ctaSection = document.querySelector('.cta-section');
    if (ctaSection) {
        ctaSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Communication functions
function openCommunication() {
    window.location.href = '../communication/';
}

function openTelegram() {
    window.open('https://t.me/pixelgarage_contact', '_blank');
}

function openWhatsApp() {
    window.open('https://wa.me/48123456789?text=Cześć! Jestem zainteresowany projektami PixelGarage.', '_blank');
}

// Close modals on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeProjectModal();
        closeContactModal();
    }
});

// Lazy loading for images
function setupLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// Initialize lazy loading
setTimeout(setupLazyLoading, 1000);
