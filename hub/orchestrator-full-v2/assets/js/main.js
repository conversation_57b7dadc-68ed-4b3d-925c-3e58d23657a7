// PixelGarage - Main JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    initNavbar();
    initAnimations();
    initCounters();
    initSkillBars();
    initSmoothScrolling();
    initMobileMenu();
    initPremiumEffects();
    initPremiumNavbarEffects();

    // Initialize floating contact widget after delay
    setTimeout(initFloatingContact, 2000);

    // Initialize premium particles
    setTimeout(createPremiumParticles, 1000);
    setTimeout(createInteractiveParticles, 1500);
}

// Premium Navbar Effects Initialization
function initPremiumNavbarEffects() {
    const navbar = document.querySelector('.navbar');
    if (!navbar) return;

    // Add premium entrance animation
    navbar.style.transform = 'translateY(-100%)';
    navbar.style.opacity = '0';

    setTimeout(() => {
        navbar.style.transition = 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
        navbar.style.transform = 'translateY(0)';
        navbar.style.opacity = '1';
    }, 300);

    // Add premium glow effect on page load
    setTimeout(() => {
        navbar.style.boxShadow = '0 8px 40px rgba(102, 126, 234, 0.3)';
        setTimeout(() => {
            navbar.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
        }, 1000);
    }, 1000);

    // Add premium nav links stagger animation
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach((link, index) => {
        link.style.opacity = '0';
        link.style.transform = 'translateY(-20px)';

        setTimeout(() => {
            link.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            link.style.opacity = '1';
            link.style.transform = 'translateY(0)';
        }, 500 + (index * 100));
    });
}

// Premium effects initialization
function initPremiumEffects() {
    // Add premium orbs to hero background
    addPremiumOrbs();

    // Initialize scroll-triggered animations
    initScrollAnimations();

    // Add premium hover effects to cards
    initCardHoverEffects();
}

function addPremiumOrbs() {
    const heroBackground = document.querySelector('.hero-background');
    if (!heroBackground) return;

    // Add additional orbs for premium effect
    const orb4 = document.createElement('div');
    orb4.className = 'gradient-orb orb-4';
    heroBackground.appendChild(orb4);

    const orb5 = document.createElement('div');
    orb5.className = 'gradient-orb orb-5';
    heroBackground.appendChild(orb5);
}

function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for premium animations
    const animateElements = document.querySelectorAll('.service-card, .section-header, .contact-container, .feature-card');
    animateElements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = `all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) ${index * 0.1}s`;
        observer.observe(el);
    });
}

function initCardHoverEffects() {
    const cards = document.querySelectorAll('.glass-effect');

    cards.forEach(card => {
        card.addEventListener('mouseenter', (e) => {
            e.target.style.transform = 'translateY(-5px) scale(1.02)';
            e.target.style.boxShadow = '0 20px 60px rgba(102, 126, 234, 0.2)';
        });

        card.addEventListener('mouseleave', (e) => {
            e.target.style.transform = 'translateY(0) scale(1)';
            e.target.style.boxShadow = '';
        });
    });
}

// Premium Navbar functionality
function initNavbar() {
    const navbar = document.querySelector('.navbar');
    const navLinks = document.querySelectorAll('.nav-link');
    const navBrand = document.querySelector('.nav-brand');

    if (!navbar) {
        return;
    }

    // Premium navbar scroll effect with smooth transitions
    let lastScrollY = window.scrollY;
    let ticking = false;

    function updateNavbar() {
        const scrollY = window.scrollY;

        // Premium scroll effects - always visible navbar with dynamic styling
        if (scrollY > 50) {
            navbar.classList.add('scrolled');
            navbar.style.backdropFilter = 'blur(25px)';
            navbar.style.background = 'rgba(10, 10, 10, 0.95)';
            navbar.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)';
        } else {
            navbar.classList.remove('scrolled');
            navbar.style.backdropFilter = 'blur(20px)';
            navbar.style.background = 'rgba(10, 10, 10, 0.8)';
            navbar.style.boxShadow = 'none';
        }

        // Always keep navbar visible with premium floating effect
        navbar.style.transform = 'translateY(0)';

        // Add subtle scale effect on scroll
        const scaleValue = Math.min(1 + (scrollY * 0.0001), 1.02);
        navbar.style.transform = `translateY(0) scale(${scaleValue})`;

        lastScrollY = scrollY;
        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateNavbar);
            ticking = true;
        }
    }

    window.addEventListener('scroll', requestTick);

    // Premium active link highlighting with smooth transitions
    window.addEventListener('scroll', () => {
        let current = '';
        const sections = document.querySelectorAll('section[id]');
        const scrollY = window.scrollY;

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (scrollY >= (sectionTop - 200)) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    });

    // Premium logo hover effects with advanced animations
    if (navBrand) {
        navBrand.addEventListener('mouseenter', () => {
            navBrand.style.transform = 'scale(1.1) rotate(5deg)';
            navBrand.style.filter = 'drop-shadow(0 0 15px rgba(102, 126, 234, 0.6))';
            navBrand.style.transition = 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
        });

        navBrand.addEventListener('mouseleave', () => {
            navBrand.style.transform = 'scale(1) rotate(0deg)';
            navBrand.style.filter = 'none';
        });
    }

    // Premium nav link hover effects with ripple animation
    navLinks.forEach(link => {
        link.addEventListener('mouseenter', (e) => {
            e.target.style.transform = 'translateY(-3px) scale(1.05)';
            e.target.style.boxShadow = '0 8px 25px rgba(102, 126, 234, 0.3)';
            e.target.style.background = 'linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(245, 158, 11, 0.1))';
            e.target.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';

            // Create ripple effect
            createRippleEffect(e.target, e);
        });

        link.addEventListener('mouseleave', (e) => {
            if (!e.target.classList.contains('active')) {
                e.target.style.transform = 'translateY(0) scale(1)';
                e.target.style.boxShadow = 'none';
                e.target.style.background = 'transparent';
            }
        });
    });

    // Add premium navbar glow effect on mouse move
    navbar.addEventListener('mousemove', (e) => {
        const rect = navbar.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        navbar.style.background = `radial-gradient(circle at ${x}px ${y}px, rgba(102, 126, 234, 0.15) 0%, rgba(10, 10, 10, 0.95) 50%)`;
    });

    navbar.addEventListener('mouseleave', () => {
        navbar.style.background = 'rgba(10, 10, 10, 0.95)';
    });

    // Premium ripple effect function
    function createRippleEffect(element, event) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.6) 0%, transparent 70%);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
            z-index: 0;
        `;

        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);

        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
            }
        }, 600);
    }

    // Ensure navbar stays visible during scroll
    window.addEventListener('scroll', () => {
        // Force navbar to stay visible
        if (navbar.style.transform.includes('translateY(-100%)')) {
            navbar.style.transform = navbar.style.transform.replace('translateY(-100%)', 'translateY(0)');
        }
    });
}

// Animation on scroll
function initAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.service-card, .section-header, .contact-container');
    animateElements.forEach(el => observer.observe(el));
}

// Counter animation - synchronized timing for all counters
function initCounters() {
    const counters = document.querySelectorAll('.stat-number');
    const animationDuration = 2000; // 2 seconds for all counters
    const frameRate = 60; // 60 FPS
    const totalFrames = (animationDuration / 1000) * frameRate;

    const animateCounter = (counter) => {
        const target = parseInt(counter.getAttribute('data-target'));
        // Skip animation if no data-target or invalid target
        if (isNaN(target)) return;

        let currentFrame = 0;
        counter.innerText = '0';

        const timer = setInterval(() => {
            currentFrame++;
            const progress = currentFrame / totalFrames;

            // Use easing function for smooth animation
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.floor(easeOutQuart * target);

            counter.innerText = currentValue;

            if (currentFrame >= totalFrames) {
                counter.innerText = target;
                clearInterval(timer);
            }
        }, 1000 / frameRate);
    };

    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                // Only animate if has data-target attribute
                if (counter.hasAttribute('data-target')) {
                    animateCounter(counter);
                }
                counterObserver.unobserve(counter);
            }
        });
    });

    counters.forEach(counter => {
        // Only observe counters that have data-target attribute
        if (counter.hasAttribute('data-target')) {
            counterObserver.observe(counter);
        }
    });
}

// Skill bars animation
function initSkillBars() {
    const skillBars = document.querySelectorAll('.skill-fill');
    
    const skillObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const skillBar = entry.target;
                const skillLevel = skillBar.getAttribute('data-skill');
                skillBar.style.width = skillLevel + '%';
                skillObserver.unobserve(skillBar);
            }
        });
    });
    
    skillBars.forEach(bar => skillObserver.observe(bar));
}

// Smooth scrolling
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Premium Mobile menu
function initMobileMenu() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navItems = document.querySelectorAll('.nav-item');

    if (hamburger && navMenu) {
        hamburger.addEventListener('click', () => {
            const isActive = hamburger.classList.contains('active');

            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');

            // Animate nav items
            if (!isActive) {
                navItems.forEach((item, index) => {
                    setTimeout(() => {
                        item.style.animation = `slideInLeft 0.3s ease-out forwards`;
                    }, index * 100);
                });
            } else {
                navItems.forEach(item => {
                    item.style.animation = '';
                });
            }

            // Prevent body scroll when menu is open
            document.body.style.overflow = isActive ? 'auto' : 'hidden';
        });

        // Close menu when clicking on a link
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
                document.body.style.overflow = 'auto';

                navItems.forEach(item => {
                    item.style.animation = '';
                });
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!hamburger.contains(e.target) && !navMenu.contains(e.target)) {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
                document.body.style.overflow = 'auto';

                navItems.forEach(item => {
                    item.style.animation = '';
                });
            }
        });
    }
}

// Communication functions
function openCommunication() {
    window.location.href = 'communication/';
}

function openTelegram() {
    const message = encodeURIComponent("Cześć! Jestem zainteresowany usługami PixelGarage. Czy możemy porozmawiać?");
    window.open(`https://t.me/pixel_garage?text=${message}`, '_blank');
}

function openWhatsApp() {
    const message = encodeURIComponent("Cześć! Jestem zainteresowany usługami PixelGarage. Czy możemy omówić moje potrzeby?");
    window.open(`https://wa.me/34645577385?text=${message}`, '_blank');
}

// Floating Contact Widget
function initFloatingContact() {
    // Only create if not already exists
    if (document.getElementById('floatingContactWidget')) return;

    const widget = document.createElement('div');
    widget.id = 'floatingContactWidget';
    widget.className = 'floating-contact-widget';
    widget.innerHTML = `
        <div class="contact-toggle" onclick="toggleContactWidget()">
            <i class="fas fa-comments"></i>
            <span class="contact-badge">2</span>
        </div>
        <div class="contact-options">
            <button class="contact-option telegram" onclick="openTelegram()" title="Telegram">
                <i class="fab fa-telegram"></i>
                <span>Telegram</span>
            </button>
            <button class="contact-option whatsapp" onclick="openWhatsApp()" title="WhatsApp">
                <i class="fab fa-whatsapp"></i>
                <span>WhatsApp</span>
            </button>
        </div>
    `;

    document.body.appendChild(widget);
}

function toggleContactWidget() {
    const widget = document.getElementById('floatingContactWidget');
    if (widget) {
        widget.classList.toggle('expanded');
    }
}

// Scroll to services
function scrollToServices() {
    const servicesSection = document.querySelector('#services-preview');
    if (servicesSection) {
        servicesSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Premium Particle System
function createPremiumParticles() {
    const particlesContainer = document.querySelector('.floating-particles');
    if (!particlesContainer) return;

    // Clear existing particles
    particlesContainer.innerHTML = '';

    const particleCount = window.innerWidth > 768 ? 80 : 40;
    const colors = [
        'rgba(102, 126, 234, 0.4)',
        'rgba(245, 158, 11, 0.3)',
        'rgba(255, 255, 255, 0.2)',
        'rgba(79, 172, 254, 0.3)',
        'rgba(240, 147, 251, 0.3)'
    ];

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';

        const size = Math.random() * 4 + 1;
        const color = colors[Math.floor(Math.random() * colors.length)];
        const duration = Math.random() * 8 + 6;
        const delay = Math.random() * 5;

        particle.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            background: ${color};
            border-radius: 50%;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: particleFloat ${duration}s ease-in-out infinite;
            animation-delay: ${delay}s;
            box-shadow: 0 0 ${size * 2}px ${color};
        `;

        particlesContainer.appendChild(particle);
    }
}

// Enhanced particle creation with mouse interaction
function createInteractiveParticles() {
    const hero = document.querySelector('.hero');
    if (!hero) return;

    let mouseX = 0;
    let mouseY = 0;

    hero.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;

        // Create temporary particles on mouse move
        if (Math.random() > 0.95) {
            createTemporaryParticle(mouseX, mouseY);
        }
    });
}

function createTemporaryParticle(x, y) {
    const particle = document.createElement('div');
    particle.style.cssText = `
        position: fixed;
        width: 3px;
        height: 3px;
        background: rgba(102, 126, 234, 0.6);
        border-radius: 50%;
        left: ${x}px;
        top: ${y}px;
        pointer-events: none;
        z-index: 1000;
        animation: fadeOut 2s ease-out forwards;
        box-shadow: 0 0 10px rgba(102, 126, 234, 0.6);
    `;

    document.body.appendChild(particle);

    setTimeout(() => {
        if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
        }
    }, 2000);
}

// Add fadeOut animation
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeOut {
        0% {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
        100% {
            opacity: 0;
            transform: scale(0.5) translateY(-50px);
        }
    }
`;
document.head.appendChild(style);

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize premium particles system
setTimeout(() => {
    createPremiumParticles();
    createInteractiveParticles();
}, 1000);

// Reinitialize particles on window resize
window.addEventListener('resize', debounce(() => {
    createPremiumParticles();
}, 300));

// Loading animation
function showLoading() {
    const loader = document.createElement('div');
    loader.className = 'loader';
    loader.innerHTML = `
        <div class="loader-content">
            <div class="loader-spinner"></div>
            <p>Ładowanie...</p>
        </div>
    `;
    document.body.appendChild(loader);
}

function hideLoading() {
    const loader = document.querySelector('.loader');
    if (loader) {
        loader.remove();
    }
}

// Error handling
window.addEventListener('error', (e) => {
    console.error('JavaScript Error:', e.error);
});

// Performance monitoring
window.addEventListener('load', () => {
    const loadTime = performance.now();
    console.log(`Page loaded in ${loadTime.toFixed(2)}ms`);
});

// Service worker registration (for PWA capabilities)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}

// Theme switching (future feature)
function toggleTheme() {
    document.body.classList.toggle('light-theme');
    localStorage.setItem('theme', document.body.classList.contains('light-theme') ? 'light' : 'dark');
}

// Load saved theme
function loadTheme() {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'light') {
        document.body.classList.add('light-theme');
    }
}

// Initialize theme on load
loadTheme();

// VIP Academy progress animation
function animateVIPProgress() {
    const progressFill = document.querySelector('.progress-fill-mini');
    if (progressFill) {
        const targetWidth = 60; // 18/30 = 60%
        let currentWidth = 0;
        const increment = targetWidth / 50;

        const timer = setInterval(() => {
            currentWidth += increment;
            if (currentWidth >= targetWidth) {
                progressFill.style.width = targetWidth + '%';
                clearInterval(timer);
            } else {
                progressFill.style.width = currentWidth + '%';
            }
        }, 30);
    }
}

// Initialize VIP Academy animations
function initVIPAcademy() {
    const vipSection = document.querySelector('.vip-academy-section');
    if (!vipSection) return;

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateVIPProgress();
                observer.unobserve(entry.target);
            }
        });
    });

    observer.observe(vipSection);
}

// Initialize VIP Academy on load
setTimeout(initVIPAcademy, 1000);

// Export functions for global access
window.PixelGarage = {
    openCommunication,
    openTelegram,
    openWhatsApp,
    scrollToServices,
    toggleTheme,
    animateVIPProgress
};
