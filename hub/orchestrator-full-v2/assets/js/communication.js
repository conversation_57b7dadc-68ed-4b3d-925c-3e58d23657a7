// Communication Page JavaScript

// Quick message templates
const quickMessages = {
    quote: "<PERSON><PERSON><PERSON><PERSON>! Chciałbym otrzymać wycenę dla mojego projektu. <PERSON><PERSON> możemy omówić szczegóły?",
    consultation: "<PERSON><PERSON><PERSON><PERSON>! Jestem zainteresowany bezpłatną konsultacją projektową. Kiedy możemy się umówić?",
    support: "<PERSON><PERSON><PERSON>ć! Potrzebuję wsparcia technicznego z moim projektem. Czy możesz mi pomóc?",
    partnership: "<PERSON><PERSON><PERSON>ć! Jestem zainteresowany długoterminową współpracą. Omówmy możliwości!",
    custom: "C<PERSON>ść! Potrzebuję dedykowanego rozwiązania dla mojego biznesu. Czy możemy porozmawia<PERSON>?",
    academy: "Cześć! Jestem zainteresowany AI Academy. <PERSON>zy moż<PERSON><PERSON> opowiedzieć mi więcej o kursach?"
};



// Initialize communication page
document.addEventListener('DOMContentLoaded', function() {
    initializeCommunication();

    // Ensure stat numbers are displayed correctly
    setTimeout(() => {
        fixStatNumbers();

        // Keep checking for the first 5 seconds to ensure stats stay correct
        let checkCount = 0;
        const checkInterval = setInterval(() => {
            fixStatNumbers();
            checkCount++;
            if (checkCount >= 5) {
                clearInterval(checkInterval);
            }
        }, 1000);
    }, 100);
});
});

function initializeCommunication() {
    createChatBubbles();
}

// Create floating chat bubbles animation
function createChatBubbles() {
    const container = document.querySelector('.chat-bubbles');
    if (!container) return;

    for (let i = 0; i < 15; i++) {
        const bubble = document.createElement('div');
        bubble.className = 'chat-bubble';
        bubble.style.cssText = `
            position: absolute;
            width: ${Math.random() * 40 + 20}px;
            height: ${Math.random() * 40 + 20}px;
            background: rgba(102, 126, 234, ${Math.random() * 0.3 + 0.1});
            border-radius: 50% 50% 50% 0;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: float ${Math.random() * 8 + 4}s ease-in-out infinite;
            animation-delay: ${Math.random() * 4}s;
        `;
        container.appendChild(bubble);
    }
}

// Send quick message
function sendQuickMessage(type) {
    const message = quickMessages[type];
    if (message) {
        const encodedMessage = encodeURIComponent(message);
        window.open(`https://t.me/pixel_garage?text=${encodedMessage}`, '_blank');
    }
}

// Open communication channels
function openTelegram() {
    window.open('https://t.me/pixel_garage', '_blank');
}

function openWhatsApp() {
    const message = encodeURIComponent("Cześć! Jestem zainteresowany usługami PixelGarage.");
    window.open(`https://wa.me/34645577385?text=${message}`, '_blank');
}

function openCommunication() {
    scrollToChat();
}





// Scroll functions
function scrollToChat() {
    const optionsSection = document.querySelector('.communication-options');
    if (optionsSection) {
        optionsSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

function scrollToContact() {
    const contactSection = document.querySelector('.contact-info');
    if (contactSection) {
        contactSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Status simulation
function simulateOnlineStatus() {
    const statusDots = document.querySelectorAll('.status-dot.online');
    statusDots.forEach(dot => {
        setInterval(() => {
            dot.style.opacity = dot.style.opacity === '0.5' ? '1' : '0.5';
        }, 2000);
    });
}

// Initialize status simulation
setTimeout(simulateOnlineStatus, 1000);

// Create floating contact widget for all pages
function createFloatingContactWidget() {
    // Only create if not already exists
    if (document.getElementById('floatingContactWidget')) return;

    const widget = document.createElement('div');
    widget.id = 'floatingContactWidget';
    widget.className = 'floating-contact-widget';
    widget.innerHTML = `
        <div class="contact-toggle" onclick="toggleContactWidget()">
            <i class="fas fa-comments"></i>
            <span class="contact-badge">2</span>
        </div>
        <div class="contact-options">
            <button class="contact-option telegram" onclick="openTelegram()" title="Telegram">
                <i class="fab fa-telegram"></i>
                <span>Telegram</span>
            </button>
            <button class="contact-option whatsapp" onclick="openWhatsApp()" title="WhatsApp">
                <i class="fab fa-whatsapp"></i>
                <span>WhatsApp</span>
            </button>
        </div>
    `;

    document.body.appendChild(widget);
}

function toggleContactWidget() {
    const widget = document.getElementById('floatingContactWidget');
    widget.classList.toggle('expanded');
}

// Initialize floating contact widget on all pages
setTimeout(createFloatingContactWidget, 2000);

// Mobile menu functionality
function toggleMobileMenu() {
    const navContent = document.querySelector('.nav-content');
    const menuToggle = document.querySelector('.mobile-menu-toggle');

    navContent.classList.toggle('active');
    menuToggle.classList.toggle('active');
}

// Close mobile menu when clicking outside
document.addEventListener('click', function(event) {
    const navContent = document.querySelector('.nav-content');
    const menuToggle = document.querySelector('.mobile-menu-toggle');
    const navbar = document.querySelector('.navbar');

    if (navContent && navContent.classList.contains('active')) {
        if (!navbar.contains(event.target)) {
            navContent.classList.remove('active');
            menuToggle.classList.remove('active');
        }
    }
});

// Close mobile menu when clicking on nav links
document.addEventListener('DOMContentLoaded', function() {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            const navContent = document.querySelector('.nav-content');
            const menuToggle = document.querySelector('.mobile-menu-toggle');

            if (navContent && navContent.classList.contains('active')) {
                navContent.classList.remove('active');
                menuToggle.classList.remove('active');
            }
        });
    });
});

// Make toggleMobileMenu globally available
window.toggleMobileMenu = toggleMobileMenu;

// Fix stat numbers function
function fixStatNumbers() {
    const statNumbers = document.querySelectorAll('.stat-number-text');
    const correctValues = ['< 2h', '16h/dzień', '5+ lat'];

    statNumbers.forEach((stat, index) => {
        if (index < correctValues.length) {
            // Force the correct value
            stat.textContent = correctValues[index];
            stat.innerHTML = correctValues[index];

            // Prevent any counter animation on these elements
            stat.classList.add('no-animation');
            stat.setAttribute('data-original-text', correctValues[index]);
        }
    });

    // Also check for any .stat-number elements that might be interfering
    const oldStatNumbers = document.querySelectorAll('.stat-number');
    oldStatNumbers.forEach(stat => {
        // If it doesn't have data-target, it shouldn't be animated
        if (!stat.hasAttribute('data-target')) {
            stat.classList.add('no-animation');
        }
    });
}



// Analytics tracking
function trackCommunicationEvent(action, channel) {
    // Track user interactions for analytics
    console.log(`Communication Event: ${action} via ${channel}`);
    
    // Here you would typically send to analytics service
    // gtag('event', action, {
    //     event_category: 'Communication',
    //     event_label: channel
    // });
}

// Track communication channel usage
document.addEventListener('click', function(e) {
    if (e.target.closest('.telegram-card') || e.target.closest('[onclick*="openTelegram"]')) {
        trackCommunicationEvent('open_telegram', 'telegram');
    } else if (e.target.closest('.whatsapp-card') || e.target.closest('[onclick*="openWhatsApp"]')) {
        trackCommunicationEvent('open_whatsapp', 'whatsapp');
    }
});


