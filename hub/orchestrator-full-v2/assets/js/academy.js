// Tech Academy Page JavaScript

// Academy data
const academyData = {
    availableSpots: 25,
    weeksDuration: 12,
    practicalFocus: 100,
    whatsappNumber: "48123456789" // Replace with your actual WhatsApp number
};

// Initialize academy page
document.addEventListener('DOMContentLoaded', function() {
    initializeAcademy();
});

function initializeAcademy() {
    createFloatingIcons();
    setupFormValidation();
    initializeFAQ();
    animateCounters();
    initializePremiumAnimations();
}

// Update interest progress
function updateInterestProgress() {
    const progressFill = document.querySelector('.progress-fill');
    const currentInterestElement = document.getElementById('currentInterest');

    if (progressFill && currentInterestElement) {
        const progress = (academyData.currentInterest / academyData.targetInterest) * 100;
        progressFill.setAttribute('data-progress', progress);
        progressFill.style.width = progress + '%';
        currentInterestElement.textContent = academyData.currentInterest;
    }

    // Update status indicator
    updateStatusIndicator();
}

// Update status indicator
function updateStatusIndicator() {
    const statusDot = document.querySelector('.status-dot.pending');
    const statusText = statusDot?.parentElement.querySelector('span');

    if (statusDot && statusText) {
        const remaining = academyData.targetInterest - academyData.currentInterest;

        if (remaining <= 0) {
            statusDot.classList.remove('pending');
            statusDot.classList.add('online');
            statusText.textContent = 'Gotowi do uruchomienia! Skontaktujemy się wkrótce.';
        } else {
            statusText.textContent = `Uruchomienie po zebraniu ${remaining} wyrażeń zainteresowania`;
        }
    }
}

// Create floating icons animation
function createFloatingIcons() {
    const container = document.querySelector('.floating-icons');
    if (!container) return;
    
    const icons = ['💻', '🚀', '⚡', '🔧', '📱', '🌟', '🎯', '💡'];
    
    for (let i = 0; i < 12; i++) {
        const icon = document.createElement('div');
        icon.className = 'floating-icon';
        icon.textContent = icons[Math.floor(Math.random() * icons.length)];
        icon.style.cssText = `
            position: absolute;
            font-size: ${Math.random() * 20 + 15}px;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: float ${Math.random() * 8 + 6}s ease-in-out infinite;
            animation-delay: ${Math.random() * 4}s;
            opacity: ${Math.random() * 0.5 + 0.3};
            pointer-events: none;
        `;
        container.appendChild(icon);
    }
}

// Setup form validation
function setupFormValidation() {
    const form = document.getElementById('registrationForm');
    if (!form) return;
    
    form.addEventListener('submit', handleFormSubmit);
    
    // Real-time validation
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', validateField);
        input.addEventListener('input', clearFieldError);
    });
}

// Handle form submission
function handleFormSubmit(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // Validate form
    if (!validateForm(form)) {
        return;
    }
    
    // Show loading state
    const submitBtn = form.querySelector('.btn-submit');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Zapisywanie...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Success
        showRegistrationSuccess();

        // Send WhatsApp notification
        sendWhatsAppNotification(data);

        // Track registration
        trackRegistration(data);

        // Reset form
        form.reset();
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

    }, 2000);
}

// Validate form
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!validateField({ target: field })) {
            isValid = false;
        }
    });
    
    return isValid;
}

// Validate individual field
function validateField(e) {
    const field = e.target;
    const value = field.value.trim();
    const fieldName = field.name;
    
    // Remove existing error
    clearFieldError({ target: field });
    
    let isValid = true;
    let errorMessage = '';
    
    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'To pole jest wymagane';
    }
    
    // Email validation
    if (fieldName === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            errorMessage = 'Podaj prawidłowy adres email';
        }
    }
    
    // Phone validation
    if (fieldName === 'phone' && value) {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{9,}$/;
        if (!phoneRegex.test(value)) {
            isValid = false;
            errorMessage = 'Podaj prawidłowy numer telefonu';
        }
    }
    
    // Show error if invalid
    if (!isValid) {
        showFieldError(field, errorMessage);
    }
    
    return isValid;
}

// Show field error
function showFieldError(field, message) {
    const formGroup = field.closest('.form-group');
    if (!formGroup) return;
    
    // Remove existing error
    const existingError = formGroup.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    
    // Add error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.style.cssText = `
        color: #ef4444;
        font-size: 0.8rem;
        margin-top: 4px;
    `;
    errorDiv.textContent = message;
    
    formGroup.appendChild(errorDiv);
    
    // Add error styling to field
    field.style.borderColor = '#ef4444';
}

// Clear field error
function clearFieldError(e) {
    const field = e.target;
    const formGroup = field.closest('.form-group');
    if (!formGroup) return;
    
    const errorDiv = formGroup.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.remove();
    }
    
    // Reset field styling
    field.style.borderColor = '';
}

// Show registration success
function showRegistrationSuccess() {
    const form = document.getElementById('registrationForm');
    const success = document.getElementById('registrationSuccess');
    
    if (form && success) {
        form.style.display = 'none';
        success.style.display = 'block';
        
        // Scroll to success message
        success.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });
        
        // Show confetti effect
        showConfetti();
    }
}

// Show confetti effect
function showConfetti() {
    // Simple confetti effect
    for (let i = 0; i < 50; i++) {
        const confetti = document.createElement('div');
        confetti.style.cssText = `
            position: fixed;
            width: 10px;
            height: 10px;
            background: ${['#f59e0b', '#10b981', '#3b82f6', '#ef4444'][Math.floor(Math.random() * 4)]};
            left: ${Math.random() * 100}vw;
            top: -10px;
            z-index: 9999;
            animation: confetti-fall 3s linear forwards;
            pointer-events: none;
        `;
        
        document.body.appendChild(confetti);
        
        setTimeout(() => confetti.remove(), 3000);
    }
}

// Add confetti animation
const style = document.createElement('style');
style.textContent = `
    @keyframes confetti-fall {
        to {
            transform: translateY(100vh) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Initialize FAQ
function initializeFAQ() {
    console.log('Initializing FAQ...');
    const faqQuestions = document.querySelectorAll('.faq-question');
    console.log('Found FAQ questions:', faqQuestions.length);

    faqQuestions.forEach((question, index) => {
        // Remove onclick attribute to avoid conflicts
        question.removeAttribute('onclick');

        question.addEventListener('click', () => {
            console.log('FAQ question clicked:', index);
            const faqItem = question.closest('.faq-item');
            const isActive = faqItem.classList.contains('active');

            // Close all FAQ items
            document.querySelectorAll('.faq-item').forEach(item => {
                item.classList.remove('active');
            });

            // Open clicked item if it wasn't active
            if (!isActive) {
                faqItem.classList.add('active');
                console.log('FAQ item opened:', index);
            } else {
                console.log('FAQ item closed:', index);
            }
        });
    });
}

// Toggle FAQ (for onclick attribute - backup function)
function toggleFAQ(element) {
    const faqItem = element.closest('.faq-item');
    const isActive = faqItem.classList.contains('active');

    // Close all FAQ items
    document.querySelectorAll('.faq-item').forEach(item => {
        item.classList.remove('active');
    });

    // Open clicked item if it wasn't active
    if (!isActive) {
        faqItem.classList.add('active');
    }
}

// Animate counters
function animateCounters() {
    const counters = document.querySelectorAll('.stat-number[data-target], .instructor-stats .stat-number[data-target]');

    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseInt(counter.getAttribute('data-target'));
                animateCounter(counter, target);
                counterObserver.unobserve(counter);
            }
        });
    });

    counters.forEach(counter => counterObserver.observe(counter));
}

// Animate individual counter
function animateCounter(element, target) {
    let current = 0;
    const increment = target / 50;
    const isInstructorStat = element.closest('.instructor-stats');

    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            element.textContent = isInstructorStat ? target + '+' : target;
            clearInterval(timer);
        } else {
            element.textContent = Math.floor(current) + (isInstructorStat ? '+' : '');
        }
    }, 30);
}

// Initialize premium animations
function initializePremiumAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.feature-card, .timeline-item, .instructor-container');
    animateElements.forEach(el => observer.observe(el));

    // Add parallax effect to hero background
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.gradient-orb');

        parallaxElements.forEach((element, index) => {
            const speed = 0.5 + (index * 0.1);
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });
    });

    // Add mouse movement effect to cards
    const cards = document.querySelectorAll('.feature-card, .timeline-content');
    cards.forEach(card => {
        card.addEventListener('mousemove', (e) => {
            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const rotateX = (y - centerY) / 10;
            const rotateY = (centerX - x) / 10;

            card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)';
        });
    });
}

// Track interest
function trackRegistration(data) {
    console.log('Interest tracked:', data);

    // Here you would typically send to analytics service
    // gtag('event', 'tech_academy_interest', {
    //     event_category: 'Tech Academy',
    //     event_label: data.specialization,
    //     value: 1
    // });

    // Send to backend
    // fetch('/api/tech-academy/interest', {
    //     method: 'POST',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify(data)
    // });
}

// Scroll functions
function scrollToRegistration() {
    const registrationSection = document.getElementById('registration');
    if (registrationSection) {
        registrationSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

function scrollToCurriculum() {
    const curriculumSection = document.getElementById('curriculum');
    if (curriculumSection) {
        curriculumSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Communication functions
function openCommunication() {
    window.location.href = '../communication/';
}

function openTelegram() {
    const message = encodeURIComponent("Cześć! Jestem zainteresowany AI Academy. Czy możesz opowiedzieć mi więcej?");
    window.open(`https://t.me/pixel_garage?text=${message}`, '_blank');
}

function openWhatsApp() {
    const message = encodeURIComponent("Cześć! Jestem zainteresowany AI Academy. Czy możesz opowiedzieć mi więcej?");
    window.open(`https://wa.me/34645577385?text=${message}`, '_blank');
}

// Send WhatsApp notification for new interest
function sendWhatsAppNotification(formData) {
    const message = `🎯 NOWE ZAINTERESOWANIE - Tech Academy

👤 Imię: ${formData.fullName}
📧 Email: ${formData.email}
📱 Telefon: ${formData.phone || 'Nie podano'}
💼 Doświadczenie: ${formData.experience}
🔧 Specjalizacja: ${formData.specialization}
💭 Motywacja: ${formData.motivation || 'Nie podano'}

📊 Status: Nowe zgłoszenie zainteresowania

⏰ Data zgłoszenia: ${new Date().toLocaleString('pl-PL')}`;

    const encodedMessage = encodeURIComponent(message);

    // Open WhatsApp with the notification message
    window.open(`https://wa.me/${academyData.whatsappNumber}?text=${encodedMessage}`, '_blank');

    console.log('WhatsApp notification sent:', formData);
}

// Price calculator
function calculatePrice() {
    const basePrice = academyData.regularPrice;
    const discount = academyData.earlyBirdDiscount;
    const discountedPrice = basePrice * (1 - discount / 100);
    
    return {
        regular: basePrice,
        discounted: discountedPrice,
        savings: basePrice - discountedPrice
    };
}

// Show price modal
function showPriceModal() {
    const prices = calculatePrice();
    
    const modal = document.createElement('div');
    modal.className = 'price-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="closePriceModal()"></div>
        <div class="modal-content glass-effect">
            <div class="modal-header">
                <h2>Cennik AI Academy</h2>
                <button class="modal-close" onclick="closePriceModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="price-comparison">
                    <div class="price-option regular">
                        <h3>Regularna cena</h3>
                        <div class="price">${prices.regular.toLocaleString()} PLN</div>
                        <p>Standardowa cena po uruchomieniu</p>
                    </div>
                    <div class="price-option vip">
                        <div class="vip-badge">VIP Early Bird</div>
                        <h3>Pre-rejestracja</h3>
                        <div class="price">${prices.discounted.toLocaleString()} PLN</div>
                        <div class="savings">Oszczędzasz ${prices.savings.toLocaleString()} PLN!</div>
                        <p>Tylko dla pierwszych 30 uczestników</p>
                        <button class="btn-primary" onclick="scrollToRegistration(); closePriceModal();">
                            Zapisz się teraz
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
}

function closePriceModal() {
    const modal = document.querySelector('.price-modal');
    if (modal) {
        modal.remove();
        document.body.style.overflow = 'auto';
    }
}

// Auto-save form data
function setupAutoSave() {
    const form = document.getElementById('registrationForm');
    if (!form) return;
    
    const inputs = form.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        // Load saved data
        const savedValue = localStorage.getItem(`academy_form_${input.name}`);
        if (savedValue && input.type !== 'checkbox') {
            input.value = savedValue;
        } else if (savedValue && input.type === 'checkbox') {
            input.checked = savedValue === 'true';
        }
        
        // Save on change
        input.addEventListener('change', () => {
            if (input.type === 'checkbox') {
                localStorage.setItem(`academy_form_${input.name}`, input.checked);
            } else {
                localStorage.setItem(`academy_form_${input.name}`, input.value);
            }
        });
    });
}

// Clear saved form data
function clearSavedFormData() {
    const form = document.getElementById('registrationForm');
    if (!form) return;
    
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        localStorage.removeItem(`academy_form_${input.name}`);
    });
}

// Initialize auto-save
setTimeout(setupAutoSave, 1000);

// Test FAQ functionality (for debugging)
function testFAQ() {
    console.log('Testing FAQ...');
    const firstFaqItem = document.querySelector('.faq-item');
    if (firstFaqItem) {
        firstFaqItem.classList.add('active');
        console.log('First FAQ item should now be open');
    }
}

// Make testFAQ available globally for debugging
window.testFAQ = testFAQ;

// Progress indicator for form completion
function updateFormProgress() {
    const form = document.getElementById('registrationForm');
    if (!form) return;
    
    const requiredFields = form.querySelectorAll('[required]');
    const filledFields = Array.from(requiredFields).filter(field => {
        if (field.type === 'checkbox') {
            return field.checked;
        }
        return field.value.trim() !== '';
    });
    
    const progress = (filledFields.length / requiredFields.length) * 100;
    
    // Update progress indicator if exists
    const progressIndicator = document.querySelector('.form-progress');
    if (progressIndicator) {
        progressIndicator.style.width = progress + '%';
    }
    
    return progress;
}

// Add form progress tracking
document.addEventListener('input', updateFormProgress);
document.addEventListener('change', updateFormProgress);
