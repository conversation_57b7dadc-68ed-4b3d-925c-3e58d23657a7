// PixelGarage - Complete Translation System
// Comprehensive translations for Polish, English, and Spanish

const translations = {
    pl: {
        // Navigation
        nav: {
            home: "Strona Główna",
            services: "Katalog Usług",
            portfolio: "Portfolio", 
            academy: "Tech Academy",
            contact: "Kontakt Live",
            liveChat: "Live Chat",
            register: "Zapisz się"
        },

        // Common elements
        common: {
            readMore: "Czytaj więcej",
            learnMore: "<PERSON><PERSON><PERSON> się więcej",
            getStarted: "<PERSON><PERSON><PERSON>cznij",
            contact: "Kontakt",
            close: "Zamknij",
            loading: "Ładowanie...",
            error: "Błąd",
            success: "<PERSON>k<PERSON>",
            submit: "Wyślij",
            cancel: "Anuluj",
            save: "<PERSON>ap<PERSON><PERSON>",
            edit: "Edytuj",
            delete: "Usuń",
            confirm: "Potwierd<PERSON>",
            back: "Wstecz",
            next: "Dalej",
            previous: "Poprzedni",
            search: "Szukaj",
            filter: "Filtruj",
            sort: "Sortuj",
            all: "<PERSON><PERSON><PERSON>t<PERSON>",
            none: "<PERSON>rak",
            yes: "Tak",
            no: "Nie",
            telegram: "Telegram",
            whatsapp: "WhatsApp",
            users: "Użytkowników",
            cards: "Wizytówek",
            uptime: "Uptime",
            savings: "Oszczędności",
            lessTime: "Mniej czasu",
            satisfaction: "Zadowolenie",
            activeUsers: "Aktywnych użytkowników",
            success12weeks: "Sukces w 12 tyg.",
            aiWorkouts: "Treningów AI",
            fullPortfolio: "Pełne Portfolio (12 projektów)",
            startCollaboration: "Rozpocznij Współpracę",
            number: "Liczba",
            metric: "Metryka",
            rating: "Ocena",
            openApp: "Otwórz Aplikację",
            viewDemo: "Zobacz Demo",
            orderService: "Zamów Usługę",
            liveChat: "Live Chat",
            exploreServices: "Poznaj Nasze Usługi",
            viewPortfolio: "Zobacz Portfolio",
            digitalServices: "Usług Cyfrowych",
            categories: "Kategorii",
            projectsOnTime: "% Projektów na czas"
        },

        // Brand elements
        brand: {
            name: "PixelGarage",
            pixel: "Pixel",
            garage: "Garage"
        },

        // Meta elements
        meta: {
            title: "PixelGarage - Tworzymy Przyszłość Cyfrową z AI | Portfolio & Usługi"
        },

        // Landing Page
        landing: {
            // Hero Section
            hero: {
                badge: "Nowa Firma • Świeże Podejście • Innowacyjne Rozwiązania",
                title: "Rozpoczynamy przygodę z",
                titleHighlight: "technologią AI",
                subtitle: "Twoja cyfrowa prawa ręka",
                description: "Absolwent Polsko-Japońskiej Akademii Technik Komputerowych z certyfikowanym 5-letnim doświadczeniem w enterprise software development i quality assurance. Jako uznany ambasador technologii Java, prowadziłem szkolenia dla korporacji i instytucji edukacyjnych. PixelGarage to moja autorska firma consultingowa, specjalizująca się w dostarczaniu zaawansowanych rozwiązań AI dla wszystkich dążących do cyfrowej transformacji.",
                cta1: "Poznaj Nasze Usługi",
                cta2: "Zobacz Portfolio",
                credentials: "Moje kwalifikacje:",
                badges: {
                    graduate: "🎓 PJAIT Graduate",
                    java: "☕ Java Expert",
                    typescript: "🔧 TypeScript Pro",
                    qa: "🎯 QA Specialist",
                    trainer: "👨‍🏫 Programming Trainer",
                    debugger: "😎 Debugger Whisperer"
                },
                profile: {
                    name: "Robert - PixelGarage",
                    role: "Founder & Lead Developer",
                    skills: {
                        ai: "AI Development",
                        qa: "Quality Assurance",
                        web: "Web Development",
                        pm: "Project Management"
                    }
                },
                profile: {
                    name: "Robert - PixelGarage",
                    role: "Founder & Lead Developer",
                    skills: {
                        ai: "AI Development",
                        qa: "Quality Assurance",
                        web: "Web Development",
                        pm: "Project Management"
                    }
                }
            },

            // Stats Section
            stats: {
                projects: "Projektów Portfolio",
                technologies: "Technologii",
                experience: "Lat Doświadczenia",
                clients: "Zadowolonych Klientów"
            },

            // Services Overview
            services: {
                badge: "Flagowe Projekty",
                title: "3 Flagowe Aplikacje AI",
                titleHighlight: "Aplikacje",
                subtitle: "Najlepsze realizacje z portfolio 12 projektów - każda gotowa do komercjalizacji z unikalną wartością biznesową",
                viewAll: "Zobacz wszystkie usługi",
                sectionTitle: "Nasze Usługi",
                sectionSubtitle: "Przyszłościowe rozwiązania cyfrowe z wykorzystaniem najnowszych technologii",
                catalog: {
                    title: "Katalog Rozwiązań",
                    description: "163+ innowacyjnych produktów cyfrowych wykorzystujących najnowsze technologie",
                    cta: "Odkryj rozwiązania"
                },
                portfolio: {
                    title: "Portfolio Realizacji",
                    description: "12 projektów demonstracyjnych pokazujących możliwości nowoczesnych technologii",
                    cta: "Eksploruj portfolio"
                },
                consultation: {
                    title: "Darmowa Konsultacja",
                    description: "Bezpłatna analiza Twojego projektu z ekspertem - poznaj możliwości i otrzymaj profesjonalne wskazówki",
                    cta: "Umów konsultację"
                },
                triangle: {
                    title: "Trójkąt Przełamany",
                    description: "U nas masz wszystko: szybko, tanio i profesjonalnie - przełamujemy klasyczny trójkąt zależności",
                    cta: "Zobacz jak to robimy"
                },
                academyCard: {
                    title: "Tech Academy Exclusive",
                    description: "Ekskluzywne szkolenia technologiczne na każdym poziomie zaawansowania - od podstaw po ekspertów",
                    cta: "Dołącz do Academy"
                }
            },

            // Projects
            projects: {
                smartcard: {
                    name: "SmartCard.pl - AI Business Cards",
                    description: "AI-powered generator wizytówek cyfrowych z avatarami AI, kodami QR, NFC i zaawansowaną analityką. Kompletny system networking dla profesjonalistów.",
                    badge: "💰 Revenue Ready",
                    status: "Live & Monetizing"
                },
                eventai: {
                    name: "EventAI.pl - Smart Event Planning",
                    description: "Inteligentny planer wydarzeń z Llama 3.1 8B AI, optymalizacją budżetu, marketplace dostawców i zaawansowanymi analytics dla organizatorów.",
                    badge: "🚀 Innovative",
                    status: "In Development"
                },
                fitgenius: {
                    name: "FitGenius.pl - AI Fitness Coach",
                    description: "Enterprise-grade AI fitness coaching platform z personalizowanymi treningami, polską bazą żywności, form checkerem i zaawansowaną gamifikacją.",
                    badge: "🏆 Perfection Mode",
                    status: "Completed"
                },
                portfolioCta: "Chcesz zobaczyć pozostałe 9 projektów?"
            },

            // Tech Academy Section
            academy: {
                badge: "VIP • EKSKLUZYWNE • LIMITOWANE MIEJSCA",
                title: "Tech Academy",
                subtitle: "Praktyczne szkolenia z ekspertem",
                description: "Najbardziej zaawansowana akademia technologiczna w Polsce. Ekskluzywne szkolenia weekendowe, mentoring 1-on-1 i praktyczne umiejętności na rynek 2025.",
                cta1: "Zapisz się na VIP listę",
                cta2: "Zobacz Program",
                features: {
                    live: {
                        title: "Live Sessions",
                        description: "Interaktywne sesje prowadzone przez eksperta w czasie rzeczywistym, zapewniające bezpośredni kontakt i możliwość zadawania pytań"
                    },
                    group: {
                        title: "Grupa Dostosowana",
                        description: "Curriculum dostosowany do poziomu zaawansowania uczestników, zapewniający optymalne tempo nauki i maksymalizację efektów"
                    },
                    schedule: {
                        title: "Weekendy/Wieczory",
                        description: "Profesjonalny harmonogram dostosowany do potrzeb pracujących specjalistów, umożliwiający rozwój bez konfliktów z obowiązkami zawodowymi"
                    },
                    practice: {
                        title: "Praktyka 2025",
                        description: "Kompetencje technologiczne zgodne z aktualnymi trendami rynkowymi i wymaganiami pracodawców w sektorze IT na rok 2025"
                    }
                },
                badges: {
                    exclusive: "VIP EXCLUSIVE",
                    limited: "LIMITOWANE MIEJSCA"
                },
                status: "Limitowana akademia - zapisz się jako jeden z pierwszych",
                benefits: {
                    access: "Ekskluzywny dostęp",
                    early: "Early Access",
                    priority: "Priorytetowy dostęp do sesji",
                    contact: "Bezpośredni kontakt z ekspertem",
                    materials: "Materiały premium"
                },
                cta: {
                    register: "Zapisz się na limitowaną akademię",
                    details: "Zapytaj o szczegóły"
                },
                stats: {
                    spots: "Miejsc dostępnych",
                    weeks: "Tygodni intensywnych",
                    live: "% Live Sessions"
                }
            },

            // Contact Section
            contact: {
                title: "Gotowy na rozpoczęcie projektu?",
                subtitle: "Skontaktuj się ze mną już teraz i omówmy Twoje potrzeby"
            },

            // Footer
            footer: {
                description: "Tworzymy przyszłość cyfrową z najnowszymi technologiami",
                copyright: "© 2025 PixelGarage. Wszystkie prawa zastrzeżone.",
                services: "Usługi",
                contact: "Kontakt"
            }
        },

        // Brand elements
        brand: {
            name: "PixelGarage",
            pixel: "Pixel",
            garage: "Garage"
        },

        // Meta elements
        meta: {
            title: "PixelGarage - Tworzymy Przyszłość Cyfrową z AI | Portfolio & Usługi"
        }
        },

        // Services Page
        services: {
            // Hero Section
            hero: {
                badge: "Profesjonalne Rozwiązania Cyfrowe • Gotowe Produkty • Szybkie Wdrożenie",
                title: "Katalog Usług",
                titleHighlight: "PixelGarage",
                description: "Odkryj naszą kompleksową ofertę usług cyfrowych. Od prostych narzędzi po zaawansowane systemy biznesowe. Każde rozwiązanie zaprojektowane z myślą o maksymalnej efektywności i ROI.",
                stats: {
                    services: "Usług Cyfrowych",
                    categories: "Kategorii",
                    collaboration: "Współpracy i Zaufania"
                }
            },

            // Categories
            categories: {
                title: "Kategorie Usług",
                subtitle: "Wybierz obszar, który Cię interesuje",
                web: "Aplikacje Web",
                mobile: "Mobile & PWA", 
                desktop: "Aplikacje Desktop",
                ai: "Sztuczna Inteligencja",
                automation: "Automatyzacja Biznesowa",
                content: "Tworzenie Treści"
            },

            // Express Solutions
            express: {
                badge: "Szybkie Rozwiązania • 1-2 Tygodnie • Gotowe Szablony",
                title: "Express Solutions",
                subtitle: "Gotowe rozwiązania dla natychmiastowego wdrożenia",
                loadMore: "Załaduj więcej usług"
            },

            // Wishlist Section
            wishlist: {
                badge: "Premium Solutions • Enterprise Grade • Custom Development",
                title: "Wishlist - Kompleksowe Rozwiązania", 
                subtitle: "Zaawansowane projekty dla przedsiębiorstw dążących do cyfrowej transformacji"
            },

            // Service Actions
            actions: {
                order: "Zamów",
                details: "Szczegóły",
                demo: "Demo",
                quote: "Wycena"
            }
        },

        // Portfolio/Gallery Page
        portfolio: {
            // Hero Section
            hero: {
                badge: "Innowacyjne Projekty • Zaawansowane Technologie • Pełna Funkcjonalność",
                title: "Portfolio",
                titleHighlight: "Projektów",
                description: "Odkryj nasze zrealizowane projekty AI i aplikacje webowe. Każdy projekt to unikalne rozwiązanie biznesowe z pełną funkcjonalnością i nowoczesnym designem.",
                cta1: "Przejrzyj Realizacje",
                cta2: "Rozpocznij Współpracę",
                stats: {
                    projects: "Projektów",
                    technologies: "Technologii",
                    experience: "Lat Doświadczenia"
                }
            },

            // Filters
            filters: {
                all: "Wszystkie",
                featured: "Wyróżnione",
                web: "Web Apps",
                mobile: "Mobile",
                ai: "AI Tools",
                business: "Business"
            },

            // Project Actions
            actions: {
                viewLive: "Zobacz Live",
                viewDemo: "Demo",
                orderSimilar: "Zamów podobny",
                contactTelegram: "Telegram",
                contactWhatsApp: "WhatsApp"
            }
        },

        // Tech Academy Page
        academy: {
            // Hero Section
            hero: {
                badge: "VIP • EKSKLUZYWNE • LIMITOWANE MIEJSCA",
                title: "Tech Academy",
                subtitle: "dla Profesjonalistów",
                description: "Najbardziej zaawansowana akademia technologiczna w Polsce. Ekskluzywne szkolenia weekendowe, mentoring 1-on-1 i praktyczne umiejętności na rynek 2025. Dołącz do elitarnej grupy ekspertów tech.",
                cta1: "Zapisz się na VIP listę",
                cta2: "Zobacz Program",
                stats: {
                    spots: "Miejsc dostępnych",
                    weeks: "Tygodni intensywnych",
                    live: "% Live Sessions"
                }
            },

            // Features
            features: {
                title: "Dlaczego Tech Academy?",
                subtitle: "Najbardziej zaawansowany program szkoleniowy technologiczny w Polsce",
                mentoring: {
                    title: "Mentoring 1-on-1",
                    description: "Indywidualne sesje z doświadczonym mentorem. Personalizowane podejście do rozwoju każdego uczestnika."
                },
                projects: {
                    title: "Praktyczne Projekty",
                    description: "Tworzenie rzeczywistych aplikacji i rozwiązań. Portfolio gotowe do prezentacji pracodawcom."
                },
                sessions: {
                    title: "Live Weekend Sessions",
                    description: "Ekskluzywne sesje weekendowe i wieczorne. Nauka bez zakłócania pracy zawodowej."
                },
                community: {
                    title: "Społeczność Profesjonalistów",
                    description: "Dołącz do społeczności tech professionals. Networking i wymiana doświadczeń."
                },
                career: {
                    title: "Rozwój Kariery",
                    description: "Wsparcie w rozwoju zawodowym. Praktyczne umiejętności cenione na rynku 2025."
                },
                professional: {
                    title: "Profesjonalne Podejście",
                    description: "Szacunek, zaufanie i profesjonalizm w każdym aspekcie współpracy i nauki."
                }
            },

            // Registration Form
            form: {
                title: "Dołącz do Tech Academy",
                subtitle: "Pierwsza grupa startuje już wkrótce - zabezpiecz swoje miejsce w historii",
                fields: {
                    fullName: "Imię i nazwisko",
                    email: "Email",
                    phone: "Telefon",
                    experience: "Doświadczenie",
                    specialization: "Specjalizacja",
                    motivation: "Dlaczego chcesz dołączyć do Tech Academy?"
                },
                placeholders: {
                    fullName: "Jan Kowalski",
                    email: "<EMAIL>",
                    phone: "+48 123 456 789",
                    motivation: "Opowiedz nam o swoich celach zawodowych, motywacji i tym, co chcesz osiągnąć dzięki Tech Academy..."
                },
                submit: "Zabezpiecz miejsce w pierwszej grupie",
                success: {
                    title: "Dziękujemy!",
                    message: "Zostałeś dodany do listy zainteresowanych Tech Academy. Skontaktujemy się z Tobą, gdy zbierzemy wystarczającą liczbę uczestników.",
                    cta: "Dołącz do grupy Telegram"
                }
            }
        },

        // Communication Page
        communication: {
            // Hero Section
            hero: {
                badge: "Dostępny 16h/dzień • Natychmiastowe Odpowiedzi • Profesjonalne Wsparcie",
                title: "Komunikacja",
                titleHighlight: "Live",
                description: "Skontaktuj się ze mną natychmiast przez Telegram lub WhatsApp. Jestem dostępny 24/7 dla konsultacji, wycen projektów, wsparcia technicznego i odpowiedzi na wszystkie pytania.",
                cta1: "Telegram",
                cta2: "WhatsApp",
                stats: {
                    response: "< 2h",
                    responseLabel: "Czas odpowiedzi",
                    availability: "16h/dzień",
                    availabilityLabel: "Dostępność",
                    experience: "5+ lat",
                    experienceLabel: "Doświadczenie"
                },
                status: "Online teraz • Gotowy do rozmowy!"
            },

            // Communication Options
            options: {
                title: "Wybierz sposób komunikacji",
                subtitle: "Każdy kanał oferuje natychmiastowy kontakt i profesjonalne wsparcie",
                telegram: {
                    title: "Telegram",
                    description: "Szybka komunikacja z możliwością wysyłania plików, zdjęć i dokumentów",
                    features: [
                        "Natychmiastowe wiadomości",
                        "Wysyłanie plików do 2GB",
                        "Bezpieczne szyfrowanie",
                        "Historia rozmów"
                    ]
                },
                whatsapp: {
                    title: "WhatsApp",
                    description: "Popularna platforma z możliwością rozmów głosowych i wideo",
                    features: [
                        "Wiadomości tekstowe",
                        "Rozmowy głosowe",
                        "Połączenia wideo",
                        "Udostępnianie lokalizacji"
                    ]
                },
                cta: "Rozpocznij chat"
            },

            // Quick Actions
            quickActions: {
                title: "Szybkie akcje",
                subtitle: "Wybierz gotowy szablon wiadomości dla szybszego kontaktu",
                quote: {
                    title: "Zapytaj o wycenę",
                    description: "Otrzymaj szczegółową wycenę dla swojego projektu"
                },
                consultation: {
                    title: "Umów konsultację",
                    description: "Bezpłatna konsultacja projektowa i techniczna"
                },
                support: {
                    title: "Wsparcie techniczne",
                    description: "Pomoc z istniejącymi projektami i rozwiązaniami"
                },
                partnership: {
                    title: "Współpraca",
                    description: "Omów możliwości długoterminowej współpracy"
                },
                custom: {
                    title: "Projekt na zamówienie",
                    description: "Dedykowane rozwiązanie dla Twojego biznesu"
                },
                academy: {
                    title: "Tech Academy",
                    description: "Zapytaj o kursy i szkolenia technologiczne"
                }
            }
        },

        // Brand elements
        brand: {
            name: "PixelGarage",
            pixel: "Pixel",
            garage: "Garage"
        },

        // Meta elements
        meta: {
            title: "PixelGarage - Creating Digital Future with AI | Portfolio & Services"
        }
    },

    en: {
        // Navigation
        nav: {
            home: "Home",
            services: "Services Catalog",
            portfolio: "Portfolio",
            academy: "Tech Academy",
            contact: "Live Contact",
            liveChat: "Live Chat",
            register: "Register"
        },

        // Common elements
        common: {
            readMore: "Read more",
            learnMore: "Learn more",
            getStarted: "Get started",
            contact: "Contact",
            close: "Close",
            loading: "Loading...",
            error: "Error",
            success: "Success",
            submit: "Submit",
            cancel: "Cancel",
            save: "Save",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            back: "Back",
            next: "Next",
            previous: "Previous",
            search: "Search",
            filter: "Filter",
            sort: "Sort",
            all: "All",
            none: "None",
            yes: "Yes",
            no: "No",
            telegram: "Telegram",
            whatsapp: "WhatsApp",
            users: "Users",
            cards: "Cards",
            uptime: "Uptime",
            savings: "Savings",
            lessTime: "Less time",
            satisfaction: "Satisfaction",
            activeUsers: "Active users",
            success12weeks: "Success in 12 weeks",
            aiWorkouts: "AI Workouts",
            fullPortfolio: "Full Portfolio (12 projects)",
            startCollaboration: "Start Collaboration",
            number: "Number",
            metric: "Metric",
            rating: "Rating"
        },

        // Brand elements
        brand: {
            name: "PixelGarage",
            pixel: "Pixel",
            garage: "Garage"
        },

        // Meta elements
        meta: {
            title: "PixelGarage - Creating Digital Future with AI | Portfolio & Services"
        },

        // Landing Page
        landing: {
            // Hero Section
            hero: {
                badge: "New Company • Fresh Approach • Innovative Solutions",
                title: "Starting our journey with",
                titleHighlight: "AI technology",
                subtitle: "Your digital right hand",
                description: "Graduate of Polish-Japanese Academy of Information Technology with certified 5-year experience in enterprise software development and quality assurance. As a recognized Java technology ambassador, I conducted training for corporations and educational institutions. PixelGarage is my consulting company, specializing in delivering advanced AI solutions for all pursuing digital transformation.",
                cta1: "Explore Our Services",
                cta2: "View Portfolio",
                credentials: "My qualifications:",
                badges: {
                    graduate: "🎓 PJAIT Graduate",
                    java: "☕ Java Expert",
                    typescript: "🔧 TypeScript Pro",
                    qa: "🎯 QA Specialist",
                    trainer: "👨‍🏫 Programming Trainer",
                    debugger: "😎 Debugger Whisperer"
                },
                profile: {
                    name: "Robert - PixelGarage",
                    role: "Founder & Lead Developer",
                    skills: {
                        ai: "AI Development",
                        qa: "Quality Assurance",
                        web: "Web Development",
                        pm: "Project Management"
                    }
                }
            },

            // Stats Section
            stats: {
                projects: "Portfolio Projects",
                technologies: "Technologies",
                experience: "Years Experience",
                clients: "Satisfied Clients"
            },

            // Services Overview
            services: {
                badge: "Flagship Projects",
                title: "3 Flagship AI Applications",
                titleHighlight: "Applications",
                subtitle: "Best implementations from our 12-project portfolio - each ready for commercialization with unique business value",
                viewAll: "View all services",
                sectionTitle: "Our Services",
                sectionSubtitle: "Future-ready digital solutions using cutting-edge technologies",
                catalog: {
                    title: "Solutions Catalog",
                    description: "163+ innovative digital products utilizing the latest technologies",
                    cta: "Discover solutions"
                },
                portfolio: {
                    title: "Portfolio Showcase",
                    description: "12 demonstration projects showcasing modern technology capabilities",
                    cta: "Explore portfolio"
                },
                consultation: {
                    title: "Free Consultation",
                    description: "Free project analysis with an expert - discover possibilities and receive professional guidance",
                    cta: "Schedule consultation"
                },
                triangle: {
                    title: "Triangle Broken",
                    description: "With us you get everything: fast, cheap and professional - we break the classic dependency triangle",
                    cta: "See how we do it"
                },
                academyCard: {
                    title: "Tech Academy Exclusive",
                    description: "Exclusive technology training at every skill level - from basics to experts",
                    cta: "Join Academy"
                }
            },

            // Projects
            projects: {
                smartcard: {
                    name: "SmartCard.pl",
                    description: "AI-powered digital business card generator with AI avatars, QR codes, NFC and advanced analytics. Complete networking system for professionals.",
                    badge: "💰 Revenue Ready",
                    status: "Live & Monetizing"
                },
                eventai: {
                    name: "EventAI.pl",
                    description: "Intelligent event planner with Llama 3.1 8B AI, budget optimization, vendor marketplace and advanced analytics for organizers.",
                    badge: "🚀 Innovative",
                    status: "In Development"
                },
                fitgenius: {
                    name: "FitGenius.pl",
                    description: "Enterprise-grade AI fitness coaching platform with personalized workouts, Polish food database, form checker and advanced gamification.",
                    badge: "🏆 Perfection Mode",
                    status: "Completed"
                },
                portfolioCta: "Want to see the remaining 9 projects?"
            },

            // Tech Academy Section
            academy: {
                badge: "VIP • EXCLUSIVE • LIMITED SPOTS",
                title: "Tech Academy",
                subtitle: "Practical training with expert",
                description: "The most advanced technology academy in Poland. Exclusive weekend training, 1-on-1 mentoring and practical skills for the 2025 market.",
                cta1: "Join VIP List",
                cta2: "View Program",
                features: {
                    live: {
                        title: "Live Sessions",
                        description: "Interactive sessions led by expert in real-time, ensuring direct contact and ability to ask questions"
                    },
                    group: {
                        title: "Tailored Group",
                        description: "Curriculum adapted to participants' skill level, ensuring optimal learning pace and maximum results"
                    },
                    schedule: {
                        title: "Weekends/Evenings",
                        description: "Professional schedule adapted to working professionals' needs, enabling development without conflicts with work duties"
                    },
                    practice: {
                        title: "2025 Practice",
                        description: "Technology competencies aligned with current market trends and employer requirements in IT sector for 2025"
                    }
                },
                badges: {
                    exclusive: "VIP EXCLUSIVE",
                    limited: "LIMITED SPOTS"
                },
                status: "Limited academy - register as one of the first",
                benefits: {
                    access: "Exclusive access",
                    early: "Early Access",
                    priority: "Priority access to sessions",
                    contact: "Direct contact with expert",
                    materials: "Premium materials"
                },
                cta: {
                    register: "Register for limited academy",
                    details: "Ask for details"
                },
                stats: {
                    spots: "Available Spots",
                    weeks: "Intensive Weeks",
                    live: "% Live Sessions"
                }
            },

            // Contact Section
            contact: {
                title: "Ready to start your project?",
                subtitle: "Contact me now and let's discuss your needs"
            },

            // Footer
            footer: {
                description: "Creating digital future with cutting-edge technologies",
                copyright: "© 2025 PixelGarage. All rights reserved.",
                services: "Services",
                contact: "Contact"
            }
        },

        // Services Page
        services: {
            // Hero Section
            hero: {
                badge: "Professional Digital Solutions • Ready Products • Fast Implementation",
                title: "Services Catalog",
                titleHighlight: "PixelGarage",
                description: "Discover our comprehensive digital services offering. From simple tools to advanced business systems. Every solution designed for maximum efficiency and ROI.",
                stats: {
                    services: "Digital Services",
                    categories: "Categories",
                    collaboration: "Collaboration & Trust"
                }
            },

            // Categories
            categories: {
                title: "Service Categories",
                subtitle: "Choose the area that interests you",
                web: "Web Applications",
                mobile: "Mobile & PWA",
                desktop: "Desktop Applications",
                ai: "Artificial Intelligence",
                automation: "Business Automation",
                content: "Content Creation"
            },

            // Express Solutions
            express: {
                badge: "Quick Solutions • 1-2 Weeks • Ready Templates",
                title: "Express Solutions",
                subtitle: "Ready solutions for immediate implementation",
                loadMore: "Load more services"
            },

            // Wishlist Section
            wishlist: {
                badge: "Premium Solutions • Enterprise Grade • Custom Development",
                title: "Wishlist - Comprehensive Solutions",
                subtitle: "Advanced projects for enterprises pursuing digital transformation"
            },

            // Service Actions
            actions: {
                order: "Order",
                details: "Details",
                demo: "Demo",
                quote: "Quote"
            }
        },

        // Portfolio/Gallery Page
        portfolio: {
            // Hero Section
            hero: {
                badge: "Innovative Projects • Advanced Technologies • Full Functionality",
                title: "Project",
                titleHighlight: "Portfolio",
                description: "Discover our completed AI projects and web applications. Each project is a unique business solution with full functionality and modern design.",
                cta1: "Browse Projects",
                cta2: "Start Collaboration",
                stats: {
                    projects: "Projects",
                    technologies: "Technologies",
                    experience: "Years Experience"
                }
            },

            // Filters
            filters: {
                all: "All",
                featured: "Featured",
                web: "Web Apps",
                mobile: "Mobile",
                ai: "AI Tools",
                business: "Business"
            },

            // Project Actions
            actions: {
                viewLive: "View Live",
                viewDemo: "Demo",
                orderSimilar: "Order Similar",
                contactTelegram: "Telegram",
                contactWhatsApp: "WhatsApp"
            }
        },

        // Tech Academy Page
        academy: {
            // Hero Section
            hero: {
                badge: "VIP • EXCLUSIVE • LIMITED SPOTS",
                title: "Tech Academy",
                subtitle: "for Professionals",
                description: "The most advanced technology academy in Poland. Exclusive weekend training, 1-on-1 mentoring and practical skills for the 2025 market. Join the elite group of tech experts.",
                cta1: "Join VIP List",
                cta2: "View Program",
                stats: {
                    spots: "Available Spots",
                    weeks: "Intensive Weeks",
                    live: "% Live Sessions"
                }
            },

            // Features
            features: {
                title: "Why Tech Academy?",
                subtitle: "The most advanced technology training program in Poland",
                mentoring: {
                    title: "1-on-1 Mentoring",
                    description: "Individual sessions with experienced mentor. Personalized approach to each participant's development."
                },
                projects: {
                    title: "Practical Projects",
                    description: "Creating real applications and solutions. Portfolio ready for presentation to employers."
                },
                sessions: {
                    title: "Live Weekend Sessions",
                    description: "Exclusive weekend and evening sessions. Learning without disrupting professional work."
                },
                community: {
                    title: "Professional Community",
                    description: "Join the tech professionals community. Networking and experience exchange."
                },
                career: {
                    title: "Career Development",
                    description: "Support in professional development. Practical skills valued in the 2025 market."
                },
                professional: {
                    title: "Professional Approach",
                    description: "Respect, trust and professionalism in every aspect of cooperation and learning."
                }
            },

            // Registration Form
            form: {
                title: "Join Tech Academy",
                subtitle: "First group starting soon - secure your place in history",
                fields: {
                    fullName: "Full Name",
                    email: "Email",
                    phone: "Phone",
                    experience: "Experience",
                    specialization: "Specialization",
                    motivation: "Why do you want to join Tech Academy?"
                },
                placeholders: {
                    fullName: "John Smith",
                    email: "<EMAIL>",
                    phone: "****** 456 789",
                    motivation: "Tell us about your career goals, motivation and what you want to achieve through Tech Academy..."
                },
                submit: "Secure place in first group",
                success: {
                    title: "Thank you!",
                    message: "You have been added to the Tech Academy interest list. We will contact you when we gather enough participants.",
                    cta: "Join Telegram group"
                }
            }
        },

        // Communication Page
        communication: {
            // Hero Section
            hero: {
                badge: "Available 16h/day • Instant Responses • Professional Support",
                title: "Live",
                titleHighlight: "Communication",
                description: "Contact me instantly via Telegram or WhatsApp. I'm available 24/7 for consultations, project quotes, technical support and answers to all questions.",
                cta1: "Telegram",
                cta2: "WhatsApp",
                stats: {
                    response: "< 2h",
                    responseLabel: "Response Time",
                    availability: "16h/day",
                    availabilityLabel: "Availability",
                    experience: "5+ years",
                    experienceLabel: "Experience"
                },
                status: "Online now • Ready to chat!"
            },

            // Communication Options
            options: {
                title: "Choose communication method",
                subtitle: "Each channel offers instant contact and professional support",
                telegram: {
                    title: "Telegram",
                    description: "Fast communication with ability to send files, photos and documents",
                    features: [
                        "Instant messages",
                        "File sharing up to 2GB",
                        "Secure encryption",
                        "Chat history"
                    ]
                },
                whatsapp: {
                    title: "WhatsApp",
                    description: "Popular platform with voice and video call capabilities",
                    features: [
                        "Text messages",
                        "Voice calls",
                        "Video calls",
                        "Location sharing"
                    ]
                },
                cta: "Start chat"
            },

            // Quick Actions
            quickActions: {
                title: "Quick actions",
                subtitle: "Choose ready message template for faster contact",
                quote: {
                    title: "Request quote",
                    description: "Get detailed quote for your project"
                },
                consultation: {
                    title: "Schedule consultation",
                    description: "Free project and technical consultation"
                },
                support: {
                    title: "Technical support",
                    description: "Help with existing projects and solutions"
                },
                partnership: {
                    title: "Partnership",
                    description: "Discuss long-term cooperation opportunities"
                },
                custom: {
                    title: "Custom project",
                    description: "Dedicated solution for your business"
                },
                academy: {
                    title: "Tech Academy",
                    description: "Ask about courses and technology training"
                }
            }
        }
    },

    es: {
        // Navigation
        nav: {
            home: "Inicio",
            services: "Catálogo de Servicios",
            portfolio: "Portafolio",
            academy: "Tech Academy",
            contact: "Contacto en Vivo",
            liveChat: "Chat en Vivo",
            register: "Registrarse"
        },

        // Common elements
        common: {
            readMore: "Leer más",
            learnMore: "Saber más",
            getStarted: "Comenzar",
            contact: "Contacto",
            close: "Cerrar",
            loading: "Cargando...",
            error: "Error",
            success: "Éxito",
            submit: "Enviar",
            cancel: "Cancelar",
            save: "Guardar",
            edit: "Editar",
            delete: "Eliminar",
            confirm: "Confirmar",
            back: "Atrás",
            next: "Siguiente",
            previous: "Anterior",
            search: "Buscar",
            filter: "Filtrar",
            sort: "Ordenar",
            all: "Todos",
            none: "Ninguno",
            yes: "Sí",
            no: "No",
            telegram: "Telegram",
            whatsapp: "WhatsApp",
            users: "Usuarios",
            cards: "Tarjetas",
            uptime: "Tiempo activo",
            savings: "Ahorros",
            lessTime: "Menos tiempo",
            satisfaction: "Satisfacción",
            activeUsers: "Usuarios activos",
            success12weeks: "Éxito en 12 semanas",
            aiWorkouts: "Entrenamientos IA",
            fullPortfolio: "Portafolio Completo (12 proyectos)",
            startCollaboration: "Iniciar Colaboración",
            number: "Número",
            metric: "Métrica",
            rating: "Calificación",
            openApp: "Abrir App",
            viewDemo: "Ver Demo",
            orderService: "Ordenar Servicio",
            liveChat: "Chat en Vivo",
            exploreServices: "Explorar Nuestros Servicios",
            viewPortfolio: "Ver Portafolio",
            digitalServices: "Servicios Digitales",
            categories: "Categorías",
            projectsOnTime: "% Proyectos a tiempo"
        },

        // Landing Page
        landing: {
            // Hero Section
            hero: {
                badge: "Nueva Empresa • Enfoque Fresco • Soluciones Innovadoras",
                title: "Comenzando nuestro viaje con",
                titleHighlight: "tecnología IA",
                subtitle: "Tu mano derecha digital",
                description: "Bienvenido a PixelGarage - donde la tecnología se encuentra con la creatividad. Me especializo en crear soluciones innovadoras de IA que transforman ideas en realidad digital.",
                cta1: "Explorar Nuestros Servicios",
                cta2: "Ver Portafolio",
                credentials: "Mis calificaciones:",
                badges: {
                    graduate: "Graduado PJAIT",
                    java: "Experto Java",
                    typescript: "Pro TypeScript",
                    qa: "Especialista QA",
                    trainer: "Entrenador de Programación",
                    debugger: "Susurrador de Debugger"
                },
                profile: {
                    name: "Robert - PixelGarage Founder",
                    role: "Founder & Lead Developer",
                    skills: {
                        ai: "Desarrollo IA",
                        qa: "Aseguramiento de Calidad",
                        web: "Desarrollo Web",
                        pm: "Gestión de Proyectos"
                    }
                }
            },

            // Stats Section
            stats: {
                projects: "Proyectos",
                technologies: "Tecnologías",
                experience: "Años de Experiencia",
                clients: "Clientes Satisfechos"
            },

            // Services Overview
            services: {
                badge: "Servicios Integrales • Soluciones Profesionales • Entrega Rápida",
                title: "Nuestras",
                titleHighlight: "Aplicaciones Insignia",
                subtitle: "Descubre nuestras mejores soluciones tecnológicas",
                viewAll: "Ver todos los servicios",
                sectionTitle: "Nuestros Servicios",
                sectionSubtitle: "Soluciones digitales futuras utilizando las últimas tecnologías",
                catalog: {
                    title: "Catálogo de Soluciones",
                    description: "163+ productos digitales innovadores utilizando las últimas tecnologías",
                    cta: "Descubrir soluciones"
                },
                portfolio: {
                    title: "Implementaciones de Portafolio",
                    description: "12 proyectos de demostración mostrando las posibilidades de tecnologías modernas",
                    cta: "Explorar portafolio"
                },
                consultation: {
                    title: "Consulta Gratuita",
                    description: "Análisis gratuito de tu proyecto con un experto - conoce las posibilidades y obtén orientación profesional",
                    cta: "Programar consulta"
                },
                triangle: {
                    title: "Triángulo Roto",
                    description: "Con nosotros tienes todo: rápido, barato y profesional - rompemos el triángulo clásico de dependencias",
                    cta: "Ver cómo lo hacemos"
                },
                academyCard: {
                    title: "Tech Academy Exclusivo",
                    description: "Entrenamiento tecnológico exclusivo en todos los niveles de avance - desde básicos hasta expertos",
                    cta: "Unirse a Academy"
                }
            },

            // Projects
            projects: {
                smartcard: {
                    name: "SmartCard.pl - AI Business Cards",
                    description: "Generador de tarjetas de presentación digitales con IA, avatares IA, códigos QR, NFC y análisis avanzado. Sistema completo de networking para profesionales.",
                    badge: "💰 Revenue Ready",
                    status: "Live & Monetizing"
                },
                eventai: {
                    name: "EventAI.pl - Smart Event Planning",
                    description: "Planificador inteligente de eventos con Llama 3.1 8B IA, optimización de presupuesto, marketplace de proveedores y análisis avanzado para organizadores.",
                    badge: "🚀 Innovative",
                    status: "In Development"
                },
                fitgenius: {
                    name: "FitGenius.pl - AI Fitness Coach",
                    description: "Plataforma de coaching fitness IA de grado empresarial con entrenamientos personalizados, base de datos de alimentos española, verificador de forma y gamificación avanzada.",
                    badge: "🏆 Perfection Mode",
                    status: "Completed"
                },
                portfolioCta: "¿Quieres ver los 9 proyectos restantes?"
            },

            // Tech Academy Section
            academy: {
                badge: "VIP • EXCLUSIVO • PLAZAS LIMITADAS",
                title: "Tech Academy",
                subtitle: "para Profesionales",
                description: "La academia de tecnología más avanzada de Polonia. Entrenamiento exclusivo de fin de semana, mentoría 1-a-1 y habilidades prácticas para el mercado 2025.",
                cta1: "Unirse a Lista VIP",
                cta2: "Ver Programa",
                stats: {
                    spots: "Plazas Disponibles",
                    weeks: "Semanas Intensivas",
                    live: "% Sesiones en Vivo"
                }
            }
        },

        // Services Page
        services: {
            // Hero Section
            hero: {
                badge: "Soluciones Digitales Profesionales • Productos Listos • Implementación Rápida",
                title: "Catálogo de Servicios",
                titleHighlight: "PixelGarage",
                description: "Descubre nuestra oferta integral de servicios digitales. Desde herramientas simples hasta sistemas empresariales avanzados. Cada solución diseñada para máxima eficiencia y ROI.",
                stats: {
                    services: "Servicios Digitales",
                    categories: "Categorías",
                    collaboration: "Colaboración y Confianza"
                }
            },

            // Categories
            categories: {
                title: "Categorías de Servicios",
                subtitle: "Elige el área que te interesa",
                web: "Aplicaciones Web",
                mobile: "Móvil y PWA",
                desktop: "Aplicaciones de Escritorio",
                ai: "Inteligencia Artificial",
                automation: "Automatización Empresarial",
                content: "Creación de Contenido"
            },

            // Express Solutions
            express: {
                badge: "Soluciones Rápidas • 1-2 Semanas • Plantillas Listas",
                title: "Soluciones Express",
                subtitle: "Soluciones listas para implementación inmediata",
                loadMore: "Cargar más servicios"
            },

            // Wishlist Section
            wishlist: {
                badge: "Soluciones Premium • Grado Empresarial • Desarrollo Personalizado",
                title: "Lista de Deseos - Soluciones Integrales",
                subtitle: "Proyectos avanzados para empresas que buscan transformación digital"
            },

            // Service Actions
            actions: {
                order: "Ordenar",
                details: "Detalles",
                demo: "Demo",
                quote: "Cotización"
            }
        },

        // Portfolio/Gallery Page
        portfolio: {
            // Hero Section
            hero: {
                badge: "Proyectos Innovadores • Tecnologías Avanzadas • Funcionalidad Completa",
                title: "Portafolio de",
                titleHighlight: "Proyectos",
                description: "Descubre nuestros proyectos de IA completados y aplicaciones web. Cada proyecto es una solución empresarial única con funcionalidad completa y diseño moderno.",
                cta1: "Explorar Proyectos",
                cta2: "Iniciar Colaboración",
                stats: {
                    projects: "Proyectos",
                    technologies: "Tecnologías",
                    experience: "Años de Experiencia"
                }
            },

            // Filters
            filters: {
                all: "Todos",
                featured: "Destacados",
                web: "Apps Web",
                mobile: "Móvil",
                ai: "Herramientas IA",
                business: "Negocios"
            },

            // Project Actions
            actions: {
                viewLive: "Ver en Vivo",
                viewDemo: "Demo",
                orderSimilar: "Ordenar Similar",
                contactTelegram: "Telegram",
                contactWhatsApp: "WhatsApp"
            }
        },

        // Tech Academy Page
        academy: {
            // Hero Section
            hero: {
                badge: "VIP • EXCLUSIVO • PLAZAS LIMITADAS",
                title: "Tech Academy",
                subtitle: "para Profesionales",
                description: "La academia de tecnología más avanzada de Polonia. Entrenamiento exclusivo de fin de semana, mentoría 1-a-1 y habilidades prácticas para el mercado 2025. Únete al grupo élite de expertos tech.",
                cta1: "Unirse a Lista VIP",
                cta2: "Ver Programa",
                stats: {
                    spots: "Plazas Disponibles",
                    weeks: "Semanas Intensivas",
                    live: "% Sesiones en Vivo"
                }
            },

            // Features
            features: {
                title: "¿Por qué Tech Academy?",
                subtitle: "El programa de entrenamiento tecnológico más avanzado de Polonia",
                mentoring: {
                    title: "Mentoría 1-a-1",
                    description: "Sesiones individuales con mentor experimentado. Enfoque personalizado para el desarrollo de cada participante."
                },
                projects: {
                    title: "Proyectos Prácticos",
                    description: "Creación de aplicaciones y soluciones reales. Portafolio listo para presentar a empleadores."
                },
                sessions: {
                    title: "Sesiones en Vivo de Fin de Semana",
                    description: "Sesiones exclusivas de fin de semana y noche. Aprendizaje sin interrumpir el trabajo profesional."
                },
                community: {
                    title: "Comunidad de Profesionales",
                    description: "Únete a la comunidad de profesionales tech. Networking e intercambio de experiencias."
                },
                career: {
                    title: "Desarrollo de Carrera",
                    description: "Apoyo en desarrollo profesional. Habilidades prácticas valoradas en el mercado 2025."
                },
                professional: {
                    title: "Enfoque Profesional",
                    description: "Respeto, confianza y profesionalismo en cada aspecto de cooperación y aprendizaje."
                }
            },

            // Registration Form
            form: {
                title: "Únete a Tech Academy",
                subtitle: "Primer grupo comenzando pronto - asegura tu lugar en la historia",
                fields: {
                    fullName: "Nombre Completo",
                    email: "Email",
                    phone: "Teléfono",
                    experience: "Experiencia",
                    specialization: "Especialización",
                    motivation: "¿Por qué quieres unirte a Tech Academy?"
                },
                placeholders: {
                    fullName: "Juan Pérez",
                    email: "<EMAIL>",
                    phone: "+34 123 456 789",
                    motivation: "Cuéntanos sobre tus objetivos profesionales, motivación y lo que quieres lograr a través de Tech Academy..."
                },
                submit: "Asegurar lugar en primer grupo",
                success: {
                    title: "¡Gracias!",
                    message: "Has sido agregado a la lista de interesados de Tech Academy. Te contactaremos cuando reunamos suficientes participantes.",
                    cta: "Unirse al grupo de Telegram"
                }
            }
        },

        // Communication Page
        communication: {
            // Hero Section
            hero: {
                badge: "Disponible 16h/día • Respuestas Instantáneas • Soporte Profesional",
                title: "Comunicación",
                titleHighlight: "en Vivo",
                description: "Contáctame instantáneamente por Telegram o WhatsApp. Estoy disponible 24/7 para consultas, cotizaciones de proyectos, soporte técnico y respuestas a todas las preguntas.",
                cta1: "Telegram",
                cta2: "WhatsApp",
                stats: {
                    response: "< 2h",
                    responseLabel: "Tiempo de Respuesta",
                    availability: "16h/día",
                    availabilityLabel: "Disponibilidad",
                    experience: "5+ años",
                    experienceLabel: "Experiencia"
                },
                status: "En línea ahora • ¡Listo para chatear!"
            },

            // Communication Options
            options: {
                title: "Elige método de comunicación",
                subtitle: "Cada canal ofrece contacto instantáneo y soporte profesional",
                telegram: {
                    title: "Telegram",
                    description: "Comunicación rápida con capacidad de enviar archivos, fotos y documentos",
                    features: [
                        "Mensajes instantáneos",
                        "Compartir archivos hasta 2GB",
                        "Cifrado seguro",
                        "Historial de chat"
                    ]
                },
                whatsapp: {
                    title: "WhatsApp",
                    description: "Plataforma popular con capacidades de llamadas de voz y video",
                    features: [
                        "Mensajes de texto",
                        "Llamadas de voz",
                        "Videollamadas",
                        "Compartir ubicación"
                    ]
                },
                cta: "Iniciar chat"
            },

            // Quick Actions
            quickActions: {
                title: "Acciones rápidas",
                subtitle: "Elige plantilla de mensaje lista para contacto más rápido",
                quote: {
                    title: "Solicitar cotización",
                    description: "Obtén cotización detallada para tu proyecto"
                },
                consultation: {
                    title: "Programar consulta",
                    description: "Consulta gratuita de proyecto y técnica"
                },
                support: {
                    title: "Soporte técnico",
                    description: "Ayuda con proyectos y soluciones existentes"
                },
                partnership: {
                    title: "Asociación",
                    description: "Discutir oportunidades de cooperación a largo plazo"
                },
                custom: {
                    title: "Proyecto personalizado",
                    description: "Solución dedicada para tu negocio"
                },
                academy: {
                    title: "Tech Academy",
                    description: "Preguntar sobre cursos y entrenamiento tecnológico"
                }
            }
        },

        // Brand elements
        brand: {
            name: "PixelGarage",
            pixel: "Pixel",
            garage: "Garage"
        },

        // Meta elements
        meta: {
            title: "PixelGarage - Creando Futuro Digital con IA | Portafolio y Servicios"
        },

        // Footer
        footer: {
            brand: "Creando futuro digital con tecnología IA",
            services: "Usługi",
            contact: "Kontakt",
            portfolio: "Portfolio",
            webApps: "Web Applications",
            mobileApps: "Mobile Apps",
            aiSolutions: "AI Solutions",
            liveChat: "Live Chat",
            telegram: "Telegram",
            whatsapp: "WhatsApp",
            copyright: "Wszystkie prawa zastrzeżone."
        },

        // Service Categories
        serviceCategories: {
            web: "Aplikacje Web",
            mobile: "Mobile & PWA",
            desktop: "Aplikacje Desktop",
            ai: "Sztuczna Inteligencja",
            automation: "Automatyzacja Biznesowa",
            content: "Tworzenie Treści",
            audio: "Audio & Voice",
            photo: "Foto & Video",
            documents: "Przetwarzanie Dokumentów",
            chatbots: "Chatboty",
            quickDeploy: "Szybkie Wdrożenia"
        },

        // Experience Levels
        experienceLevels: {
            junior: "Junior (0-2 lata)",
            mid: "Mid (2-5 lat)",
            senior: "Senior (5+ lat)"
        },

        // Specializations
        specializations: {
            frontend: "Frontend Development",
            backend: "Backend Development",
            fullstack: "Full Stack Development",
            qaManual: "QA Manual",
            qaAutomation: "QA Automation",
            devops: "DevOps"
        },

        // Meta tags (titles and descriptions)
        meta: {
            landing: {
                title: "PixelGarage - Tworzymy Przyszłość Cyfrową z AI | Portfolio & Usługi",
                description: "PixelGarage - Ekspert AI Development & QA. 5 lat doświadczenia, absolwent PJAIT. Kompleksowe usługi AI, portfolio projektów i akademia rozwoju."
            },
            services: {
                title: "Katalog Usług AI - PixelGarage | 163+ Rozwiązań Cyfrowych",
                description: "Kompleksowy katalog usług AI PixelGarage. Web aplikacje, mobile apps, chatboty, automatyzacja biznesowa i więcej. Profesjonalne rozwiązania dla Twojej firmy."
            },
            portfolio: {
                title: "Portfolio Projektów - PixelGarage | 12 Zrealizowanych Projektów AI",
                description: "Odkryj portfolio PixelGarage - 12 innowacyjnych projektów AI. SmartCard, BrandMe, FitGenius i więcej. Animowana galeria z pełną prezentacją."
            },
            academy: {
                title: "Tech Academy - PixelGarage | Ekskluzywne Szkolenia Technologiczne 2025",
                description: "Dołącz do ekskluzywnej Tech Academy PixelGarage. Kompleksowe szkolenia technologiczne dla profesjonalistów. Praktyczne umiejętności na rynek 2025!"
            },
            communication: {
                title: "Komunikacja Live - PixelGarage | Telegram & WhatsApp",
                description: "Skontaktuj się z PixelGarage natychmiast przez Telegram lub WhatsApp. Dostępny 24/7 dla konsultacji, wycen i wsparcia technicznego."
            }
        }
    },

    en: {
        // Navigation
        nav: {
            home: "Home",
            services: "Services Catalog",
            portfolio: "Portfolio",
            academy: "Tech Academy",
            contact: "Live Contact",
            liveChat: "Live Chat",
            register: "Register"
        },

        // Common elements
        common: {
            readMore: "Read more",
            learnMore: "Learn more",
            getStarted: "Get started",
            contact: "Contact",
            close: "Close",
            loading: "Loading...",
            error: "Error",
            success: "Success",
            submit: "Submit",
            cancel: "Cancel",
            save: "Save",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            back: "Back",
            next: "Next",
            previous: "Previous",
            search: "Search",
            filter: "Filter",
            sort: "Sort",
            all: "All",
            none: "None",
            yes: "Yes",
            no: "No",
            telegram: "Telegram",
            whatsapp: "WhatsApp",
            users: "Users",
            cards: "Cards",
            uptime: "Uptime",
            savings: "Savings",
            lessTime: "Less time",
            satisfaction: "Satisfaction",
            activeUsers: "Active users",
            success12weeks: "Success in 12 weeks",
            aiWorkouts: "AI Workouts",
            fullPortfolio: "Full Portfolio (12 projects)",
            startCollaboration: "Start Collaboration",
            number: "Number",
            metric: "Metric",
            rating: "Rating",
            openApp: "Open App",
            viewDemo: "View Demo",
            orderService: "Order Service",
            liveChat: "Live Chat",
            exploreServices: "Explore Our Services",
            viewPortfolio: "View Portfolio",
            digitalServices: "Digital Services",
            categories: "Categories",
            projectsOnTime: "% Projects on time"
        },

        // Landing Page
        landing: {
            // Hero Section
            hero: {
                badge: "New Company • Fresh Approach • Innovative Solutions",
                title: "Starting our journey with",
                titleHighlight: "AI technology",
                subtitle: "Your digital right hand",
                description: "Welcome to PixelGarage - where technology meets creativity. I specialize in creating innovative AI solutions that transform ideas into digital reality.",
                cta1: "Explore Our Services",
                cta2: "View Portfolio",
                credentials: "My qualifications:",
                badges: {
                    graduate: "PJAIT Graduate",
                    java: "Java Expert",
                    typescript: "TypeScript Pro",
                    qa: "QA Specialist",
                    trainer: "Programming Trainer",
                    debugger: "Debugger Whisperer"
                },
                profile: {
                    name: "Robert - PixelGarage Founder",
                    role: "Founder & Lead Developer",
                    skills: {
                        ai: "AI Development",
                        qa: "Quality Assurance",
                        web: "Web Development",
                        pm: "Project Management"
                    }
                }
            },

            // Stats Section
            stats: {
                projects: "Projects",
                technologies: "Technologies",
                experience: "Years Experience",
                clients: "Satisfied Clients"
            },

            // Services Overview
            services: {
                badge: "Comprehensive Services • Professional Solutions • Fast Delivery",
                title: "Our Flagship",
                titleHighlight: "Applications",
                subtitle: "Discover our best technological solutions",
                viewAll: "View all services",
                sectionTitle: "Our Services",
                sectionSubtitle: "Future digital solutions using the latest technologies",
                catalog: {
                    title: "Solutions Catalog",
                    description: "163+ innovative digital products using the latest technologies",
                    cta: "Discover solutions"
                },
                portfolio: {
                    title: "Portfolio Implementations",
                    description: "12 demonstration projects showing the possibilities of modern technologies",
                    cta: "Explore portfolio"
                },
                consultation: {
                    title: "Free Consultation",
                    description: "Free analysis of your project with an expert - learn the possibilities and get professional guidance",
                    cta: "Schedule consultation"
                },
                triangle: {
                    title: "Triangle Broken",
                    description: "With us you have everything: fast, cheap and professional - we break the classic dependency triangle",
                    cta: "See how we do it"
                },
                academyCard: {
                    title: "Tech Academy Exclusive",
                    description: "Exclusive technology training at every level of advancement - from basics to experts",
                    cta: "Join Academy"
                }
            },

            // Projects
            projects: {
                smartcard: {
                    name: "SmartCard.pl - AI Business Cards",
                    description: "AI-powered digital business card generator with AI avatars, QR codes, NFC and advanced analytics. Complete networking system for professionals.",
                    badge: "💰 Revenue Ready",
                    status: "Live & Monetizing"
                },
                eventai: {
                    name: "EventAI.pl - Smart Event Planning",
                    description: "Intelligent event planner with Llama 3.1 8B AI, budget optimization, vendor marketplace and advanced analytics for organizers.",
                    badge: "🚀 Innovative",
                    status: "In Development"
                },
                fitgenius: {
                    name: "FitGenius.pl - AI Fitness Coach",
                    description: "Enterprise-grade AI fitness coaching platform with personalized workouts, Polish food database, form checker and advanced gamification.",
                    badge: "🏆 Perfection Mode",
                    status: "Completed"
                },
                portfolioCta: "Want to see the remaining 9 projects?"
            },

            // Tech Academy Section
            academy: {
                badge: "VIP • EXCLUSIVE • LIMITED SPOTS",
                title: "Tech Academy",
                subtitle: "for Professionals",
                description: "The most advanced technology academy in Poland. Exclusive weekend training, 1-on-1 mentoring and practical skills for the 2025 market.",
                cta1: "Join VIP List",
                cta2: "View Program",
                stats: {
                    spots: "Available Spots",
                    weeks: "Intensive Weeks",
                    live: "% Live Sessions"
                }
            }
        },

        // Services Page
        services: {
            // Hero Section
            hero: {
                badge: "Professional Digital Solutions • Ready Products • Fast Implementation",
                title: "Services Catalog",
                titleHighlight: "PixelGarage",
                description: "Discover our comprehensive digital services offering. From simple tools to advanced business systems. Every solution designed for maximum efficiency and ROI.",
                stats: {
                    services: "Digital Services",
                    categories: "Categories",
                    collaboration: "Collaboration & Trust"
                }
            },

            // Categories
            categories: {
                title: "Service Categories",
                subtitle: "Choose the area that interests you",
                web: "Web Applications",
                mobile: "Mobile & PWA",
                desktop: "Desktop Applications",
                ai: "Artificial Intelligence",
                automation: "Business Automation",
                content: "Content Creation"
            },

            // Express Solutions
            express: {
                badge: "Quick Solutions • 1-2 Weeks • Ready Templates",
                title: "Express Solutions",
                subtitle: "Ready solutions for immediate implementation",
                loadMore: "Load more services"
            },

            // Wishlist Section
            wishlist: {
                badge: "Premium Solutions • Enterprise Grade • Custom Development",
                title: "Wishlist - Comprehensive Solutions",
                subtitle: "Advanced projects for enterprises pursuing digital transformation"
            },

            // Service Actions
            actions: {
                order: "Order",
                details: "Details",
                demo: "Demo",
                quote: "Quote"
            }
        },

        // Portfolio/Gallery Page
        portfolio: {
            // Hero Section
            hero: {
                badge: "Innovative Projects • Advanced Technologies • Full Functionality",
                title: "Project",
                titleHighlight: "Portfolio",
                description: "Discover our completed AI projects and web applications. Each project is a unique business solution with full functionality and modern design.",
                cta1: "Browse Projects",
                cta2: "Start Collaboration",
                stats: {
                    projects: "Projects",
                    technologies: "Technologies",
                    experience: "Years Experience"
                }
            },

            // Filters
            filters: {
                all: "All",
                featured: "Featured",
                web: "Web Apps",
                mobile: "Mobile",
                ai: "AI Tools",
                business: "Business"
            },

            // Project Actions
            actions: {
                viewLive: "View Live",
                viewDemo: "Demo",
                orderSimilar: "Order Similar",
                contactTelegram: "Telegram",
                contactWhatsApp: "WhatsApp"
            }
        },

        // Tech Academy Page
        academy: {
            // Hero Section
            hero: {
                badge: "VIP • EXCLUSIVE • LIMITED SPOTS",
                title: "Tech Academy",
                subtitle: "for Professionals",
                description: "The most advanced technology academy in Poland. Exclusive weekend training, 1-on-1 mentoring and practical skills for the 2025 market. Join the elite group of tech experts.",
                cta1: "Join VIP List",
                cta2: "View Program",
                stats: {
                    spots: "Available Spots",
                    weeks: "Intensive Weeks",
                    live: "% Live Sessions"
                }
            },

            // Features
            features: {
                title: "Why Tech Academy?",
                subtitle: "The most advanced technology training program in Poland",
                mentoring: {
                    title: "1-on-1 Mentoring",
                    description: "Individual sessions with experienced mentor. Personalized approach to each participant's development."
                },
                projects: {
                    title: "Practical Projects",
                    description: "Creating real applications and solutions. Portfolio ready for presentation to employers."
                },
                sessions: {
                    title: "Live Weekend Sessions",
                    description: "Exclusive weekend and evening sessions. Learning without disrupting professional work."
                },
                community: {
                    title: "Professional Community",
                    description: "Join the tech professionals community. Networking and experience exchange."
                },
                career: {
                    title: "Career Development",
                    description: "Support in professional development. Practical skills valued in the 2025 market."
                },
                professional: {
                    title: "Professional Approach",
                    description: "Respect, trust and professionalism in every aspect of cooperation and learning."
                }
            },

            // Registration Form
            form: {
                title: "Join Tech Academy",
                subtitle: "First group starting soon - secure your place in history",
                fields: {
                    fullName: "Full Name",
                    email: "Email",
                    phone: "Phone",
                    experience: "Experience",
                    specialization: "Specialization",
                    motivation: "Why do you want to join Tech Academy?"
                },
                placeholders: {
                    fullName: "John Smith",
                    email: "<EMAIL>",
                    phone: "****** 456 789",
                    motivation: "Tell us about your career goals, motivation and what you want to achieve through Tech Academy..."
                },
                submit: "Secure place in first group",
                success: {
                    title: "Thank you!",
                    message: "You have been added to the Tech Academy interest list. We will contact you when we gather enough participants.",
                    cta: "Join Telegram group"
                }
            }
        },

        // Communication Page
        communication: {
            // Hero Section
            hero: {
                badge: "Available 16h/day • Instant Responses • Professional Support",
                title: "Live",
                titleHighlight: "Communication",
                description: "Contact me instantly via Telegram or WhatsApp. I'm available 24/7 for consultations, project quotes, technical support and answers to all questions.",
                cta1: "Telegram",
                cta2: "WhatsApp",
                stats: {
                    response: "< 2h",
                    responseLabel: "Response Time",
                    availability: "16h/day",
                    availabilityLabel: "Availability",
                    experience: "5+ years",
                    experienceLabel: "Experience"
                },
                status: "Online now • Ready to chat!"
            },

            // Communication Options
            options: {
                title: "Choose communication method",
                subtitle: "Each channel offers instant contact and professional support",
                telegram: {
                    title: "Telegram",
                    description: "Fast communication with ability to send files, photos and documents",
                    features: [
                        "Instant messages",
                        "File sharing up to 2GB",
                        "Secure encryption",
                        "Chat history"
                    ]
                },
                whatsapp: {
                    title: "WhatsApp",
                    description: "Popular platform with voice and video call capabilities",
                    features: [
                        "Text messages",
                        "Voice calls",
                        "Video calls",
                        "Location sharing"
                    ]
                },
                cta: "Start chat"
            },

            // Quick Actions
            quickActions: {
                title: "Quick actions",
                subtitle: "Choose ready message template for faster contact",
                quote: {
                    title: "Request quote",
                    description: "Get detailed quote for your project"
                },
                consultation: {
                    title: "Schedule consultation",
                    description: "Free project and technical consultation"
                },
                support: {
                    title: "Technical support",
                    description: "Help with existing projects and solutions"
                },
                partnership: {
                    title: "Partnership",
                    description: "Discuss long-term cooperation opportunities"
                },
                custom: {
                    title: "Custom project",
                    description: "Dedicated solution for your business"
                },
                academy: {
                    title: "Tech Academy",
                    description: "Ask about courses and technology training"
                }
            }
        },

        // Brand elements
        brand: {
            name: "PixelGarage",
            pixel: "Pixel",
            garage: "Garage"
        },

        // Meta elements
        meta: {
            title: "PixelGarage - Creating Digital Future with AI | Portfolio & Services"
        },

        // Footer
        footer: {
            brand: "Creating digital future with AI technology",
            services: "Services",
            contact: "Contact",
            portfolio: "Portfolio",
            webApps: "Web Applications",
            mobileApps: "Mobile Apps",
            aiSolutions: "AI Solutions",
            liveChat: "Live Chat",
            telegram: "Telegram",
            whatsapp: "WhatsApp",
            copyright: "All rights reserved."
        },

        // Service Categories
        serviceCategories: {
            web: "Web Applications",
            mobile: "Mobile & PWA",
            desktop: "Desktop Applications",
            ai: "Artificial Intelligence",
            automation: "Business Automation",
            content: "Content Creation",
            audio: "Audio & Voice",
            photo: "Photo & Video",
            documents: "Document Processing",
            chatbots: "Chatbots",
            quickDeploy: "Quick Deployments"
        },

        // Experience Levels
        experienceLevels: {
            junior: "Junior (0-2 years)",
            mid: "Mid (2-5 years)",
            senior: "Senior (5+ years)"
        },

        // Specializations
        specializations: {
            frontend: "Frontend Development",
            backend: "Backend Development",
            fullstack: "Full Stack Development",
            qaManual: "QA Manual",
            qaAutomation: "QA Automation",
            devops: "DevOps"
        },

        // Meta tags (titles and descriptions)
        meta: {
            landing: {
                title: "PixelGarage - Creating Digital Future with AI | Portfolio & Services",
                description: "PixelGarage - AI Development & QA Expert. 5 years experience, PJAIT graduate. Comprehensive AI services, project portfolio and development academy."
            },
            services: {
                title: "AI Services Catalog - PixelGarage | 163+ Digital Solutions",
                description: "Comprehensive PixelGarage AI services catalog. Web applications, mobile apps, chatbots, business automation and more. Professional solutions for your company."
            },
            portfolio: {
                title: "Project Portfolio - PixelGarage | 12 Completed AI Projects",
                description: "Discover PixelGarage portfolio - 12 innovative AI projects. SmartCard, BrandMe, FitGenius and more. Animated gallery with full presentation."
            },
            academy: {
                title: "Tech Academy - PixelGarage | Exclusive Technology Training 2025",
                description: "Join exclusive PixelGarage Tech Academy. Comprehensive technology training for professionals. Practical skills for 2025 market!"
            },
            communication: {
                title: "Live Communication - PixelGarage | Telegram & WhatsApp",
                description: "Contact PixelGarage instantly via Telegram or WhatsApp. Available 24/7 for consultations, quotes and technical support."
            }
        },

        // Footer
        footer: {
            brand: "Creando futuro digital con tecnología IA",
            services: "Servicios",
            contact: "Contacto",
            portfolio: "Portafolio",
            webApps: "Aplicaciones Web",
            mobileApps: "Apps Móviles",
            aiSolutions: "Soluciones IA",
            liveChat: "Chat en Vivo",
            telegram: "Telegram",
            whatsapp: "WhatsApp",
            copyright: "Todos los derechos reservados."
        },

        // Service Categories
        serviceCategories: {
            web: "Aplicaciones Web",
            mobile: "Móvil y PWA",
            desktop: "Aplicaciones de Escritorio",
            ai: "Inteligencia Artificial",
            automation: "Automatización Empresarial",
            content: "Creación de Contenido",
            audio: "Audio y Voz",
            photo: "Foto y Video",
            documents: "Procesamiento de Documentos",
            chatbots: "Chatbots",
            quickDeploy: "Despliegues Rápidos"
        },

        // Experience Levels
        experienceLevels: {
            junior: "Junior (0-2 años)",
            mid: "Mid (2-5 años)",
            senior: "Senior (5+ años)"
        },

        // Specializations
        specializations: {
            frontend: "Desarrollo Frontend",
            backend: "Desarrollo Backend",
            fullstack: "Desarrollo Full Stack",
            qaManual: "QA Manual",
            qaAutomation: "QA Automatización",
            devops: "DevOps"
        },

        // Meta tags (titles and descriptions)
        meta: {
            landing: {
                title: "PixelGarage - Creando Futuro Digital con IA | Portafolio y Servicios",
                description: "PixelGarage - Experto en Desarrollo IA y QA. 5 años de experiencia, graduado PJAIT. Servicios integrales de IA, portafolio de proyectos y academia de desarrollo."
            },
            services: {
                title: "Catálogo de Servicios IA - PixelGarage | 163+ Soluciones Digitales",
                description: "Catálogo integral de servicios IA de PixelGarage. Aplicaciones web, apps móviles, chatbots, automatización empresarial y más. Soluciones profesionales para tu empresa."
            },
            portfolio: {
                title: "Portafolio de Proyectos - PixelGarage | 12 Proyectos IA Completados",
                description: "Descubre el portafolio de PixelGarage - 12 proyectos innovadores de IA. SmartCard, BrandMe, FitGenius y más. Galería animada con presentación completa."
            },
            academy: {
                title: "Tech Academy - PixelGarage | Entrenamiento Tecnológico Exclusivo 2025",
                description: "Únete a la exclusiva Tech Academy de PixelGarage. Entrenamiento tecnológico integral para profesionales. ¡Habilidades prácticas para el mercado 2025!"
            },
            communication: {
                title: "Comunicación en Vivo - PixelGarage | Telegram y WhatsApp",
                description: "Contacta PixelGarage instantáneamente vía Telegram o WhatsApp. Disponible 24/7 para consultas, cotizaciones y soporte técnico."
            }
        }
    }
};

// Current language state
let currentLanguage = 'pl';

// Translation utility functions
function t(key, lang = currentLanguage) {
    const keys = key.split('.');
    let value = translations[lang];
    
    for (const k of keys) {
        if (value && typeof value === 'object') {
            value = value[k];
        } else {
            return key; // Return key if translation not found
        }
    }
    
    return value || key;
}

function setLanguage(lang) {
    if (translations[lang]) {
        currentLanguage = lang;
        localStorage.setItem('pixelgarage_language', lang);
        updatePageContent();
        updateLanguageSelector();
    }
}

function getCurrentLanguage() {
    return currentLanguage;
}

function initializeLanguage() {
    const savedLang = localStorage.getItem('pixelgarage_language');
    const browserLang = navigator.language.split('-')[0];
    
    if (savedLang && translations[savedLang]) {
        currentLanguage = savedLang;
    } else if (translations[browserLang]) {
        currentLanguage = browserLang;
    } else {
        currentLanguage = 'pl'; // Default to Polish
    }
    
    updatePageContent();
    updateLanguageSelector();
}

function updatePageContent() {
    // Update all elements with data-translate attribute
    const elements = document.querySelectorAll('[data-translate]');
    elements.forEach(element => {
        const key = element.getAttribute('data-translate');
        const translation = t(key);
        
        if (element.tagName === 'INPUT' && element.type === 'submit') {
            element.value = translation;
        } else if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
            element.placeholder = translation;
        } else {
            element.textContent = translation;
        }
    });
    
    // Update HTML lang attribute
    document.documentElement.lang = currentLanguage;
    
    // Update page title and meta description if available
    updatePageMeta();
}

function updatePageMeta() {
    const pageType = document.body.getAttribute('data-page') || 'landing';
    
    // Update title
    const titleElement = document.querySelector('title');
    if (titleElement) {
        const titleKey = `meta.${pageType}.title`;
        const title = t(titleKey);
        if (title !== titleKey) {
            titleElement.textContent = title;
        }
    }
    
    // Update meta description
    const metaDesc = document.querySelector('meta[name="description"]');
    if (metaDesc) {
        const descKey = `meta.${pageType}.description`;
        const description = t(descKey);
        if (description !== descKey) {
            metaDesc.setAttribute('content', description);
        }
    }
}

function updateLanguageSelector() {
    const selector = document.querySelector('.language-selector');
    if (selector) {
        const buttons = selector.querySelectorAll('.lang-btn');
        buttons.forEach(btn => {
            btn.classList.toggle('active', btn.getAttribute('data-lang') === currentLanguage);
        });
    }
}

// Make functions globally available
window.translations = translations;
window.t = t;
window.setLanguage = setLanguage;
window.getCurrentLanguage = getCurrentLanguage;
window.initializeLanguage = initializeLanguage;
window.updatePageContent = updatePageContent;
window.updateLanguageSelector = updateLanguageSelector;

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { translations, t, setLanguage, getCurrentLanguage, initializeLanguage };
}
