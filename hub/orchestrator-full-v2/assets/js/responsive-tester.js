/**
 * Responsive Design Tester
 * Tool for testing responsiveness across all device sizes
 */

class ResponsiveTester {
    constructor() {
        this.breakpoints = {
            'Ultra Small (320px)': 320,
            'Small Phone (375px)': 375,
            'Large Phone (414px)': 414,
            'Large Phone Landscape (480px)': 480,
            'Tablet Portrait (768px)': 768,
            'Tablet Landscape (1024px)': 1024,
            'Small Desktop (1200px)': 1200,
            'Medium Desktop (1440px)': 1440,
            'Large Desktop (1920px)': 1920,
            'Full HD (1920px)': 1920,
            '2K/QHD (2560px)': 2560,
            '4K UHD (3840px)': 3840,
            '8K (7680px)': 7680
        };
        
        this.currentBreakpoint = null;
        this.init();
    }
    
    init() {
        this.createTesterUI();
        this.detectCurrentBreakpoint();
        this.addEventListeners();
        
        // Auto-detect on window resize
        window.addEventListener('resize', () => {
            this.detectCurrentBreakpoint();
        });
    }
    
    createTesterUI() {
        // Only create in development/testing environment
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            const testerHTML = `
                <div id="responsive-tester" style="
                    position: fixed;
                    top: 10px;
                    right: 10px;
                    background: rgba(0, 0, 0, 0.9);
                    color: white;
                    padding: 15px;
                    border-radius: 10px;
                    z-index: 10000;
                    font-family: 'Inter', sans-serif;
                    font-size: 12px;
                    min-width: 200px;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                ">
                    <div style="margin-bottom: 10px; font-weight: 600; color: #00d4ff;">
                        📱 Responsive Tester
                    </div>
                    <div id="current-size" style="margin-bottom: 10px;">
                        <strong>Current:</strong> <span id="size-display">-</span>
                    </div>
                    <div id="current-breakpoint" style="margin-bottom: 15px; color: #00ff88;">
                        <strong>Breakpoint:</strong> <span id="breakpoint-display">-</span>
                    </div>
                    <div style="margin-bottom: 10px; font-weight: 600;">Quick Test:</div>
                    <div id="breakpoint-buttons" style="display: grid; grid-template-columns: 1fr 1fr; gap: 5px;">
                        ${Object.entries(this.breakpoints).map(([name, width]) => `
                            <button onclick="responsiveTester.testBreakpoint(${width})" style="
                                background: #333;
                                color: white;
                                border: 1px solid #555;
                                padding: 5px;
                                border-radius: 5px;
                                cursor: pointer;
                                font-size: 10px;
                                transition: all 0.2s;
                            " onmouseover="this.style.background='#555'" onmouseout="this.style.background='#333'">
                                ${width}px
                            </button>
                        `).join('')}
                    </div>
                    <button onclick="responsiveTester.toggleTester()" style="
                        background: #ff4444;
                        color: white;
                        border: none;
                        padding: 8px;
                        border-radius: 5px;
                        cursor: pointer;
                        width: 100%;
                        margin-top: 10px;
                        font-size: 11px;
                    ">
                        Hide Tester
                    </button>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', testerHTML);
        }
    }
    
    detectCurrentBreakpoint() {
        const width = window.innerWidth;
        const height = window.innerHeight;
        
        // Update size display
        const sizeDisplay = document.getElementById('size-display');
        if (sizeDisplay) {
            sizeDisplay.textContent = `${width} × ${height}px`;
        }
        
        // Determine current breakpoint
        let currentBreakpoint = 'Custom';
        
        if (width <= 374) {
            currentBreakpoint = 'Ultra Small (≤374px)';
        } else if (width <= 413) {
            currentBreakpoint = 'Small Phone (375-413px)';
        } else if (width <= 479) {
            currentBreakpoint = 'Medium Phone (414-479px)';
        } else if (width <= 767) {
            currentBreakpoint = 'Large Phone (480-767px)';
        } else if (width <= 1023) {
            currentBreakpoint = 'Tablet Portrait (768-1023px)';
        } else if (width <= 1199) {
            currentBreakpoint = 'Tablet Landscape (1024-1199px)';
        } else if (width <= 1439) {
            currentBreakpoint = 'Medium Desktop (1200-1439px)';
        } else if (width <= 1919) {
            currentBreakpoint = 'Large Desktop (1440-1919px)';
        } else if (width <= 2559) {
            currentBreakpoint = 'Full HD (1920-2559px)';
        } else if (width <= 3839) {
            currentBreakpoint = '2K/QHD (2560-3839px)';
        } else if (width <= 7679) {
            currentBreakpoint = '4K UHD (3840-7679px)';
        } else {
            currentBreakpoint = '8K+ (≥7680px)';
        }
        
        this.currentBreakpoint = currentBreakpoint;
        
        // Update breakpoint display
        const breakpointDisplay = document.getElementById('breakpoint-display');
        if (breakpointDisplay) {
            breakpointDisplay.textContent = currentBreakpoint;
        }
        
        // Log to console for debugging
        console.log(`📱 Responsive Debug: ${width}×${height}px - ${currentBreakpoint}`);
    }
    
    testBreakpoint(width) {
        // This would typically be used with browser dev tools
        console.log(`🔧 Testing breakpoint: ${width}px`);
        console.log(`Use browser dev tools to set viewport to ${width}px width`);
        
        // Show instructions
        alert(`To test ${width}px breakpoint:\n\n1. Open browser dev tools (F12)\n2. Click device toolbar icon\n3. Set width to ${width}px\n4. Refresh page to see responsive changes`);
    }
    
    toggleTester() {
        const tester = document.getElementById('responsive-tester');
        if (tester) {
            tester.style.display = tester.style.display === 'none' ? 'block' : 'none';
        }
    }
    
    addEventListeners() {
        // Add keyboard shortcut to toggle tester
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                this.toggleTester();
            }
        });
    }
    
    // Method to test specific components
    testComponent(selector) {
        const elements = document.querySelectorAll(selector);
        console.log(`🧪 Testing component: ${selector}`);
        console.log(`Found ${elements.length} elements`);
        
        elements.forEach((el, index) => {
            const rect = el.getBoundingClientRect();
            console.log(`Element ${index + 1}:`, {
                width: rect.width,
                height: rect.height,
                visible: rect.width > 0 && rect.height > 0,
                inViewport: rect.top >= 0 && rect.left >= 0 && 
                           rect.bottom <= window.innerHeight && 
                           rect.right <= window.innerWidth
            });
        });
    }
    
    // Method to check for responsive issues
    checkResponsiveIssues() {
        const issues = [];
        
        // Check for horizontal overflow
        if (document.body.scrollWidth > window.innerWidth) {
            issues.push('⚠️ Horizontal overflow detected');
        }
        
        // Check for elements that might be too small on mobile
        if (window.innerWidth <= 480) {
            const buttons = document.querySelectorAll('button, .btn');
            buttons.forEach(btn => {
                const rect = btn.getBoundingClientRect();
                if (rect.height < 44) {
                    issues.push(`⚠️ Button too small for touch: ${rect.height}px height`);
                }
            });
        }
        
        // Check for text that might be too small
        const textElements = document.querySelectorAll('p, span, div');
        textElements.forEach(el => {
            const fontSize = parseFloat(window.getComputedStyle(el).fontSize);
            if (fontSize < 14 && window.innerWidth <= 480) {
                issues.push(`⚠️ Text might be too small on mobile: ${fontSize}px`);
            }
        });
        
        if (issues.length > 0) {
            console.warn('🚨 Responsive Issues Found:');
            issues.forEach(issue => console.warn(issue));
        } else {
            console.log('✅ No responsive issues detected');
        }
        
        return issues;
    }
}

// Initialize responsive tester
const responsiveTester = new ResponsiveTester();

// Add global methods for easy testing
window.testResponsive = {
    checkIssues: () => responsiveTester.checkResponsiveIssues(),
    testComponent: (selector) => responsiveTester.testComponent(selector),
    getCurrentBreakpoint: () => responsiveTester.currentBreakpoint,
    toggle: () => responsiveTester.toggleTester()
};

// Auto-check for issues on load
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        responsiveTester.checkResponsiveIssues();
    }, 1000);
});

console.log('📱 Responsive Tester loaded! Use Ctrl+Shift+R to toggle or window.testResponsive for testing methods.');
