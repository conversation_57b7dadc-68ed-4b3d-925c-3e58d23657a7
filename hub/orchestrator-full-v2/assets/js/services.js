// Services Page JavaScript - Professional Catalog System

// Quick Solutions Data
const quickSolutionsData = [
    {
        id: 'qs1',
        title: 'AI Chatbot dla Firmy',
        description: 'Inteligentny chatbot z bazą wiedzy o Twojej firmie, integrowany ze stroną',
        category: 'Web Applications',
        priceRange: '3,500 - 8,000 PLN',
        time: '1-2 tygodni',
        tech: ['Claude.ai', 'JavaScript', 'Webhook integration'],
        includes: 'Training na dokumentach, widget na stronę, panel administracyjny'
    },
    {
        id: 'qs2',
        title: 'Landing Page z AI Copywriting',
        description: 'Strona sprzedażowa z automatycznie generowanymi tekstami dopasowanymi do branży',
        category: 'Web Applications',
        priceRange: '2,500 - 6,000 PLN',
        time: '1 tydzień',
        tech: ['Next.js', 'Claude.ai API', 'Vercel deployment'],
        includes: 'Responsive design, SEO optimization, contact forms'
    },
    {
        id: 'qs3',
        title: 'AI Personal Assistant PWA',
        description: 'Aplik<PERSON><PERSON> webowa (PWA) - osobisty asystent z voice commands',
        category: 'Mobile-First',
        priceRange: '5,000 - 12,000 PLN',
        time: '2-3 tygodni',
        tech: ['Claude.ai', 'Web Speech API', 'PWA'],
        includes: 'Offline functionality, push notifications, voice interface'
    },
    {
        id: 'qs4',
        title: 'Document AI Processor',
        description: 'Narzędzie do automatycznego przetwarzania dokumentów PDF/Word',
        category: 'AI Automation',
        priceRange: '4,000 - 9,000 PLN',
        time: '2 tygodnie',
        tech: ['Claude Code', 'PDF.js', 'Python FastAPI'],
        includes: 'OCR integration, data extraction, output formatting'
    },
    {
        id: 'qs5',
        title: 'Blog Post Generator Pro',
        description: 'Generator artykułów blogowych z research, images i SEO',
        category: 'Content Creation',
        priceRange: '3,000 - 7,000 PLN',
        time: '1-2 tygodni',
        tech: ['Claude.ai', 'Unsplash API', 'Markdown'],
        includes: 'Topic research, image selection, meta descriptions'
    },
    {
        id: 'qs6',
        title: 'Podcast Transcription Service',
        description: 'Automatyczna transkrypcja podcastów z timestamps i chapters',
        category: 'Audio & Voice',
        priceRange: '2,500 - 6,000 PLN',
        time: '1 tydzień',
        tech: ['Whisper API', 'Speaker ID', 'React'],
        includes: 'Speaker identification, chapter markers, export formats'
    }
];

// Wishlist Data - Comprehensive AI Services Catalog 2025
const wishlistData = {
    saas: [
        {
            id: 'w1',
            title: 'AI-Powered SaaS MVP Development',
            description: 'Kompleksowe tworzenie aplikacji SaaS z integracją AI od koncepcji do wdrożenia',
            priceRange: '25,000-85,000 PLN',
            time: '6-12 tygodni',
            tech: ['Claude 4', 'GPT-4', 'React/Next.js', 'Python/FastAPI']
        },
        {
            id: 'w2',
            title: 'Inteligentne Dashboardy Analityczne',
            description: 'Zaawansowane dashboardy z predykcyjną analityką i automatycznym raportowaniem',
            priceRange: '18,000-45,000 PLN',
            time: '4-8 tygodni',
            tech: ['Analytics AI', 'Data Visualization', 'Predictive Models', 'React']
        },
        {
            id: 'w3',
            title: 'E-commerce z AI Assistant',
            description: 'Sklepy internetowe z AI-powered rekomendacjami i obsługą klienta',
            priceRange: '35,000-120,000 PLN',
            time: '8-16 tygodni',
            tech: ['Shopify Plus', 'WooCommerce', 'ChatGPT API', 'Recommendation Engine']
        },
        {
            id: 'w4',
            title: 'Platformy B2B z Automatyzacją',
            description: 'Systemy zarządzania relacjami z klientami z AI-powered lead scoring',
            priceRange: '45,000-180,000 PLN',
            time: '10-20 tygodni',
            tech: ['CRM AI', 'Lead Scoring', 'B2B Automation', 'Sales Intelligence']
        },
        {
            id: 'w5',
            title: 'AI Content Management Systems',
            description: 'CMS z automatyczną optymalizacją SEO i generowaniem treści',
            priceRange: '22,000-65,000 PLN',
            time: '6-12 tygodni',
            tech: ['CMS', 'SEO AI', 'Content Generation', 'Multi-site']
        },
        {
            id: 'w6',
            title: 'Platformy E-learningowe z AI',
            description: 'Systemy edukacyjne z personalizowanymi ścieżkami nauki',
            priceRange: '40,000-150,000 PLN',
            time: '10-18 tygodni',
            tech: ['Education AI', 'Adaptive Learning', 'Progress Tracking', 'LMS']
        },
        {
            id: 'w7',
            title: 'Real Estate AI Platforms',
            description: 'Platformy nieruchomości z wirtualnymi tourami i AI-powered wycenami',
            priceRange: '55,000-200,000 PLN',
            time: '12-24 tygodni',
            tech: ['Three.js', 'Matterport API', 'ML models', 'Property Valuation AI']
        },
        {
            id: 'w8',
            title: 'AI-Powered Booking Systems',
            description: 'Systemy rezerwacyjne z inteligentną optymalizacją cen i dostępności',
            priceRange: '30,000-95,000 PLN',
            time: '8-14 tygodni',
            tech: ['Booking AI', 'Dynamic Pricing', 'Calendar Integration', 'Payment Systems']
        },
        {
            id: 'w9',
            title: 'Fintech Applications',
            description: 'Aplikacje finansowe z AI fraud detection i robo-advisors',
            priceRange: '80,000-350,000 PLN',
            time: '16-32 tygodni',
            tech: ['Fintech AI', 'Fraud Detection', 'Robo-advisors', 'Blockchain']
        },
        {
            id: 'w10',
            title: 'Healthcare Management Platforms',
            description: 'Systemy zarządzania placówkami medycznymi z AI diagnostyką',
            priceRange: '120,000-500,000 PLN',
            time: '24-40 tygodni',
            tech: ['Healthcare AI', 'Medical Diagnostics', 'HIPAA Compliance', 'EMR Integration']
        },
        {
            id: 'w11',
            title: 'AI-Enhanced Portfolio Websites',
            description: 'Strony portfolio z dynamiczną personalizacją treści dla odwiedzających',
            priceRange: '8,000-25,000 PLN',
            time: '3-6 tygodni',
            tech: ['Portfolio AI', 'Personalization', 'Dynamic Content', 'Analytics']
        }
    ],
    mobile: [
        {
            id: 'm1',
            title: 'AI Personal Assistant Apps',
            description: 'Mobilni asystenci z rozpoznawaniem mowy i zarządzaniem zadaniami',
            priceRange: '45,000-120,000 PLN',
            time: '10-18 tygodni',
            tech: ['React Native', 'Expo', 'Speech-to-Text APIs', 'Task Management']
        },
        {
            id: 'm2',
            title: 'Smart Fitness & Health Apps',
            description: 'Aplikacje fitness z AI personal trainerem i analizą postępów',
            priceRange: '35,000-85,000 PLN',
            time: '8-14 tygodni',
            tech: ['Flutter', 'TensorFlow Lite', 'HealthKit', 'AI Trainer']
        },
        {
            id: 'm3',
            title: 'AI-Powered Dating Apps',
            description: 'Aplikacje randkowe z inteligentnym matchingiem i chatbotami',
            priceRange: '50,000-140,000 PLN',
            time: '12-20 tygodni',
            tech: ['Dating AI', 'Matching Algorithm', 'Chat Integration', 'Social Features']
        },
        {
            id: 'm4',
            title: 'Smart Finance Mobile Apps',
            description: 'Aplikacje finansowe z AI budgetingiem i prognozami wydatków',
            priceRange: '40,000-110,000 PLN',
            time: '10-16 tygodni',
            tech: ['Finance AI', 'Budget Tracking', 'Expense Prediction', 'Banking APIs']
        },
        {
            id: 'm5',
            title: 'Educational Apps for Kids',
            description: 'Edukacyjne aplikacje dla dzieci z adaptacyjnym uczeniem',
            priceRange: '30,000-75,000 PLN',
            time: '8-14 tygodni',
            tech: ['Education AI', 'Adaptive Learning', 'Gamification', 'Child Safety']
        },
        {
            id: 'm6',
            title: 'AI Travel Companion Apps',
            description: 'Aplikacje podróżnicze z AI planowaniem tras i rekomendacjami',
            priceRange: '35,000-90,000 PLN',
            time: '8-16 tygodni',
            tech: ['Travel AI', 'Route Planning', 'Google Places API', 'ML Recommendations']
        },
        {
            id: 'm7',
            title: 'Smart Home Control Apps',
            description: 'Aplikacje do sterowania domem z AI automatyzacją i predykcją',
            priceRange: '45,000-125,000 PLN',
            time: '10-18 tygodni',
            tech: ['IoT Integration', 'Home Automation', 'AI Prediction', 'Smart Devices']
        },
        {
            id: 'm8',
            title: 'AI Food & Nutrition Apps',
            description: 'Aplikacje żywieniowe z rozpoznawaniem jedzenia i planowaniem diet',
            priceRange: '32,000-80,000 PLN',
            time: '8-14 tygodni',
            tech: ['Food Recognition AI', 'Nutrition Analysis', 'Diet Planning', 'Health Tracking']
        },
        {
            id: 'm9',
            title: 'Mental Health & Wellness Apps',
            description: 'Aplikacje zdrowia psychicznego z AI terapeutą i monitoringiem nastroju',
            priceRange: '55,000-150,000 PLN',
            time: '12-20 tygodni',
            tech: ['Mental Health AI', 'Mood Tracking', 'Therapy Bot', 'Wellness Analytics']
        },
        {
            id: 'm10',
            title: 'AI-Enhanced Social Media Apps',
            description: 'Platformy społecznościowe z inteligentną moderacją i content curation',
            priceRange: '80,000-250,000 PLN',
            time: '16-28 tygodni',
            tech: ['Social AI', 'Content Moderation', 'Curation Algorithm', 'Community Features']
        },
        {
            id: 'm11',
            title: 'Smart Shopping Assistant Apps',
            description: 'Aplikacje zakupowe z AI price tracking i personalizowanymi ofertami',
            priceRange: '38,000-95,000 PLN',
            time: '8-16 tygodni',
            tech: ['Shopping AI', 'Price Tracking', 'Deal Finder', 'Personalization']
        }
    ],
    desktop: [
        {
            id: 'd1',
            title: 'AI-Powered Design Suite',
            description: 'Kompleksowe narzędzie do projektowania z AI asystentem i automatyzacją',
            priceRange: '65,000-180,000 PLN',
            time: '14-24 tygodni',
            tech: ['Design AI', 'Creative Suite', 'Automation Tools', 'Asset Management']
        },
        {
            id: 'd2',
            title: 'Intelligent Document Processing',
            description: 'Aplikacja do przetwarzania dokumentów z OCR i automatyczną klasyfikacją',
            priceRange: '45,000-120,000 PLN',
            time: '10-18 tygodni',
            tech: ['OCR Technology', 'Document AI', 'Classification', 'Workflow Automation']
        },
        {
            id: 'd3',
            title: 'AI Code Assistant IDE Plugin',
            description: 'Wtyczki do IDE z zaawansowanym code completion i refactoring',
            priceRange: '35,000-85,000 PLN',
            time: '8-16 tygodni',
            tech: ['Code AI', 'IDE Integration', 'Refactoring', 'Code Analysis']
        },
        {
            id: 'd4',
            title: 'Smart Project Management Tool',
            description: 'Narzędzie PM z AI planowaniem zadań i prognozowaniem deadlinów',
            priceRange: '50,000-140,000 PLN',
            time: '12-20 tygodni',
            tech: ['Project AI', 'Task Planning', 'Deadline Prediction', 'Team Collaboration']
        },
        {
            id: 'd5',
            title: 'AI Video Editing Suite',
            description: 'Profesjonalne narzędzie do edycji wideo z automatyczną obróbką',
            priceRange: '80,000-220,000 PLN',
            time: '16-28 tygodni',
            tech: ['Video AI', 'Automated Editing', 'Effects Engine', 'Rendering Optimization']
        },
        {
            id: 'd6',
            title: 'Intelligent Data Analysis Platform',
            description: 'Platforma analityczna z automatycznym odkrywaniem wzorców w danych',
            priceRange: '60,000-160,000 PLN',
            time: '12-22 tygodni',
            tech: ['Data AI', 'Pattern Recognition', 'Analytics Engine', 'Visualization']
        },
        {
            id: 'd7',
            title: 'AI-Enhanced CAD Software',
            description: 'Oprogramowanie CAD z AI optymalizacją projektów i generowaniem wariantów',
            priceRange: '120,000-350,000 PLN',
            time: '24-40 tygodni',
            tech: ['CAD AI', 'Design Optimization', 'Variant Generation', '3D Modeling']
        },
        {
            id: 'd8',
            title: 'Smart Inventory Management System',
            description: 'System zarządzania magazynem z AI prognozowaniem popytu',
            priceRange: '55,000-150,000 PLN',
            time: '12-20 tygodni',
            tech: ['Inventory AI', 'Demand Forecasting', 'Supply Chain', 'Warehouse Management']
        },
        {
            id: 'd9',
            title: 'AI-Powered Accounting Software',
            description: 'Oprogramowanie księgowe z automatyczną kategoryzacją i wykrywaniem błędów',
            priceRange: '70,000-200,000 PLN',
            time: '14-26 tygodni',
            tech: ['Accounting AI', 'Auto-categorization', 'Error Detection', 'Financial Analytics']
        },
        {
            id: 'd10',
            title: 'Intelligent Security Monitoring',
            description: 'System monitoringu bezpieczeństwa z AI wykrywaniem zagrożeń',
            priceRange: '85,000-250,000 PLN',
            time: '16-30 tygodni',
            tech: ['Security AI', 'Threat Detection', 'Monitoring Systems', 'Alert Management']
        },
        {
            id: 'd11',
            title: 'AI Content Creator Studio',
            description: 'Studio do tworzenia treści z generowaniem tekstu, obrazów i wideo',
            priceRange: '45,000-125,000 PLN',
            time: '10-18 tygodni',
            tech: ['Content AI', 'Multi-media Generation', 'Creative Tools', 'Asset Library']
        }
    ],
    chatbots: [
        {
            id: 'c1',
            title: 'Customer Support Bot',
            description: 'Chatbot obsługi klienta z training na dokumentacji firmy',
            priceRange: '3,500 PLN',
            time: '1-2 tygodnie',
            tech: ['Support AI', '24/7', 'Custom Training', 'Documentation']
        },
        {
            id: 'c2',
            title: 'Sales Assistant Bot',
            description: 'AI sprzedawca z lead qualification i product recommendations',
            priceRange: '4,200 PLN',
            time: '1-2 tygodnie',
            tech: ['Sales AI', 'Lead Qualification', 'Recommendations', 'CRM']
        },
        {
            id: 'c3',
            title: 'FAQ Intelligence Bot',
            description: 'Inteligentny bot FAQ z self-learning capabilities',
            priceRange: '2,800 PLN',
            time: '1 tydzień',
            tech: ['FAQ AI', 'Self-learning', 'Knowledge Base', 'Auto-update']
        },
        {
            id: 'c4',
            title: 'Appointment Booking Bot',
            description: 'Bot do umawiania wizyt z calendar integration i confirmations',
            priceRange: '3,200 PLN',
            time: '1-2 tygodnie',
            tech: ['Booking AI', 'Calendar Integration', 'Confirmations', 'Scheduling']
        },
        {
            id: 'c5',
            title: 'Product Advisor Bot',
            description: 'AI doradca produktowy dla e-commerce z personalization',
            priceRange: '3,800 PLN',
            time: '1-2 tygodnie',
            tech: ['Product AI', 'E-commerce', 'Personalization', 'Recommendations']
        },
        {
            id: 'c6',
            title: 'HR Screening Bot',
            description: 'Bot do pre-screeningu kandydatów z AI evaluation',
            priceRange: '4,500 PLN',
            time: '2-3 tygodnie',
            tech: ['HR AI', 'Recruitment', 'Screening', 'Evaluation']
        },
        {
            id: 'c7',
            title: 'Real Estate Bot',
            description: 'AI asystent nieruchomości z property matching i market insights',
            priceRange: '4,800 PLN',
            time: '2-3 tygodnie',
            tech: ['Real Estate AI', 'Property Matching', 'Market Analysis', 'Insights']
        },
        {
            id: 'c8',
            title: 'Educational Tutor Bot',
            description: 'Bot edukacyjny z adaptive learning i progress tracking',
            priceRange: '3,600 PLN',
            time: '1-2 tygodnie',
            tech: ['Education AI', 'Adaptive Learning', 'Progress Tracking', 'Tutoring']
        },
        {
            id: 'c9',
            title: 'Health Symptom Checker',
            description: 'AI checker objawów z recommendations (non-medical advice)',
            priceRange: '5,200 PLN',
            time: '2-3 tygodnie',
            tech: ['Health AI', 'Symptom Analysis', 'Recommendations', 'Medical Database']
        },
        {
            id: 'c10',
            title: 'Legal Advisor Bot',
            description: 'Bot z podstawowym poradnictwem prawnym (informacyjnym)',
            priceRange: '4,800 PLN',
            time: '2-3 tygodnie',
            tech: ['Legal AI', 'Legal Advice', 'Information', 'Law Database']
        }
    ],
    audio: [
        {
            id: 'a1',
            title: 'AI Voice Cloning Studio',
            description: 'Profesjonalne studio do klonowania głosów z wysoką jakością i kontrolą',
            priceRange: '25,000-65,000 PLN',
            time: '6-12 tygodni',
            tech: ['ElevenLabs', 'Custom Voice Models', 'Audio Processing', 'Voice Synthesis']
        },
        {
            id: 'a2',
            title: 'Intelligent Podcast Production',
            description: 'Automatyczna produkcja podcastów z AI edycją i mastering',
            priceRange: '18,000-45,000 PLN',
            time: '4-8 tygodni',
            tech: ['Podcast AI', 'Auto-editing', 'Mastering', 'Audio Enhancement']
        },
        {
            id: 'a3',
            title: 'Multi-language Audio Translation',
            description: 'System tłumaczenia audio z zachowaniem głosu i emocji',
            priceRange: '35,000-85,000 PLN',
            time: '8-16 tygodni',
            tech: ['Whisper', 'Google Translate', 'Voice Synthesis', 'Emotion Preservation']
        },
        {
            id: 'a4',
            title: 'AI Music Composition Platform',
            description: 'Platforma do komponowania muzyki z AI w różnych stylach',
            priceRange: '40,000-100,000 PLN',
            time: '10-18 tygodni',
            tech: ['Music AI', 'Composition Engine', 'Style Transfer', 'MIDI Generation']
        },
        {
            id: 'a5',
            title: 'Smart Audio Transcription Service',
            description: 'Usługa transkrypcji z rozpoznawaniem mówców i czasowaniem',
            priceRange: '22,000-55,000 PLN',
            time: '5-10 tygodni',
            tech: ['Whisper API', 'Speaker ID', 'Timestamping', 'Multi-language']
        },
        {
            id: 'a6',
            title: 'Voice-Activated Business Assistant',
            description: 'Asystent głosowy dla firm z integracją systemów biznesowych',
            priceRange: '30,000-75,000 PLN',
            time: '8-14 tygodni',
            tech: ['Voice AI', 'Business Integration', 'Command Recognition', 'Workflow Automation']
        },
        {
            id: 'a7',
            title: 'AI Sound Design Suite',
            description: 'Narzędzia do projektowania dźwięku z generowaniem efektów AI',
            priceRange: '45,000-110,000 PLN',
            time: '10-18 tygodni',
            tech: ['Sound AI', 'Effect Generation', 'Audio Synthesis', 'Creative Tools']
        },
        {
            id: 'a8',
            title: 'Intelligent Call Center Solution',
            description: 'System call center z AI routing i analizą rozmów',
            priceRange: '55,000-140,000 PLN',
            time: '12-20 tygodni',
            tech: ['Call Center AI', 'Smart Routing', 'Conversation Analysis', 'Performance Analytics']
        },
        {
            id: 'a9',
            title: 'Audio Content Personalization',
            description: 'System personalizacji treści audio dla platform streamingowych',
            priceRange: '50,000-130,000 PLN',
            time: '12-18 tygodni',
            tech: ['Audio AI', 'Personalization', 'Streaming', 'Content Curation']
        },
        {
            id: 'a10',
            title: 'AI-Enhanced Radio Broadcasting',
            description: 'Rozwiązania dla stacji radiowych z automatycznym programowaniem',
            priceRange: '65,000-180,000 PLN',
            time: '14-24 tygodni',
            tech: ['Radio AI', 'Auto-programming', 'Content Scheduling', 'Audience Analytics']
        },
        {
            id: 'a11',
            title: 'Voice Biometrics Security System',
            description: 'System bezpieczeństwa oparty na rozpoznawaniu głosu',
            priceRange: '70,000-200,000 PLN',
            time: '16-26 tygodni',
            tech: ['Voice Biometrics', 'Security AI', 'Authentication', 'Access Control']
        }
    ],
    content: [
        {
            id: 'co1',
            title: 'AI-Powered Content Strategy Platform',
            description: 'Platforma do tworzenia strategii contentu z analizą konkurencji i trendów',
            priceRange: '20,000-50,000 PLN',
            time: '6-10 tygodni',
            tech: ['Content Strategy AI', 'Competitor Analysis', 'Trend Analysis', 'Planning Tools']
        },
        {
            id: 'co2',
            title: 'Multi-Channel Content Generator',
            description: 'Generator treści dla wszystkich kanałów marketingowych z brand voice',
            priceRange: '25,000-60,000 PLN',
            time: '8-12 tygodni',
            tech: ['Multi-channel AI', 'Brand Voice', 'Content Generation', 'Marketing Automation']
        },
        {
            id: 'co3',
            title: 'SEO-Optimized Blog Automation',
            description: 'Automatyzacja pisania blogów z optymalizacją SEO i keyword research',
            priceRange: '18,000-40,000 PLN',
            time: '5-8 tygodni',
            tech: ['Blog AI', 'SEO Optimization', 'Keyword Research', 'Content Automation']
        },
        {
            id: 'co4',
            title: 'AI Email Marketing Suite',
            description: 'Kompleksowe narzędzie do email marketingu z personalizacją AI',
            priceRange: '22,000-55,000 PLN',
            time: '6-12 tygodni',
            tech: ['Email AI', 'Marketing Automation', 'Personalization', 'Campaign Management']
        },
        {
            id: 'co5',
            title: 'Social Media Content Automation',
            description: 'Automatyczne tworzenie i publikowanie treści w mediach społecznościowych',
            priceRange: '15,000-35,000 PLN',
            time: '4-8 tygodni',
            tech: ['Social Media AI', 'Content Automation', 'Publishing', 'Scheduling']
        },
        {
            id: 'co6',
            title: 'AI Scriptwriting for Videos',
            description: 'Narzędzie do pisania scenariuszy wideo z analizą engagement',
            priceRange: '20,000-45,000 PLN',
            time: '5-9 tygodni',
            tech: ['Video AI', 'Scriptwriting', 'Engagement Analysis', 'Content Optimization']
        },
        {
            id: 'co7',
            title: 'Product Description Generator',
            description: 'Automatyczne generowanie opisów produktów dla e-commerce',
            priceRange: '12,000-30,000 PLN',
            time: '3-6 tygodni',
            tech: ['Product AI', 'E-commerce', 'Description Generation', 'SEO Optimization']
        },
        {
            id: 'co8',
            title: 'AI Press Release Writer',
            description: 'Narzędzie do tworzenia komunikatów prasowych z dystrybucją',
            priceRange: '16,000-38,000 PLN',
            time: '4-7 tygodni',
            tech: ['Press Release AI', 'PR Writing', 'Distribution', 'Media Outreach']
        },
        {
            id: 'co9',
            title: 'Multilingual Content Localization',
            description: 'System lokalizacji treści z uwzględnieniem kontekstu kulturowego',
            priceRange: '28,000-70,000 PLN',
            time: '8-14 tygodni',
            tech: ['Localization AI', 'Cultural Context', 'Multi-language', 'Content Adaptation']
        },
        {
            id: 'co10',
            title: 'AI-Enhanced Ghostwriting Service',
            description: 'Platforma ghostwritingu z AI research i fact-checking',
            priceRange: '30,000-75,000 PLN',
            time: '8-15 tygodni',
            tech: ['Ghostwriting AI', 'Research Automation', 'Fact-checking', 'Content Quality']
        },
        {
            id: 'co11',
            title: 'Brand Voice Consistency Checker',
            description: 'Narzędzie do sprawdzania spójności tonu marki w treściach',
            priceRange: '18,000-42,000 PLN',
            time: '5-9 tygodni',
            tech: ['Brand Voice AI', 'Consistency Check', 'Tone Analysis', 'Content Review']
        }
    ],
    virtual: [
        {
            id: 'v1',
            title: 'AI-Enhanced Virtual Property Tours',
            description: 'Immersyjne wirtualne spacery po nieruchomościach z AI przewodnikiem',
            priceRange: '35,000-85,000 PLN',
            time: '8-16 tygodni',
            tech: ['Virtual Tours', 'AI Guide', 'Property Visualization', 'Interactive Navigation']
        },
        {
            id: 'v2',
            title: 'Interactive Museum Virtual Experiences',
            description: 'Wirtualne wycieczki po muzeach z AI kuratorskim przewodnikiem',
            priceRange: '45,000-120,000 PLN',
            time: '10-20 tygodni',
            tech: ['Museum AI', 'Virtual Reality', 'Cultural Heritage', 'Interactive Exhibits']
        },
        {
            id: 'v3',
            title: '360° Business Showroom Builder',
            description: 'Kreator wirtualnych showroomów z konfiguratorem produktów',
            priceRange: '30,000-70,000 PLN',
            time: '8-14 tygodni',
            tech: ['360° Technology', 'Product Configurator', 'Virtual Showroom', 'Business Presentation']
        },
        {
            id: 'v4',
            title: 'AI-Powered City Walking Tours',
            description: 'Interaktywne wirtualne spacery po miastach z historycznymi informacjami',
            priceRange: '40,000-95,000 PLN',
            time: '10-18 tygodni',
            tech: ['City Tours AI', 'Historical Data', 'Interactive Maps', 'Cultural Information']
        },
        {
            id: 'v5',
            title: 'Virtual Event Spaces Platform',
            description: 'Platforma do organizacji wydarzeń wirtualnych z networking AI',
            priceRange: '55,000-140,000 PLN',
            time: '12-22 tygodni',
            tech: ['Virtual Events', 'Networking AI', 'Event Management', 'Social Interaction']
        },
        {
            id: 'v6',
            title: 'Immersive Educational Campus Tours',
            description: 'Wirtualne wycieczki po kampusach uniwersyteckich z AI doradcą',
            priceRange: '35,000-80,000 PLN',
            time: '8-15 tygodni',
            tech: ['Education Tours', 'Campus AI', 'Student Guidance', 'University Information']
        },
        {
            id: 'v7',
            title: '360° Retail Experience Creator',
            description: 'Narzędzie do tworzenia immersyjnych doświadczeń zakupowych',
            priceRange: '42,000-100,000 PLN',
            time: '10-18 tygodni',
            tech: ['Retail AI', 'Shopping Experience', 'Product Interaction', 'E-commerce Integration']
        },
        {
            id: 'v8',
            title: 'Virtual Workplace Collaboration Spaces',
            description: 'Wirtualne przestrzenie biurowe do pracy zdalnej z AI facilitatorem',
            priceRange: '50,000-125,000 PLN',
            time: '12-20 tygodni',
            tech: ['Virtual Office', 'Collaboration AI', 'Remote Work', 'Team Interaction']
        },
        {
            id: 'v9',
            title: 'AI-Guided Nature Exploration',
            description: 'Wirtualne eksploracje natury z AI biologiem i przewodnikiem',
            priceRange: '38,000-90,000 PLN',
            time: '9-16 tygodni',
            tech: ['Nature AI', 'Biology Guide', 'Environmental Education', 'Wildlife Information']
        },
        {
            id: 'v10',
            title: 'Historic Site Reconstruction',
            description: 'Rekonstrukcje historycznych miejsc z AI archeologiem',
            priceRange: '65,000-180,000 PLN',
            time: '16-28 tygodni',
            tech: ['Historical AI', 'Archaeological Data', '3D Reconstruction', 'Time Travel Experience']
        },
        {
            id: 'v11',
            title: 'Virtual Travel Destination Previews',
            description: 'Interaktywne podglądy destynacji turystycznych z AI travel plannerm',
            priceRange: '32,000-75,000 PLN',
            time: '8-14 tygodni',
            tech: ['Travel AI', 'Destination Preview', 'Trip Planning', 'Tourism Information']
        }
    ],
    visual: [
        {
            id: 'vi1',
            title: 'AI Photo Animation Studio',
            description: 'Profesjonalne studio do animowania zdjęć z efektami cinematograficznymi',
            priceRange: '22,000-55,000 PLN',
            time: '5-12 tygodni',
            tech: ['Runway ML', 'Stable Video Diffusion', 'Motion Estimation', 'Cinematic Effects']
        },
        {
            id: 'vi2',
            title: 'Smart Product Photography Automation',
            description: 'Automatyzacja fotografii produktowej z AI background removal i styling',
            priceRange: '18,000-40,000 PLN',
            time: '4-8 tygodni',
            tech: ['Product Photography', 'Background Removal', 'AI Styling', 'E-commerce Optimization']
        },
        {
            id: 'vi3',
            title: 'AI-Enhanced Video Storytelling',
            description: 'Tworzenie video stories z pojedynczych zdjęć z AI narratorem',
            priceRange: '25,000-60,000 PLN',
            time: '6-12 tygodni',
            tech: ['Video AI', 'Storytelling', 'Photo Animation', 'AI Narrator']
        },
        {
            id: 'vi4',
            title: 'Interactive Photo Gallery Generator',
            description: 'Generator interaktywnych galerii z AI opisami i kategoryzacją',
            priceRange: '15,000-35,000 PLN',
            time: '4-8 tygodni',
            tech: ['Gallery AI', 'Auto-categorization', 'Interactive Design', 'Content Management']
        },
        {
            id: 'vi5',
            title: 'AI Portrait Video Creator',
            description: 'Tworzenie mówiących portretów z synchronizacją ust i mimiki',
            priceRange: '30,000-70,000 PLN',
            time: '8-14 tygodni',
            tech: ['Portrait AI', 'Lip Sync', 'Facial Animation', 'Video Generation']
        },
        {
            id: 'vi6',
            title: 'Real Estate Visual Enhancement',
            description: 'AI enhancement zdjęć nieruchomości z virtual staging',
            priceRange: '20,000-45,000 PLN',
            time: '5-10 tygodni',
            tech: ['Real Estate AI', 'Virtual Staging', 'Photo Enhancement', 'Property Visualization']
        },
        {
            id: 'vi7',
            title: 'Social Media Visual Content Generator',
            description: 'Automatyczne tworzenie content wizualnego dla social media',
            priceRange: '16,000-38,000 PLN',
            time: '4-8 tygodni',
            tech: ['Social Media AI', 'Visual Content', 'Auto-generation', 'Brand Consistency']
        },
        {
            id: 'vi8',
            title: 'AI Fashion Photography Suite',
            description: 'Narzędzia do fotografii modowej z AI model replacement',
            priceRange: '45,000-110,000 PLN',
            time: '10-18 tygodni',
            tech: ['Fashion AI', 'Model Replacement', 'Style Transfer', 'Photography Enhancement']
        },
        {
            id: 'vi9',
            title: 'Historic Photo Restoration & Animation',
            description: 'Renowacja starych zdjęć i ich animacja z kontekstem historycznym',
            priceRange: '28,000-65,000 PLN',
            time: '8-14 tygodni',
            tech: ['Photo Restoration', 'Historical AI', 'Animation', 'Context Enhancement']
        },
        {
            id: 'vi10',
            title: 'AI-Powered Brand Visual Identity',
            description: 'Tworzenie spójnej identyfikacji wizualnej marki z AI',
            priceRange: '35,000-80,000 PLN',
            time: '8-16 tygodni',
            tech: ['Brand AI', 'Visual Identity', 'Design Consistency', 'Brand Guidelines']
        },
        {
            id: 'vi11',
            title: '360° Product Visualization Platform',
            description: 'Platforma do tworzenia 360° wizualizacji produktów',
            priceRange: '40,000-95,000 PLN',
            time: '9-18 tygodni',
            tech: ['360° Technology', '3D Reconstruction', 'WebGL', 'Product Visualization']
        }
    ],
    documents: [
        {
            id: 'doc1',
            title: 'AI-Powered Legal Document Analysis',
            description: 'Analiza dokumentów prawnych z wyciąganiem kluczowych informacji',
            priceRange: '35,000-85,000 PLN',
            time: '8-16 tygodni',
            tech: ['Legal AI', 'Document Analysis', 'Information Extraction', 'Legal Intelligence']
        },
        {
            id: 'doc2',
            title: 'Multi-Language Meeting Transcription',
            description: 'Transkrypcja spotkań z automatycznym tłumaczeniem i streszczeniem',
            priceRange: '25,000-60,000 PLN',
            time: '6-12 tygodni',
            tech: ['Meeting AI', 'Multi-language', 'Transcription', 'Auto-summary']
        },
        {
            id: 'doc3',
            title: 'Intelligent Contract Management System',
            description: 'System zarządzania umowami z AI analizą ryzyka i terminów',
            priceRange: '45,000-120,000 PLN',
            time: '10-20 tygodni',
            tech: ['Contract AI', 'Risk Analysis', 'Deadline Management', 'Legal Compliance']
        },
        {
            id: 'doc4',
            title: 'Medical Records Processing Platform',
            description: 'Platforma do przetwarzania dokumentacji medycznej z AI diagnostyką',
            priceRange: '80,000-200,000 PLN',
            time: '16-32 tygodni',
            tech: ['Medical AI', 'Records Processing', 'Diagnostic Support', 'Healthcare Compliance']
        },
        {
            id: 'doc5',
            title: 'Academic Research Paper Analyzer',
            description: 'Narzędzie do analizy prac naukowych z wyciąganiem insights',
            priceRange: '30,000-70,000 PLN',
            time: '8-14 tygodni',
            tech: ['Research AI', 'Academic Analysis', 'Insight Extraction', 'Citation Management']
        },
        {
            id: 'doc6',
            title: 'Financial Document Intelligence',
            description: 'Analiza dokumentów finansowych z wykrywaniem anomalii',
            priceRange: '50,000-130,000 PLN',
            time: '12-20 tygodni',
            tech: ['Financial AI', 'Anomaly Detection', 'Document Intelligence', 'Risk Assessment']
        },
        {
            id: 'doc7',
            title: 'Voice-to-Document Workflow Automation',
            description: 'Automatyzacja tworzenia dokumentów z nagrań głosowych',
            priceRange: '20,000-50,000 PLN',
            time: '5-10 tygodni',
            tech: ['Voice AI', 'Document Automation', 'Workflow', 'Speech-to-Text']
        },
        {
            id: 'doc8',
            title: 'Technical Documentation Generator',
            description: 'Generator dokumentacji technicznej z analizy kodu i systemów',
            priceRange: '28,000-65,000 PLN',
            time: '8-14 tygodni',
            tech: ['Technical AI', 'Code Analysis', 'Documentation Generation', 'System Analysis']
        },
        {
            id: 'doc9',
            title: 'Compliance Document Monitoring',
            description: 'System monitorowania zgodności dokumentów z regulacjami',
            priceRange: '40,000-100,000 PLN',
            time: '10-18 tygodni',
            tech: ['Compliance AI', 'Regulatory Monitoring', 'Document Tracking', 'Legal Updates']
        },
        {
            id: 'doc10',
            title: 'AI-Enhanced Subtitling Service',
            description: 'Automatyczne tworzenie napisów z synchronizacją i tłumaczeniem',
            priceRange: '18,000-42,000 PLN',
            time: '4-8 tygodni',
            tech: ['Subtitling AI', 'Synchronization', 'Translation', 'Video Processing']
        }
    ],
};

// Services data structure - comprehensive list based on Polish AI services
const servicesData = [
    // Web Development & Design
    {
        id: 'landing-pages-ai',
        category: 'web',
        title: 'Landing Pages z AI Optimization',
        description: 'Profesjonalne strony docelowe z automatyczną optymalizacją konwersji',
        price: 2500,
        time: '2-3 tygodnie',
        tags: ['Landing Page', 'AI Optimization', 'Conversion'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'responsive-websites',
        category: 'web',
        title: 'Pełne Strony Internetowe z Responsive Design',
        description: 'Kompleksowe strony internetowe dostosowane do wszystkich urządzeń',
        price: 4500,
        time: '4-6 tygodni',
        tags: ['Responsive', 'Full Website', 'Modern Design'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'interactive-cv-portfolio',
        category: 'web',
        title: 'Interaktywne Strony CV i Portfolio',
        description: 'Profesjonalne portfolio z interaktywnymi elementami i animacjami',
        price: 1800,
        time: '2-3 tygodnie',
        tags: ['Portfolio', 'CV', 'Interactive'],
        featured: true,
        priceRange: 'low'
    },
    {
        id: 'dynamic-blogs',
        category: 'web',
        title: 'Dynamiczne Blogi i Platformy Treści',
        description: 'Zaawansowane systemy blogowe z zarządzaniem treścią',
        price: 3500,
        time: '3-5 tygodni',
        tags: ['Blog', 'CMS', 'Content Platform'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'ecommerce-ai-features',
        category: 'web',
        title: 'Sklepy Internetowe z AI Features',
        description: 'E-commerce z inteligentnymi rekomendacjami i automatyzacją',
        price: 6500,
        time: '6-8 tygodni',
        tags: ['E-commerce', 'AI Features', 'Recommendations'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'saas-pwa-apps',
        category: 'web',
        title: 'Aplikacje Webowe (SaaS/PWA)',
        description: 'Zaawansowane aplikacje webowe i Progressive Web Apps',
        price: 8500,
        time: '8-12 tygodni',
        tags: ['SaaS', 'PWA', 'Web App'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'analytics-dashboards',
        category: 'web',
        title: 'Dashboardy Analityczne',
        description: 'Interaktywne dashboardy z wizualizacją danych i raportami',
        price: 4200,
        time: '4-6 tygodni',
        tags: ['Analytics', 'Dashboard', 'Data Visualization'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'cms-systems',
        category: 'web',
        title: 'Systemy Zarządzania Treścią (CMS)',
        description: 'Zaawansowane systemy CMS z panelem administracyjnym',
        price: 5200,
        time: '5-7 tygodni',
        tags: ['CMS', 'Admin Panel', 'Content Management'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'social-platforms',
        category: 'web',
        title: 'Platformy Społecznościowe',
        description: 'Niestandardowe platformy społecznościowe z funkcjami community',
        price: 12000,
        time: '12-16 tygodni',
        tags: ['Social Media', 'Community', 'User Interaction'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'booking-systems',
        category: 'web',
        title: 'Systemy Booking i Rezerwacji',
        description: 'Kompleksowe systemy rezerwacji z kalendarzem i płatnościami',
        price: 4800,
        time: '5-7 tygodni',
        tags: ['Booking', 'Reservations', 'Calendar'],
        featured: false,
        priceRange: 'medium'
    },

    // Mobile Applications
    {
        id: 'ios-android-apps',
        category: 'mobile',
        title: 'Aplikacje Mobilne iOS & Android',
        description: 'Natywne aplikacje mobilne dla obu platform',
        price: 12000,
        time: '10-16 tygodni',
        tags: ['iOS', 'Android', 'Native Apps'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'progressive-web-apps',
        category: 'mobile',
        title: 'Progressive Web Apps (PWA)',
        description: 'Aplikacje webowe z funkcjonalnością mobilną',
        price: 5500,
        time: '6-8 tygodni',
        tags: ['PWA', 'Mobile-First', 'Offline'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'hybrid-apps',
        category: 'mobile',
        title: 'Aplikacje Hybrydowe',
        description: 'Cross-platform aplikacje z jedną bazą kodu',
        price: 8500,
        time: '8-12 tygodni',
        tags: ['Hybrid', 'Cross-platform', 'React Native'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'task-management-apps',
        category: 'mobile',
        title: 'Aplikacje do Zarządzania Zadaniami',
        description: 'Produktywne aplikacje do organizacji pracy i zadań',
        price: 4500,
        time: '5-7 tygodni',
        tags: ['Task Management', 'Productivity', 'Organization'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'fitness-health-apps',
        category: 'mobile',
        title: 'Aplikacje Fitness i Zdrowia',
        description: 'Aplikacje do monitorowania zdrowia i aktywności fizycznej',
        price: 6200,
        time: '6-9 tygodni',
        tags: ['Fitness', 'Health', 'Tracking'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'educational-apps',
        category: 'mobile',
        title: 'Aplikacje Edukacyjne',
        description: 'Interaktywne aplikacje do nauki i rozwoju umiejętności',
        price: 5800,
        time: '6-8 tygodni',
        tags: ['Education', 'Learning', 'Interactive'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'ecommerce-mobile-apps',
        category: 'mobile',
        title: 'Aplikacje E-commerce',
        description: 'Mobilne sklepy internetowe z płatnościami i zarządzaniem',
        price: 8200,
        time: '8-12 tygodni',
        tags: ['E-commerce', 'Shopping', 'Payments'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'social-mobile-apps',
        category: 'mobile',
        title: 'Aplikacje Społecznościowe',
        description: 'Mobilne platformy społecznościowe z czatem i mediami',
        price: 9500,
        time: '10-14 tygodni',
        tags: ['Social Media', 'Chat', 'Community'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'productivity-apps',
        category: 'mobile',
        title: 'Aplikacje Produktywności',
        description: 'Narzędzia mobilne do zwiększenia efektywności pracy',
        price: 4800,
        time: '5-7 tygodni',
        tags: ['Productivity', 'Efficiency', 'Tools'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'finance-management-apps',
        category: 'mobile',
        title: 'Aplikacje do Zarządzania Finansami',
        description: 'Mobilne narzędzia do kontroli budżetu i finansów osobistych',
        price: 5500,
        time: '6-8 tygodni',
        tags: ['Finance', 'Budget', 'Money Management'],
        featured: false,
        priceRange: 'medium'
    },

    // AI Automation & Chatbots
    {
        id: 'customer-support-chatbots',
        category: 'chatbots',
        title: 'Inteligentne Chatboty Obsługi Klienta',
        description: 'Zaawansowane chatboty z AI do obsługi klientów 24/7',
        price: 3500,
        time: '3-5 tygodni',
        tags: ['Chatbot', '24/7 Support', 'AI'],
        featured: true,
        priceRange: 'medium'
    },
    {
        id: 'sales-ai-assistants',
        category: 'chatbots',
        title: 'AI Asystenci Sprzedażowi',
        description: 'Inteligentni asystenci do wsparcia procesów sprzedażowych',
        price: 4200,
        time: '4-6 tygodni',
        tags: ['Sales Assistant', 'Lead Generation', 'AI'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'social-media-bots',
        category: 'chatbots',
        title: 'Boty do Social Media',
        description: 'Automatyzacja interakcji w mediach społecznościowych',
        price: 2800,
        time: '3-4 tygodnie',
        tags: ['Social Media', 'Automation', 'Bot'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'business-process-automation',
        category: 'chatbots',
        title: 'Automatyzacja Procesów Biznesowych',
        description: 'Kompleksowa automatyzacja workflow i procesów firmowych',
        price: 6500,
        time: '6-10 tygodni',
        tags: ['Process Automation', 'Workflow', 'Business'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'document-analysis-ai',
        category: 'chatbots',
        title: 'Systemy AI do Analizy Dokumentów',
        description: 'Automatyczna analiza i przetwarzanie dokumentów',
        price: 5500,
        time: '5-8 tygodni',
        tags: ['Document Analysis', 'OCR', 'AI Processing'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'nlp-tools',
        category: 'chatbots',
        title: 'Narzędzia do Przetwarzania Języka Naturalnego',
        description: 'Zaawansowane systemy NLP do analizy tekstu i komunikacji',
        price: 4800,
        time: '5-7 tygodni',
        tags: ['NLP', 'Text Analysis', 'Language Processing'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'ai-recommendation-systems',
        category: 'chatbots',
        title: 'AI Systemy Rekomendacji',
        description: 'Inteligentne systemy rekomendacji produktów i treści',
        price: 5200,
        time: '5-8 tygodni',
        tags: ['Recommendations', 'Machine Learning', 'Personalization'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'email-marketing-automation',
        category: 'chatbots',
        title: 'Automatyzacja Email Marketingu',
        description: 'AI-powered systemy do automatyzacji kampanii emailowych',
        price: 3800,
        time: '4-6 tygodni',
        tags: ['Email Marketing', 'Automation', 'Campaigns'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'ai-content-moderators',
        category: 'chatbots',
        title: 'AI Moderatorzy Treści',
        description: 'Automatyczne moderowanie treści i filtrowanie spam',
        price: 4200,
        time: '4-6 tygodni',
        tags: ['Content Moderation', 'Spam Filter', 'AI Safety'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'intelligent-faq-systems',
        category: 'chatbots',
        title: 'Inteligentne Systemy FAQ',
        description: 'Dynamiczne systemy FAQ z AI do automatycznych odpowiedzi',
        price: 3200,
        time: '3-5 tygodni',
        tags: ['FAQ', 'Knowledge Base', 'Auto-Response'],
        featured: false,
        priceRange: 'medium'
    },

    // Content Creation & Copywriting
    {
        id: 'ai-blog-generators',
        category: 'content',
        title: 'AI Generatory Treści Blogowych',
        description: 'Automatyczne tworzenie wysokiej jakości artykułów blogowych',
        price: 3200,
        time: '3-4 tygodnie',
        tags: ['Blog Generator', 'Content Creation', 'SEO'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'social-media-content-automation',
        category: 'content',
        title: 'Automatyczne Tworzenie Postów Social Media',
        description: 'Generowanie treści dla wszystkich platform społecznościowych',
        price: 2800,
        time: '2-3 tygodnie',
        tags: ['Social Media', 'Content Automation', 'Multi-platform'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'product-description-generators',
        category: 'content',
        title: 'Generatory Opisów Produktów',
        description: 'Automatyczne tworzenie atrakcyjnych opisów produktów',
        price: 2500,
        time: '2-3 tygodnie',
        tags: ['Product Descriptions', 'E-commerce', 'Copywriting'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'ai-copywriting-campaigns',
        category: 'content',
        title: 'AI Copywriting dla Kampanii Reklamowych',
        description: 'Profesjonalne teksty reklamowe generowane przez AI',
        price: 3500,
        time: '3-5 tygodni',
        tags: ['Copywriting', 'Ad Campaigns', 'Marketing'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'newsletter-generators',
        category: 'content',
        title: 'Systemy Tworzenia Newsletterów',
        description: 'Automatyczne generowanie profesjonalnych newsletterów',
        price: 2800,
        time: '3-4 tygodnie',
        tags: ['Newsletter', 'Email Marketing', 'Automation'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'press-release-generators',
        category: 'content',
        title: 'Generatory Komunikatów Prasowych',
        description: 'AI do tworzenia profesjonalnych komunikatów prasowych',
        price: 2200,
        time: '2-3 tygodnie',
        tags: ['Press Release', 'PR', 'Media'],
        featured: false,
        priceRange: 'low'
    },
    {
        id: 'seo-copywriting-tools',
        category: 'content',
        title: 'Narzędzia do SEO Copywriting',
        description: 'Zaawansowane narzędzia do optymalizacji treści pod SEO',
        price: 3800,
        time: '4-5 tygodni',
        tags: ['SEO', 'Copywriting', 'Optimization'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'ai-video-scriptwriters',
        category: 'content',
        title: 'AI Scenarzyści Video',
        description: 'Automatyczne tworzenie scenariuszy do filmów i prezentacji',
        price: 3200,
        time: '3-4 tygodnie',
        tags: ['Video Scripts', 'Storytelling', 'Content'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'content-localization-systems',
        category: 'content',
        title: 'Systemy Lokalizacji Treści',
        description: 'AI do adaptacji treści na różne rynki i kultury',
        price: 4200,
        time: '4-6 tygodni',
        tags: ['Localization', 'Translation', 'Cultural Adaptation'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'content-strategy-generators',
        category: 'content',
        title: 'Generatory Content Strategy',
        description: 'AI do planowania strategii treści i kalendarza publikacji',
        price: 3500,
        time: '3-5 tygodni',
        tags: ['Content Strategy', 'Planning', 'Calendar'],
        featured: false,
        priceRange: 'medium'
    },

    // Business Automation
    {
        id: 'crm-systems-ai',
        category: 'automation',
        title: 'Systemy CRM z AI',
        description: 'Zaawansowane systemy CRM z inteligentnymi funkcjami',
        price: 6500,
        time: '6-10 tygodni',
        tags: ['CRM', 'AI Analytics', 'Customer Management'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'invoice-automation',
        category: 'automation',
        title: 'Automatyzacja Fakturowania',
        description: 'Kompletna automatyzacja procesów fakturowania',
        price: 4200,
        time: '4-6 tygodni',
        tags: ['Invoice Automation', 'Billing', 'Finance'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'hr-management-tools',
        category: 'automation',
        title: 'HR Management Tools',
        description: 'Kompleksowe narzędzia do zarządzania zasobami ludzkimi',
        price: 5800,
        time: '6-8 tygodni',
        tags: ['HR', 'Employee Management', 'Recruitment'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'project-management-systems',
        category: 'automation',
        title: 'Systemy Zarządzania Projektami',
        description: 'Zaawansowane platformy do zarządzania projektami i zespołami',
        price: 5200,
        time: '5-8 tygodni',
        tags: ['Project Management', 'Team Collaboration', 'Workflow'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'lead-generation-tools',
        category: 'automation',
        title: 'Narzędzia do Lead Generation',
        description: 'Automatyczne systemy pozyskiwania i zarządzania leadami',
        price: 4500,
        time: '4-7 tygodni',
        tags: ['Lead Generation', 'Sales', 'Automation'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'customer-support-platforms',
        category: 'automation',
        title: 'Customer Support Platforms',
        description: 'Kompleksowe platformy obsługi klienta z ticketing',
        price: 4800,
        time: '5-7 tygodni',
        tags: ['Customer Support', 'Ticketing', 'Help Desk'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'inventory-management-systems',
        category: 'automation',
        title: 'Inventory Management Systems',
        description: 'Systemy zarządzania magazynem i stanami magazynowymi',
        price: 5500,
        time: '6-8 tygodni',
        tags: ['Inventory', 'Warehouse', 'Stock Management'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'financial-tracking-tools',
        category: 'automation',
        title: 'Financial Tracking Tools',
        description: 'Narzędzia do śledzenia i analizy finansów biznesowych',
        price: 4200,
        time: '4-6 tygodni',
        tags: ['Finance', 'Tracking', 'Analytics'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'contract-management-systems',
        category: 'automation',
        title: 'Contract Management Systems',
        description: 'Systemy zarządzania umowami i dokumentami prawnymi',
        price: 5800,
        time: '6-9 tygodni',
        tags: ['Contract Management', 'Legal', 'Document Management'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'workflow-automation-builders',
        category: 'automation',
        title: 'Workflow Automation Builders',
        description: 'Konstruktory do budowania automatycznych procesów biznesowych',
        price: 6200,
        time: '6-10 tygodni',
        tags: ['Workflow', 'Process Builder', 'Automation'],
        featured: false,
        priceRange: 'high'
    },

    // Audio & Voice Solutions
    {
        id: 'audio-transcription-systems',
        category: 'audio',
        title: 'Systemy Transkrypcji Audio',
        description: 'Automatyczna transkrypcja nagrań audio z wysoką dokładnością',
        price: 3500,
        time: '3-5 tygodni',
        tags: ['Transcription', 'Audio Processing', 'Speech-to-Text'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'voice-to-text-apps',
        category: 'audio',
        title: 'Voice-to-Text Aplikacje',
        description: 'Aplikacje do konwersji mowy na tekst w czasie rzeczywistym',
        price: 4200,
        time: '4-6 tygodni',
        tags: ['Voice-to-Text', 'Real-time', 'Speech Recognition'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'ai-voice-assistants',
        category: 'audio',
        title: 'AI Asystenci Głosowi',
        description: 'Inteligentni asystenci głosowi z rozpoznawaniem mowy',
        price: 5200,
        time: '5-8 tygodni',
        tags: ['Voice Assistant', 'AI', 'Speech Recognition'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'audio-editing-tools',
        category: 'audio',
        title: 'Narzędzia do Edycji Audio',
        description: 'AI-powered narzędzia do automatycznej edycji audio',
        price: 3800,
        time: '4-6 tygodni',
        tags: ['Audio Editing', 'AI Processing', 'Sound Enhancement'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'speech-synthesis-systems',
        category: 'audio',
        title: 'Systemy Syntezy Mowy',
        description: 'Zaawansowane systemy text-to-speech z naturalnymi głosami',
        price: 4500,
        time: '5-7 tygodni',
        tags: ['Text-to-Speech', 'Voice Synthesis', 'Natural Voice'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'pronunciation-learning-apps',
        category: 'audio',
        title: 'Aplikacje do Nauki Wymowy',
        description: 'Interaktywne aplikacje do nauki poprawnej wymowy języków',
        price: 4200,
        time: '4-6 tygodni',
        tags: ['Pronunciation', 'Language Learning', 'Speech Training'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'podcast-ai-tools',
        category: 'audio',
        title: 'Podcastowe Narzędzia AI',
        description: 'Narzędzia AI do automatyzacji produkcji podcastów',
        price: 3500,
        time: '3-5 tygodni',
        tags: ['Podcast', 'Audio Production', 'Automation'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'speech-analysis-systems',
        category: 'audio',
        title: 'Systemy Analizy Mowy',
        description: 'Zaawansowana analiza mowy pod kątem emocji i intencji',
        price: 5500,
        time: '6-8 tygodni',
        tags: ['Speech Analysis', 'Emotion Detection', 'Voice Analytics'],
        featured: false,
        priceRange: 'high'
    },
    {
        id: 'voice-command-interfaces',
        category: 'audio',
        title: 'Voice Command Interfaces',
        description: 'Interfejsy sterowane głosem dla aplikacji i systemów',
        price: 4800,
        time: '5-7 tygodni',
        tags: ['Voice Commands', 'Interface', 'Hands-free'],
        featured: false,
        priceRange: 'medium'
    },
    {
        id: 'audio-content-generators',
        category: 'audio',
        title: 'Audio Content Generators',
        description: 'AI do automatycznego generowania treści audio i muzyki',
        price: 5200,
        time: '5-8 tygodni',
        tags: ['Audio Generation', 'Music AI', 'Content Creation'],
        featured: false,
        priceRange: 'high'
    }
];



let currentServices = [];
let displayedServices = 0;
const servicesPerPage = 9;
let currentView = 'grid';
let currentWishlistCategory = 'saas';

// Initialize services page
document.addEventListener('DOMContentLoaded', function() {
    initializeServices();
    loadQuickSolutions();
    loadWishlist();
    setupEventListeners();

    // Wait a bit for DOM to be fully ready, then animate stats and update counts
    setTimeout(() => {
        animateStats();
        updateCategoryCounts();
    }, 100);
});

function setupEventListeners() {
    // Filter event listeners
    const categoryFilter = document.getElementById('categoryFilter');
    const priceFilter = document.getElementById('priceFilter');
    const timeFilter = document.getElementById('timeFilter');
    const searchInput = document.getElementById('searchInput');
    const sortSelect = document.getElementById('sortSelect');

    if (categoryFilter) categoryFilter.addEventListener('change', filterServices);
    if (priceFilter) priceFilter.addEventListener('change', filterServices);
    if (timeFilter) timeFilter.addEventListener('change', filterServices);
    if (searchInput) searchInput.addEventListener('input', searchServices);
    if (sortSelect) sortSelect.addEventListener('change', sortServices);
}

function animateStats() {
    // Calculate total services count
    const totalServices = calculateTotalServicesCount();

    // Update total services count
    const totalServicesElement = document.getElementById('totalServicesCount');
    if (totalServicesElement) {
        animateNumber(totalServicesElement, totalServices);
    }

    // Calculate and update total categories count
    const totalCategories = calculateTotalCategoriesCount();
    const totalCategoriesElement = document.getElementById('totalCategoriesCount');
    if (totalCategoriesElement) {
        animateNumber(totalCategoriesElement, totalCategories);
    }

    // Animate other stats
    const stats = document.querySelectorAll('.stat-number[data-target]');
    stats.forEach(stat => {
        const target = parseInt(stat.getAttribute('data-target'));
        animateNumber(stat, target);
    });

    // Update category counts
    updateCategoryCounts();
}

function calculateTotalServicesCount() {
    let total = 0;

    // Count services from servicesData (basic catalog)
    total += servicesData.length;

    // Count quick solutions (express services)
    total += quickSolutionsData.length;

    // Count wishlist items (premium services)
    Object.keys(wishlistData).forEach(category => {
        total += wishlistData[category].length;
    });

    // Total: 60 (servicesData) + 6 (quickSolutions) + 97 (wishlist) = 163+ services
    return total;
}

function calculateTotalCategoriesCount() {
    // Return the count of displayed categories (the ones shown in the UI)
    const displayedCategories = ['web', 'mobile', 'chatbots', 'content', 'automation', 'audio'];
    return displayedCategories.length;
}

function animateNumber(element, target) {
    let current = 0;
    const increment = target / 50;
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current) + (target >= 100 ? '+' : '');
    }, 30);
}

function updateCategoryCounts() {
    // Initialize counts for all visible categories - only count basic services + quick solutions
    const categoryCounts = {
        'web': 0,
        'mobile': 0,
        'chatbots': 0,
        'content': 0,
        'automation': 0,
        'audio': 0
    };

    // Count from servicesData (basic catalog services)
    servicesData.forEach(service => {
        if (categoryCounts.hasOwnProperty(service.category)) {
            categoryCounts[service.category]++;
        }
    });

    // Count from quickSolutionsData (express services)
    quickSolutionsData.forEach(solution => {
        const category = getCategoryFromQuickSolution(solution.category);
        if (categoryCounts.hasOwnProperty(category)) {
            categoryCounts[category]++;
        }
    });

    // Note: Wishlist items are premium services and not counted in basic categories
    // They are displayed separately in the wishlist section

    // Update category count displays with real counts
    Object.keys(categoryCounts).forEach(category => {
        const categoryCard = document.querySelector(`[data-category="${category}"] .category-count`);
        if (categoryCard) {
            categoryCard.textContent = `${categoryCounts[category]} usług`;
        }
    });
}

function getCategoryFromQuickSolution(category) {
    const mapping = {
        'Web Applications': 'web',
        'Mobile-First': 'mobile',
        'AI Automation': 'automation',
        'Content Creation': 'content',
        'Audio & Voice': 'audio'
    };
    return mapping[category] || 'web';
}

function mapWishlistToDisplayCategory(wishlistCategory) {
    const mapping = {
        'saas': 'web',
        'mobile': 'mobile',
        'desktop': 'automation',
        'chatbots': 'chatbots',
        'audio': 'audio',
        'content': 'content',
        'virtual': 'web',
        'visual': 'content',
        'documents': 'automation'
    };
    return mapping[wishlistCategory] || 'web';
}

function initializeServices() {
    currentServices = [...servicesData];
    displayedServices = 0;
    loadServices();
    updateLoadMoreButton();
}

function loadQuickSolutions() {
    const grid = document.getElementById('quickSolutionsGrid');
    if (!grid) return;

    grid.innerHTML = '';

    // Show first 6 quick solutions
    quickSolutionsData.slice(0, 6).forEach(solution => {
        const card = createQuickSolutionCard(solution);
        grid.appendChild(card);
    });
}

function createQuickSolutionCard(solution) {
    const card = document.createElement('div');
    card.className = 'quick-solution-card';

    card.innerHTML = `
        <div class="solution-header">
            <div class="solution-category">${solution.category}</div>
            <div class="solution-time">${solution.time}</div>
        </div>
        <h3 class="solution-title">${solution.title}</h3>
        <p class="solution-description">${solution.description}</p>
        <div class="solution-tech">
            ${solution.tech.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
        </div>
        <div class="solution-pricing">
            <span class="price-range">${solution.priceRange}</span>
        </div>
        <div class="solution-includes">
            <strong>Includes:</strong> ${solution.includes}
        </div>
        <div class="service-actions">
            <button class="btn-service primary" onclick="orderQuickSolution('${solution.id}')">
                <i class="fas fa-rocket"></i>
                Zamów Express
            </button>
            <button class="btn-service secondary" onclick="viewQuickSolutionDetails('${solution.id}')">
                <i class="fas fa-info-circle"></i>
                Szczegóły
            </button>
        </div>
    `;

    return card;
}

function loadWishlist() {
    showWishlistCategory(currentWishlistCategory);
}

function showWishlistCategory(category) {
    currentWishlistCategory = category;

    // Update tab states
    document.querySelectorAll('.wishlist-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-category="${category}"]`).classList.add('active');

    // Load content
    const content = document.getElementById('wishlistContent');
    if (!content) return;

    const items = wishlistData[category] || [];

    content.innerHTML = `
        <div class="wishlist-grid">
            ${items.map(item => createWishlistCard(item)).join('')}
        </div>
    `;
}

function createWishlistCard(item) {
    return `
        <div class="wishlist-item">
            <div class="wishlist-header">
                <h3 class="wishlist-title">${item.title}</h3>
                <div class="wishlist-price">${item.priceRange}</div>
            </div>
            <p class="wishlist-description">${item.description}</p>
            <div class="wishlist-tech">
                <div class="wishlist-tech-label">Technologie:</div>
                <div class="wishlist-tech-tags">
                    ${item.tech.map(tech => `<span class="wishlist-tech-tag">${tech}</span>`).join('')}
                </div>
            </div>
            <div class="wishlist-meta">
                <div class="wishlist-time">${item.time}</div>
                <button class="btn-wishlist" onclick="orderWishlistItem('${item.id}')">
                    <i class="fas fa-crown"></i>
                    Zapytaj o projekt
                </button>
            </div>
        </div>
    `;
}



// Category filtering
function filterByCategory(category) {
    // Update category card states
    document.querySelectorAll('.category-card').forEach(card => {
        card.classList.remove('active');
    });
    const categoryCard = document.querySelector(`[data-category="${category}"]`);
    if (categoryCard) {
        categoryCard.classList.add('active');
    }

    // Update filter and reload services
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        categoryFilter.value = category;
    }

    // Filter services and scroll to catalog
    filterServices();

    // Scroll to services catalog
    const catalogSection = document.querySelector('.services-catalog');
    if (catalogSection) {
        catalogSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

function showAllQuickSolutions() {
    // Create modal with all quick solutions
    const modal = document.createElement('div');
    modal.className = 'service-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="closeServiceModal()"></div>
        <div class="modal-content glass-effect" style="max-width: 1000px;">
            <div class="modal-header">
                <h2>Wszystkie Szybkie Rozwiązania AI</h2>
                <button class="modal-close" onclick="closeServiceModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">
                    Kompletna lista ekspresowych rozwiązań AI gotowych do wdrożenia w 1-4 tygodnie
                </p>
                <div class="quick-solutions-grid">
                    ${quickSolutionsData.map(solution => createQuickSolutionCard(solution).outerHTML).join('')}
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
}

function sortServices() {
    const sortValue = document.getElementById('sortSelect').value;

    switch (sortValue) {
        case 'price-asc':
            currentServices.sort((a, b) => a.price - b.price);
            break;
        case 'price-desc':
            currentServices.sort((a, b) => b.price - a.price);
            break;
        case 'time-asc':
            currentServices.sort((a, b) => {
                const timeA = parseInt(a.time.match(/\d+/)[0]);
                const timeB = parseInt(b.time.match(/\d+/)[0]);
                return timeA - timeB;
            });
            break;
        case 'popularity':
            currentServices.sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
            break;
        default:
            currentServices = [...servicesData];
            break;
    }

    resetServicesDisplay();
}

function searchForService(searchTerm) {
    document.getElementById('searchInput').value = searchTerm;
    searchServices();

    // Scroll to services section
    document.querySelector('.services-catalog').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

function closeServiceModal() {
    const modal = document.querySelector('.service-modal');
    if (modal) {
        modal.remove();
        document.body.style.overflow = '';
    }
}

function orderQuickSolution(solutionId) {
    const solution = quickSolutionsData.find(s => s.id === solutionId);
    if (solution) {
        showContactOptions(solution, 'quick');
    }
}

function showContactOptions(item, type = 'service') {
    const modal = document.createElement('div');
    modal.className = 'service-modal';

    const itemTitle = item.title;
    const itemPrice = item.priceRange || `${item.price} PLN`;

    modal.innerHTML = `
        <div class="modal-overlay" onclick="closeServiceModal()"></div>
        <div class="modal-content glass-effect" style="max-width: 500px;">
            <div class="modal-header">
                <h2>Wybierz sposób kontaktu</h2>
                <button class="modal-close" onclick="closeServiceModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="contact-item-info">
                    <h3>${itemTitle}</h3>
                    <p class="contact-price">${itemPrice}</p>
                </div>

                <div class="contact-options">
                    <button class="contact-btn telegram" onclick="contactViaTelegram('${item.id}', '${type}')">
                        <i class="fab fa-telegram"></i>
                        <div>
                            <strong>Telegram</strong>
                            <span>Szybka odpowiedź • Preferowane</span>
                        </div>
                    </button>

                    <button class="contact-btn whatsapp" onclick="contactViaWhatsApp('${item.id}', '${type}')">
                        <i class="fab fa-whatsapp"></i>
                        <div>
                            <strong>WhatsApp</strong>
                            <span>Rozmowa głosowa dostępna</span>
                        </div>
                    </button>

                    <button class="contact-btn email" onclick="contactViaEmail('${item.id}', '${type}')">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <strong>Email</strong>
                            <span>Szczegółowa wycena</span>
                        </div>
                    </button>
                </div>

                <div class="contact-note">
                    <i class="fas fa-info-circle"></i>
                    <span>Odpowiadamy w ciągu 2-4 godzin w dni robocze</span>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
}

function contactViaTelegram(itemId, type) {
    const item = findItemById(itemId, type);
    if (item) {
        const message = `🚀 Cześć! Jestem zainteresowany ${type === 'quick' ? 'szybkim rozwiązaniem' : 'usługą'}:

📋 *${item.title}*
💰 Budżet: ${item.priceRange || item.price + ' PLN'}
⏱️ Czas: ${item.time || 'do ustalenia'}

Czy możemy omówić szczegóły i następne kroki?`;

        const encodedMessage = encodeURIComponent(message);
        window.open(`https://t.me/pixel_garage?text=${encodedMessage}`, '_blank');
        closeServiceModal();
    }
}

function contactViaWhatsApp(itemId, type) {
    const item = findItemById(itemId, type);
    if (item) {
        const message = `🚀 Cześć! Jestem zainteresowany ${type === 'quick' ? 'szybkim rozwiązaniem' : 'usługą'}:

📋 ${item.title}
💰 Budżet: ${item.priceRange || item.price + ' PLN'}
⏱️ Czas: ${item.time || 'do ustalenia'}

Czy możemy omówić szczegóły? Preferuję ${type === 'quick' ? 'szybkie wdrożenie' : 'kompleksowe rozwiązanie'}.`;

        const encodedMessage = encodeURIComponent(message);
        window.open(`https://wa.me/34645577385?text=${encodedMessage}`, '_blank');
        closeServiceModal();
    }
}

function contactViaEmail(itemId, type) {
    const item = findItemById(itemId, type);
    if (item) {
        const subject = `Zapytanie o ${type === 'quick' ? 'szybkie rozwiązanie' : 'usługę'}: ${item.title}`;
        const body = `Dzień dobry,

Jestem zainteresowany ${type === 'quick' ? 'szybkim rozwiązaniem' : 'usługą'}: ${item.title}

Szczegóły:
- Budżet: ${item.priceRange || item.price + ' PLN'}
- Czas realizacji: ${item.time || 'do ustalenia'}
${item.tech ? '- Technologie: ' + item.tech.join(', ') : ''}

Proszę o kontakt w celu omówienia szczegółów projektu.

Pozdrawiam`;

        const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
        window.location.href = mailtoLink;
        closeServiceModal();
    }
}

function findItemById(itemId, type) {
    if (type === 'quick') {
        return quickSolutionsData.find(item => item.id === itemId);
    } else if (type === 'wishlist') {
        for (const category in wishlistData) {
            const item = wishlistData[category].find(i => i.id === itemId);
            if (item) return item;
        }
    } else {
        return servicesData.find(item => item.id === itemId);
    }
    return null;
}

function viewQuickSolutionDetails(solutionId) {
    const solution = quickSolutionsData.find(s => s.id === solutionId);
    if (solution) {
        showQuickSolutionModal(solution);
    }
}

function orderWishlistItem(itemId) {
    // Find item in all categories
    let item = null;
    for (const category in wishlistData) {
        item = wishlistData[category].find(i => i.id === itemId);
        if (item) break;
    }

    if (item) {
        showContactOptions(item, 'wishlist');
    }
}

function showQuickSolutionModal(solution) {
    const modal = document.createElement('div');
    modal.className = 'service-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="closeServiceModal()"></div>
        <div class="modal-content glass-effect">
            <div class="modal-header">
                <h2>${solution.title}</h2>
                <button class="modal-close" onclick="closeServiceModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="service-category">${solution.category}</div>
                <p class="service-description">${solution.description}</p>
                <div class="service-details">
                    <div class="detail-item">
                        <strong>Cena:</strong> ${solution.priceRange}
                    </div>
                    <div class="detail-item">
                        <strong>Czas realizacji:</strong> ${solution.time}
                    </div>
                    <div class="detail-item">
                        <strong>Technologie:</strong> ${solution.tech.join(', ')}
                    </div>
                    <div class="detail-item">
                        <strong>Includes:</strong> ${solution.includes}
                    </div>
                </div>
                <div class="modal-actions">
                    <button class="btn-primary" onclick="orderQuickSolution('${solution.id}')">
                        <i class="fas fa-rocket"></i>
                        Zamów Express
                    </button>
                    <button class="btn-secondary" onclick="requestQuote('${solution.id}')">
                        <i class="fas fa-calculator"></i>
                        Zapytaj o wycenę
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
}

// Load and display services
function filterServices() {
    const categoryFilter = document.getElementById('categoryFilter');
    const priceFilter = document.getElementById('priceFilter');
    const timeFilter = document.getElementById('timeFilter');
    const searchInput = document.getElementById('searchInput');

    const category = categoryFilter ? categoryFilter.value : '';
    const price = priceFilter ? priceFilter.value : '';
    const time = timeFilter ? timeFilter.value : '';
    const search = searchInput ? searchInput.value.toLowerCase() : '';

    currentServices = servicesData.filter(service => {
        // Category filter
        if (category && service.category !== category) return false;

        // Price filter
        if (price) {
            switch (price) {
                case 'low':
                    if (service.price > 3000) return false;
                    break;
                case 'medium':
                    if (service.price < 3000 || service.price > 6000) return false;
                    break;
                case 'high':
                    if (service.price < 6000) return false;
                    break;
            }
        }

        // Time filter
        if (time) {
            const serviceWeeks = parseInt(service.time.match(/\d+/)[0]);
            switch (time) {
                case 'fast':
                    if (serviceWeeks > 3) return false;
                    break;
                case 'medium':
                    if (serviceWeeks < 4 || serviceWeeks > 7) return false;
                    break;
                case 'long':
                    if (serviceWeeks < 8) return false;
                    break;
            }
        }

        // Search filter
        if (search) {
            return service.title.toLowerCase().includes(search) ||
                   service.description.toLowerCase().includes(search) ||
                   service.tags.some(tag => tag.toLowerCase().includes(search));
        }

        return true;
    });

    resetServicesDisplay();
}

function searchServices() {
    filterServices();
}

function resetFilters() {
    const categoryFilter = document.getElementById('categoryFilter');
    const priceFilter = document.getElementById('priceFilter');
    const timeFilter = document.getElementById('timeFilter');
    const searchInput = document.getElementById('searchInput');

    if (categoryFilter) categoryFilter.value = '';
    if (priceFilter) priceFilter.value = '';
    if (timeFilter) timeFilter.value = '';
    if (searchInput) searchInput.value = '';

    // Remove active state from category cards
    document.querySelectorAll('.category-card').forEach(card => {
        card.classList.remove('active');
    });

    currentServices = [...servicesData];
    resetServicesDisplay();
}

function resetServicesDisplay() {
    const servicesGrid = document.getElementById('servicesGrid');
    if (servicesGrid) {
        servicesGrid.innerHTML = '';
    }
    displayedServices = 0;
    loadServices();
}

function loadServices() {
    const servicesGrid = document.getElementById('servicesGrid');
    if (!servicesGrid) return;

    const servicesToShow = currentServices.slice(displayedServices, displayedServices + servicesPerPage);

    servicesToShow.forEach(service => {
        const serviceElement = createServiceElement(service);
        servicesGrid.appendChild(serviceElement);
    });

    displayedServices += servicesToShow.length;
    updateLoadMoreButton();
}

function createServiceElement(service) {
    const element = document.createElement('div');
    element.className = `service-item ${currentView === 'list' ? 'list-view' : ''}`;

    element.innerHTML = `
        <div class="service-category">${getCategoryName(service.category)}</div>
        <h3 class="service-title">${service.title}</h3>
        <p class="service-description">${service.description}</p>
        <div class="service-meta">
            <span class="service-price">${service.price} PLN</span>
            <span class="service-time">${service.time}</span>
        </div>
        <div class="service-tags">
            ${service.tags.map(tag => `<span class="service-tag">${tag}</span>`).join('')}
        </div>
        <div class="service-actions">
            <button class="btn-service primary" onclick="orderService('${service.id}')">
                <i class="fas fa-shopping-cart"></i>
                Zamów
            </button>
            <button class="btn-service secondary" onclick="viewServiceDetails('${service.id}')">
                <i class="fas fa-info-circle"></i>
                Szczegóły
            </button>
        </div>
    `;

    return element;
}

function getCategoryName(category) {
    const categories = {
        'web': 'Web Applications',
        'mobile': 'Mobile & PWA',
        'chatbots': 'Chatboty & AI',
        'content': 'Content Creation',
        'automation': 'Business Automation',
        'audio': 'Audio & Voice',
        'analytics': 'Analytics & Data'
    };
    return categories[category] || category;
}

function updateLoadMoreButton() {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (!loadMoreBtn) return;

    if (displayedServices >= currentServices.length) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'block';
        loadMoreBtn.innerHTML = `
            <i class="fas fa-plus"></i>
            Załaduj więcej usług (${currentServices.length - displayedServices} pozostało)
        `;
    }
}

function loadMoreServices() {
    loadServices();
}

function toggleView(view) {
    currentView = view;

    // Update button states
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    const activeBtn = document.querySelector(`[data-view="${view}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }

    // Update grid class
    const grid = document.getElementById('servicesGrid');
    if (grid) {
        if (view === 'list') {
            grid.classList.add('list-view');
        } else {
            grid.classList.remove('list-view');
        }
    }

    resetServicesDisplay();
}

function orderService(serviceId) {
    const service = servicesData.find(s => s.id === serviceId);
    if (service) {
        showContactOptions(service, 'service');
    }
}

function viewServiceDetails(serviceId) {
    const service = servicesData.find(s => s.id === serviceId);
    if (service) {
        showServiceModal(service);
    }
}

function showServiceModal(service) {
    const modal = document.createElement('div');
    modal.className = 'service-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="closeServiceModal()"></div>
        <div class="modal-content glass-effect">
            <div class="modal-header">
                <h2>${service.title}</h2>
                <button class="modal-close" onclick="closeServiceModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="service-category">${getCategoryName(service.category)}</div>
                <p class="service-description">${service.description}</p>
                <div class="service-details">
                    <div class="detail-item">
                        <strong>Cena:</strong> ${service.price} PLN
                    </div>
                    <div class="detail-item">
                        <strong>Czas realizacji:</strong> ${service.time}
                    </div>
                    <div class="detail-item">
                        <strong>Kategoria:</strong> ${getCategoryName(service.category)}
                    </div>
                    <div class="detail-item">
                        <strong>Technologie:</strong> ${service.tags.join(', ')}
                    </div>
                </div>
                <div class="modal-actions">
                    <button class="btn-primary" onclick="orderService('${service.id}')">
                        <i class="fas fa-shopping-cart"></i>
                        Zamów usługę
                    </button>
                    <button class="btn-secondary" onclick="requestQuote('${service.id}')">
                        <i class="fas fa-calculator"></i>
                        Zapytaj o wycenę
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
}

function requestQuote(serviceId) {
    const service = servicesData.find(s => s.id === serviceId);
    if (service) {
        contactViaEmail(serviceId, 'service');
    }
}

// Advanced search function
function performAdvancedSearch() {
    const category = document.getElementById('categoryFilter').value;
    const price = document.getElementById('priceFilter').value;
    const time = document.getElementById('timeFilter').value;
    const query = document.getElementById('searchInput').value;

    // Build URL parameters
    const params = new URLSearchParams();
    if (category) params.set('category', category);
    if (price) params.set('price', price);
    if (time) params.set('time', time);
    if (query) params.set('query', query);

    // Navigate to search page
    const searchURL = 'search.html' + (params.toString() ? '?' + params.toString() : '');
    window.location.href = searchURL;
}

// Enhanced search functionality for main page
function searchServices() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput && searchInput.value.trim()) {
        // If there's a search query, redirect to advanced search
        performAdvancedSearch();
    } else {
        // Otherwise, filter normally
        filterServices();
    }
}

// Create service element
function createServiceElement(service) {
    const serviceDiv = document.createElement('div');
    serviceDiv.className = `service-item ${currentView === 'list' ? 'list-view' : ''}`;
    serviceDiv.setAttribute('data-category', service.category);
    serviceDiv.setAttribute('data-price-range', service.priceRange);
    
    const timeRange = getTimeRange(service.time);
    serviceDiv.setAttribute('data-time-range', timeRange);
    
    serviceDiv.innerHTML = `
        <div class="service-content">
            <div class="service-category">${getCategoryName(service.category)}</div>
            <h3 class="service-title">${service.title}</h3>
            <p class="service-description">${service.description}</p>
            <div class="service-meta">
                <div class="service-price">${service.price.toLocaleString()} PLN</div>
                <div class="service-time">⏱️ ${service.time}</div>
            </div>
            <div class="service-tags">
                ${service.tags.map(tag => `<span class="service-tag">${tag}</span>`).join('')}
            </div>
            <div class="service-actions">
                <button class="btn-service primary" onclick="orderService('${service.id}')">
                    <i class="fas fa-shopping-cart"></i>
                    Zamów
                </button>
                <button class="btn-service secondary" onclick="viewServiceDetails('${service.id}')">
                    <i class="fas fa-info-circle"></i>
                    Szczegóły
                </button>
            </div>
        </div>
    `;
    
    return serviceDiv;
}

// Get category display name
function getCategoryName(category) {
    const categoryNames = {
        'web': 'Web Applications',
        'mobile': 'Mobile Apps',
        'chatbots': 'Chatboty & AI',
        'content': 'Content Creation',
        'automation': 'Automatyzacja',
        'audio': 'Audio & Voice',
        'visual': 'Image & Visual',
        'analytics': 'Analytics',
        'education': 'Edukacja',
        'health': 'Zdrowie',
        'realestate': 'Nieruchomości',
        'ecommerce': 'E-commerce'
    };
    return categoryNames[category] || category;
}

// Get time range for filtering
function getTimeRange(timeString) {
    if (timeString.includes('1-2')) return 'fast';
    if (timeString.includes('3-') || timeString.includes('4-') || timeString.includes('5-') || 
        timeString.includes('6-') || timeString.includes('7-') || timeString.includes('8-')) return 'medium';
    return 'long';
}

// Filter services
function filterServices() {
    const categoryFilter = document.getElementById('categoryFilter').value;
    const priceFilter = document.getElementById('priceFilter').value;
    const timeFilter = document.getElementById('timeFilter').value;
    
    currentServices = servicesData.filter(service => {
        const categoryMatch = !categoryFilter || service.category === categoryFilter;
        const priceMatch = !priceFilter || service.priceRange === priceFilter;
        const timeMatch = !timeFilter || getTimeRange(service.time) === timeFilter;
        
        return categoryMatch && priceMatch && timeMatch;
    });
    
    resetServicesDisplay();
}

// Search services
function searchServices() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    
    if (!searchTerm) {
        filterServices();
        return;
    }
    
    currentServices = servicesData.filter(service => {
        return service.title.toLowerCase().includes(searchTerm) ||
               service.description.toLowerCase().includes(searchTerm) ||
               service.tags.some(tag => tag.toLowerCase().includes(searchTerm));
    });
    
    resetServicesDisplay();
}

// Reset filters
function resetFilters() {
    document.getElementById('categoryFilter').value = '';
    document.getElementById('priceFilter').value = '';
    document.getElementById('timeFilter').value = '';
    document.getElementById('searchInput').value = '';
    
    currentServices = [...servicesData];
    resetServicesDisplay();
}

// Reset services display
function resetServicesDisplay() {
    const servicesGrid = document.getElementById('servicesGrid');
    servicesGrid.innerHTML = '';
    displayedServices = 0;
    loadServices();
}

// Load more services
function loadMoreServices() {
    loadServices();
}

// Update load more button
function updateLoadMoreButton() {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (displayedServices >= currentServices.length) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'block';
    }
}

// Toggle view (grid/list)
function toggleView(view) {
    currentView = view;
    
    // Update button states
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');
    
    // Update grid class
    const servicesGrid = document.getElementById('servicesGrid');
    if (view === 'list') {
        servicesGrid.classList.add('list-view');
    } else {
        servicesGrid.classList.remove('list-view');
    }
    
    // Update service items
    document.querySelectorAll('.service-item').forEach(item => {
        if (view === 'list') {
            item.classList.add('list-view');
        } else {
            item.classList.remove('list-view');
        }
    });
}

// Order service
function orderService(serviceId) {
    const service = servicesData.find(s => s.id === serviceId);
    if (service) {
        const message = `Cześć! Jestem zainteresowany usługą: ${service.title} (${service.price} PLN). Czy możemy omówić szczegóły?`;
        const encodedMessage = encodeURIComponent(message);
        window.open(`https://t.me/pixelgarage_contact?text=${encodedMessage}`, '_blank');
    }
}

// View service details
function viewServiceDetails(serviceId) {
    const service = servicesData.find(s => s.id === serviceId);
    if (service) {
        // Create modal or redirect to detailed page
        showServiceModal(service);
    }
}

// Show service modal
function showServiceModal(service) {
    const modal = document.createElement('div');
    modal.className = 'service-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="closeServiceModal()"></div>
        <div class="modal-content glass-effect">
            <div class="modal-header">
                <h2>${service.title}</h2>
                <button class="modal-close" onclick="closeServiceModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="service-category">${getCategoryName(service.category)}</div>
                <p class="service-description">${service.description}</p>
                <div class="service-details">
                    <div class="detail-item">
                        <strong>Cena:</strong> ${service.price.toLocaleString()} PLN
                    </div>
                    <div class="detail-item">
                        <strong>Czas realizacji:</strong> ${service.time}
                    </div>
                    <div class="detail-item">
                        <strong>Technologie:</strong> ${service.tags.join(', ')}
                    </div>
                </div>
                <div class="modal-actions">
                    <button class="btn-primary" onclick="orderService('${service.id}')">
                        <i class="fas fa-shopping-cart"></i>
                        Zamów usługę
                    </button>
                    <button class="btn-secondary" onclick="requestQuote('${service.id}')">
                        <i class="fas fa-calculator"></i>
                        Zapytaj o wycenę
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
}

// Close service modal
function closeServiceModal() {
    const modal = document.querySelector('.service-modal');
    if (modal) {
        modal.remove();
        document.body.style.overflow = 'auto';
    }
}

// Request quote
function requestQuote(serviceId) {
    const service = servicesData.find(s => s.id === serviceId);
    if (service) {
        const message = `Cześć! Chciałbym otrzymać szczegółową wycenę dla usługi: ${service.title}. Czy możemy omówić moje wymagania?`;
        const encodedMessage = encodeURIComponent(message);
        window.open(`https://t.me/pixelgarage_contact?text=${encodedMessage}`, '_blank');
    }
}

// Request custom service
function requestCustomService() {
    const message = `Cześć! Potrzebuję dedykowanego rozwiązania, które nie znajduje się w Waszym katalogu. Czy możemy omówić moje wymagania?`;
    const encodedMessage = encodeURIComponent(message);
    window.open(`https://t.me/pixelgarage_contact?text=${encodedMessage}`, '_blank');
}

// Communication functions
function openCommunication() {
    window.location.href = '../communication/';
}

function openTelegram() {
    window.open('https://t.me/pixel_garage', '_blank');
}

function openWhatsApp() {
    window.open('https://wa.me/34645577385?text=Cześć! Jestem zainteresowany usługami PixelGarage.', '_blank');
}

function scrollToContact() {
    const contactSection = document.querySelector('.quick-order');
    if (contactSection) {
        contactSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}
