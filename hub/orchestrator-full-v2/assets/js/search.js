// Search Page JavaScript

let searchResults = [];
let currentSearchView = 'grid';
let currentPage = 1;
const resultsPerPage = 12;
let searchFilters = {
    category: '',
    price: '',
    time: '',
    sort: 'relevance',
    query: ''
};

// Initialize search page
document.addEventListener('DOMContentLoaded', function() {
    initializeSearchPage();
    setupSearchEventListeners();
    loadInitialSearch();
});

function initializeSearchPage() {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    
    // Set filters from URL parameters
    if (urlParams.get('category')) {
        searchFilters.category = urlParams.get('category');
        document.getElementById('searchCategoryFilter').value = searchFilters.category;
    }
    
    if (urlParams.get('price')) {
        searchFilters.price = urlParams.get('price');
        document.getElementById('searchPriceFilter').value = searchFilters.price;
    }
    
    if (urlParams.get('time')) {
        searchFilters.time = urlParams.get('time');
        document.getElementById('searchTimeFilter').value = searchFilters.time;
    }
    
    if (urlParams.get('query')) {
        searchFilters.query = urlParams.get('query');
        document.getElementById('searchQueryInput').value = searchFilters.query;
    }
    
    if (urlParams.get('sort')) {
        searchFilters.sort = urlParams.get('sort');
        document.getElementById('searchSortFilter').value = searchFilters.sort;
    }
}

function setupSearchEventListeners() {
    // Filter change listeners
    document.getElementById('searchCategoryFilter').addEventListener('change', updateSearchFilters);
    document.getElementById('searchPriceFilter').addEventListener('change', updateSearchFilters);
    document.getElementById('searchTimeFilter').addEventListener('change', updateSearchFilters);
    document.getElementById('searchSortFilter').addEventListener('change', updateSearchFilters);
    document.getElementById('searchQueryInput').addEventListener('input', debounce(updateSearchQuery, 300));
    
    // Search button
    document.querySelector('.search-btn').addEventListener('click', performSearch);
    
    // Enter key in search input
    document.getElementById('searchQueryInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
}

function loadInitialSearch() {
    // Perform initial search if there are filters or query
    if (searchFilters.category || searchFilters.price || searchFilters.time || searchFilters.query) {
        performSearch();
    } else {
        // Show all services by default
        searchResults = [...servicesData, ...quickSolutionsData];
        displaySearchResults();
    }
}

function updateSearchFilters() {
    searchFilters.category = document.getElementById('searchCategoryFilter').value;
    searchFilters.price = document.getElementById('searchPriceFilter').value;
    searchFilters.time = document.getElementById('searchTimeFilter').value;
    searchFilters.sort = document.getElementById('searchSortFilter').value;
    
    performSearch();
}

function updateSearchQuery() {
    searchFilters.query = document.getElementById('searchQueryInput').value;
    performSearch();
}

function performSearch() {
    // Combine all data sources
    const allServices = [
        ...servicesData,
        ...quickSolutionsData.map(qs => ({
            ...qs,
            category: getCategoryFromQuickSolution(qs.category),
            price: extractPriceFromRange(qs.priceRange),
            time: qs.time,
            tags: qs.tech || [],
            type: 'quick-solution'
        }))
    ];
    
    // Add wishlist items
    Object.keys(wishlistData).forEach(category => {
        wishlistData[category].forEach(item => {
            allServices.push({
                ...item,
                category: category,
                price: extractPriceFromRange(item.priceRange),
                time: item.time,
                tags: item.tech || [],
                type: 'wishlist'
            });
        });
    });
    
    // Filter results
    searchResults = allServices.filter(service => {
        // Category filter
        if (searchFilters.category && service.category !== searchFilters.category) {
            return false;
        }
        
        // Price filter
        if (searchFilters.price) {
            const price = service.price || 0;
            switch (searchFilters.price) {
                case 'low':
                    if (price > 3000) return false;
                    break;
                case 'medium':
                    if (price < 3000 || price > 6000) return false;
                    break;
                case 'high':
                    if (price < 6000) return false;
                    break;
            }
        }
        
        // Time filter
        if (searchFilters.time && service.time) {
            const timeText = service.time.toLowerCase();
            const weeks = parseInt(timeText.match(/\d+/)?.[0] || 0);
            switch (searchFilters.time) {
                case 'fast':
                    if (weeks > 3) return false;
                    break;
                case 'medium':
                    if (weeks < 4 || weeks > 8) return false;
                    break;
                case 'long':
                    if (weeks < 9) return false;
                    break;
            }
        }
        
        // Query filter
        if (searchFilters.query) {
            const query = searchFilters.query.toLowerCase();
            const searchableText = [
                service.title,
                service.description,
                ...(service.tags || []),
                service.category
            ].join(' ').toLowerCase();
            
            if (!searchableText.includes(query)) {
                return false;
            }
        }
        
        return true;
    });
    
    // Sort results
    sortSearchResults();
    
    // Reset pagination
    currentPage = 1;
    
    // Display results
    displaySearchResults();
    updateSearchStats();
    updateURL();
}

function sortSearchResults() {
    switch (searchFilters.sort) {
        case 'price-asc':
            searchResults.sort((a, b) => (a.price || 0) - (b.price || 0));
            break;
        case 'price-desc':
            searchResults.sort((a, b) => (b.price || 0) - (a.price || 0));
            break;
        case 'time-asc':
            searchResults.sort((a, b) => {
                const timeA = parseInt(a.time?.match(/\d+/)?.[0] || 999);
                const timeB = parseInt(b.time?.match(/\d+/)?.[0] || 999);
                return timeA - timeB;
            });
            break;
        case 'popularity':
            searchResults.sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
            break;
        case 'relevance':
        default:
            // Keep current order (relevance based on filtering)
            break;
    }
}

function displaySearchResults() {
    const grid = document.getElementById('searchResultsGrid');
    const noResultsSection = document.getElementById('noResultsSection');
    
    if (searchResults.length === 0) {
        grid.innerHTML = '';
        noResultsSection.style.display = 'block';
        return;
    }
    
    noResultsSection.style.display = 'none';
    
    // Calculate pagination
    const startIndex = (currentPage - 1) * resultsPerPage;
    const endIndex = startIndex + resultsPerPage;
    const pageResults = searchResults.slice(startIndex, endIndex);
    
    // Clear grid
    grid.innerHTML = '';
    
    // Add results
    pageResults.forEach(service => {
        const serviceElement = createSearchResultElement(service);
        grid.appendChild(serviceElement);
    });
    
    // Update pagination
    updateSearchPagination();
}

function createSearchResultElement(service) {
    const element = document.createElement('div');
    element.className = `search-result-card ${currentSearchView === 'list' ? 'list-view' : ''}`;

    const serviceType = service.type || 'service';
    const badgeText = serviceType === 'quick-solution' ? 'Express' :
                     serviceType === 'wishlist' ? 'Premium' : 'Standard';

    const priceDisplay = service.priceRange || `${service.price} PLN`;
    const rating = service.rating || 4.8;
    const ratingStars = '★'.repeat(Math.floor(rating)) + (rating % 1 >= 0.5 ? '☆' : '');

    element.innerHTML = `
        <div class="search-result-header">
            <div class="search-result-category">${getCategoryDisplayName(service.category)}</div>
            <div class="search-result-price">${priceDisplay}</div>
        </div>

        <h3 class="search-result-title">${service.title}</h3>
        <p class="search-result-description">${service.description}</p>

        <div class="search-result-meta">
            <div class="search-result-time">
                <i class="fas fa-clock"></i>
                <span>${service.time || 'Do ustalenia'}</span>
            </div>
            <div class="search-result-rating">
                <span class="rating-stars">${ratingStars}</span>
                <span class="rating-value">${rating}</span>
            </div>
        </div>

        ${(service.tags && service.tags.length > 0) ? `
        <div class="search-result-tags">
            ${service.tags.slice(0, 3).map(tag => `<span class="search-result-tag">${tag}</span>`).join('')}
        </div>
        ` : ''}

        <div class="search-result-actions">
            <button class="search-result-btn primary" onclick="orderSearchResult('${service.id}', '${serviceType}')">
                <i class="fas fa-shopping-cart"></i>
                Zamów teraz
            </button>
            <button class="search-result-btn secondary" onclick="viewSearchResultDetails('${service.id}', '${serviceType}')">
                <i class="fas fa-info-circle"></i>
                Szczegóły
            </button>
        </div>
    `;

    return element;
}

function updateSearchStats() {
    const countElement = document.getElementById('searchResultsCount');
    const infoElement = document.getElementById('searchResultsInfo');
    
    if (countElement) {
        countElement.textContent = searchResults.length;
    }
    
    if (infoElement) {
        if (searchResults.length === 0) {
            infoElement.textContent = 'Nie znaleziono usług spełniających kryteria';
        } else {
            const hasFilters = searchFilters.category || searchFilters.price || searchFilters.time || searchFilters.query;
            if (hasFilters) {
                infoElement.textContent = `Znaleziono ${searchResults.length} usług spełniających kryteria wyszukiwania`;
            } else {
                infoElement.textContent = `Wyświetlane wszystkie dostępne usługi (${searchResults.length})`;
            }
        }
    }
}

function updateSearchPagination() {
    const pagination = document.getElementById('searchPagination');
    const totalPages = Math.ceil(searchResults.length / resultsPerPage);
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // Previous button
    paginationHTML += `
        <button class="pagination-btn" ${currentPage === 1 ? 'disabled' : ''} onclick="goToPage(${currentPage - 1})">
            <i class="fas fa-chevron-left"></i>
        </button>
    `;
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
                <button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="goToPage(${i})">
                    ${i}
                </button>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += '<span class="pagination-dots">...</span>';
        }
    }
    
    // Next button
    paginationHTML += `
        <button class="pagination-btn" ${currentPage === totalPages ? 'disabled' : ''} onclick="goToPage(${currentPage + 1})">
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    pagination.innerHTML = paginationHTML;
}

function goToPage(page) {
    currentPage = page;
    displaySearchResults();
    
    // Scroll to top of results
    document.querySelector('.search-results-section').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

function updateURL() {
    const params = new URLSearchParams();
    
    if (searchFilters.category) params.set('category', searchFilters.category);
    if (searchFilters.price) params.set('price', searchFilters.price);
    if (searchFilters.time) params.set('time', searchFilters.time);
    if (searchFilters.query) params.set('query', searchFilters.query);
    if (searchFilters.sort !== 'relevance') params.set('sort', searchFilters.sort);
    
    const newURL = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
    window.history.replaceState({}, '', newURL);
}

// Utility functions
function getCategoryFromQuickSolution(category) {
    const mapping = {
        'Web Applications': 'web',
        'Mobile-First': 'mobile',
        'AI Automation': 'automation',
        'Content Creation': 'content',
        'Audio & Voice': 'audio'
    };
    return mapping[category] || 'web';
}

function getCategoryDisplayName(category) {
    const categories = {
        'web': 'Web Applications',
        'mobile': 'Mobile & PWA',
        'chatbots': 'Chatboty & AI',
        'content': 'Content Creation',
        'automation': 'Business Automation',
        'audio': 'Audio & Voice',
        'saas': 'SaaS & Web Apps',
        'desktop': 'Desktop Solutions',
        'visual': 'Visual Content'
    };
    return categories[category] || category;
}

function extractPriceFromRange(priceRange) {
    if (!priceRange) return 0;
    const match = priceRange.match(/(\d+,?\d*)/);
    return match ? parseInt(match[1].replace(',', '')) : 0;
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Event handlers
function toggleAdvancedSearch() {
    const form = document.getElementById('advancedSearchForm');
    const toggle = document.querySelector('.search-toggle span');
    
    if (form.style.display === 'none') {
        form.style.display = 'block';
        toggle.textContent = 'Ukryj filtry';
    } else {
        form.style.display = 'none';
        toggle.textContent = 'Pokaż filtry';
    }
}

function toggleSearchView(view) {
    currentSearchView = view;
    
    // Update button states
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');
    
    // Update grid class
    const grid = document.getElementById('searchResultsGrid');
    if (view === 'list') {
        grid.classList.add('list-view');
    } else {
        grid.classList.remove('list-view');
    }
    
    displaySearchResults();
}

function quickSearch(query) {
    document.getElementById('searchQueryInput').value = query;
    searchFilters.query = query;
    performSearch();
}

function clearSearchFilters() {
    // Reset all filters
    searchFilters = {
        category: '',
        price: '',
        time: '',
        sort: 'relevance',
        query: ''
    };
    
    // Reset form elements
    document.getElementById('searchCategoryFilter').value = '';
    document.getElementById('searchPriceFilter').value = '';
    document.getElementById('searchTimeFilter').value = '';
    document.getElementById('searchSortFilter').value = 'relevance';
    document.getElementById('searchQueryInput').value = '';
    
    // Perform search
    performSearch();
}

function orderSearchResult(serviceId, serviceType) {
    let service = null;
    
    if (serviceType === 'quick-solution') {
        service = quickSolutionsData.find(s => s.id === serviceId);
    } else if (serviceType === 'wishlist') {
        for (const category in wishlistData) {
            service = wishlistData[category].find(s => s.id === serviceId);
            if (service) break;
        }
    } else {
        service = servicesData.find(s => s.id === serviceId);
    }
    
    if (service) {
        showContactOptions(service, serviceType);
    }
}

function viewSearchResultDetails(serviceId, serviceType) {
    let service = null;
    
    if (serviceType === 'quick-solution') {
        service = quickSolutionsData.find(s => s.id === serviceId);
        if (service) showQuickSolutionModal(service);
    } else if (serviceType === 'wishlist') {
        for (const category in wishlistData) {
            service = wishlistData[category].find(s => s.id === serviceId);
            if (service) break;
        }
        if (service) showWishlistModal(service);
    } else {
        service = servicesData.find(s => s.id === serviceId);
        if (service) showServiceModal(service);
    }
}

function showWishlistModal(item) {
    const modal = document.createElement('div');
    modal.className = 'service-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="closeServiceModal()"></div>
        <div class="modal-content glass-effect">
            <div class="modal-header">
                <h2>${item.title}</h2>
                <button class="modal-close" onclick="closeServiceModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="service-category premium">Premium Solution</div>
                <p class="service-description">${item.description}</p>
                <div class="service-details">
                    <div class="detail-item">
                        <strong>Cena:</strong> ${item.priceRange}
                    </div>
                    <div class="detail-item">
                        <strong>Czas realizacji:</strong> ${item.time}
                    </div>
                    <div class="detail-item">
                        <strong>Technologie:</strong> ${item.tech.join(', ')}
                    </div>
                </div>
                <div class="modal-actions">
                    <button class="btn-primary" onclick="orderWishlistItem('${item.id}')">
                        <i class="fas fa-crown"></i>
                        Zapytaj o projekt
                    </button>
                    <button class="btn-secondary" onclick="requestQuote('${item.id}')">
                        <i class="fas fa-calculator"></i>
                        Wycena premium
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
}

// Navigation functions
function performAdvancedSearch() {
    // This function is called from the main search page
    const category = document.getElementById('categoryFilter').value;
    const price = document.getElementById('priceFilter').value;
    const time = document.getElementById('timeFilter').value;
    const query = document.getElementById('searchInput').value;
    
    // Build URL parameters
    const params = new URLSearchParams();
    if (category) params.set('category', category);
    if (price) params.set('price', price);
    if (time) params.set('time', time);
    if (query) params.set('query', query);
    
    // Navigate to search page
    const searchURL = 'search.html' + (params.toString() ? '?' + params.toString() : '');
    window.location.href = searchURL;
}

// Contact functions
function openTelegram() {
    window.open('https://t.me/pixel_garage', '_blank');
}

function openWhatsApp() {
    window.open('https://wa.me/34645577385', '_blank');
}

function openEmail() {
    window.location.href = 'mailto:<EMAIL>';
}

function requestCustomService() {
    const message = `Cześć! Szukam usługi, której nie ma w katalogu. Czy możemy omówić stworzenie dedykowanego rozwiązania?`;
    const encodedMessage = encodeURIComponent(message);
    window.open(`https://t.me/pixel_garage?text=${encodedMessage}`, '_blank');
}

// ===== PREMIUM NAVBAR FUNCTIONALITY =====
function initializeNavbar() {
    const navbar = document.getElementById('navbar');

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
}

function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobileMenu');
    const hamburgerBtn = document.querySelector('.hamburger-btn');

    if (!mobileMenu || !hamburgerBtn) return;

    mobileMenu.classList.toggle('active');
    hamburgerBtn.classList.toggle('active');

    // Prevent body scroll when menu is open
    if (mobileMenu.classList.contains('active')) {
        document.body.style.overflow = 'hidden';
    } else {
        document.body.style.overflow = '';
    }
}

// Close mobile menu when clicking outside
document.addEventListener('click', function(event) {
    const mobileMenu = document.getElementById('mobileMenu');
    const hamburgerBtn = document.querySelector('.hamburger-btn');
    const navbar = document.querySelector('.premium-navbar');

    if (mobileMenu && mobileMenu.classList.contains('active')) {
        if (!navbar.contains(event.target)) {
            mobileMenu.classList.remove('active');
            hamburgerBtn.classList.remove('active');
            document.body.style.overflow = '';
        }
    }
});

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeSearch();
    loadSearchResults();
    initializeNavbar();
});
