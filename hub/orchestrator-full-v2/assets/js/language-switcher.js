// Language Switcher Component for PixelGarage

class LanguageSwitcher {
    constructor() {
        console.log('🔧 Creating LanguageSwitcher instance...');
        this.languages = {
            pl: { name: '<PERSON><PERSON>', flag: '🇵🇱' },
            en: { name: 'English', flag: '🇬🇧' },
            es: { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸' }
        };
        this.init();
    }

    init() {
        console.log('🚀 Initializing language switcher...');
        this.createSwitcher();
        this.bindEvents();
        this.updateActiveLanguage();
        console.log('✅ Language switcher initialized');
    }

    createSwitcher() {
        console.log('🔨 Creating language switcher...');

        // Check if switcher already exists
        if (document.querySelector('.language-switcher')) {
            console.log('⚠️ Language switcher already exists');
            return;
        }

        // Create overlay for dropdown positioning
        const overlay = document.createElement('div');
        overlay.className = 'lang-dropdown-overlay';
        document.body.appendChild(overlay);
        console.log('📄 Created dropdown overlay');

        const switcher = document.createElement('div');
        switcher.className = 'language-switcher';
        switcher.innerHTML = this.generateSwitcherHTML();

        // Always create floating language switcher in bottom left corner
        console.log('📍 Creating floating language switcher in bottom left corner');
        switcher.classList.add('floating-language-switcher');
        document.body.appendChild(switcher);

        // Debug: Check if switcher was added
        setTimeout(() => {
            const addedSwitcher = document.querySelector('.floating-language-switcher');
            console.log('🔍 Floating switcher check:', {
                exists: !!addedSwitcher,
                element: addedSwitcher,
                position: addedSwitcher ? getComputedStyle(addedSwitcher).position : 'none',
                bottom: addedSwitcher ? getComputedStyle(addedSwitcher).bottom : 'none',
                left: addedSwitcher ? getComputedStyle(addedSwitcher).left : 'none',
                zIndex: addedSwitcher ? getComputedStyle(addedSwitcher).zIndex : 'none'
            });
        }, 100);

        // Move dropdown to body for better z-index control
        setTimeout(() => {
            const dropdown = switcher.querySelector('.lang-dropdown');
            if (dropdown) {
                console.log('🔄 Moving dropdown to body for better positioning');
                dropdown.remove();
                document.body.appendChild(dropdown);

                // Store reference for positioning
                switcher.setAttribute('data-dropdown-id', 'main-lang-dropdown');
                dropdown.setAttribute('id', 'main-lang-dropdown');

                console.log('✅ Dropdown moved to body');
            }
        }, 100);

        console.log('✅ Language switcher created successfully');
    }

    generateSwitcherHTML() {
        const currentLang = (typeof getCurrentLanguage === 'function') ? getCurrentLanguage() : 'pl';
        const currentLangData = this.languages[currentLang];
        console.log('🎨 Generating switcher HTML for language:', currentLang);

        const html = `
            <div class="lang-selector">
                <button class="lang-current" aria-label="Select Language">
                    <span class="lang-flag">${currentLangData.flag}</span>
                    <span class="lang-code">${currentLang.toUpperCase()}</span>
                    <i class="fas fa-chevron-down"></i>
                </button>
                <div class="lang-dropdown">
                    ${Object.entries(this.languages).map(([code, data]) => `
                        <button class="lang-option ${code === currentLang ? 'active' : ''}"
                                data-lang="${code}"
                                aria-label="Switch to ${data.name}">
                            <span class="lang-flag">${data.flag}</span>
                            <span class="lang-name">${data.name}</span>
                        </button>
                    `).join('')}
                </div>
            </div>
        `;

        console.log('📝 Generated HTML:', html);
        return html;
    }

    bindEvents() {
        document.addEventListener('click', (e) => {
            // Toggle dropdown
            if (e.target.closest('.lang-current')) {
                e.preventDefault();
                e.stopPropagation();
                console.log('🖱️ Language button clicked');

                const button = e.target.closest('.lang-current');
                const selector = e.target.closest('.lang-selector');
                // Look for dropdown in body first, then in selector
                let dropdown = document.getElementById('main-lang-dropdown');
                if (!dropdown) {
                    dropdown = selector ? selector.querySelector('.lang-dropdown') : null;
                }
                const overlay = document.querySelector('.lang-dropdown-overlay');

                console.log('🔍 Found elements:', { button, selector, dropdown, overlay });
                console.log('🔍 Dropdown details:', {
                    exists: !!dropdown,
                    id: dropdown?.id,
                    classes: dropdown?.className,
                    style: dropdown?.style.cssText,
                    computedStyle: dropdown ? {
                        display: getComputedStyle(dropdown).display,
                        visibility: getComputedStyle(dropdown).visibility,
                        opacity: getComputedStyle(dropdown).opacity,
                        zIndex: getComputedStyle(dropdown).zIndex
                    } : null
                });

                // Check if dropdown is currently active
                const isActive = dropdown && dropdown.classList.contains('active');

                // Close all dropdowns first
                this.closeAllDropdowns();

                // If it wasn't active, open it
                if (!isActive && dropdown) {
                    // Position dropdown using fixed positioning
                    this.positionDropdown(button, dropdown);

                    // Add active classes
                    dropdown.classList.add('active');
                    if (selector) selector.classList.add('dropdown-open');

                    console.log('✅ Dropdown opened');
                    console.log('🔍 After opening:', {
                        classes: dropdown.className,
                        style: dropdown.style.cssText,
                        computedStyle: {
                            display: getComputedStyle(dropdown).display,
                            visibility: getComputedStyle(dropdown).visibility,
                            opacity: getComputedStyle(dropdown).opacity,
                            zIndex: getComputedStyle(dropdown).zIndex
                        }
                    });

                    // Activate overlay
                    if (overlay) overlay.classList.add('active');
                } else if (!dropdown) {
                    console.error('❌ Dropdown not found');
                }

                return;
            }

            // Select language
            if (e.target.closest('.lang-option')) {
                e.preventDefault();
                e.stopPropagation();
                const langCode = e.target.closest('.lang-option').getAttribute('data-lang');
                console.log('🌐 Language selected:', langCode);
                this.switchLanguage(langCode);
                return;
            }

            // Close dropdown when clicking outside or on overlay
            if (!e.target.closest('.lang-selector') || e.target.classList.contains('lang-dropdown-overlay')) {
                this.closeAllDropdowns();
            }
        });

        // Close dropdown on escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllDropdowns();
            }
        });

        // Reposition dropdown on scroll and resize
        window.addEventListener('scroll', () => {
            const activeDropdown = document.querySelector('.lang-dropdown.active');
            if (activeDropdown) {
                const button = document.querySelector('.lang-current');
                this.positionDropdown(button, activeDropdown);
            }
        });

        window.addEventListener('resize', () => {
            const activeDropdown = document.querySelector('.lang-dropdown.active');
            if (activeDropdown) {
                const button = document.querySelector('.lang-current');
                this.positionDropdown(button, activeDropdown);
            }
        });
    }

    positionDropdown(button, dropdown) {
        if (!button || !dropdown) {
            console.error('❌ Missing button or dropdown for positioning');
            return;
        }

        const buttonRect = button.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const isFloating = button.closest('.floating-language-switcher');

        console.log('📐 Positioning dropdown:', { buttonRect, viewportWidth, viewportHeight, isFloating });

        let top, left;

        if (isFloating) {
            // For floating switcher in bottom left, position dropdown above the button
            top = buttonRect.top - 200 - 8; // dropdown height + margin
            left = buttonRect.left;

            // Ensure dropdown doesn't go above viewport
            if (top < 10) {
                top = buttonRect.bottom + 8; // Show below if no space above
            }
        } else {
            // For navbar switcher, position dropdown below button, aligned to right
            top = buttonRect.bottom + 8;
            left = buttonRect.right - 150; // dropdown width (align to right edge of button)

            // If dropdown would go below viewport, show above button
            if (top + 200 > viewportHeight) {
                top = buttonRect.top - 200 - 8;
            }
        }

        // Adjust if dropdown would go off-screen horizontally
        if (left < 10) {
            left = buttonRect.left; // Align to left edge of button
        }
        if (left + 150 > viewportWidth - 10) {
            left = viewportWidth - 160; // Keep 10px margin from right edge
        }

        // Ensure dropdown stays within viewport
        top = Math.max(10, Math.min(top, viewportHeight - 210));
        left = Math.max(10, Math.min(left, viewportWidth - 160));

        // Apply position with fixed positioning
        dropdown.style.position = 'fixed';
        dropdown.style.top = `${top}px`;
        dropdown.style.left = `${left}px`;
        dropdown.style.zIndex = '9999999';

        console.log('📍 Dropdown positioned at:', { top, left, isFloating });
    }

    switchLanguage(langCode) {
        if (this.languages[langCode]) {
            setLanguage(langCode);
            this.updateActiveLanguage();
            this.closeAllDropdowns();

            // Show language change notification
            this.showLanguageChangeNotification(langCode);

            // Update URL if needed (for SEO)
            this.updateURL(langCode);
        }
    }

    updateActiveLanguage() {
        const currentLang = getCurrentLanguage();
        const currentLangData = this.languages[currentLang];
        
        // Update current language display
        const langCurrent = document.querySelector('.lang-current');
        if (langCurrent) {
            const flag = langCurrent.querySelector('.lang-flag');
            const code = langCurrent.querySelector('.lang-code');
            
            if (flag) flag.textContent = currentLangData.flag;
            if (code) code.textContent = currentLang.toUpperCase();
        }

        // Update active state in dropdown
        const langOptions = document.querySelectorAll('.lang-option');
        langOptions.forEach(option => {
            const isActive = option.getAttribute('data-lang') === currentLang;
            option.classList.toggle('active', isActive);
        });
    }

    closeDropdown() {
        this.closeAllDropdowns();
    }

    closeAllDropdowns() {
        const dropdowns = document.querySelectorAll('.lang-dropdown.active');
        const selectors = document.querySelectorAll('.lang-selector.dropdown-open');
        const overlay = document.querySelector('.lang-dropdown-overlay');

        dropdowns.forEach(dropdown => dropdown.classList.remove('active'));
        selectors.forEach(selector => selector.classList.remove('dropdown-open'));
        if (overlay) overlay.classList.remove('active');

        console.log('🔒 All dropdowns closed');
    }

    showLanguageChangeNotification(langCode) {
        const langName = this.languages[langCode].name;
        const notification = document.createElement('div');
        notification.className = 'language-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-globe"></i>
                <span>Language changed to ${langName}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => notification.classList.add('show'), 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    updateURL(langCode) {
        // Update URL parameter for SEO (optional)
        const url = new URL(window.location);
        url.searchParams.set('lang', langCode);

        // Use replaceState to avoid adding to history
        window.history.replaceState({}, '', url.toString());
    }

    // Debug function to test dropdown visibility
    testDropdown() {
        console.log('🧪 Testing dropdown visibility...');
        const dropdown = document.getElementById('main-lang-dropdown') || document.querySelector('.lang-dropdown');

        if (dropdown) {
            console.log('📋 Dropdown found:', {
                element: dropdown,
                classes: dropdown.className,
                style: dropdown.style.cssText,
                computedStyle: {
                    display: getComputedStyle(dropdown).display,
                    visibility: getComputedStyle(dropdown).visibility,
                    opacity: getComputedStyle(dropdown).opacity,
                    zIndex: getComputedStyle(dropdown).zIndex,
                    position: getComputedStyle(dropdown).position,
                    top: getComputedStyle(dropdown).top,
                    left: getComputedStyle(dropdown).left
                },
                boundingRect: dropdown.getBoundingClientRect()
            });

            // Force show dropdown for testing
            dropdown.style.opacity = '1';
            dropdown.style.visibility = 'visible';
            dropdown.style.transform = 'translateY(0) scale(1)';
            dropdown.style.display = 'block';
            dropdown.style.zIndex = '9999999';
            dropdown.style.background = 'red'; // Make it very visible

            console.log('🔴 Forced dropdown to be visible with red background');
        } else {
            console.error('❌ No dropdown found');
        }
    }
}

// CSS for Language Switcher (inject into page)
function injectLanguageSwitcherCSS() {
    if (document.querySelector('#language-switcher-css')) {
        return;
    }

    const style = document.createElement('style');
    style.id = 'language-switcher-css';
    style.textContent = `
        .language-switcher {
            position: relative;
            z-index: 9999;
            margin-right: 12px;
        }

        .lang-selector {
            position: relative;
            z-index: 9999;
        }

        .lang-current {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .lang-current:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .lang-flag {
            font-size: 16px;
        }

        .lang-code {
            font-size: 12px;
            font-weight: 600;
        }

        .lang-current i {
            font-size: 10px;
            transition: transform 0.3s ease;
        }

        .lang-selector.dropdown-open .lang-current i {
            transform: rotate(180deg);
        }

        .lang-dropdown {
            position: fixed !important;
            top: auto;
            right: auto;
            min-width: 150px;
            background: rgba(255, 255, 255, 0.98) !important;
            backdrop-filter: blur(25px);
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4),
                        0 0 0 1px rgba(255, 255, 255, 0.3),
                        inset 0 1px 0 rgba(255, 255, 255, 0.5);
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px) scale(0.95);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-top: 8px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            z-index: 9999999 !important;
            max-height: 300px;
            overflow-y: auto;
            pointer-events: auto;
            display: block !important;
        }

        .lang-dropdown.active {
            opacity: 1 !important;
            visibility: visible !important;
            transform: translateY(0) scale(1) !important;
            display: block !important;
        }

        .lang-option {
            display: flex;
            align-items: center;
            gap: 12px;
            width: 100%;
            padding: 12px 16px;
            background: none;
            border: none;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 4px;
        }

        .lang-option:hover {
            background: rgba(99, 102, 241, 0.1);
            color: #6366f1;
        }

        .lang-option.active {
            background: rgba(99, 102, 241, 0.2);
            color: #6366f1;
            font-weight: 600;
        }

        .lang-name {
            font-size: 14px;
        }

        .floating-language-switcher {
            position: fixed;
            bottom: 30px;
            left: 30px;
            z-index: 9999999;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 16px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            animation: floatIn 0.6s ease-out;
            padding: 4px;
        }

        .floating-language-switcher:hover {
            transform: translateY(-4px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
            background: rgba(255, 255, 255, 0.98);
        }

        @keyframes floatIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Special styles for floating switcher */
        .floating-language-switcher .lang-current {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
            padding: 12px 16px;
            font-weight: 600;
            min-width: 100px;
        }

        .floating-language-switcher .lang-current:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.5);
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .floating-language-switcher .lang-flag {
            font-size: 18px;
        }

        .floating-language-switcher .lang-code {
            font-size: 13px;
            font-weight: 700;
        }

        /* Enhanced visibility for floating switcher */
        .floating-language-switcher {
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.25),
                        0 0 0 1px rgba(255, 255, 255, 0.4),
                        inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
        }

        .floating-language-switcher::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
            border-radius: 18px;
            z-index: -1;
            opacity: 0.3;
            animation: borderGlow 3s ease-in-out infinite;
        }

        @keyframes borderGlow {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
        }

        /* Overlay to ensure dropdown is always visible */
        .lang-dropdown-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: transparent;
            z-index: 999998 !important;
            pointer-events: none;
        }

        .lang-dropdown-overlay.active {
            pointer-events: auto;
        }

        .language-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(99, 102, 241, 0.95);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .language-notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        @media (max-width: 768px) {
            .floating-language-switcher {
                bottom: 20px;
                left: 20px;
                padding: 3px;
                border-radius: 12px;
            }

            .lang-dropdown {
                min-width: 140px;
                max-height: 250px;
            }

            .lang-current {
                padding: 6px 10px;
                font-size: 14px;
            }

            .lang-option {
                padding: 10px 12px;
                font-size: 13px;
            }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .lang-dropdown {
                background: rgba(255, 255, 255, 1);
                border: 2px solid #000;
            }

            .lang-option:hover {
                background: #000;
                color: #fff;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .lang-dropdown {
                transition: opacity 0.2s ease;
                transform: none;
            }

            .lang-dropdown.active {
                transform: none;
            }
        }
    `;

    document.head.appendChild(style);
    console.log('✅ Language switcher CSS injected');
}

// Initialize language switcher when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌐 Initializing Language Switcher...');
    injectLanguageSwitcherCSS();

    // Wait for translations to be available
    if (typeof translations !== 'undefined') {
        console.log('✅ Translations available, creating switcher');
        window.languageSwitcherInstance = new LanguageSwitcher();
    } else {
        console.log('⏳ Waiting for translations...');
        setTimeout(() => {
            if (typeof translations !== 'undefined') {
                console.log('✅ Translations loaded, creating switcher');
                window.languageSwitcherInstance = new LanguageSwitcher();
            } else {
                console.error('❌ Translations not available');
            }
        }, 100);
    }
});

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LanguageSwitcher;
}

// Global test function
window.testLanguageSwitcher = function() {
    console.log('🧪 Testing language switcher...');
    const switcher = document.querySelector('.language-switcher');
    if (switcher && window.languageSwitcherInstance) {
        window.languageSwitcherInstance.testDropdown();
    } else {
        console.error('❌ Language switcher not found or not initialized');
    }
};
