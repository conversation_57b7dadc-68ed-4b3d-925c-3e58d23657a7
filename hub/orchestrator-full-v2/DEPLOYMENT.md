# 🚀 PixelGarage Deployment Guide

Kompletny przewodnik wdrażania aplikacji PixelGarage na serwer mikr.us bez Dockera.

## 📋 Wymagania

### Lokalne wymagania:
- Linux/macOS/WSL
- SSH client
- rsync
- curl (do testowania)

### Serwer mikr.us:
- Ubuntu/Debian
- Dostęp SSH z kluczem
- Uprawnienia sudo
- Minimum 1GB RAM
- 10GB przestrzeni dyskowej

## 🔧 Konfiguracja

### 1. Przygotowanie kluczy SSH

```bash
# Wygeneruj klucz SSH (jeśli nie masz)
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Skopiuj klucz na serwer
ssh-copy-id <EMAIL>
```

### 2. Konfiguracja deploymentu

```bash
# Nadaj uprawnienia wykonywania
chmod +x deploy-config.sh

# Uruchom konfigurację
./deploy-config.sh
```

Skrypt zapyta o:
- Hostname serwera (np. `myapp.mikr.us`)
- Nazwa użytkownika SSH (domyślnie `root`)
- Ścieżka na serwerze (domyślnie `/var/www/html`)
- Lokalna ścieżka projektu (domyślnie `./orchestrator-full-v2`)

## 🚀 Deployment

### 1. Podstawowy deployment

```bash
# Uruchom deployment
./deploy.sh
```

Skrypt automatycznie:
- ✅ Sprawdzi zależności
- ✅ Zweryfikuje lokalne pliki
- ✅ Przetestuje połączenie z serwerem
- ✅ Utworzy backup obecnego wdrożenia
- ✅ Przygotuje środowisko serwera
- ✅ Wdroży pliki aplikacji
- ✅ Skonfiguruje nginx
- ✅ Ustawi odpowiednie uprawnienia
- ✅ Zweryfikuje wdrożenie

### 2. SSL Certificate (opcjonalnie)

```bash
# Nadaj uprawnienia
chmod +x setup-ssl.sh

# Zainstaluj SSL
./setup-ssl.sh
```

## 📁 Struktura wdrożenia

```
/var/www/html/
├── index.html              # Strona główna
├── assets/
│   ├── css/               # Style CSS
│   └── js/                # JavaScript
├── services/              # Katalog usług
├── gallery/               # Portfolio
├── academy/               # Tech Academy
├── communication/         # Kontakt Live
└── projects/              # Projekty portfolio
```

## 🔧 Konfiguracja nginx

Skrypt automatycznie konfiguruje nginx z:

- **Gzip compression** - kompresja plików
- **Cache headers** - cache dla statycznych zasobów
- **Security headers** - nagłówki bezpieczeństwa
- **SPA routing** - obsługa Single Page Application
- **Error handling** - obsługa błędów

## 🔍 Weryfikacja

### Sprawdź status serwisów:
```bash
ssh <EMAIL> "
    sudo systemctl status nginx
    sudo nginx -t
"
```

### Sprawdź logi:
```bash
ssh <EMAIL> "
    sudo tail -f /var/log/nginx/access.log
    sudo tail -f /var/log/nginx/error.log
"
```

### Test wydajności:
```bash
curl -I http://your-server.mikr.us
```

## 🔄 Aktualizacje

### Ponowny deployment:
```bash
./deploy.sh
```

### Przywracanie z backupu:
```bash
ssh <EMAIL> "
    sudo cp -r /var/backups/pixelgarage-YYYYMMDD-HHMMSS/* /var/www/html/
    sudo systemctl reload nginx
"
```

## 🛠️ Rozwiązywanie problemów

### Problem: Brak połączenia SSH
```bash
# Sprawdź połączenie
ssh -v <EMAIL>

# Sprawdź klucze SSH
ssh-add -l
```

### Problem: Błędy nginx
```bash
# Sprawdź konfigurację
sudo nginx -t

# Sprawdź logi
sudo tail -f /var/log/nginx/error.log
```

### Problem: Uprawnienia plików
```bash
# Napraw uprawnienia
sudo chown -R www-data:www-data /var/www/html
sudo find /var/www/html -type d -exec chmod 755 {} \;
sudo find /var/www/html -type f -exec chmod 644 {} \;
```

## 📊 Monitoring

### Sprawdź wykorzystanie zasobów:
```bash
ssh <EMAIL> "
    df -h
    free -h
    top -n 1
"
```

### Sprawdź ruch sieciowy:
```bash
# Analiza logów nginx
ssh <EMAIL> "
    sudo tail -n 100 /var/log/nginx/access.log | awk '{print \$1}' | sort | uniq -c | sort -nr
"
```

## 🔐 Bezpieczeństwo

### Zalecenia:
1. **Firewall**: Skonfiguruj UFW
2. **SSH**: Wyłącz logowanie hasłem
3. **Updates**: Regularne aktualizacje systemu
4. **Backup**: Automatyczne backupy
5. **SSL**: Zawsze używaj HTTPS

### Konfiguracja firewall:
```bash
ssh <EMAIL> "
    sudo ufw allow ssh
    sudo ufw allow 'Nginx Full'
    sudo ufw enable
"
```

## 📞 Wsparcie

W przypadku problemów:
1. Sprawdź logi deploymentu
2. Zweryfikuj konfigurację serwera
3. Przetestuj połączenie SSH
4. Sprawdź status nginx

---

**PixelGarage** - Professional Digital Solutions Platform
Deployment Guide v1.0
