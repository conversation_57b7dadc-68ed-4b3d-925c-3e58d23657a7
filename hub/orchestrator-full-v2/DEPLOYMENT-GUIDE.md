# 🚀 PixelGarage.space - Complete Deployment Guide

## Overview
This guide will help you deploy the complete PixelGarage platform to your mikr.us server with the domain `pixelgarage.space`.

## Prerequisites
- ✅ Domain: `pixelgarage.space` (purchased and ready)
- ✅ mikr.us server with SSH access
- ✅ Sudo privileges on the server
- ✅ Local machine with bash, rsync, ssh, curl

## 🎯 Quick Start (Automated Deployment)

### Step 1: Make Scripts Executable
```bash
chmod +x orchestrator-full-v2/auto-deploy-pixelgarage.sh
chmod +x orchestrator-full-v2/setup-domain.sh
chmod +x orchestrator-full-v2/manage-pixelgarage.sh
```

### Step 2: Run Automated Deployment
```bash
cd orchestrator-full-v2
./auto-deploy-pixelgarage.sh
```

The script will:
1. ✅ Check dependencies
2. ✅ Validate local files
3. ✅ Test server connection
4. ✅ Create backup of existing deployment
5. ✅ Prepare server environment
6. ✅ Deploy all files
7. ✅ Configure nginx for pixelgarage.space
8. ✅ Set proper permissions
9. ✅ Verify deployment
10. ✅ Optionally setup SSL certificate

### Step 3: Configure DNS
```bash
./setup-domain.sh
```

Configure these DNS records in your domain registrar:

**A Records:**
- Name: `@` → Value: `[Your Server IP]`
- Name: `www` → Value: `[Your Server IP]`

### Step 4: Verify Deployment
After DNS propagation (up to 48 hours):
- Visit: `http://pixelgarage.space`
- Test all sections: Services, Portfolio, Academy, Communication
- Test translation system (PL/EN/ES language switcher)

## 🔧 Manual Deployment (Alternative)

If you prefer manual control:

### 1. Server Preparation
```bash
# SSH to your server
ssh <EMAIL>

# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y nginx curl wget unzip certbot python3-certbot-nginx

# Create directory
sudo mkdir -p /var/www/pixelgarage.space
sudo chown -R www-data:www-data /var/www/pixelgarage.space
```

### 2. Deploy Files
```bash
# From your local machine
rsync -avz --delete \
  --exclude='.git*' \
  --exclude='*.sh' \
  --exclude='*.md' \
  ./orchestrator-full-v2/ \
  <EMAIL>:/var/www/pixelgarage.space/
```

### 3. Configure Nginx
```bash
# SSH to server
ssh <EMAIL>

# Create nginx config
sudo nano /etc/nginx/sites-available/pixelgarage.space
```

Copy the nginx configuration from `auto-deploy-pixelgarage.sh` (lines 175-250).

```bash
# Enable site
sudo ln -sf /etc/nginx/sites-available/pixelgarage.space /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl reload nginx
```

## 🌐 Platform Features

### Multi-language Support
- 🇵🇱 Polish (default)
- 🇬🇧 English
- 🇪🇸 Spanish
- Automatic browser language detection
- Language switcher in navigation

### Sections Deployed
1. **Landing Page** (`/`) - Hero, services overview, featured projects
2. **Services Catalog** (`/services/`) - 163+ AI services, categories, wishlist
3. **Portfolio Gallery** (`/gallery/`) - 12 interactive project demos
4. **Tech Academy** (`/academy/`) - VIP training program registration
5. **Live Communication** (`/communication/`) - Telegram/WhatsApp contact

### Projects Included
- SmartCard.pl - AI business cards generator
- EventAI.pl - Smart event planning platform
- FitGenius.pl - AI fitness coaching app
- BrandMe.pl - Personal branding platform
- GastroAI.pl - Restaurant management system
- And 7 more innovative projects

## 🔒 SSL Certificate Setup

### Automatic (Recommended)
```bash
sudo certbot --nginx -d pixelgarage.space -d www.pixelgarage.space
```

### Manual
```bash
sudo certbot certonly --nginx -d pixelgarage.space -d www.pixelgarage.space
```

## 📊 Post-Deployment Management

### Use Management Script
```bash
./manage-pixelgarage.sh
```

Options:
1. Check server status
2. View logs (access/error/system)
3. Update website
4. Test functionality
5. Manage SSL certificates
6. Optimize performance
7. Manage backups

### Manual Commands
```bash
# Check nginx status
sudo systemctl status nginx

# View error logs
sudo tail -f /var/log/nginx/pixelgarage.space.error.log

# Restart nginx
sudo systemctl restart nginx

# Check SSL certificate
sudo certbot certificates

# Renew SSL
sudo certbot renew
```

## 🔍 Troubleshooting

### Website Not Loading
1. Check nginx status: `sudo systemctl status nginx`
2. Check nginx config: `sudo nginx -t`
3. Check DNS propagation: `nslookup pixelgarage.space`
4. Check server IP: `curl ifconfig.me`

### SSL Issues
1. Check certificate: `sudo certbot certificates`
2. Renew if needed: `sudo certbot renew`
3. Check nginx SSL config in `/etc/nginx/sites-available/pixelgarage.space`

### Translation System Not Working
1. Check if files exist: `ls /var/www/pixelgarage.space/assets/js/translations.js`
2. Check browser console for JavaScript errors
3. Verify language switcher CSS is loaded

### Performance Issues
1. Enable gzip compression in nginx
2. Optimize images and assets
3. Use CDN for static assets
4. Monitor server resources

## 📈 Monitoring & Analytics

### Server Monitoring
- CPU/Memory usage: `htop`
- Disk space: `df -h`
- Network: `netstat -tulpn`

### Website Analytics
- Nginx access logs: `/var/log/nginx/pixelgarage.space.access.log`
- Error tracking: `/var/log/nginx/pixelgarage.space.error.log`
- SSL certificate expiry monitoring

### Backup Strategy
- Automated daily backups
- Manual backup before updates
- Database backups (if applicable)
- Configuration backups

## 🎉 Success Checklist

- [ ] Website loads at `http://pixelgarage.space`
- [ ] All 5 sections accessible and functional
- [ ] Translation system works (PL/EN/ES)
- [ ] All 12 project demos working
- [ ] Contact forms redirect to Telegram/WhatsApp
- [ ] SSL certificate installed and working
- [ ] DNS properly configured
- [ ] Backup system in place
- [ ] Monitoring configured

## 📞 Support

If you encounter issues:
1. Check logs: `./manage-pixelgarage.sh` → View logs
2. Test functionality: `./manage-pixelgarage.sh` → Test website
3. Review this guide
4. Check nginx and system status

## 🔄 Updates

To update the website:
```bash
./manage-pixelgarage.sh
# Choose option 3: Update website
```

Or manually:
```bash
rsync -avz --delete ./orchestrator-full-v2/ user@server:/var/www/pixelgarage.space/
```

---

**🎯 Your PixelGarage.space platform is now ready for the world!**
