<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Komunikacja Live - PixelGarage | Telegram & WhatsApp</title>
    <meta name="description" content="Skontaktuj się z PixelGarage natychmiast przez Telegram lub WhatsApp. Dostępny 24/7 dla konsultacji, wycen i wsparcia technicznego.">
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/communication.css">
    <link rel="stylesheet" href="../assets/css/responsive-universal.css">
    <link rel="stylesheet" href="../assets/css/responsive-components.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Premium Navigation -->
    <nav class="navbar glass-effect premium-navbar">
        <div class="nav-container">
            <!-- Logo Section - Lewo (1/6) -->
            <div class="nav-brand premium-brand">
                <a href="../" class="logo-container premium-logo" title="Strona Główna">
                    <div class="logo-glow"></div>
                    <span class="logo-icon premium-icon">🎯</span>
                    <span class="logo-text premium-text">
                        Pixel<span class="logo-accent premium-accent">Garage</span>
                    </span>
                    <div class="logo-particles"></div>
                </a>
            </div>

            <!-- Premium Navigation Menu - Centrum (3/6) -->
            <ul class="nav-menu premium-nav-menu">
                <li class="nav-item premium-nav-item">
                    <button class="nav-link premium-nav-link katalog-btn" onclick="window.location.href='../services/'">
                        <div class="nav-btn-glow"></div>
                        <i class="fas fa-magic"></i>
                        <span data-translate="nav.services">Katalog</span>
                        <div class="nav-btn-ripple"></div>
                    </button>
                </li>
                <li class="nav-item premium-nav-item">
                    <button class="nav-link premium-nav-link portfolio-btn" onclick="window.location.href='../gallery/'">
                        <div class="nav-btn-glow"></div>
                        <i class="fas fa-star"></i>
                        <span data-translate="nav.portfolio">Portfolio</span>
                        <div class="nav-btn-ripple"></div>
                    </button>
                </li>
                <li class="nav-item premium-nav-item">
                    <button class="nav-link premium-nav-link academy-btn" onclick="window.location.href='../academy/'">
                        <div class="nav-btn-glow"></div>
                        <i class="fas fa-graduation-cap"></i>
                        <span data-translate="nav.academy">Tech Academy</span>
                        <div class="nav-btn-ripple"></div>
                    </button>
                </li>
            </ul>

            <!-- Contact Actions - Prawo (2/6) -->
            <div class="nav-actions">
                <button class="premium-contact telegram" onclick="openTelegram()" title="Telegram">
                    <div class="contact-glow telegram-glow"></div>
                    <i class="fab fa-telegram"></i>
                    <span data-translate="common.telegram">Telegram</span>
                    <div class="contact-pulse"></div>
                </button>
                <button class="premium-contact whatsapp" onclick="openWhatsApp()" title="WhatsApp">
                    <div class="contact-glow whatsapp-glow"></div>
                    <i class="fab fa-whatsapp"></i>
                    <span data-translate="common.whatsapp">WhatsApp</span>
                    <div class="contact-pulse"></div>
                </button>
            </div>

            <!-- Mobile Hamburger -->
            <div class="hamburger premium-hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <!-- Premium Background Effects -->
        <div class="navbar-effects">
            <div class="floating-particles"></div>
            <div class="gradient-waves"></div>
            <div class="light-rays"></div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="communication-hero">
        <div class="hero-background">
            <div class="gradient-orb orb-1"></div>
            <div class="gradient-orb orb-2"></div>
            <div class="chat-bubbles"></div>
        </div>
        
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-bolt"></i>
                    <span data-translate="communication.hero.badge">Dostępny 16h/dzień • Natychmiastowe Odpowiedzi • Profesjonalne Wsparcie</span>
                </div>

                <h1 class="hero-title">
                    <span data-translate="communication.hero.title">Komunikacja</span>
                    <span class="gradient-text" data-translate="communication.hero.titleHighlight">Live</span>
                </h1>

                <p class="hero-description" data-translate="communication.hero.description">
                    Skontaktuj się ze mną natychmiast przez Telegram lub WhatsApp. Jestem dostępny 24/7
                    dla konsultacji, wycen projektów, wsparcia technicznego i odpowiedzi na wszystkie pytania.
                </p>
                
                <div class="hero-stats">
                    <div class="stat-item">
                        <div class="stat-number-text">< 2h</div>
                        <div class="stat-label">Czas odpowiedzi</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number-text">16h/dzień</div>
                        <div class="stat-label">Dostępność</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number-text">5+ lat</div>
                        <div class="stat-label">Doświadczenie</div>
                    </div>
                </div>
                
                <div class="hero-actions">
                    <button class="btn-primary large telegram-btn" onclick="openTelegram()">
                        <i class="fab fa-telegram"></i>
                        <span>Telegram</span>
                        <small>Preferowane</small>
                    </button>
                    <button class="btn-secondary large whatsapp-btn" onclick="openWhatsApp()">
                        <i class="fab fa-whatsapp"></i>
                        <span>WhatsApp</span>
                        <small>Rozmowy głosowe</small>
                    </button>
                </div>
                
                <div class="status-indicator">
                    <div class="status-dot online"></div>
                    <span>Online teraz • Gotowy do rozmowy!</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Communication Options -->
    <section class="communication-options">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Wybierz sposób komunikacji</h2>
                <p class="section-subtitle">Każdy kanał oferuje natychmiastowy kontakt i profesjonalne wsparcie</p>
            </div>
            
            <div class="options-grid">
                <div class="option-card telegram-card glass-effect" onclick="openTelegram()">
                    <div class="option-icon">
                        <i class="fab fa-telegram"></i>
                    </div>
                    <h3>Telegram</h3>
                    <p>Szybka komunikacja z możliwością wysyłania plików, zdjęć i dokumentów</p>
                    <div class="option-features">
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>Natychmiastowe wiadomości</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>Wysyłanie plików do 2GB</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>Bezpieczne szyfrowanie</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>Historia rozmów</span>
                        </div>
                    </div>
                    <div class="option-cta">
                        <span>Rozpocznij chat</span>
                        <i class="fas fa-arrow-right"></i>
                    </div>
                </div>

                <div class="option-card whatsapp-card glass-effect" onclick="openWhatsApp()">
                    <div class="option-icon">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <h3>WhatsApp</h3>
                    <p>Popularna platforma z możliwością rozmów głosowych i wideo</p>
                    <div class="option-features">
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>Wiadomości tekstowe</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>Rozmowy głosowe</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>Połączenia wideo</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>Udostępnianie lokalizacji</span>
                        </div>
                    </div>
                    <div class="option-cta">
                        <span>Rozpocznij chat</span>
                        <i class="fas fa-arrow-right"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Actions -->
    <section class="quick-actions">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Szybkie akcje</h2>
                <p class="section-subtitle">Wybierz gotowy szablon wiadomości dla szybszego kontaktu</p>
            </div>
            
            <div class="actions-grid">
                <div class="action-card glass-effect" onclick="sendQuickMessage('quote')">
                    <div class="action-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <h4>Zapytaj o wycenę</h4>
                    <p>Otrzymaj szczegółową wycenę dla swojego projektu</p>
                </div>

                <div class="action-card glass-effect" onclick="sendQuickMessage('consultation')">
                    <div class="action-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h4>Umów konsultację</h4>
                    <p>Bezpłatna konsultacja projektowa i techniczna</p>
                </div>

                <div class="action-card glass-effect" onclick="sendQuickMessage('support')">
                    <div class="action-icon">
                        <i class="fas fa-life-ring"></i>
                    </div>
                    <h4>Wsparcie techniczne</h4>
                    <p>Pomoc z istniejącymi projektami i rozwiązaniami</p>
                </div>

                <div class="action-card glass-effect" onclick="sendQuickMessage('partnership')">
                    <div class="action-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h4>Współpraca</h4>
                    <p>Omów możliwości długoterminowej współpracy</p>
                </div>

                <div class="action-card glass-effect" onclick="sendQuickMessage('custom')">
                    <div class="action-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h4>Projekt na zamówienie</h4>
                    <p>Dedykowane rozwiązanie dla Twojego biznesu</p>
                </div>

                <div class="action-card glass-effect" onclick="sendQuickMessage('academy')">
                    <div class="action-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h4>Tech Academy</h4>
                    <p>Zapytaj o kursy i szkolenia technologiczne</p>
                </div>
            </div>
        </div>
    </section>



    <!-- Contact Info -->
    <section class="contact-info">
        <div class="container">
            <div class="info-container glass-effect">
                <div class="info-content">
                    <h2>Informacje kontaktowe</h2>
                    <div class="contact-details">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fab fa-telegram"></i>
                            </div>
                            <div class="contact-text">
                                <h4>Telegram</h4>
                                <p>@pixel_garage</p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fab fa-whatsapp"></i>
                            </div>
                            <div class="contact-text">
                                <h4>WhatsApp</h4>
                                <p>+34 645 577 385</p>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="contact-text">
                                <h4>Dostępność</h4>
                                <p>24/7 - zawsze online</p>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-language"></i>
                            </div>
                            <div class="contact-text">
                                <h4>Języki</h4>
                                <p>Polski, Angielski, Hiszpański</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="info-visual">
                    <div class="communication-preview">
                        <div class="phone-mockup">
                            <div class="phone-screen">
                                <div class="chat-preview">
                                    <div class="chat-message received">
                                        <div class="message-bubble">
                                            Cześć! Jestem zainteresowany usługami PixelGarage
                                        </div>
                                        <div class="message-time">14:30</div>
                                    </div>
                                    <div class="chat-message sent">
                                        <div class="message-bubble">
                                            Świetnie! Opowiedz mi więcej o swoim projekcie 🚀
                                        </div>
                                        <div class="message-time">14:31</div>
                                    </div>
                                    <div class="typing-indicator">
                                        <div class="typing-dots">
                                            <span></span>
                                            <span></span>
                                            <span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div class="logo-container">
                        <span class="logo-icon">🎯</span>
                        <span class="logo-text">Pixel<span class="logo-accent">Garage</span></span>
                    </div>
                    <p>Tworzymy przyszłość cyfrową z technologią AI</p>
                </div>
                <div class="footer-links">
                    <div class="footer-section">
                        <h4>Kontakt</h4>
                        <a href="#" onclick="openTelegram()">Telegram</a>
                        <a href="#" onclick="openWhatsApp()">WhatsApp</a>
                    </div>
                    <div class="footer-section">
                        <h4>Usługi</h4>
                        <a href="../services/">Katalog Usług</a>
                        <a href="../gallery/">Portfolio</a>
                        <a href="../academy/">Tech Academy</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 PixelGarage. Wszystkie prawa zastrzeżone.</p>
            </div>
        </div>
    </footer>

    <script src="../assets/js/translations.js"></script>
    <script src="../assets/js/language-switcher.js"></script>
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/communication.js"></script>

    <script>
        // Initialize translation system
        document.addEventListener('DOMContentLoaded', function() {
            initializeLanguage();
        });
    </script>
</body>
</html>
