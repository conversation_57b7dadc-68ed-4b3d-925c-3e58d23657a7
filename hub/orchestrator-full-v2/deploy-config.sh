#!/bin/bash

# ===== PIXELGARAGE DEPLOYMENT CONFIGURATION =====
# Configuration script for mikr.us deployment
# Run this first to set up your deployment settings

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 PixelGarage Deployment Configuration${NC}"
echo "======================================"
echo ""

# Function to read user input
read_input() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    if [ -n "$default" ]; then
        read -p "$prompt [$default]: " input
        if [ -z "$input" ]; then
            input="$default"
        fi
    else
        read -p "$prompt: " input
        while [ -z "$input" ]; do
            echo -e "${RED}This field is required!${NC}"
            read -p "$prompt: " input
        done
    fi
    
    eval "$var_name='$input'"
}

echo "Please provide your mikr.us server details:"
echo ""

# Collect server information
read_input "Server hostname (e.g., myapp.mikr.us)" "" SERVER_HOST
read_input "SSH username" "root" SERVER_USER
read_input "Server path for website" "/var/www/html" SERVER_PATH
read_input "Local project path" "./orchestrator-full-v2" LOCAL_PATH

echo ""
echo -e "${YELLOW}Configuration Summary:${NC}"
echo "Server Host: $SERVER_HOST"
echo "SSH User: $SERVER_USER"
echo "Server Path: $SERVER_PATH"
echo "Local Path: $LOCAL_PATH"
echo ""

read -p "Is this configuration correct? (y/n): " confirm
if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
    echo "Configuration cancelled."
    exit 1
fi

# Update deploy.sh with the configuration
echo ""
echo -e "${BLUE}Updating deploy.sh with your configuration...${NC}"

# Create a backup of the original deploy.sh
cp deploy.sh deploy.sh.backup

# Update the configuration in deploy.sh
sed -i.tmp "s/SERVER_HOST=\"your-server.mikr.us\"/SERVER_HOST=\"$SERVER_HOST\"/" deploy.sh
sed -i.tmp "s/SERVER_USER=\"your-username\"/SERVER_USER=\"$SERVER_USER\"/" deploy.sh
sed -i.tmp "s|SERVER_PATH=\"/var/www/html\"|SERVER_PATH=\"$SERVER_PATH\"|" deploy.sh
sed -i.tmp "s|LOCAL_PATH=\"./orchestrator-full-v2\"|LOCAL_PATH=\"$LOCAL_PATH\"|" deploy.sh

# Remove temporary file
rm -f deploy.sh.tmp

# Make deploy.sh executable
chmod +x deploy.sh

echo -e "${GREEN}✅ Configuration completed successfully!${NC}"
echo ""
echo "Next steps:"
echo "1. Ensure you have SSH key access to your server"
echo "2. Run: ./deploy.sh"
echo ""
echo -e "${YELLOW}Important Notes:${NC}"
echo "• Make sure your SSH key is added to the server"
echo "• The script will install nginx if not present"
echo "• A backup will be created before deployment"
echo "• The script requires sudo access on the server"
echo ""
