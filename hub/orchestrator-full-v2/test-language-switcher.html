<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Language Switcher - PixelGarage</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-content {
            color: white;
            line-height: 1.6;
        }
        
        .test-buttons {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .test-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .navbar-test {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px 30px;
            border-radius: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo {
            color: white;
            font-size: 24px;
            font-weight: 700;
        }
        
        .nav-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .status-indicator {
            padding: 10px 20px;
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.3);
            border-radius: 8px;
            color: #22c55e;
            font-size: 14px;
            margin: 10px 0;
        }
        
        .content-layers {
            position: relative;
            z-index: 1;
        }
        
        .layer {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            position: relative;
        }
        
        .layer-label {
            position: absolute;
            top: -10px;
            left: 20px;
            background: rgba(99, 102, 241, 0.8);
            color: white;
            padding: 4px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
        }
    </style>
</head>
<body data-page="test">
    <!-- Test Navbar -->
    <nav class="navbar-test">
        <div class="logo">
            🎯 PixelGarage
        </div>
        <div class="nav-actions">
            <!-- Language switcher will be added here automatically -->
        </div>
    </nav>

    <div class="test-container">
        <div class="test-header">
            <h1 data-translate="nav.home">🌐 Test Language Switcher</h1>
            <p data-translate="landing.hero.description">Sprawdź czy switcher językowy działa poprawnie nad wszystkimi warstwami</p>
            <div class="status-indicator">
                <i class="fas fa-check-circle"></i>
                <span>Language Switcher Active</span>
            </div>
        </div>

        <div class="test-section">
            <h2 data-translate="landing.services.title">Test Navigation</h2>
            <div class="test-content">
                <p data-translate="landing.hero.subtitle">Kliknij na switcher językowy w prawym górnym rogu nawigacji.</p>
                <p data-translate="common.learnMore">Dropdown powinien pojawić się nad wszystkimi elementami strony.</p>
            </div>
            <div class="test-buttons">
                <button class="test-btn" data-translate="nav.home">Strona Główna</button>
                <button class="test-btn" data-translate="nav.services">Katalog Usług</button>
                <button class="test-btn" data-translate="nav.portfolio">Portfolio</button>
                <button class="test-btn" data-translate="nav.academy">Tech Academy</button>
            </div>
        </div>

        <div class="content-layers">
            <div class="layer" style="z-index: 10;">
                <div class="layer-label">Z-Index: 10</div>
                <h3 data-translate="services.hero.title">Warstwa 1</h3>
                <p data-translate="services.hero.description">Ta warstwa ma z-index: 10. Switcher językowy powinien być widoczny nad nią.</p>
            </div>

            <div class="layer" style="z-index: 100;">
                <div class="layer-label">Z-Index: 100</div>
                <h3 data-translate="portfolio.hero.title">Warstwa 2</h3>
                <p data-translate="portfolio.hero.description">Ta warstwa ma z-index: 100. Switcher językowy nadal powinien być widoczny.</p>
            </div>

            <div class="layer" style="z-index: 1000;">
                <div class="layer-label">Z-Index: 1000</div>
                <h3 data-translate="academy.hero.title">Warstwa 3</h3>
                <p data-translate="academy.hero.description">Ta warstwa ma z-index: 1000. Switcher językowy musi być nad nią!</p>
            </div>

            <div class="layer" style="z-index: 5000;">
                <div class="layer-label">Z-Index: 5000</div>
                <h3 data-translate="communication.hero.title">Warstwa 4</h3>
                <p data-translate="communication.hero.description">Ta warstwa ma bardzo wysoki z-index: 5000. Switcher językowy z z-index: 99999 powinien być nad nią.</p>
            </div>
        </div>

        <div class="test-section">
            <h2>Test Instructions</h2>
            <div class="test-content">
                <ol style="color: white;">
                    <li><strong>Kliknij switcher językowy</strong> - Dropdown powinien się otworzyć nad wszystkimi warstwami</li>
                    <li><strong>Zmień język na angielski</strong> - Cała zawartość powinna się przetłumaczyć</li>
                    <li><strong>Zmień język na hiszpański</strong> - Ponownie wszystko powinno się przetłumaczyć</li>
                    <li><strong>Przewiń stronę</strong> - Dropdown powinien się automatycznie repozycjonować</li>
                    <li><strong>Zmień rozmiar okna</strong> - Dropdown powinien się dostosować</li>
                    <li><strong>Kliknij poza dropdown</strong> - Powinien się zamknąć</li>
                    <li><strong>Naciśnij ESC</strong> - Dropdown powinien się zamknąć</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h2>Debug Console</h2>
            <div class="test-content">
                <p>Otwórz konsolę przeglądarki (F12) i sprawdź:</p>
                <div class="test-buttons">
                    <button class="test-btn" onclick="console.log('Current language:', getCurrentLanguage())">
                        Check Current Language
                    </button>
                    <button class="test-btn" onclick="console.log('Available translations:', Object.keys(translations))">
                        Check Available Languages
                    </button>
                    <button class="test-btn" onclick="console.log('Dropdown z-index:', getComputedStyle(document.querySelector('.lang-dropdown')).zIndex)">
                        Check Dropdown Z-Index
                    </button>
                    <button class="test-btn" onclick="setLanguage('en'); console.log('Switched to English')">
                        Force Switch to English
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/translations.js"></script>
    <script src="assets/js/language-switcher.js"></script>
    
    <script>
        // Initialize translation system
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌐 Initializing language switcher test...');
            initializeLanguage();
            
            // Add some test logging
            console.log('Available languages:', Object.keys(translations));
            console.log('Current language:', getCurrentLanguage());
            
            // Test dropdown positioning
            setTimeout(() => {
                const dropdown = document.querySelector('.lang-dropdown');
                if (dropdown) {
                    console.log('Dropdown z-index:', getComputedStyle(dropdown).zIndex);
                    console.log('Dropdown position:', getComputedStyle(dropdown).position);
                }
            }, 1000);
        });
        
        // Add test for language switching
        function testLanguageSwitching() {
            console.log('🧪 Testing language switching...');
            
            const languages = ['pl', 'en', 'es'];
            let currentIndex = 0;
            
            const interval = setInterval(() => {
                const lang = languages[currentIndex];
                setLanguage(lang);
                console.log(`Switched to: ${lang}`);
                
                currentIndex++;
                if (currentIndex >= languages.length) {
                    clearInterval(interval);
                    console.log('✅ Language switching test completed');
                }
            }, 2000);
        }
        
        // Expose test function globally
        window.testLanguageSwitching = testLanguageSwitching;
        
        console.log('🎯 Test page loaded. Run testLanguageSwitching() to auto-test language switching.');
    </script>
</body>
</html>
