#!/bin/bash

# ===== PIXELGARAGE.SPACE DOMAIN SETUP HELPER =====
# Helper script for DNS configuration and domain setup
# Author: <PERSON> <PERSON> <PERSON><PERSON><PERSON>Garage Founder

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🌐 PixelGarage.space Domain Setup Helper"
echo "======================================"

# Get server information
read -p "Enter your mikr.us server hostname: " SERVER_HOST
read -p "Enter your mikr.us username: " SERVER_USER

# Get server IP
log_info "Detecting server IP address..."
SERVER_IP=$(ssh "$SERVER_USER@$SERVER_HOST" "curl -s ifconfig.me" 2>/dev/null || echo "")

if [ -z "$SERVER_IP" ]; then
    log_warning "Could not automatically detect server IP."
    read -p "Please enter your server IP address manually: " SERVER_IP
fi

echo ""
log_success "Server IP detected: $SERVER_IP"
echo ""

# DNS Configuration Instructions
echo "🔧 DNS CONFIGURATION FOR PIXELGARAGE.SPACE"
echo "=========================================="
echo ""
echo "Configure these DNS records in your domain registrar panel:"
echo ""
echo "1. A Record (Root domain):"
echo "   Name: @ (or leave empty)"
echo "   Type: A"
echo "   Value: $SERVER_IP"
echo "   TTL: 3600 (1 hour)"
echo ""
echo "2. A Record (WWW subdomain):"
echo "   Name: www"
echo "   Type: A" 
echo "   Value: $SERVER_IP"
echo "   TTL: 3600 (1 hour)"
echo ""
echo "Alternative CNAME setup:"
echo "   Name: www"
echo "   Type: CNAME"
echo "   Value: $SERVER_HOST"
echo "   TTL: 3600 (1 hour)"
echo ""

# Test DNS propagation
test_dns() {
    log_info "Testing DNS propagation..."
    
    echo "Testing pixelgarage.space..."
    if nslookup pixelgarage.space | grep -q "$SERVER_IP"; then
        log_success "pixelgarage.space resolves correctly!"
    else
        log_warning "pixelgarage.space does not resolve to $SERVER_IP yet."
        echo "This is normal if you just configured DNS. Propagation can take up to 48 hours."
    fi
    
    echo ""
    echo "Testing www.pixelgarage.space..."
    if nslookup www.pixelgarage.space | grep -q "$SERVER_IP"; then
        log_success "www.pixelgarage.space resolves correctly!"
    else
        log_warning "www.pixelgarage.space does not resolve to $SERVER_IP yet."
    fi
}

# Check website accessibility
test_website() {
    log_info "Testing website accessibility..."
    
    # Test direct IP
    if curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_IP" | grep -q "200"; then
        log_success "Website accessible via IP: http://$SERVER_IP"
    else
        log_error "Website not accessible via IP"
    fi
    
    # Test domain
    if curl -s -o /dev/null -w "%{http_code}" "http://pixelgarage.space" | grep -q "200"; then
        log_success "Website accessible via domain: http://pixelgarage.space"
    else
        log_warning "Website not yet accessible via domain (DNS may still be propagating)"
    fi
}

# SSL Setup reminder
ssl_setup() {
    echo ""
    echo "🔒 SSL CERTIFICATE SETUP"
    echo "========================"
    echo ""
    echo "After DNS propagation is complete, set up SSL certificate:"
    echo ""
    echo "1. SSH to your server:"
    echo "   ssh $SERVER_USER@$SERVER_HOST"
    echo ""
    echo "2. Install certbot (if not already installed):"
    echo "   sudo apt update"
    echo "   sudo apt install -y certbot python3-certbot-nginx"
    echo ""
    echo "3. Get SSL certificate:"
    echo "   sudo certbot --nginx -d pixelgarage.space -d www.pixelgarage.space"
    echo ""
    echo "4. Test auto-renewal:"
    echo "   sudo certbot renew --dry-run"
    echo ""
}

# Main menu
main_menu() {
    while true; do
        echo ""
        echo "Choose an option:"
        echo "1. Show DNS configuration instructions"
        echo "2. Test DNS propagation"
        echo "3. Test website accessibility"
        echo "4. Show SSL setup instructions"
        echo "5. Exit"
        echo ""
        read -p "Enter your choice (1-5): " choice
        
        case $choice in
            1)
                echo ""
                echo "🔧 DNS CONFIGURATION FOR PIXELGARAGE.SPACE"
                echo "=========================================="
                echo ""
                echo "Configure these DNS records:"
                echo ""
                echo "A Record: @ → $SERVER_IP"
                echo "A Record: www → $SERVER_IP"
                echo ""
                ;;
            2)
                test_dns
                ;;
            3)
                test_website
                ;;
            4)
                ssl_setup
                ;;
            5)
                echo "Goodbye!"
                exit 0
                ;;
            *)
                log_error "Invalid choice. Please enter 1-5."
                ;;
        esac
    done
}

# Show initial information
echo ""
echo "📋 SUMMARY"
echo "=========="
echo "Domain: pixelgarage.space"
echo "Server: $SERVER_HOST"
echo "IP: $SERVER_IP"
echo ""

# Run main menu
main_menu
