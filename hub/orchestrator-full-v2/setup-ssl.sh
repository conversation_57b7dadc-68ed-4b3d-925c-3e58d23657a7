#!/bin/bash

# ===== SSL SETUP SCRIPT FOR PIXELGARAGE =====
# Optional script to set up SSL certificate with Let's Encrypt
# Run after successful deployment

set -e

# Configuration (will be updated by deploy-config.sh)
SERVER_HOST="your-server.mikr.us"
SERVER_USER="your-username"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${BLUE}🔒 PixelGarage SSL Setup${NC}"
echo "======================="
echo ""

# Check if configuration is set
if [ "$SERVER_HOST" = "your-server.mikr.us" ] || [ "$SERVER_USER" = "your-username" ]; then
    log_error "Please run deploy-config.sh first to configure your server details!"
    exit 1
fi

log_info "Setting up SSL certificate for $SERVER_HOST..."

# Install certbot and configure SSL
ssh "$SERVER_USER@$SERVER_HOST" "
    # Update package list
    sudo apt update
    
    # Install certbot
    if ! command -v certbot &> /dev/null; then
        sudo apt install -y certbot python3-certbot-nginx
    fi
    
    # Get SSL certificate
    sudo certbot --nginx -d $SERVER_HOST --non-interactive --agree-tos --email admin@$SERVER_HOST
    
    # Set up auto-renewal
    sudo systemctl enable certbot.timer
    sudo systemctl start certbot.timer
    
    # Test nginx configuration
    sudo nginx -t
    
    # Reload nginx
    sudo systemctl reload nginx
"

if [ $? -eq 0 ]; then
    log_success "SSL certificate installed successfully!"
    echo ""
    echo "Your website is now available at: https://$SERVER_HOST"
    echo ""
    echo "SSL certificate will auto-renew every 90 days."
else
    log_error "SSL setup failed. Please check the error messages above."
    exit 1
fi
