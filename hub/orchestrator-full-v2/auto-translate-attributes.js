// Auto-add translation attributes to PixelGarage elements
// This script automatically adds data-translate attributes to elements that need translation

function autoAddTranslationAttributes() {
    console.log('🌐 Auto-adding translation attributes...');
    
    // Mapping of text content to translation keys
    const translationMap = {
        // Page title and meta
        'PixelGarage - Tworzymy Przyszłość Cyfrową z AI | Portfolio & Usługi': 'meta.title',
        
        // Navigation and brand
        'PixelGarage': 'brand.name',
        'Pixel': 'brand.pixel',
        'Garage': 'brand.garage',
        
        // Hero section
        'Robert - PixelGarage': 'landing.hero.profile.name',
        'Founder & Lead Developer': 'landing.hero.profile.role',
        'AI Development': 'landing.hero.profile.skills.ai',
        'Quality Assurance': 'landing.hero.profile.skills.qa',
        'Web Development': 'landing.hero.profile.skills.web',
        'Project Management': 'landing.hero.profile.skills.pm',
        'Projektów Portfolio': 'landing.stats.projects',
        
        // Services section
        'Nasze Usługi': 'landing.services.sectionTitle',
        'Przyszłościowe rozwiązania cyfrowe z wykorzystaniem najnowszych technologii': 'landing.services.sectionSubtitle',
        'Katalog Rozwiązań': 'landing.services.catalog.title',
        '163+ innowacyjnych produktów cyfrowych wykorzystujących najnowsze technologie': 'landing.services.catalog.description',
        'Odkryj rozwiązania': 'landing.services.catalog.cta',
        'Portfolio Realizacji': 'landing.services.portfolio.title',
        '12 projektów demonstracyjnych pokazujących możliwości nowoczesnych technologii': 'landing.services.portfolio.description',
        'Eksploruj portfolio': 'landing.services.portfolio.cta',
        'Darmowa Konsultacja': 'landing.services.consultation.title',
        'Bezpłatna analiza Twojego projektu z ekspertem - poznaj możliwości i otrzymaj profesjonalne wskazówki': 'landing.services.consultation.description',
        'Umów konsultację': 'landing.services.consultation.cta',
        'Trójkąt Przełamany': 'landing.services.triangle.title',
        'U nas masz wszystko: szybko, tanio i profesjonalnie - przełamujemy klasyczny trójkąt zależności': 'landing.services.triangle.description',
        'Zobacz jak to robimy': 'landing.services.triangle.cta',
        'Tech Academy Exclusive': 'landing.services.academyCard.title',
        'Ekskluzywne szkolenia technologiczne na każdym poziomie zaawansowania - od podstaw po ekspertów': 'landing.services.academyCard.description',
        'Dołącz do Academy': 'landing.services.academyCard.cta',
        
        // Projects
        'SmartCard.pl': 'landing.projects.smartcard.name',
        'AI-powered generator wizytówek cyfrowych z avatarami AI, kodami QR, NFC i zaawansowaną analityką. Kompletny system networking dla profesjonalistów.': 'landing.projects.smartcard.description',
        '💰 Revenue Ready': 'landing.projects.smartcard.badge',
        'Live & Monetizing': 'landing.projects.smartcard.status',
        'EventAI.pl': 'landing.projects.eventai.name',
        'Inteligentny planer wydarzeń z Llama 3.1 8B AI, optymalizacją budżetu, marketplace dostawców i zaawansowanymi analytics dla organizatorów.': 'landing.projects.eventai.description',
        '🚀 Innovative': 'landing.projects.eventai.badge',
        'In Development': 'landing.projects.eventai.status',
        'FitGenius.pl': 'landing.projects.fitgenius.name',
        'Enterprise-grade AI fitness coaching platform z personalizowanymi treningami, polską bazą żywności, form checkerem i zaawansowaną gamifikacją.': 'landing.projects.fitgenius.description',
        '🏆 Perfection Mode': 'landing.projects.fitgenius.badge',
        'Completed': 'landing.projects.fitgenius.status',
        'Chcesz zobaczyć pozostałe 9 projektów?': 'landing.projects.portfolioCta',
        
        // Academy section
        'VIP EXCLUSIVE': 'landing.academy.badges.exclusive',
        'LIMITOWANE MIEJSCA': 'landing.academy.badges.limited',
        'Live Sessions': 'landing.academy.features.live.title',
        'Interaktywne sesje prowadzone przez eksperta w czasie rzeczywistym, zapewniające bezpośredni kontakt i możliwość zadawania pytań': 'landing.academy.features.live.description',
        'Grupa Dostosowana': 'landing.academy.features.group.title',
        'Curriculum dostosowany do poziomu zaawansowania uczestników, zapewniający optymalne tempo nauki i maksymalizację efektów': 'landing.academy.features.group.description',
        'Weekendy/Wieczory': 'landing.academy.features.schedule.title',
        'Profesjonalny harmonogram dostosowany do potrzeb pracujących specjalistów, umożliwiający rozwój bez konfliktów z obowiązkami zawodowymi': 'landing.academy.features.schedule.description',
        'Praktyka 2025': 'landing.academy.features.practice.title',
        'Kompetencje technologiczne zgodne z aktualnymi trendami rynkowymi i wymaganiami pracodawców w sektorze IT na rok 2025': 'landing.academy.features.practice.description',
        'Limitowana akademia - zapisz się jako jeden z pierwszych': 'landing.academy.status',
        'Ekskluzywny dostęp': 'landing.academy.benefits.access',
        'Early Access': 'landing.academy.benefits.early',
        'Priorytetowy dostęp do sesji': 'landing.academy.benefits.priority',
        'Bezpośredni kontakt z ekspertem': 'landing.academy.benefits.contact',
        'Materiały premium': 'landing.academy.benefits.materials',
        'Zapisz się na limitowaną akademię': 'landing.academy.cta.register',
        'Zapytaj o szczegóły': 'landing.academy.cta.details',
        
        // Contact section
        'Gotowy na rozpoczęcie projektu?': 'landing.contact.title',
        'Skontaktuj się ze mną już teraz i omówmy Twoje potrzeby': 'landing.contact.subtitle',
        'Telegram': 'common.telegram',
        'WhatsApp': 'common.whatsapp',
        
        // Footer
        'Tworzymy przyszłość cyfrową z najnowszymi technologiami': 'landing.footer.description',
        '© 2025 PixelGarage. Wszystkie prawa zastrzeżone.': 'landing.footer.copyright',
        'Usługi': 'landing.footer.services',
        'Kontakt': 'landing.footer.contact',
        
        // Common elements
        'Użytkowników': 'common.users',
        'Wizytówek': 'common.cards',
        'Uptime': 'common.uptime',
        'Oszczędności': 'common.savings',
        'Mniej czasu': 'common.lessTime',
        'Zadowolenie': 'common.satisfaction',
        'Aktywnych użytkowników': 'common.activeUsers',
        'Sukces w 12 tyg.': 'common.success12weeks',
        'Treningów AI': 'common.aiWorkouts',
        'Pełne Portfolio (12 projektów)': 'common.fullPortfolio',
        'Rozpocznij Współpracę': 'common.startCollaboration'
    };
    
    // Function to add translation attribute
    function addTranslationAttribute(element, key) {
        if (!element.hasAttribute('data-translate')) {
            element.setAttribute('data-translate', key);
            console.log(`✅ Added: "${element.textContent.trim()}" -> ${key}`);
            return true;
        }
        return false;
    }
    
    // Process all text elements
    let addedCount = 0;
    
    // Find elements by exact text match
    Object.entries(translationMap).forEach(([text, key]) => {
        // Use XPath to find elements with exact text content
        const xpath = `//text()[normalize-space(.)='${text}']/parent::*`;
        const result = document.evaluate(xpath, document, null, XPathResult.UNORDERED_NODE_SNAPSHOT_TYPE, null);
        
        for (let i = 0; i < result.snapshotLength; i++) {
            const element = result.snapshotItem(i);
            if (element && element.textContent.trim() === text) {
                if (addTranslationAttribute(element, key)) {
                    addedCount++;
                }
            }
        }
    });
    
    // Process specific selectors
    const specificSelectors = [
        { selector: 'title', key: 'meta.title' },
        { selector: '.logo-text', key: 'brand.name' },
        { selector: '.logo-accent', key: 'brand.garage' },
        { selector: '.stat-number', key: 'common.number' },
        { selector: '.metric-number', key: 'common.metric' },
        { selector: '.rating-text', key: 'common.rating' }
    ];
    
    specificSelectors.forEach(({ selector, key }) => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (!element.hasAttribute('data-translate') && element.textContent.trim()) {
                if (addTranslationAttribute(element, key)) {
                    addedCount++;
                }
            }
        });
    });
    
    console.log(`✅ Added ${addedCount} translation attributes`);
    
    // Update page content after adding attributes
    if (typeof updatePageContent === 'function') {
        updatePageContent();
        console.log('🔄 Updated page content with new translations');
    }
    
    return addedCount;
}

// Auto-run when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', autoAddTranslationAttributes);
} else {
    autoAddTranslationAttributes();
}

// Export for manual use
window.autoAddTranslationAttributes = autoAddTranslationAttributes;
