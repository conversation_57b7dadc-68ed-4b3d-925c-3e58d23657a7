#!/bin/bash

# ===== PIXELGARAGE DEPLOYMENT SCRIPT FOR MIKR.US =====
# Deployment script for PixelGarage portfolio platform
# Target: mikr.us hosting without Docker
# Author: <PERSON> <PERSON> <PERSON>xelGarage Founder

set -e  # Exit on any error

# ===== CONFIGURATION =====
echo "🚀 PixelGarage Deployment Script v1.0"
echo "======================================"

# Server configuration - CONFIGURE THESE VALUES
DOMAIN="pixelgarage.space"
SERVER_HOST="your-server.mikr.us"  # Replace with your actual mikr.us server
SERVER_USER="your-username"        # Replace with your mikr.us username
SERVER_PATH="/var/www/pixelgarage.space"
LOCAL_PATH="./orchestrator-full-v2"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ===== FUNCTIONS =====
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log_info "Checking dependencies..."
    
    if ! command -v rsync &> /dev/null; then
        log_error "rsync is required but not installed. Please install rsync."
        exit 1
    fi
    
    if ! command -v ssh &> /dev/null; then
        log_error "ssh is required but not installed. Please install openssh-client."
        exit 1
    fi
    
    log_success "All dependencies are installed."
}

# Validate local files
validate_local_files() {
    log_info "Validating local files..."
    
    if [ ! -d "$LOCAL_PATH" ]; then
        log_error "Local path $LOCAL_PATH does not exist!"
        exit 1
    fi
    
    # Check for essential files
    essential_files=(
        "$LOCAL_PATH/index.html"
        "$LOCAL_PATH/assets/css"
        "$LOCAL_PATH/assets/js"
        "$LOCAL_PATH/services/index.html"
        "$LOCAL_PATH/gallery/index.html"
        "$LOCAL_PATH/academy/index.html"
        "$LOCAL_PATH/communication/index.html"
    )
    
    for file in "${essential_files[@]}"; do
        if [ ! -e "$file" ]; then
            log_error "Essential file/directory missing: $file"
            exit 1
        fi
    done
    
    log_success "Local files validation passed."
}

# Test server connection
test_connection() {
    log_info "Testing server connection..."
    
    if ssh -o ConnectTimeout=10 -o BatchMode=yes "$SERVER_USER@$SERVER_HOST" exit 2>/dev/null; then
        log_success "Server connection successful."
    else
        log_error "Cannot connect to server. Please check:"
        echo "  - Server host: $SERVER_HOST"
        echo "  - Username: $SERVER_USER"
        echo "  - SSH key authentication"
        exit 1
    fi
}

# Create backup of current deployment
create_backup() {
    log_info "Creating backup of current deployment..."
    
    BACKUP_DIR="/var/backups/pixelgarage-$(date +%Y%m%d-%H%M%S)"
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        if [ -d '$SERVER_PATH' ]; then
            sudo mkdir -p /var/backups
            sudo cp -r '$SERVER_PATH' '$BACKUP_DIR'
            echo 'Backup created at: $BACKUP_DIR'
        else
            echo 'No existing deployment found, skipping backup.'
        fi
    "
    
    log_success "Backup completed."
}

# Prepare server environment
prepare_server() {
    log_info "Preparing server environment..."
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        # Create directory structure
        sudo mkdir -p '$SERVER_PATH'
        sudo chown -R www-data:www-data '$SERVER_PATH'
        sudo chmod -R 755 '$SERVER_PATH'
        
        # Install/update nginx if not present
        if ! command -v nginx &> /dev/null; then
            sudo apt update
            sudo apt install -y nginx
        fi
        
        # Ensure nginx is running
        sudo systemctl enable nginx
        sudo systemctl start nginx
    "
    
    log_success "Server environment prepared."
}

# Deploy files
deploy_files() {
    log_info "Deploying files to server..."
    
    # Sync files with rsync
    rsync -avz --delete \
        --exclude='.git*' \
        --exclude='node_modules' \
        --exclude='*.log' \
        --exclude='deploy.sh' \
        --exclude='README.md' \
        --exclude='QUICK_START.md' \
        "$LOCAL_PATH/" "$SERVER_USER@$SERVER_HOST:$SERVER_PATH/"
    
    log_success "Files deployed successfully."
}

# Configure nginx
configure_nginx() {
    log_info "Configuring nginx..."
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        # Create nginx configuration
        sudo tee /etc/nginx/sites-available/pixelgarage > /dev/null << EOF
server {
    listen 80;
    listen [::]:80;

    server_name $DOMAIN www.$DOMAIN $SERVER_HOST;
    root $SERVER_PATH;
    index index.html;
    
    # Security headers
    add_header X-Frame-Options \"SAMEORIGIN\" always;
    add_header X-Content-Type-Options \"nosniff\" always;
    add_header X-XSS-Protection \"1; mode=block\" always;
    add_header Referrer-Policy \"no-referrer-when-downgrade\" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Cache static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        expires 1y;
        add_header Cache-Control \"public, immutable\";
    }
    
    # Main location
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # API endpoints (if needed)
    location /api/ {
        # Add API configuration here if needed
    }
    
    # Error pages
    error_page 404 /index.html;
    error_page 500 502 503 504 /index.html;
}
EOF
        
        # Enable site
        sudo ln -sf /etc/nginx/sites-available/pixelgarage /etc/nginx/sites-enabled/
        sudo rm -f /etc/nginx/sites-enabled/default
        
        # Test nginx configuration
        sudo nginx -t
        
        # Reload nginx
        sudo systemctl reload nginx
    "
    
    log_success "Nginx configured successfully."
}

# Set proper permissions
set_permissions() {
    log_info "Setting proper file permissions..."
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        sudo chown -R www-data:www-data '$SERVER_PATH'
        sudo find '$SERVER_PATH' -type d -exec chmod 755 {} \;
        sudo find '$SERVER_PATH' -type f -exec chmod 644 {} \;
    "
    
    log_success "Permissions set successfully."
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check if main files exist
    ssh "$SERVER_USER@$SERVER_HOST" "
        if [ -f '$SERVER_PATH/index.html' ]; then
            echo 'Main index.html found ✓'
        else
            echo 'Main index.html missing ✗'
            exit 1
        fi
        
        if [ -d '$SERVER_PATH/assets' ]; then
            echo 'Assets directory found ✓'
        else
            echo 'Assets directory missing ✗'
            exit 1
        fi
        
        if [ -f '$SERVER_PATH/services/index.html' ]; then
            echo 'Services page found ✓'
        else
            echo 'Services page missing ✗'
            exit 1
        fi
    "
    
    # Test HTTP response
    if curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_HOST" | grep -q "200"; then
        log_success "Website is responding correctly!"
    else
        log_warning "Website might not be responding correctly. Please check manually."
    fi
    
    log_success "Deployment verification completed."
}

# ===== MAIN DEPLOYMENT PROCESS =====
main() {
    echo ""
    log_info "Starting PixelGarage deployment process..."
    echo ""
    
    # Pre-deployment checks
    check_dependencies
    validate_local_files
    test_connection
    
    # Deployment process
    create_backup
    prepare_server
    deploy_files
    configure_nginx
    set_permissions
    verify_deployment
    
    echo ""
    log_success "🎉 PixelGarage deployment completed successfully!"
    echo ""
    echo "Your application is now live at: http://$SERVER_HOST"
    echo ""
    echo "Next steps:"
    echo "1. Test all pages and functionality"
    echo "2. Configure SSL certificate (recommended)"
    echo "3. Set up monitoring and backups"
    echo ""
}

# ===== SCRIPT EXECUTION =====
# Check if configuration is provided
if [ "$SERVER_HOST" = "your-server.mikr.us" ] || [ "$SERVER_USER" = "your-username" ]; then
    log_error "Please configure the script first!"
    echo ""
    echo "Edit the following variables in this script:"
    echo "  SERVER_HOST=\"your-actual-server.mikr.us\""
    echo "  SERVER_USER=\"your-actual-username\""
    echo "  SERVER_PATH=\"/var/www/html\" (or your preferred path)"
    echo ""
    exit 1
fi

# Run main function
main "$@"
