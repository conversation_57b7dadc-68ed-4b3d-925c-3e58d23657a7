<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Translation System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .language-switcher {
            margin-bottom: 30px;
            text-align: center;
        }
        .lang-btn {
            padding: 10px 20px;
            margin: 0 5px;
            border: 2px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .lang-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
        }
        .translation-key {
            font-family: monospace;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body data-page="test">
    <div class="test-container">
        <h1>🌐 Translation System Test</h1>
        <p>Test wszystkich 3 języków systemu tłumaczeń PixelGarage</p>
        
        <div class="language-switcher">
            <button class="lang-btn active" data-lang="pl" onclick="switchLanguage('pl')">
                🇵🇱 Polski
            </button>
            <button class="lang-btn" data-lang="en" onclick="switchLanguage('en')">
                🇬🇧 English
            </button>
            <button class="lang-btn" data-lang="es" onclick="switchLanguage('es')">
                🇪🇸 Español
            </button>
        </div>

        <div class="test-section">
            <h3>Navigation</h3>
            <div class="test-item">
                <strong data-translate="nav.home">Strona Główna</strong>
                <div class="translation-key">nav.home</div>
            </div>
            <div class="test-item">
                <strong data-translate="nav.services">Katalog Usług</strong>
                <div class="translation-key">nav.services</div>
            </div>
            <div class="test-item">
                <strong data-translate="nav.portfolio">Portfolio</strong>
                <div class="translation-key">nav.portfolio</div>
            </div>
            <div class="test-item">
                <strong data-translate="nav.academy">Tech Academy</strong>
                <div class="translation-key">nav.academy</div>
            </div>
            <div class="test-item">
                <strong data-translate="nav.contact">Kontakt Live</strong>
                <div class="translation-key">nav.contact</div>
            </div>
        </div>

        <div class="test-section">
            <h3>Common Elements</h3>
            <div class="test-item">
                <strong data-translate="common.telegram">Telegram</strong>
                <div class="translation-key">common.telegram</div>
            </div>
            <div class="test-item">
                <strong data-translate="common.whatsapp">WhatsApp</strong>
                <div class="translation-key">common.whatsapp</div>
            </div>
            <div class="test-item">
                <strong data-translate="common.openApp">Otwórz Aplikację</strong>
                <div class="translation-key">common.openApp</div>
            </div>
            <div class="test-item">
                <strong data-translate="common.viewDemo">Zobacz Demo</strong>
                <div class="translation-key">common.viewDemo</div>
            </div>
            <div class="test-item">
                <strong data-translate="common.fullPortfolio">Pełne Portfolio (12 projektów)</strong>
                <div class="translation-key">common.fullPortfolio</div>
            </div>
        </div>

        <div class="test-section">
            <h3>Landing Page</h3>
            <div class="test-item">
                <strong data-translate="landing.hero.title">Rozpoczynamy przygodę z</strong>
                <div class="translation-key">landing.hero.title</div>
            </div>
            <div class="test-item">
                <strong data-translate="landing.hero.titleHighlight">technologią AI</strong>
                <div class="translation-key">landing.hero.titleHighlight</div>
            </div>
            <div class="test-item">
                <strong data-translate="landing.hero.subtitle">Twoja cyfrowa prawa ręka</strong>
                <div class="translation-key">landing.hero.subtitle</div>
            </div>
            <div class="test-item">
                <strong data-translate="landing.services.sectionTitle">Nasze Usługi</strong>
                <div class="translation-key">landing.services.sectionTitle</div>
            </div>
        </div>

        <div class="test-section">
            <h3>Projects</h3>
            <div class="test-item">
                <strong data-translate="landing.projects.smartcard.name">SmartCard.pl - AI Business Cards</strong>
                <div class="translation-key">landing.projects.smartcard.name</div>
            </div>
            <div class="test-item">
                <strong data-translate="landing.projects.eventai.name">EventAI.pl - Smart Event Planning</strong>
                <div class="translation-key">landing.projects.eventai.name</div>
            </div>
            <div class="test-item">
                <strong data-translate="landing.projects.fitgenius.name">FitGenius.pl - AI Fitness Coach</strong>
                <div class="translation-key">landing.projects.fitgenius.name</div>
            </div>
        </div>

        <div class="test-section">
            <h3>Services</h3>
            <div class="test-item">
                <strong data-translate="services.hero.title">Katalog Usług</strong>
                <div class="translation-key">services.hero.title</div>
            </div>
            <div class="test-item">
                <strong data-translate="services.categories.title">Kategorie Usług</strong>
                <div class="translation-key">services.categories.title</div>
            </div>
        </div>

        <div class="test-section">
            <h3>Brand & Meta</h3>
            <div class="test-item">
                <strong data-translate="brand.name">PixelGarage</strong>
                <div class="translation-key">brand.name</div>
            </div>
            <div class="test-item">
                <strong data-translate="meta.title">PixelGarage - Tworzymy Przyszłość Cyfrową z AI | Portfolio & Usługi</strong>
                <div class="translation-key">meta.title</div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/translations.js"></script>
    <script src="assets/js/language-switcher.js"></script>
    
    <script>
        function switchLanguage(lang) {
            setLanguage(lang);
            
            // Update button states
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.classList.toggle('active', btn.getAttribute('data-lang') === lang);
            });
        }

        // Initialize translation system
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌐 Initializing translation test...');
            initializeLanguage();
            
            // Test translation function
            console.log('Testing translation function:');
            console.log('Polish nav.home:', t('nav.home', 'pl'));
            console.log('English nav.home:', t('nav.home', 'en'));
            console.log('Spanish nav.home:', t('nav.home', 'es'));
        });
    </script>
</body>
</html>
