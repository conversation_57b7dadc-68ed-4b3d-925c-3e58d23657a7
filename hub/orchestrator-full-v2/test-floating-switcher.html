<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Floating Language Switcher</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            text-align: center;
        }
        
        .test-header {
            margin-bottom: 60px;
        }
        
        .test-header h1 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .test-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .test-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 60px 0;
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .test-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .test-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #fff;
        }
        
        .test-card p {
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .test-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            text-align: left;
        }
        
        .test-item strong {
            color: #ffd700;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-working { background: #4CAF50; }
        .status-error { background: #f44336; }
        
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin: 40px 0;
            text-align: left;
        }
        
        .instructions h3 {
            margin-bottom: 20px;
            color: #ffd700;
        }
        
        .instructions ol {
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 10px 0;
            line-height: 1.6;
        }
        
        .floating-notice {
            position: fixed;
            bottom: 120px;
            left: 30px;
            background: rgba(255, 215, 0, 0.9);
            color: #333;
            padding: 15px 20px;
            border-radius: 10px;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            animation: pulse 2s infinite;
            z-index: 999998;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .demo-text {
            margin: 40px 0;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
        }
        
        @media (max-width: 768px) {
            .test-header h1 {
                font-size: 2rem;
            }
            
            .test-content {
                grid-template-columns: 1fr;
            }
            
            .floating-notice {
                bottom: 100px;
                left: 20px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body data-page="test">
    <div class="floating-notice">
        ⬇️ Sprawdź dolny lewy róg!
    </div>

    <div class="test-container">
        <div class="test-header">
            <h1>🌐 Floating Language Switcher Test</h1>
            <p>Testowanie pływającego przełącznika języków w dolnym lewym rogu ekranu</p>
        </div>

        <div class="test-content">
            <div class="test-card">
                <h3>🔍 Status Systemu</h3>
                <div class="test-item">
                    <strong>Translations loaded:</strong> 
                    <span id="translationsStatus">Checking...</span>
                    <span class="status-indicator" id="translationsIndicator"></span>
                </div>
                <div class="test-item">
                    <strong>Language Switcher:</strong> 
                    <span id="switcherStatus">Checking...</span>
                    <span class="status-indicator" id="switcherIndicator"></span>
                </div>
                <div class="test-item">
                    <strong>Current Language:</strong> 
                    <span id="currentLang">Detecting...</span>
                </div>
                <div class="test-item">
                    <strong>Floating Switcher:</strong> 
                    <span id="floatingStatus">Checking...</span>
                    <span class="status-indicator" id="floatingIndicator"></span>
                </div>
            </div>

            <div class="test-card">
                <h3>🧪 Test Translations</h3>
                <div class="test-item">
                    <strong data-translate="nav.home">Strona Główna</strong>
                    <div style="font-size: 12px; color: #ffd700;">nav.home</div>
                </div>
                <div class="test-item">
                    <strong data-translate="common.telegram">Telegram</strong>
                    <div style="font-size: 12px; color: #ffd700;">common.telegram</div>
                </div>
                <div class="test-item">
                    <strong data-translate="brand.name">PixelGarage</strong>
                    <div style="font-size: 12px; color: #ffd700;">brand.name</div>
                </div>
                <div class="test-item">
                    <strong data-translate="landing.hero.title">Rozpoczynamy przygodę z</strong>
                    <div style="font-size: 12px; color: #ffd700;">landing.hero.title</div>
                </div>
            </div>
        </div>

        <div class="instructions">
            <h3>📋 Instrukcje Testowania</h3>
            <ol>
                <li><strong>Sprawdź dolny lewy róg</strong> - Powinien być widoczny floating przełącznik języków</li>
                <li><strong>Kliknij na przełącznik</strong> - Powinno otworzyć się menu z 3 językami (PL/EN/ES)</li>
                <li><strong>Wybierz język</strong> - Treść powinna zmienić się natychmiast</li>
                <li><strong>Sprawdź pozycjonowanie</strong> - Menu powinno otwierać się nad przełącznikiem</li>
                <li><strong>Test responsywności</strong> - Zmień rozmiar okna i sprawdź czy działa</li>
                <li><strong>Test na mobile</strong> - Sprawdź czy działa na urządzeniach mobilnych</li>
            </ol>
        </div>

        <div class="demo-text">
            <h3 data-translate="landing.hero.title">Rozpoczynamy przygodę z</h3>
            <h2 data-translate="landing.hero.titleHighlight">technologią AI</h2>
            <p data-translate="landing.hero.subtitle">Twoja cyfrowa prawa ręka</p>
            <p data-translate="landing.hero.description">
                Witaj w PixelGarage - miejscu, gdzie technologia spotyka się z kreatywnością. 
                Specjalizuję się w tworzeniu innowacyjnych rozwiązań AI, które przekształcają pomysły w cyfrową rzeczywistość.
            </p>
        </div>

        <div class="test-content">
            <div class="test-card">
                <h3>🔗 Przejdź do innych stron</h3>
                <p>Sprawdź czy floating switcher działa na wszystkich stronach:</p>
                <div style="margin-top: 20px;">
                    <a href="index.html" style="color: #ffd700; text-decoration: none; display: block; margin: 10px 0;">🏠 Landing Page</a>
                    <a href="services/index.html" style="color: #ffd700; text-decoration: none; display: block; margin: 10px 0;">⚙️ Services</a>
                    <a href="gallery/index.html" style="color: #ffd700; text-decoration: none; display: block; margin: 10px 0;">🖼️ Portfolio</a>
                    <a href="academy/index.html" style="color: #ffd700; text-decoration: none; display: block; margin: 10px 0;">🎓 Tech Academy</a>
                    <a href="communication/index.html" style="color: #ffd700; text-decoration: none; display: block; margin: 10px 0;">💬 Communication</a>
                </div>
            </div>

            <div class="test-card">
                <h3>🎯 Oczekiwane Zachowanie</h3>
                <div class="test-item">✅ Floating switcher w dolnym lewym rogu</div>
                <div class="test-item">✅ Premium design z animacjami</div>
                <div class="test-item">✅ Dropdown otwiera się nad przełącznikiem</div>
                <div class="test-item">✅ Natychmiastowa zmiana języka</div>
                <div class="test-item">✅ Zapamiętywanie wyboru między stronami</div>
                <div class="test-item">✅ Responsywność na wszystkich urządzeniach</div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/translations.js"></script>
    <script src="assets/js/language-switcher.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Starting floating switcher test...');
            
            // Initialize translation system
            initializeLanguage();
            
            // Test system status after a delay
            setTimeout(() => {
                testSystemStatus();
            }, 1500);
        });

        function testSystemStatus() {
            // Test translations.js
            const translationsLoaded = typeof translations !== 'undefined';
            document.getElementById('translationsStatus').textContent = translationsLoaded ? 'Loaded ✓' : 'Not loaded ✗';
            document.getElementById('translationsIndicator').className = 'status-indicator ' + (translationsLoaded ? 'status-working' : 'status-error');
            
            // Test language switcher
            const switcherLoaded = typeof LanguageSwitcher !== 'undefined';
            document.getElementById('switcherStatus').textContent = switcherLoaded ? 'Loaded ✓' : 'Not loaded ✗';
            document.getElementById('switcherIndicator').className = 'status-indicator ' + (switcherLoaded ? 'status-working' : 'status-error');
            
            // Test current language
            const currentLang = getCurrentLanguage ? getCurrentLanguage() : 'Unknown';
            document.getElementById('currentLang').textContent = currentLang;
            
            // Test floating switcher
            const floatingSwitcher = document.querySelector('.floating-language-switcher');
            const hasFloatingSwitcher = floatingSwitcher !== null;
            document.getElementById('floatingStatus').textContent = hasFloatingSwitcher ? 'Found ✓' : 'Not found ✗';
            document.getElementById('floatingIndicator').className = 'status-indicator ' + (hasFloatingSwitcher ? 'status-working' : 'status-error');
            
            console.log('🔍 Test Results:', {
                translationsLoaded,
                switcherLoaded,
                currentLang,
                hasFloatingSwitcher,
                floatingSwitcher
            });
        }
    </script>
</body>
</html>
