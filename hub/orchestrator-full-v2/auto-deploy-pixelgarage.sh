#!/bin/bash

# ===== PIXELGARAGE.SPACE AUTOMATED DEPLOYMENT SCRIPT =====
# Complete automated deployment for pixelgarage.space on mikr.us
# Author: <PERSON> <PERSON> <PERSON>xelGarage Founder
# Domain: pixelgarage.space

set -e  # Exit on any error

# ===== CONFIGURATION =====
echo "🚀 PixelGarage.space Automated Deployment v2.0"
echo "=============================================="

# Domain and server configuration
DOMAIN="pixelgarage.space"
WWW_DOMAIN="www.pixelgarage.space"

# IMPORTANT: Configure these values for your mikr.us server
read -p "Enter your mikr.us server hostname (e.g., server123.mikr.us): " SERVER_HOST
read -p "Enter your mikr.us username: " SERVER_USER
read -p "Enter server path [/var/www/pixelgarage.space]: " SERVER_PATH
SERVER_PATH=${SERVER_PATH:-/var/www/pixelgarage.space}

LOCAL_PATH="./orchestrator-full-v2"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# ===== FUNCTIONS =====
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log_step "Checking dependencies..."
    
    local missing_deps=()
    
    if ! command -v rsync &> /dev/null; then
        missing_deps+=("rsync")
    fi
    
    if ! command -v ssh &> /dev/null; then
        missing_deps+=("openssh-client")
    fi
    
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        echo "Please install them using:"
        echo "  Ubuntu/Debian: sudo apt install ${missing_deps[*]}"
        echo "  macOS: brew install ${missing_deps[*]}"
        exit 1
    fi
    
    log_success "All dependencies are installed."
}

# Validate local files
validate_local_files() {
    log_step "Validating local files..."
    
    if [ ! -d "$LOCAL_PATH" ]; then
        log_error "Local path $LOCAL_PATH does not exist!"
        exit 1
    fi
    
    # Check for essential files
    essential_files=(
        "$LOCAL_PATH/index.html"
        "$LOCAL_PATH/assets/css"
        "$LOCAL_PATH/assets/js"
        "$LOCAL_PATH/services/index.html"
        "$LOCAL_PATH/gallery/index.html"
        "$LOCAL_PATH/academy/index.html"
        "$LOCAL_PATH/communication/index.html"
        "$LOCAL_PATH/assets/js/translations.js"
        "$LOCAL_PATH/assets/js/language-switcher.js"
    )
    
    for file in "${essential_files[@]}"; do
        if [ ! -e "$file" ]; then
            log_error "Essential file/directory missing: $file"
            exit 1
        fi
    done
    
    log_success "Local files validation passed."
}

# Test server connection
test_connection() {
    log_step "Testing server connection..."
    
    if ssh -o ConnectTimeout=10 -o BatchMode=yes "$SERVER_USER@$SERVER_HOST" exit 2>/dev/null; then
        log_success "Server connection successful."
    else
        log_warning "Cannot connect with SSH keys. Trying password authentication..."
        if ssh -o ConnectTimeout=10 "$SERVER_USER@$SERVER_HOST" exit; then
            log_success "Server connection successful with password."
        else
            log_error "Cannot connect to server. Please check:"
            echo "  - Server host: $SERVER_HOST"
            echo "  - Username: $SERVER_USER"
            echo "  - SSH access or password"
            exit 1
        fi
    fi
}

# Create backup of current deployment
create_backup() {
    log_step "Creating backup of current deployment..."
    
    BACKUP_DIR="/var/backups/pixelgarage-$(date +%Y%m%d-%H%M%S)"
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        if [ -d '$SERVER_PATH' ]; then
            sudo mkdir -p /var/backups
            sudo cp -r '$SERVER_PATH' '$BACKUP_DIR' 2>/dev/null || echo 'Backup creation failed, continuing...'
            echo 'Backup attempted at: $BACKUP_DIR'
        else
            echo 'No existing deployment found, skipping backup.'
        fi
    "
    
    log_success "Backup process completed."
}

# Prepare server environment
prepare_server() {
    log_step "Preparing server environment..."
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        # Update system packages
        sudo apt update
        
        # Install required packages
        sudo apt install -y nginx curl wget unzip
        
        # Create directory structure
        sudo mkdir -p '$SERVER_PATH'
        sudo mkdir -p '$SERVER_PATH/assets/css'
        sudo mkdir -p '$SERVER_PATH/assets/js'
        sudo mkdir -p '$SERVER_PATH/services'
        sudo mkdir -p '$SERVER_PATH/gallery'
        sudo mkdir -p '$SERVER_PATH/academy'
        sudo mkdir -p '$SERVER_PATH/communication'
        sudo mkdir -p '$SERVER_PATH/projects'
        
        # Set ownership
        sudo chown -R www-data:www-data '$SERVER_PATH'
        sudo chmod -R 755 '$SERVER_PATH'
        
        # Ensure nginx is running
        sudo systemctl enable nginx
        sudo systemctl start nginx
    "
    
    log_success "Server environment prepared."
}

# Deploy files
deploy_files() {
    log_step "Deploying files to server..."
    
    # Sync files with rsync
    rsync -avz --delete \
        --exclude='.git*' \
        --exclude='node_modules' \
        --exclude='*.log' \
        --exclude='deploy*.sh' \
        --exclude='auto-deploy*.sh' \
        --exclude='README.md' \
        --exclude='QUICK_START.md' \
        --exclude='DEPLOYMENT.md' \
        --exclude='test-deployment.sh' \
        --progress \
        "$LOCAL_PATH/" "$SERVER_USER@$SERVER_HOST:$SERVER_PATH/"
    
    log_success "Files deployed successfully."
}

# Configure nginx for pixelgarage.space
configure_nginx() {
    log_step "Configuring nginx for pixelgarage.space..."
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        # Create nginx configuration for pixelgarage.space
        sudo tee /etc/nginx/sites-available/pixelgarage.space > /dev/null << 'EOF'
server {
    listen 80;
    listen [::]:80;
    
    server_name pixelgarage.space www.pixelgarage.space;
    root $SERVER_PATH;
    index index.html;
    
    # Security headers
    add_header X-Frame-Options \"SAMEORIGIN\" always;
    add_header X-Content-Type-Options \"nosniff\" always;
    add_header X-XSS-Protection \"1; mode=block\" always;
    add_header Referrer-Policy \"no-referrer-when-downgrade\" always;
    add_header Content-Security-Policy \"default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data: blob:; img-src 'self' https: data: blob:; font-src 'self' https: data:;\" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types 
        text/plain 
        text/css 
        text/xml 
        text/javascript 
        application/javascript 
        application/xml+rss 
        application/json
        application/xml
        image/svg+xml;
    
    # Cache static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg|webp)$ {
        expires 1y;
        add_header Cache-Control \"public, immutable\";
        access_log off;
    }
    
    # Main location
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # Services section
    location /services/ {
        try_files \$uri \$uri/ /services/index.html;
    }
    
    # Gallery section
    location /gallery/ {
        try_files \$uri \$uri/ /gallery/index.html;
    }
    
    # Academy section
    location /academy/ {
        try_files \$uri \$uri/ /academy/index.html;
    }
    
    # Communication section
    location /communication/ {
        try_files \$uri \$uri/ /communication/index.html;
    }
    
    # Projects section
    location /projects/ {
        try_files \$uri \$uri/ =404;
    }
    
    # API endpoints (future use)
    location /api/ {
        # Add API configuration here if needed
        return 404;
    }
    
    # Redirect www to non-www
    if (\$host = www.pixelgarage.space) {
        return 301 https://pixelgarage.space\$request_uri;
    }
    
    # Error pages
    error_page 404 /index.html;
    error_page 500 502 503 504 /index.html;
    
    # Logs
    access_log /var/log/nginx/pixelgarage.space.access.log;
    error_log /var/log/nginx/pixelgarage.space.error.log;
}
EOF
        
        # Enable site
        sudo ln -sf /etc/nginx/sites-available/pixelgarage.space /etc/nginx/sites-enabled/
        sudo rm -f /etc/nginx/sites-enabled/default
        
        # Test nginx configuration
        sudo nginx -t
        
        # Reload nginx
        sudo systemctl reload nginx
    "
    
    log_success "Nginx configured successfully for pixelgarage.space."
}

# Set proper permissions
set_permissions() {
    log_step "Setting proper file permissions..."
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        sudo chown -R www-data:www-data '$SERVER_PATH'
        sudo find '$SERVER_PATH' -type d -exec chmod 755 {} \;
        sudo find '$SERVER_PATH' -type f -exec chmod 644 {} \;
        
        # Make sure nginx can read all files
        sudo chmod -R 755 '$SERVER_PATH'
    "
    
    log_success "Permissions set successfully."
}

# Configure SSL with Let's Encrypt (optional but recommended)
setup_ssl() {
    log_step "Setting up SSL certificate..."
    
    read -p "Do you want to set up SSL certificate with Let's Encrypt? (y/n): " setup_ssl_choice
    
    if [[ $setup_ssl_choice =~ ^[Yy]$ ]]; then
        ssh "$SERVER_USER@$SERVER_HOST" "
            # Install certbot
            sudo apt install -y certbot python3-certbot-nginx
            
            # Get SSL certificate
            sudo certbot --nginx -d $DOMAIN -d $WWW_DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN
            
            # Auto-renewal
            sudo systemctl enable certbot.timer
        "
        log_success "SSL certificate configured successfully."
    else
        log_info "SSL setup skipped. You can set it up later with: sudo certbot --nginx -d $DOMAIN"
    fi
}

# Verify deployment
verify_deployment() {
    log_step "Verifying deployment..."

    # Check if main files exist on server
    ssh "$SERVER_USER@$SERVER_HOST" "
        echo 'Checking deployed files...'

        if [ -f '$SERVER_PATH/index.html' ]; then
            echo '✓ Main index.html found'
        else
            echo '✗ Main index.html missing'
            exit 1
        fi

        if [ -d '$SERVER_PATH/assets' ]; then
            echo '✓ Assets directory found'
        else
            echo '✗ Assets directory missing'
            exit 1
        fi

        if [ -f '$SERVER_PATH/services/index.html' ]; then
            echo '✓ Services page found'
        else
            echo '✗ Services page missing'
            exit 1
        fi

        if [ -f '$SERVER_PATH/gallery/index.html' ]; then
            echo '✓ Gallery page found'
        else
            echo '✗ Gallery page missing'
            exit 1
        fi

        if [ -f '$SERVER_PATH/academy/index.html' ]; then
            echo '✓ Academy page found'
        else
            echo '✗ Academy page missing'
            exit 1
        fi

        if [ -f '$SERVER_PATH/communication/index.html' ]; then
            echo '✓ Communication page found'
        else
            echo '✗ Communication page missing'
            exit 1
        fi

        if [ -f '$SERVER_PATH/assets/js/translations.js' ]; then
            echo '✓ Translation system found'
        else
            echo '✗ Translation system missing'
            exit 1
        fi
    "

    # Test HTTP response
    log_info "Testing website response..."
    sleep 5  # Give nginx time to reload

    if curl -s -o /dev/null -w "%{http_code}" "http://$DOMAIN" | grep -q "200"; then
        log_success "Website is responding correctly at http://$DOMAIN!"
    elif curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_HOST" | grep -q "200"; then
        log_success "Website is responding correctly at http://$SERVER_HOST!"
        log_warning "Make sure your domain $DOMAIN points to this server."
    else
        log_warning "Website might not be responding correctly. Please check manually."
    fi

    log_success "Deployment verification completed."
}

# Setup domain DNS instructions
show_dns_instructions() {
    log_step "DNS Configuration Instructions"
    echo ""
    echo "🌐 To make pixelgarage.space work, configure these DNS records:"
    echo ""
    echo "A Record:"
    echo "  Name: @"
    echo "  Value: [Your server IP address]"
    echo "  TTL: 3600"
    echo ""
    echo "A Record:"
    echo "  Name: www"
    echo "  Value: [Your server IP address]"
    echo "  TTL: 3600"
    echo ""
    echo "Or CNAME Record (if using subdomain):"
    echo "  Name: www"
    echo "  Value: $SERVER_HOST"
    echo "  TTL: 3600"
    echo ""

    # Get server IP
    SERVER_IP=$(ssh "$SERVER_USER@$SERVER_HOST" "curl -s ifconfig.me" 2>/dev/null || echo "Unable to detect")
    if [ "$SERVER_IP" != "Unable to detect" ]; then
        echo "Your server IP address is: $SERVER_IP"
        echo ""
    fi

    echo "Configure these records in your domain registrar's DNS panel."
    echo ""
}

# ===== MAIN DEPLOYMENT PROCESS =====
main() {
    echo ""
    log_info "Starting PixelGarage.space automated deployment..."
    echo ""

    # Pre-deployment checks
    check_dependencies
    validate_local_files
    test_connection

    # Deployment process
    create_backup
    prepare_server
    deploy_files
    configure_nginx
    set_permissions
    verify_deployment

    # Optional SSL setup
    setup_ssl

    # Show DNS instructions
    show_dns_instructions

    echo ""
    log_success "🎉 PixelGarage.space deployment completed successfully!"
    echo ""
    echo "🌟 Your application is now deployed!"
    echo ""
    echo "📍 Access URLs:"
    echo "   • http://$DOMAIN (after DNS propagation)"
    echo "   • http://$SERVER_HOST (immediate access)"
    echo ""
    echo "📋 Next steps:"
    echo "   1. Configure DNS records for pixelgarage.space"
    echo "   2. Test all pages and functionality"
    echo "   3. Set up SSL certificate if not done"
    echo "   4. Configure monitoring and backups"
    echo "   5. Test translation system (PL/EN/ES)"
    echo ""
    echo "🔧 Useful commands:"
    echo "   • Check nginx status: sudo systemctl status nginx"
    echo "   • View nginx logs: sudo tail -f /var/log/nginx/pixelgarage.space.error.log"
    echo "   • Restart nginx: sudo systemctl restart nginx"
    echo ""
}

# ===== SCRIPT EXECUTION =====
# Confirm before proceeding
echo ""
echo "This script will deploy PixelGarage.space to your mikr.us server."
echo "Make sure you have:"
echo "  ✓ SSH access to your mikr.us server"
echo "  ✓ Sudo privileges on the server"
echo "  ✓ Domain pixelgarage.space ready for DNS configuration"
echo ""
read -p "Do you want to proceed? (y/n): " confirm

if [[ ! $confirm =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 0
fi

# Run main function
main "$@"
