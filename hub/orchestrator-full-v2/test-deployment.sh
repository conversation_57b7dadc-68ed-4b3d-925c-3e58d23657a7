#!/bin/bash

# ===== PIXELGARAGE DEPLOYMENT TESTING SCRIPT =====
# Test script to verify deployment functionality

set -e

# Configuration (will be updated by deploy-config.sh)
SERVER_HOST="your-server.mikr.us"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

echo -e "${BLUE}🧪 PixelGarage Deployment Testing${NC}"
echo "================================="
echo ""

# Check if configuration is set
if [ "$SERVER_HOST" = "your-server.mikr.us" ]; then
    log_error "Please run deploy-config.sh first!"
    exit 1
fi

# Test functions
test_main_page() {
    log_info "Testing main page..."
    if curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_HOST" | grep -q "200"; then
        log_success "Main page is accessible"
    else
        log_error "Main page is not accessible"
        return 1
    fi
}

test_services_page() {
    log_info "Testing services page..."
    if curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_HOST/services/" | grep -q "200"; then
        log_success "Services page is accessible"
    else
        log_error "Services page is not accessible"
        return 1
    fi
}

test_gallery_page() {
    log_info "Testing gallery page..."
    if curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_HOST/gallery/" | grep -q "200"; then
        log_success "Gallery page is accessible"
    else
        log_error "Gallery page is not accessible"
        return 1
    fi
}

test_academy_page() {
    log_info "Testing academy page..."
    if curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_HOST/academy/" | grep -q "200"; then
        log_success "Academy page is accessible"
    else
        log_error "Academy page is not accessible"
        return 1
    fi
}

test_communication_page() {
    log_info "Testing communication page..."
    if curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_HOST/communication/" | grep -q "200"; then
        log_success "Communication page is accessible"
    else
        log_error "Communication page is not accessible"
        return 1
    fi
}

test_assets() {
    log_info "Testing static assets..."
    
    # Test CSS
    if curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_HOST/assets/css/main.css" | grep -q "200"; then
        log_success "CSS files are accessible"
    else
        log_warning "Some CSS files might not be accessible"
    fi
    
    # Test JS
    if curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_HOST/assets/js/main.js" | grep -q "200"; then
        log_success "JavaScript files are accessible"
    else
        log_warning "Some JavaScript files might not be accessible"
    fi
}

test_response_time() {
    log_info "Testing response time..."
    
    response_time=$(curl -o /dev/null -s -w "%{time_total}" "http://$SERVER_HOST")
    
    if (( $(echo "$response_time < 2.0" | bc -l) )); then
        log_success "Response time is good: ${response_time}s"
    elif (( $(echo "$response_time < 5.0" | bc -l) )); then
        log_warning "Response time is acceptable: ${response_time}s"
    else
        log_error "Response time is slow: ${response_time}s"
    fi
}

test_gzip_compression() {
    log_info "Testing gzip compression..."
    
    if curl -s -H "Accept-Encoding: gzip" -I "http://$SERVER_HOST" | grep -q "Content-Encoding: gzip"; then
        log_success "Gzip compression is enabled"
    else
        log_warning "Gzip compression might not be enabled"
    fi
}

test_security_headers() {
    log_info "Testing security headers..."
    
    headers=$(curl -s -I "http://$SERVER_HOST")
    
    if echo "$headers" | grep -q "X-Frame-Options"; then
        log_success "X-Frame-Options header is present"
    else
        log_warning "X-Frame-Options header is missing"
    fi
    
    if echo "$headers" | grep -q "X-Content-Type-Options"; then
        log_success "X-Content-Type-Options header is present"
    else
        log_warning "X-Content-Type-Options header is missing"
    fi
}

# Run all tests
main() {
    echo "Testing deployment at: http://$SERVER_HOST"
    echo ""
    
    failed_tests=0
    
    # Page accessibility tests
    test_main_page || ((failed_tests++))
    test_services_page || ((failed_tests++))
    test_gallery_page || ((failed_tests++))
    test_academy_page || ((failed_tests++))
    test_communication_page || ((failed_tests++))
    
    # Performance and configuration tests
    test_assets
    test_response_time
    test_gzip_compression
    test_security_headers
    
    echo ""
    if [ $failed_tests -eq 0 ]; then
        log_success "🎉 All critical tests passed!"
        echo ""
        echo "Your PixelGarage application is successfully deployed!"
        echo "Visit: http://$SERVER_HOST"
    else
        log_error "❌ $failed_tests critical test(s) failed!"
        echo ""
        echo "Please check the deployment and server configuration."
    fi
    
    echo ""
    echo "Additional recommendations:"
    echo "• Set up SSL certificate with setup-ssl.sh"
    echo "• Configure monitoring and backups"
    echo "• Test all interactive features manually"
}

# Run tests
main "$@"
