// Global variables
let selectedServices = [];
let totalPrice = 0;

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializePriceCalculator();
    initializeDiagnosisForm();
    initializeScrollEffects();
});

// Price Calculator Functions
function initializePriceCalculator() {
    const serviceOptions = document.querySelectorAll('.service-option');
    
    serviceOptions.forEach(option => {
        option.addEventListener('click', function() {
            toggleService(this);
        });
    });
}

function toggleService(element) {
    const serviceName = element.querySelector('span').textContent;
    const servicePrice = parseInt(element.dataset.price);
    const serviceId = element.dataset.service;
    
    if (element.classList.contains('selected')) {
        // Remove service
        element.classList.remove('selected');
        selectedServices = selectedServices.filter(service => service.id !== serviceId);
        totalPrice -= servicePrice;
    } else {
        // Add service
        element.classList.add('selected');
        selectedServices.push({
            id: serviceId,
            name: serviceName,
            price: servicePrice
        });
        totalPrice += servicePrice;
    }
    
    updateSelectedServices();
}

function updateSelectedServices() {
    const selectedServicesContainer = document.getElementById('selectedServices');
    const totalPriceElement = document.getElementById('totalPrice');
    
    if (selectedServices.length === 0) {
        selectedServicesContainer.innerHTML = '<p style="color: #6b7280; font-style: italic;">Nie wybrano żadnych usług</p>';
    } else {
        let servicesHTML = '<ul style="list-style: none; padding: 0;">';
        selectedServices.forEach(service => {
            servicesHTML += `
                <li style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #e5e7eb;">
                    <span>${service.name}</span>
                    <span style="font-weight: 600; color: var(--primary-blue);">${service.price} zł</span>
                </li>
            `;
        });
        servicesHTML += '</ul>';
        selectedServicesContainer.innerHTML = servicesHTML;
    }
    
    totalPriceElement.textContent = `${totalPrice} zł`;
}

// Diagnosis Form Functions
function initializeDiagnosisForm() {
    const diagnosisForm = document.getElementById('diagnosisForm');
    
    diagnosisForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleDiagnosisSubmit();
    });
    
    // Phone number formatting
    const phoneInput = document.getElementById('contactPhone');
    phoneInput.addEventListener('input', function(e) {
        formatPhoneNumber(e.target);
    });
}

function formatPhoneNumber(input) {
    let value = input.value.replace(/\D/g, '');
    if (value.length >= 3) {
        value = value.replace(/(\d{3})(\d{3})(\d{3})/, '$1-$2-$3');
    }
    input.value = value;
}

function handleDiagnosisSubmit() {
    const formData = {
        carBrand: document.getElementById('carBrand').value,
        carYear: document.getElementById('carYear').value,
        mileage: document.getElementById('mileage').value,
        problemCategory: document.getElementById('problemCategory').value,
        problemDescription: document.getElementById('problemDescription').value,
        contactPhone: document.getElementById('contactPhone').value
    };
    
    // Validate form
    if (!validateDiagnosisForm(formData)) {
        return;
    }
    
    // Show success message
    showNotification('Dziękujemy za przesłanie formularza! Nasz ekspert skontaktuje się z Tobą w ciągu 2 godzin roboczych.', 'success');
    
    // Reset form
    document.getElementById('diagnosisForm').reset();
    
    // In a real application, you would send this data to your backend
    console.log('Diagnosis form submitted:', formData);
}

function validateDiagnosisForm(data) {
    const errors = [];
    
    if (!data.carBrand) errors.push('Wybierz markę pojazdu');
    if (!data.carYear || data.carYear < 1990 || data.carYear > 2025) errors.push('Podaj prawidłowy rok produkcji');
    if (!data.mileage || data.mileage < 0) errors.push('Podaj prawidłowy przebieg');
    if (!data.problemCategory) errors.push('Wybierz kategorię problemu');
    if (!data.problemDescription || data.problemDescription.length < 10) errors.push('Opisz problem (minimum 10 znaków)');
    if (!data.contactPhone || data.contactPhone.length < 9) errors.push('Podaj prawidłowy numer telefonu');
    
    if (errors.length > 0) {
        showNotification(errors.join('\n'), 'error');
        return false;
    }
    
    return true;
}

// Notification System
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        z-index: 1001;
        max-width: 400px;
        word-wrap: break-word;
        animation: slideIn 0.3s ease-out;
    `;
    
    // Add slide-in animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
    
    notification.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: flex-start;">
            <span style="white-space: pre-line;">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; font-size: 1.2rem; cursor: pointer; margin-left: 1rem;">&times;</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Scroll Effects
function initializeScrollEffects() {
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = target.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Add active class to navigation items on scroll
    window.addEventListener('scroll', updateActiveNavigation);
}

function updateActiveNavigation() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-menu a[href^="#"]');
    const headerHeight = document.querySelector('.header').offsetHeight;
    
    let current = '';
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop - headerHeight - 100;
        const sectionHeight = section.offsetHeight;
        
        if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
            current = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
}

// CTA Functions
function checkAvailability() {
    // In a real application, this would check actual availability
    const availableSlots = [
        'Jutro 10:00',
        'Jutro 14:30',
        'Pojutrze 9:00',
        'Pojutrze 15:00',
        'Środa 11:00'
    ];
    
    const randomSlot = availableSlots[Math.floor(Math.random() * availableSlots.length)];
    
    showNotification(
        `Dostępne terminy:\n• ${randomSlot}\n• Więcej terminów dostępnych po kontakcie telefonicznym\n\nZadzwoń: 12 345 67 89`,
        'success'
    );
}

// Emergency contact
function callEmergency() {
    if (confirm('Czy chcesz zadzwonić na numer pogotowia: 600-123-456?')) {
        window.location.href = 'tel:600123456';
    }
}

// Add emergency call functionality to emergency buttons
document.addEventListener('DOMContentLoaded', function() {
    const emergencyButtons = document.querySelectorAll('.emergency-button, .nav-emergency');
    emergencyButtons.forEach(button => {
        button.addEventListener('click', callEmergency);
    });
});

// Form validation helpers
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function validatePhone(phone) {
    const re = /^\d{3}-\d{3}-\d{3}$/;
    return re.test(phone);
}

// Price estimation based on problem category
function getEstimatedPrice(category, carBrand, carYear) {
    const basePrice = {
        'engine': 400,
        'brakes': 300,
        'suspension': 350,
        'electrical': 250,
        'transmission': 600,
        'cooling': 280,
        'exhaust': 200,
        'other': 150
    };
    
    const brandMultiplier = {
        'audi': 1.3,
        'bmw': 1.4,
        'mercedes': 1.5,
        'volkswagen': 1.1,
        'skoda': 1.0,
        'ford': 1.0,
        'opel': 0.9,
        'peugeot': 1.0,
        'renault': 1.0,
        'toyota': 1.1,
        'volvo': 1.2,
        'other': 1.0
    };
    
    const ageMultiplier = carYear > 2015 ? 1.0 : carYear > 2010 ? 1.1 : 1.2;
    
    const price = (basePrice[category] || 150) * (brandMultiplier[carBrand] || 1.0) * ageMultiplier;
    
    return Math.round(price);
}

// Analytics tracking (placeholder)
function trackEvent(eventName, eventData) {
    // Placeholder for analytics tracking
    console.log('Event tracked:', eventName, eventData);
}

// Track form submissions and calculator usage
document.addEventListener('DOMContentLoaded', function() {
    // Track calculator usage
    document.querySelectorAll('.service-option').forEach(option => {
        option.addEventListener('click', function() {
            trackEvent('calculator_service_selected', {
                service: this.dataset.service,
                price: this.dataset.price
            });
        });
    });
    
    // Track form submission
    document.getElementById('diagnosisForm').addEventListener('submit', function() {
        trackEvent('diagnosis_form_submitted', {
            timestamp: new Date().toISOString()
        });
    });
    
    // Track CTA clicks
    document.querySelector('.cta-button').addEventListener('click', function() {
        trackEvent('cta_clicked', {
            button: 'check_availability'
        });
    });
});