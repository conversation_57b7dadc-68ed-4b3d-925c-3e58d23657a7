/* ===== ANALYTICS DASHBOARD STYLES ===== */

.analytics-body {
    background: #0a0a0f;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== ANALYTICS NAVIGATION ===== */
.analytics-nav {
    background: rgba(10, 10, 15, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    padding: var(--space-md) 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
}

.analytics-nav .nav-container {
    display: grid;
    grid-template-columns: 250px 1fr 350px;
    align-items: center;
    gap: var(--space-xl);
}

/* ===== DASHBOARD CONTAINER ===== */
.dashboard-container {
    display: grid;
    grid-template-columns: 350px 1fr;
    height: 100vh;
    padding-top: 80px;
}

/* ===== DASHBOARD SIDEBAR ===== */
.dashboard-sidebar {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-right: 1px solid var(--glass-border);
    overflow-y: auto;
    padding: var(--space-xl);
}

.sidebar-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-2xl);
}

/* ===== BRAND SCORE SECTION ===== */
.brand-score-section h3 {
    color: white;
    font-weight: 700;
    margin-bottom: var(--space-lg);
    text-align: center;
}

.score-display {
    text-align: center;
    margin-bottom: var(--space-xl);
}

.score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-600) 0deg 313deg, rgba(255, 255, 255, 0.1) 313deg 360deg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-md);
    position: relative;
    animation: scoreReveal 2s ease-out;
}

.score-circle::after {
    content: '';
    position: absolute;
    inset: 12px;
    background: var(--gray-800);
    border-radius: 50%;
}

@keyframes scoreReveal {
    from {
        background: conic-gradient(var(--primary-600) 0deg 0deg, rgba(255, 255, 255, 0.1) 0deg 360deg);
    }
    to {
        background: conic-gradient(var(--primary-600) 0deg 313deg, rgba(255, 255, 255, 0.1) 313deg 360deg);
    }
}

.score-value {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: white;
    z-index: 1;
}

.score-label {
    font-size: var(--font-size-lg);
    color: var(--gray-400);
    z-index: 1;
}

.score-level {
    color: var(--primary-400);
    font-weight: 700;
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-sm);
}

.score-change {
    color: var(--success-400);
    font-size: var(--font-size-sm);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    justify-content: center;
}

.score-breakdown {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.breakdown-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.breakdown-label {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
    min-width: 100px;
}

.breakdown-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.breakdown-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-sm);
    transition: width 1s ease-out;
}

.breakdown-value {
    color: var(--primary-400);
    font-weight: 600;
    font-size: var(--font-size-sm);
    min-width: 35px;
    text-align: right;
}

/* ===== QUICK ACTIONS ===== */
.quick-actions h4,
.growth-goals h4 {
    color: white;
    font-weight: 600;
    margin-bottom: var(--space-lg);
    font-size: var(--font-size-base);
}

.action-btn {
    width: 100%;
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    color: var(--gray-300);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
    transition: all var(--transition-base);
    font-size: var(--font-size-sm);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateY(-1px);
}

.action-btn i {
    color: var(--primary-400);
    width: 16px;
    text-align: center;
}

/* ===== GROWTH GOALS ===== */
.goal-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
}

.goal-info {
    min-width: 120px;
}

.goal-title {
    color: var(--gray-200);
    font-size: var(--font-size-sm);
    font-weight: 500;
    display: block;
}

.goal-target {
    color: var(--gray-400);
    font-size: var(--font-size-xs);
    display: block;
    margin-top: var(--space-xs);
}

.goal-progress {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.goal-fill {
    height: 100%;
    background: var(--success-500);
    border-radius: var(--border-radius-sm);
    transition: width 1s ease-out;
}

.goal-percentage {
    color: var(--success-400);
    font-weight: 600;
    font-size: var(--font-size-sm);
    min-width: 35px;
    text-align: right;
}

/* ===== DASHBOARD MAIN ===== */
.dashboard-main {
    padding: var(--space-xl);
    overflow-y: auto;
    background: var(--gray-900);
    display: flex;
    flex-direction: column;
    gap: var(--space-2xl);
}

/* ===== METRICS GRID ===== */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-xl);
}

.metric-card {
    padding: var(--space-xl);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    transition: left var(--transition-slow);
}

.metric-card:hover::before {
    left: 100%;
}

.metric-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-lg);
}

.metric-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
}

.metric-period select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    color: var(--gray-300);
    padding: var(--space-sm);
    font-size: var(--font-size-xs);
    cursor: pointer;
}

.metric-content {
    margin-bottom: var(--space-lg);
}

.metric-value {
    color: white;
    font-size: var(--font-size-3xl);
    font-weight: 800;
    margin-bottom: var(--space-xs);
    display: block;
}

.metric-label {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-sm);
}

.metric-change {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.metric-change.positive {
    color: var(--success-400);
}

.metric-change.negative {
    color: var(--error-500);
}

.metric-chart {
    height: 60px;
    margin-top: var(--space-md);
}

.metric-chart canvas {
    width: 100% !important;
    height: 100% !important;
}

/* ===== CHARTS SECTION ===== */
.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-xl);
}

.chart-container {
    padding: var(--space-xl);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
}

.chart-header h3 {
    color: white;
    font-weight: 600;
    margin: 0;
}

.chart-controls {
    display: flex;
    gap: var(--space-sm);
}

.chart-btn {
    padding: var(--space-sm) var(--space-lg);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    color: var(--gray-400);
    cursor: pointer;
    transition: all var(--transition-base);
    font-size: var(--font-size-sm);
}

.chart-btn.active,
.chart-btn:hover {
    background: var(--primary-gradient);
    color: white;
    border-color: var(--primary-600);
}

.chart-content {
    height: 300px;
    position: relative;
}

.chart-content canvas {
    width: 100% !important;
    height: 100% !important;
}

.chart-legend {
    display: flex;
    gap: var(--space-lg);
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: var(--font-size-sm);
    color: var(--gray-300);
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

/* ===== CONTENT PERFORMANCE ===== */
.content-performance {
    padding: var(--space-xl);
}

.performance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
}

.performance-header h3 {
    color: white;
    font-weight: 600;
    margin: 0;
}

.performance-filters {
    display: flex;
    gap: var(--space-md);
}

.performance-filters select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    color: var(--gray-300);
    padding: var(--space-sm) var(--space-md);
    font-size: var(--font-size-sm);
    cursor: pointer;
}

.content-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.content-item {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    padding: var(--space-lg);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--glass-border);
    transition: all var(--transition-base);
}

.content-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
}

.content-rank {
    width: 30px;
    height: 30px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: var(--font-size-sm);
}

.content-info {
    flex: 1;
}

.content-title {
    color: white;
    font-weight: 600;
    margin-bottom: var(--space-xs);
    font-size: var(--font-size-base);
}

.content-meta {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    display: flex;
    gap: var(--space-md);
}

.content-metrics {
    display: flex;
    gap: var(--space-xl);
    text-align: center;
}

.content-metric {
    display: flex;
    flex-direction: column;
}

.content-metric-value {
    color: white;
    font-weight: 700;
    font-size: var(--font-size-lg);
}

.content-metric-label {
    color: var(--gray-400);
    font-size: var(--font-size-xs);
    margin-top: var(--space-xs);
}

/* ===== AI INSIGHTS ===== */
.ai-insights {
    padding: var(--space-xl);
}

.insights-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
}

.insights-header h3 {
    color: white;
    font-weight: 600;
    margin: 0;
}

.insights-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-xl);
}

.insight-tabs {
    display: flex;
    gap: var(--space-sm);
    padding: var(--space-xs);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
}

.insight-tab {
    flex: 1;
    padding: var(--space-md);
    background: transparent;
    border: none;
    border-radius: var(--border-radius-md);
    color: var(--gray-400);
    cursor: pointer;
    transition: all var(--transition-base);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.insight-tab.active {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.insight-panel {
    display: none;
}

.insight-panel.active {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

.insight-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-xl);
}

.insight-card {
    display: flex;
    gap: var(--space-lg);
    padding: var(--space-xl);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    transition: all var(--transition-base);
}

.insight-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-4px);
}

.insight-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.insight-content h4 {
    color: white;
    font-weight: 600;
    margin: 0 0 var(--space-sm) 0;
    font-size: var(--font-size-base);
}

.insight-content p {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
    line-height: 1.6;
    margin: 0 0 var(--space-lg) 0;
}

.insight-action {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    color: var(--gray-200);
    padding: var(--space-sm) var(--space-lg);
    cursor: pointer;
    transition: all var(--transition-base);
    font-size: var(--font-size-sm);
}

.insight-action:hover {
    background: var(--primary-gradient);
    color: white;
}

/* ===== TRENDS LIST ===== */
.trends-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.trend-item {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    padding: var(--space-lg);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--glass-border);
}

.trend-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--border-radius-md);
    font-weight: 600;
    font-size: var(--font-size-sm);
    min-width: 80px;
    justify-content: center;
}

.trend-indicator.up {
    background: rgba(16, 185, 129, 0.2);
    color: var(--success-400);
}

.trend-indicator.down {
    background: rgba(239, 68, 68, 0.2);
    color: var(--error-500);
}

.trend-content h4 {
    color: white;
    font-weight: 600;
    margin: 0 0 var(--space-xs) 0;
}

.trend-content p {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* ===== OPTIMIZATION SUGGESTIONS ===== */
.optimization-suggestions {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.suggestion-item {
    display: flex;
    gap: var(--space-lg);
    padding: var(--space-xl);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
}

.suggestion-priority {
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--border-radius-md);
    font-weight: 600;
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    min-width: 80px;
    text-align: center;
    align-self: flex-start;
}

.suggestion-priority.high {
    background: rgba(239, 68, 68, 0.2);
    color: var(--error-400);
}

.suggestion-priority.medium {
    background: rgba(245, 158, 11, 0.2);
    color: var(--warning-400);
}

.suggestion-priority.low {
    background: rgba(16, 185, 129, 0.2);
    color: var(--success-400);
}

.suggestion-content h4 {
    color: white;
    font-weight: 600;
    margin: 0 0 var(--space-sm) 0;
}

.suggestion-content p {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
    line-height: 1.6;
    margin: 0 0 var(--space-md) 0;
}

.suggestion-impact {
    color: var(--primary-400);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* ===== COMPETITOR ANALYSIS ===== */
.competitor-analysis {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.competitor-item {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    padding: var(--space-xl);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
}

.competitor-info {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    flex: 1;
}

.competitor-avatar {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
}

.competitor-details h4 {
    color: white;
    font-weight: 600;
    margin: 0 0 var(--space-xs) 0;
}

.competitor-details p {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    margin: 0;
}

.competitor-metrics {
    display: flex;
    gap: var(--space-xl);
}

.competitor-metric {
    text-align: center;
}

.competitor-metric .metric-label {
    color: var(--gray-400);
    font-size: var(--font-size-xs);
    margin-bottom: var(--space-xs);
}

.competitor-metric .metric-value {
    color: white;
    font-weight: 600;
    font-size: var(--font-size-base);
}

.competitor-action {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    color: var(--gray-200);
    padding: var(--space-sm) var(--space-lg);
    cursor: pointer;
    transition: all var(--transition-base);
    font-size: var(--font-size-sm);
}

.competitor-action:hover {
    background: var(--primary-gradient);
    color: white;
}

/* ===== SCORE CALCULATION MODAL ===== */
.calculation-progress {
    text-align: center;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--space-2xl);
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-sm);
    opacity: 0.5;
    transition: opacity var(--transition-base);
}

.progress-step.active {
    opacity: 1;
}

.progress-step.completed {
    opacity: 0.8;
}

.step-icon {
    width: 50px;
    height: 50px;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    font-size: var(--font-size-lg);
    transition: all var(--transition-base);
}

.progress-step.active .step-icon {
    background: var(--primary-gradient);
    border-color: var(--primary-600);
    color: white;
}

.progress-step.completed .step-icon {
    background: var(--success-500);
    border-color: var(--success-500);
    color: white;
}

.step-label {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-align: center;
}

.progress-step.active .step-label {
    color: var(--gray-200);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    margin-bottom: var(--space-lg);
}

.progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-sm);
    width: 0%;
    transition: width var(--transition-base);
}

.progress-text {
    color: var(--gray-300);
    font-size: var(--font-size-base);
    margin-bottom: var(--space-lg);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    .dashboard-container {
        grid-template-columns: 300px 1fr;
    }
    
    .analytics-nav .nav-container {
        grid-template-columns: 200px 1fr 300px;
    }
    
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
    
    .dashboard-sidebar {
        max-height: 50vh;
        order: 2;
    }
    
    .dashboard-main {
        order: 1;
    }
    
    .analytics-nav .nav-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--space-md);
    }
    
    .nav-actions {
        justify-self: center;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .insight-grid {
        grid-template-columns: 1fr;
    }
    
    .performance-header,
    .insights-header,
    .chart-header {
        flex-direction: column;
        gap: var(--space-md);
        text-align: center;
    }
    
    .competitor-item {
        flex-direction: column;
        text-align: center;
    }
    
    .competitor-metrics {
        justify-content: center;
    }
    
    .progress-steps {
        flex-direction: column;
        gap: var(--space-lg);
    }
}

@media (max-width: 480px) {
    .dashboard-main {
        padding: var(--space-lg);
    }
    
    .metric-card,
    .chart-container,
    .content-performance,
    .ai-insights {
        padding: var(--space-lg);
    }
    
    .insight-card,
    .suggestion-item,
    .competitor-item {
        padding: var(--space-lg);
        flex-direction: column;
        text-align: center;
    }
    
    .insight-tabs {
        flex-direction: column;
    }
    
    .trend-item {
        flex-direction: column;
        text-align: center;
        gap: var(--space-md);
    }
}