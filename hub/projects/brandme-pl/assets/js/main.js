/**
 * BrandMe.pl - Main JavaScript
 * Enterprise-grade Personal Brand Builder Platform
 */

class BrandMePlatform {
    constructor() {
        this.init();
        this.bindEvents();
        this.setupAnimations();
        this.initializeComponents();
    }

    init() {
        this.isScrolling = false;
        this.currentSection = '';
        this.modalOpen = false;
        
        // Wait for DOM to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.onDOMReady());
        } else {
            this.onDOMReady();
        }
    }

    onDOMReady() {
        this.setupIntersectionObserver();
        this.animateCounters();
        this.animateProgressBars();
        this.initializeFeatureCards();
        this.setupSmoothScrolling();
        
        // Add loaded class for animations
        document.body.classList.add('loaded');
    }

    bindEvents() {
        // Navigation events
        document.addEventListener('click', this.handleNavigation.bind(this));
        
        // Modal events
        document.addEventListener('click', this.handleModalTriggers.bind(this));
        
        // Mobile menu toggle
        const mobileToggle = document.querySelector('.mobile-menu-toggle');
        if (mobileToggle) {
            mobileToggle.addEventListener('click', this.toggleMobileMenu.bind(this));
        }

        // Window events
        window.addEventListener('scroll', this.throttle(this.handleScroll.bind(this), 10));
        window.addEventListener('resize', this.throttle(this.handleResize.bind(this), 100));
        
        // Feature card interactions
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', this.handleFeatureHover.bind(this));
            card.addEventListener('mouseleave', this.handleFeatureLeave.bind(this));
        });

        // Demo functionality
        this.setupDemoPlayer();
        
        // CTA buttons
        document.querySelectorAll('[id="startDemo"], .demo-btn').forEach(btn => {
            btn.addEventListener('click', this.startDemo.bind(this));
        });

        // Form submissions
        this.setupFormHandling();
        
        // Keyboard navigation
        document.addEventListener('keydown', this.handleKeyboard.bind(this));
    }

    setupAnimations() {
        // CSS Animation utilities
        this.animationConfig = {
            duration: 300,
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
            stagger: 100
        };
    }

    initializeComponents() {
        this.initializePricingCards();
        this.initializeTestimonials();
        this.initializeToolTips();
        this.setupNotifications();
    }

    // ===== NAVIGATION ===== //
    handleNavigation(e) {
        const link = e.target.closest('.nav-link, .btn[href]');
        if (!link) return;

        const href = link.getAttribute('href');
        if (href && href.startsWith('#')) {
            e.preventDefault();
            this.scrollToSection(href);
        }
    }

    scrollToSection(targetId) {
        const target = document.querySelector(targetId);
        if (!target) return;

        const navbar = document.querySelector('.navbar');
        const offset = navbar ? navbar.offsetHeight + 20 : 20;

        this.smoothScrollTo(target.offsetTop - offset, 800);
    }

    smoothScrollTo(target, duration) {
        const start = window.pageYOffset;
        const distance = target - start;
        const startTime = performance.now();

        const animateScroll = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function
            const easeInOutCubic = progress < 0.5 
                ? 4 * progress * progress * progress 
                : (progress - 1) * (2 * progress - 2) * (2 * progress - 2) + 1;
            
            window.scrollTo(0, start + distance * easeInOutCubic);
            
            if (progress < 1) {
                requestAnimationFrame(animateScroll);
            }
        };

        requestAnimationFrame(animateScroll);
    }

    setupSmoothScrolling() {
        // Add smooth scrolling behavior to all internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = anchor.getAttribute('href');
                this.scrollToSection(targetId);
            });
        });
    }

    // ===== MOBILE MENU ===== //
    toggleMobileMenu() {
        const navLinks = document.querySelector('.nav-links');
        if (!navLinks) return;

        const isOpen = navLinks.classList.contains('mobile-open');
        
        if (isOpen) {
            navLinks.classList.remove('mobile-open');
            document.body.classList.remove('mobile-menu-open');
        } else {
            navLinks.classList.add('mobile-open');
            document.body.classList.add('mobile-menu-open');
        }

        // Update mobile toggle icon
        const toggle = document.querySelector('.mobile-menu-toggle i');
        if (toggle) {
            toggle.className = isOpen ? 'fas fa-bars' : 'fas fa-times';
        }
    }

    // ===== SCROLL HANDLING ===== //
    handleScroll() {
        if (this.isScrolling) return;
        
        this.isScrolling = true;
        
        requestAnimationFrame(() => {
            this.updateNavigationState();
            this.handleScrollAnimations();
            this.isScrolling = false;
        });
    }

    updateNavigationState() {
        const navbar = document.querySelector('.navbar');
        if (!navbar) return;

        const scrolled = window.pageYOffset > 50;
        navbar.classList.toggle('scrolled', scrolled);
    }

    handleScrollAnimations() {
        // Trigger animations for elements coming into view
        this.observeElements();
    }

    // ===== INTERSECTION OBSERVER ===== //
    setupIntersectionObserver() {
        const options = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    
                    // Trigger specific animations
                    this.triggerElementAnimation(entry.target);
                }
            });
        }, options);

        // Observe all animatable elements
        document.querySelectorAll('.feature-card, .story-card, .pricing-card, .demo-section').forEach(el => {
            this.observer.observe(el);
        });
    }

    triggerElementAnimation(element) {
        if (element.classList.contains('feature-card')) {
            this.animateFeatureCard(element);
        } else if (element.classList.contains('story-card')) {
            this.animateStoryCard(element);
        } else if (element.classList.contains('pricing-card')) {
            this.animatePricingCard(element);
        }
    }

    // ===== COUNTER ANIMATIONS ===== //
    animateCounters() {
        const counters = document.querySelectorAll('[data-count]');
        
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-count'));
            const duration = 2000;
            const increment = target / (duration / 16);
            let current = 0;
            
            const updateCounter = () => {
                current += increment;
                if (current < target) {
                    counter.textContent = Math.floor(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                }
            };
            
            // Start animation when element comes into view
            setTimeout(() => {
                if (this.isElementInViewport(counter)) {
                    updateCounter();
                }
            }, 500);
        });
    }

    // ===== PROGRESS BAR ANIMATIONS ===== //
    animateProgressBars() {
        const progressBars = document.querySelectorAll('[data-progress]');
        
        progressBars.forEach(bar => {
            const progress = parseInt(bar.getAttribute('data-progress'));
            
            setTimeout(() => {
                if (this.isElementInViewport(bar)) {
                    bar.style.width = `${progress}%`;
                }
            }, 800);
        });
    }

    // ===== MODAL FUNCTIONALITY ===== //
    handleModalTriggers(e) {
        // Open modal for feature cards
        if (e.target.closest('.feature-btn')) {
            e.preventDefault();
            const featureCard = e.target.closest('.feature-card');
            const featureType = featureCard.getAttribute('data-feature');
            this.openFeatureModal(featureType);
        }

        // Close modal
        if (e.target.closest('.modal-close') || e.target.classList.contains('modal')) {
            e.preventDefault();
            this.closeModal();
        }
    }

    openFeatureModal(featureType) {
        const modal = document.getElementById('featureModal');
        if (!modal) return;

        const modalTitle = modal.querySelector('.modal-title');
        const modalBody = modal.querySelector('.modal-body');

        const featureData = this.getFeatureData(featureType);
        
        modalTitle.textContent = featureData.title;
        modalBody.innerHTML = featureData.content;

        modal.classList.add('active');
        document.body.classList.add('modal-open');
        this.modalOpen = true;

        // Focus management
        modal.querySelector('.modal-close').focus();
    }

    closeModal() {
        const modal = document.getElementById('featureModal');
        if (!modal) return;

        modal.classList.remove('active');
        document.body.classList.remove('modal-open');
        this.modalOpen = false;
    }

    getFeatureData(featureType) {
        const features = {
            'website-builder': {
                title: 'AI Website Builder',
                content: `
                    <div class="feature-detail">
                        <h4>Stwórz profesjonalną stronę w 5 minut</h4>
                        <p>Nasz AI-powered konstruktor automatycznie tworzy dopasowaną do Ciebie stronę osobistą:</p>
                        <ul>
                            <li>Automatyczny dobór layoutu na podstawie Twojej branży</li>
                            <li>Inteligentne generowanie treści</li>
                            <li>Responsywny design na wszystkie urządzenia</li>
                            <li>SEO-optymalizacja</li>
                            <li>Integracja z social media</li>
                        </ul>
                        <div class="demo-cta">
                            <button class="btn-primary">Stwórz swoją stronę</button>
                        </div>
                    </div>
                `
            },
            'content-suite': {
                title: 'Kreator Treści',
                content: `
                    <div class="feature-detail">
                        <h4>Wszystkie narzędzia do tworzenia treści</h4>
                        <p>Kompletny zestaw narzędzi do profesjonalnego content marketingu:</p>
                        <ul>
                            <li>Generator postów LinkedIn z AI</li>
                            <li>50+ szablonów Instagram Stories</li>
                            <li>Kreator miniaturek YouTube</li>
                            <li>Generator bio dla wszystkich platform</li>
                            <li>Kalendarz treści z automatycznym planowaniem</li>
                            <li>Analiza trendów i sugerowane tematy</li>
                        </ul>
                        <div class="demo-cta">
                            <button class="btn-primary">Zacznij tworzyć</button>
                        </div>
                    </div>
                `
            },
            'analytics': {
                title: 'Zaawansowana Analityka',
                content: `
                    <div class="feature-detail">
                        <h4>Śledź i optymalizuj swoją markę</h4>
                        <p>Profesjonalne narzędzia analityczne dla Twojej osobistej marki:</p>
                        <ul>
                            <li>Tracker wzrostu na wszystkich platformach</li>
                            <li>Analiza engagement i dotarcia</li>
                            <li>Konkurencja i benchmarking</li>
                            <li>Raporty ROI personal brandingu</li>
                            <li>Predykcje wzrostu z AI</li>
                            <li>Alerty o ważnych zmianach</li>
                        </ul>
                        <div class="demo-cta">
                            <button class="btn-primary">Zobacz analitykę</button>
                        </div>
                    </div>
                `
            },
            'professional': {
                title: 'Narzędzia Pro',
                content: `
                    <div class="feature-detail">
                        <h4>Profesjonalne materiały biznesowe</h4>
                        <p>Gotowe szablony i narzędzia dla profesjonalistów:</p>
                        <ul>
                            <li>Media Kit z automatycznym wypełnianiem</li>
                            <li>Speaker One-Sheet dla prelegentów</li>
                            <li>Press Kit dla dziennikarzy</li>
                            <li>Szablony prezentacji</li>
                            <li>Portfolio z case studies</li>
                            <li>Kalkulator wartości współpracy</li>
                        </ul>
                        <div class="demo-cta">
                            <button class="btn-primary">Pobierz szablony</button>
                        </div>
                    </div>
                `
            },
            'brand-score': {
                title: 'Brand Score Calculator',
                content: `
                    <div class="feature-detail">
                        <h4>Zmierz siłę swojej marki</h4>
                        <p>Kompleksowa analiza Twojej osobistej marki w oparciu o:</p>
                        <ul>
                            <li>Obecność w wyszukiwarkach</li>
                            <li>Aktywność w social media</li>
                            <li>Jakość i konsystencja treści</li>
                            <li>Engagement i interakcje</li>
                            <li>Rozpoznawalność w branży</li>
                            <li>Spójność przekazu i wizualną</li>
                        </ul>
                        <div class="demo-cta">
                            <button class="btn-primary">Sprawdź swój wynik</button>
                        </div>
                    </div>
                `
            },
            'bio-generator': {
                title: 'AI Bio Generator',
                content: `
                    <div class="feature-detail">
                        <h4>Profesjonalne bio dla każdej platformy</h4>
                        <p>AI tworzy dopasowane bio na podstawie Twoich danych:</p>
                        <ul>
                            <li>Automatyczne dostosowanie do platformy</li>
                            <li>Optymalizacja pod kątem SEO</li>
                            <li>A/B testing różnych wersji</li>
                            <li>Aktualizacje na podstawie osiągnięć</li>
                            <li>Tłumaczenia na różne języki</li>
                            <li>Integracja z LinkedIn, Twitter, Instagram</li>
                        </ul>
                        <div class="demo-cta">
                            <button class="btn-primary">Wygeneruj bio</button>
                        </div>
                    </div>
                `
            }
        };

        return features[featureType] || {
            title: 'Funkcja',
            content: '<p>Szczegóły funkcji będą dostępne wkrótce.</p>'
        };
    }

    // ===== DEMO PLAYER ===== //
    setupDemoPlayer() {
        const playButtons = document.querySelectorAll('.play-button');
        playButtons.forEach(btn => {
            btn.addEventListener('click', this.playDemo.bind(this));
        });
    }

    playDemo() {
        const player = document.querySelector('.demo-player');
        if (!player) return;

        const playButton = player.querySelector('.play-button i');
        const timeline = player.querySelector('.timeline-fill');
        const timeDisplay = player.querySelector('.time-display');

        if (playButton.classList.contains('fa-play')) {
            // Start playing
            playButton.className = 'fas fa-pause';
            this.animateDemo(timeline, timeDisplay);
        } else {
            // Pause
            playButton.className = 'fas fa-play';
            // Reset animation would go here
        }
    }

    animateDemo(timeline, timeDisplay) {
        let progress = 0;
        const duration = 5000; // 5 seconds
        const updateInterval = 50;
        const totalSteps = duration / updateInterval;
        
        const animate = () => {
            progress += 1;
            const percentage = (progress / totalSteps) * 100;
            const currentTime = Math.floor((progress / totalSteps) * 5); // 5 minutes total

            timeline.style.width = `${percentage}%`;
            timeDisplay.textContent = `${currentTime}:00 / 5:00`;

            if (progress < totalSteps) {
                setTimeout(animate, updateInterval);
            } else {
                // Reset to play button
                document.querySelector('.play-button i').className = 'fas fa-play';
            }
        };

        animate();
    }

    // ===== FEATURE INTERACTIONS ===== //
    handleFeatureHover(e) {
        const card = e.currentTarget;
        this.animateFeatureCard(card);
    }

    handleFeatureLeave(e) {
        const card = e.currentTarget;
        // Reset any hover-specific animations
    }

    animateFeatureCard(card) {
        const demo = card.querySelector('.feature-demo');
        if (demo) {
            demo.classList.add('animate-demo');
        }
    }

    initializeFeatureCards() {
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('click', (e) => {
                if (!e.target.closest('.feature-btn')) {
                    // Animate card interaction
                    card.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        card.style.transform = '';
                    }, 150);
                }
            });
        });
    }

    // ===== START DEMO FUNCTIONALITY ===== //
    startDemo() {
        this.showNotification('Demo uruchamia się...', 'info');
        
        // Simulate demo initialization
        setTimeout(() => {
            this.showBrandTransformation();
        }, 1000);
    }

    showBrandTransformation() {
        const transformationData = {
            before: {
                score: 23,
                issues: ['Słabe bio', 'Brak treści', 'Niski zasięg']
            },
            after: {
                score: 87,
                improvements: ['Profesjonalne bio', 'Regularne treści', '+247% zasięgu']
            }
        };

        // This would typically open a dedicated demo modal
        this.showNotification('Transformacja zakończona! Twój Brand Score wzrósł o 64 punkty!', 'success');
    }

    // ===== FORM HANDLING ===== //
    setupFormHandling() {
        // Newsletter subscription
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', this.handleFormSubmit.bind(this));
        });

        // Real-time validation
        document.querySelectorAll('input[type="email"]').forEach(input => {
            input.addEventListener('blur', this.validateEmail.bind(this));
        });
    }

    handleFormSubmit(e) {
        e.preventDefault();
        const form = e.target;
        const formData = new FormData(form);
        
        // Show loading state
        const submitBtn = form.querySelector('[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Wysyłanie...';
        }

        // Simulate form submission
        setTimeout(() => {
            this.showNotification('Dziękujemy! Sprawdź swoją skrzynkę email.', 'success');
            form.reset();
            
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Zapisz się';
            }
        }, 1500);
    }

    validateEmail(e) {
        const input = e.target;
        const email = input.value;
        const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        
        input.classList.toggle('invalid', !isValid && email.length > 0);
        input.classList.toggle('valid', isValid);
    }

    // ===== PRICING INTERACTIONS ===== //
    initializePricingCards() {
        document.querySelectorAll('.pricing-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-12px)';
            });
            
            card.addEventListener('mouseleave', () => {
                if (!card.classList.contains('featured')) {
                    card.style.transform = '';
                } else {
                    card.style.transform = 'scale(1.05)';
                }
            });
        });

        // Handle pricing button clicks
        document.querySelectorAll('.pricing-btn').forEach(btn => {
            btn.addEventListener('click', this.handlePricingSelect.bind(this));
        });
    }

    handlePricingSelect(e) {
        const button = e.target;
        const card = button.closest('.pricing-card');
        const planTitle = card.querySelector('.pricing-title').textContent;
        
        if (button.textContent.includes('za darmo')) {
            this.showNotification(`Witaj w planie ${planTitle}! Przygotowujemy Twoje konto...`, 'success');
        } else if (button.textContent.includes('trial')) {
            this.showNotification(`14-dniowy trial planu ${planTitle} został aktywowany!`, 'success');
        } else {
            this.showNotification('Przekierowujemy do kontaktu...', 'info');
        }
    }

    // ===== TESTIMONIALS ===== //
    initializeTestimonials() {
        // Auto-rotate testimonials if there are more than visible
        const testimonials = document.querySelectorAll('.story-card');
        if (testimonials.length > 3) {
            this.setupTestimonialRotation(testimonials);
        }
    }

    setupTestimonialRotation(testimonials) {
        let currentIndex = 0;
        const rotationInterval = 5000; // 5 seconds

        setInterval(() => {
            testimonials.forEach((testimonial, index) => {
                testimonial.style.opacity = index === currentIndex ? '1' : '0.7';
                testimonial.style.transform = index === currentIndex ? 'scale(1.02)' : 'scale(1)';
            });
            
            currentIndex = (currentIndex + 1) % testimonials.length;
        }, rotationInterval);
    }

    // ===== TOOLTIPS ===== //
    initializeToolTips() {
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            element.addEventListener('mouseenter', this.showTooltip.bind(this));
            element.addEventListener('mouseleave', this.hideTooltip.bind(this));
        });
    }

    showTooltip(e) {
        const element = e.target;
        const tooltipText = element.getAttribute('data-tooltip');
        
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = tooltipText;
        tooltip.style.position = 'absolute';
        tooltip.style.zIndex = '9999';
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = `${rect.left + rect.width / 2 - tooltip.offsetWidth / 2}px`;
        tooltip.style.top = `${rect.top - tooltip.offsetHeight - 10}px`;
        
        element._tooltip = tooltip;
    }

    hideTooltip(e) {
        const element = e.target;
        if (element._tooltip) {
            document.body.removeChild(element._tooltip);
            delete element._tooltip;
        }
    }

    // ===== NOTIFICATIONS ===== //
    setupNotifications() {
        // Create notification container if it doesn't exist
        if (!document.querySelector('.notification-container')) {
            const container = document.createElement('div');
            container.className = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
    }

    showNotification(message, type = 'info') {
        const container = document.querySelector('.notification-container');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#6366f1'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 0.75rem;
            margin-bottom: 0.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            backdrop-filter: blur(20px);
            pointer-events: auto;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;
        notification.textContent = message;

        container.appendChild(notification);

        // Animate in
        requestAnimationFrame(() => {
            notification.style.transform = 'translateX(0)';
        });

        // Remove after delay
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 4000);
    }

    // ===== KEYBOARD NAVIGATION ===== //
    handleKeyboard(e) {
        // Escape key closes modal
        if (e.key === 'Escape' && this.modalOpen) {
            this.closeModal();
        }

        // Tab navigation improvements
        if (e.key === 'Tab') {
            // Enhance tab navigation for better accessibility
        }
    }

    // ===== UTILITY METHODS ===== //
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    handleResize() {
        // Handle responsive adjustments
        this.updateMobileMenu();
        this.adjustModalSize();
    }

    updateMobileMenu() {
        const navLinks = document.querySelector('.nav-links');
        if (window.innerWidth > 768 && navLinks.classList.contains('mobile-open')) {
            navLinks.classList.remove('mobile-open');
            document.body.classList.remove('mobile-menu-open');
        }
    }

    adjustModalSize() {
        const modal = document.querySelector('.modal.active');
        if (modal) {
            const modalContent = modal.querySelector('.modal-content');
            // Adjust modal size based on viewport
        }
    }

    // ===== PERFORMANCE OPTIMIZATIONS ===== //
    observeElements() {
        // Lazy load images and content as needed
        document.querySelectorAll('img[data-src]').forEach(img => {
            if (this.isElementInViewport(img)) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
            }
        });
    }
}

// Initialize the platform when DOM is ready
const brandMePlatform = new BrandMePlatform();

// Export for potential external use
window.BrandMePlatform = BrandMePlatform;