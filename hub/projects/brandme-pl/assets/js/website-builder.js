/**
 * Website Builder - BrandMe.pl
 * AI-Powered Personal Website Builder
 */

class WebsiteBuilder {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 5;
        this.userData = {};
        this.selectedTemplate = 'professional';
        this.generatedContent = {};
        this.isProcessing = false;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeFormData();
        this.updateProgress();
        this.simulatePreviewLoading();
    }

    bindEvents() {
        // Navigation
        document.getElementById('nextStep').addEventListener('click', () => this.nextStep());
        document.getElementById('prevStep').addEventListener('click', () => this.prevStep());
        
        // Progress steps (clickable)
        document.querySelectorAll('.step').forEach(step => {
            step.addEventListener('click', (e) => {
                const stepNumber = parseInt(e.currentTarget.getAttribute('data-step'));
                this.goToStep(stepNumber);
            });
        });

        // Form interactions
        this.bindFormEvents();
        
        // Template selection
        this.bindTemplateEvents();
        
        // Content generation
        this.bindContentEvents();
        
        // Styling events
        this.bindStylingEvents();
        
        // Publication events
        this.bindPublicationEvents();
        
        // Preview controls
        this.bindPreviewEvents();
        
        // AI Processing
        this.bindAIEvents();
        
        // Save progress
        document.getElementById('saveProgress').addEventListener('click', () => this.saveProgress());
        
        // Preview site
        document.getElementById('previewSite').addEventListener('click', () => this.previewSite());
    }

    // ===== NAVIGATION ===== //
    nextStep() {
        if (this.currentStep < this.totalSteps && this.validateCurrentStep()) {
            this.currentStep++;
            this.updateStep();
            this.updateProgress();
        }
    }

    prevStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.updateStep();
            this.updateProgress();
        }
    }

    goToStep(stepNumber) {
        if (stepNumber >= 1 && stepNumber <= this.totalSteps) {
            // Only allow going to completed steps or the next immediate step
            if (stepNumber <= this.getCompletedSteps() + 1) {
                this.currentStep = stepNumber;
                this.updateStep();
                this.updateProgress();
            }
        }
    }

    updateStep() {
        // Hide all steps
        document.querySelectorAll('.builder-step').forEach(step => {
            step.classList.remove('active');
        });
        
        // Show current step
        document.getElementById(`step-${this.currentStep}`).classList.add('active');
        
        // Update navigation buttons
        this.updateNavigationButtons();
        
        // Trigger step-specific actions
        this.onStepChange();
    }

    updateProgress() {
        document.querySelectorAll('.step').forEach((step, index) => {
            const stepNumber = index + 1;
            
            step.classList.remove('active', 'completed');
            
            if (stepNumber === this.currentStep) {
                step.classList.add('active');
            } else if (stepNumber < this.currentStep) {
                step.classList.add('completed');
            }
        });
    }

    updateNavigationButtons() {
        const prevBtn = document.getElementById('prevStep');
        const nextBtn = document.getElementById('nextStep');
        const publishBtn = document.getElementById('publishSite');
        
        prevBtn.disabled = this.currentStep === 1;
        
        if (this.currentStep === this.totalSteps) {
            nextBtn.style.display = 'none';
            publishBtn.style.display = 'flex';
        } else {
            nextBtn.style.display = 'flex';
            publishBtn.style.display = 'none';
        }
    }

    getCompletedSteps() {
        return this.currentStep - 1;
    }

    validateCurrentStep() {
        switch (this.currentStep) {
            case 1:
                return this.validateProfileForm();
            case 2:
                return this.validateTemplateSelection();
            case 3:
                return this.validateContentGeneration();
            case 4:
                return this.validateStyling();
            case 5:
                return this.validatePublication();
            default:
                return true;
        }
    }

    onStepChange() {
        switch (this.currentStep) {
            case 1:
                this.onProfileStep();
                break;
            case 2:
                this.onTemplateStep();
                break;
            case 3:
                this.onContentStep();
                break;
            case 4:
                this.onStylingStep();
                break;
            case 5:
                this.onPublicationStep();
                break;
        }
    }

    // ===== FORM EVENTS ===== //
    bindFormEvents() {
        const form = document.getElementById('profile-form');
        if (!form) return;

        // Real-time validation and AI analysis
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.updateUserData();
                this.triggerAIAnalysis();
            });
            
            input.addEventListener('change', () => {
                this.updateUserData();
                this.triggerAIAnalysis();
            });
        });

        // Checkbox handling
        const checkboxes = form.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateUserData();
                this.triggerAIAnalysis();
            });
        });
    }

    updateUserData() {
        const form = document.getElementById('profile-form');
        if (!form) return;

        const formData = new FormData(form);
        
        // Basic fields
        this.userData.fullName = formData.get('fullName') || '';
        this.userData.profession = formData.get('profession') || '';
        this.userData.industry = formData.get('industry') || '';
        this.userData.experience = formData.get('experience') || '';
        this.userData.bio = formData.get('bio') || '';
        
        // Goals (checkboxes)
        this.userData.goals = [];
        const goalCheckboxes = form.querySelectorAll('input[name="goals"]:checked');
        goalCheckboxes.forEach(checkbox => {
            this.userData.goals.push(checkbox.value);
        });
    }

    triggerAIAnalysis() {
        if (!this.userData.fullName || !this.userData.profession || !this.userData.industry) {
            document.getElementById('ai-analysis').textContent = 
                'Wypełnij dane powyżej, a AI przeanalizuje Twój profil i zaproponuje najlepsze rozwiązania.';
            return;
        }

        // Simulate AI analysis
        setTimeout(() => {
            this.generateAIAnalysis();
        }, 1000);
    }

    generateAIAnalysis() {
        const analyses = {
            technology: `Na podstawie Twojego profilu w branży technologicznej, AI rekomenduje: 
                        szablon Professional z naciskiem na portfolio projektów, blog techniczny 
                        i integrację z GitHub. Idealne kolory: niebieski (zaufanie) + szary (profesjonalizm).`,
            marketing: `Twój profil marketingowy sugeruje: szablon Creative z emphasis na case studies, 
                       testimoniale klientów i portfolio kampanii. Polecane kolory: fioletowy (kreatywność) 
                       + zielony (wzrost).`,
            business: `Dla profilu biznesowego AI wybiera: szablon Business z naciskiem na osiągnięcia, 
                      leadership i networking. Najlepsze kolory: granatowy (autorytet) + złoty (sukces).`,
            creative: `Twój kreatywny profil idealnie pasuje do: szablon Creative z galerią prac, 
                      procesem twórczym i inspiracjami. Rekomendowane kolory: różowy (kreatywność) 
                      + fioletowy (artyzm).`,
            default: `AI analizuje Twój unikalny profil i dostosowuje rekomendacje. Szablon Professional 
                     będzie doskonałym wyborem z możliwością pełnej personalizacji.`
        };

        const analysis = analyses[this.userData.industry] || analyses.default;
        
        document.getElementById('ai-analysis').innerHTML = `
            <strong>✨ AI Rekomendacja:</strong><br>
            ${analysis}
        `;
    }

    validateProfileForm() {
        const required = ['fullName', 'profession', 'industry'];
        
        for (const field of required) {
            if (!this.userData[field]) {
                this.showNotification('Wypełnij wszystkie wymagane pola', 'error');
                return false;
            }
        }
        
        return true;
    }

    onProfileStep() {
        // Focus on first input
        setTimeout(() => {
            const firstInput = document.getElementById('fullName');
            if (firstInput) firstInput.focus();
        }, 100);
    }

    // ===== TEMPLATE SELECTION ===== //
    bindTemplateEvents() {
        document.querySelectorAll('.template-option').forEach(option => {
            option.addEventListener('click', (e) => {
                const template = e.currentTarget.getAttribute('data-template');
                this.selectTemplate(template);
            });
        });
    }

    selectTemplate(templateName) {
        // Remove previous selection
        document.querySelectorAll('.template-option').forEach(option => {
            option.classList.remove('selected');
        });
        
        // Add selection to clicked template
        document.querySelector(`[data-template="${templateName}"]`).classList.add('selected');
        
        this.selectedTemplate = templateName;
        this.updatePreview();
        
        this.showNotification(`Szablon "${this.getTemplateName(templateName)}" został wybrany`, 'success');
    }

    getTemplateName(template) {
        const names = {
            professional: 'Professional',
            creative: 'Creative',
            business: 'Business'
        };
        return names[template] || template;
    }

    updateTemplateRecommendation() {
        const recommendations = {
            technology: 'Professional - idealny dla ekspertów technicznych',
            marketing: 'Creative - świetny dla marketerów i content creatorów',
            business: 'Business - doskonały dla liderów i menedżerów',
            creative: 'Creative - perfekt dla projektantów i artystów',
            default: 'Professional - uniwersalny szablon dla każdej branży'
        };

        const recommendation = recommendations[this.userData.industry] || recommendations.default;
        document.getElementById('template-recommendation').textContent = 
            `Na podstawie Twojego profilu, AI rekomenduje szablon ${recommendation}.`;
    }

    validateTemplateSelection() {
        return this.selectedTemplate !== null;
    }

    onTemplateStep() {
        this.updateTemplateRecommendation();
        // Auto-select recommended template based on industry
        this.autoSelectRecommendedTemplate();
    }

    autoSelectRecommendedTemplate() {
        const industryTemplates = {
            technology: 'professional',
            marketing: 'creative',
            business: 'business',
            creative: 'creative'
        };

        const recommendedTemplate = industryTemplates[this.userData.industry] || 'professional';
        
        // Only auto-select if nothing is selected yet
        if (!document.querySelector('.template-option.selected')) {
            this.selectTemplate(recommendedTemplate);
        }
    }

    // ===== CONTENT GENERATION ===== //
    bindContentEvents() {
        // AI Generate buttons
        document.querySelectorAll('.btn-ai-generate').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const contentType = e.currentTarget.getAttribute('data-content');
                this.generateContent(contentType);
            });
        });

        // Regenerate buttons
        document.querySelectorAll('.btn-regenerate').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = e.currentTarget.closest('.content-section');
                const contentType = section.querySelector('.btn-ai-generate').getAttribute('data-content');
                this.generateContent(contentType, true);
            });
        });

        // Copy buttons
        document.querySelectorAll('.btn-copy').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = e.currentTarget.closest('.content-section');
                const textarea = section.querySelector('textarea');
                this.copyToClipboard(textarea.value);
            });
        });

        // Add skill button
        document.querySelector('.btn-add-skill')?.addEventListener('click', () => {
            this.addCustomSkill();
        });
    }

    async generateContent(contentType, regenerate = false) {
        const button = document.querySelector(`[data-content="${contentType}"]`);
        const originalText = button.innerHTML;
        
        // Show loading state
        button.innerHTML = '<i class="fas fa-magic fa-spin"></i> Generuję...';
        button.disabled = true;

        // Simulate AI processing
        await this.simulateAIProcessing();

        try {
            const content = await this.getAIGeneratedContent(contentType, regenerate);
            this.displayGeneratedContent(contentType, content);
            
            this.showNotification('Treść została wygenerowana!', 'success');
        } catch (error) {
            this.showNotification('Błąd generowania treści', 'error');
        } finally {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    async getAIGeneratedContent(contentType, regenerate = false) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        const templates = {
            headline: [
                `${this.userData.fullName} - ${this.userData.profession}`,
                `Expert ${this.userData.profession} | Transformuję biznes przez technologię`,
                `${this.userData.profession} z ${this.getExperienceText()} doświadczenia`,
                `Pomagam firmom osiągać cele jako ${this.userData.profession}`
            ],
            about: [
                `Jestem doświadczonym ${this.userData.profession.toLowerCase()} z pasją do ${this.getIndustryFocus()}. 
                Przez ostatnie ${this.getExperienceText()} pomagam firmom osiągać lepsze wyniki poprzez 
                innowacyjne rozwiązania i strategiczne podejście. Moja ekspertyza obejmuje ${this.getSkillAreas()}.`,
                
                `Jako ${this.userData.profession.toLowerCase()} z ${this.getExperienceText()} doświadczenia, 
                specjalizuję się w ${this.getIndustryFocus()}. Moim celem jest wspieranie organizacji 
                w realizacji ich wizji poprzez ${this.getApproachDescription()}.`,
                
                `Professionalist z zakresu ${this.userData.industry} z udokumentowanymi sukcesami w ${this.getAchievementAreas()}. 
                Łączę ${this.getExperienceText()} doświadczenia z nowoczesnym podejściem do ${this.getIndustryFocus()}.`
            ],
            experience: [
                `• ${this.userData.profession} | ${this.getCurrentCompany()} (2020 - obecnie)
                  - Zarządzanie projektami o wartości ponad 2M PLN
                  - Zwiększenie efektywności zespołu o 40%
                  - Implementacja nowoczesnych rozwiązań
                
                • Senior Specialist | ${this.getPreviousCompany()} (2018 - 2020)
                  - Rozwój strategii ${this.getIndustryFocus()}
                  - Mentoring młodszych specjalistów
                  - Optymalizacja procesów biznesowych`,
                
                `• Lead ${this.userData.profession} | ${this.getCurrentCompany()}
                  - Kierowanie zespołem 8+ osób
                  - Realizacja projektów dla Fortune 500
                  - ROI projektów na poziomie 250%
                
                • ${this.userData.profession} | ${this.getPreviousCompany()}
                  - Specjalizacja w ${this.getIndustryFocus()}
                  - Współpraca z międzynarodowymi klientami
                  - Certyfikacje branżowe i szkolenia`
            ],
            skills: [
                this.generateSkillsForIndustry()
            ]
        };

        const variations = templates[contentType] || ['Treść zostanie wygenerowana wkrótce.'];
        const index = regenerate ? Math.floor(Math.random() * variations.length) : 0;
        
        return variations[index];
    }

    generateSkillsForIndustry() {
        const skillSets = {
            technology: [
                'JavaScript', 'Python', 'React', 'Node.js', 'AWS', 'Docker', 
                'Kubernetes', 'Agile', 'Scrum', 'DevOps', 'Git', 'SQL'
            ],
            marketing: [
                'Google Ads', 'Facebook Ads', 'SEO', 'Content Marketing', 'Analytics',
                'Marketing Automation', 'CRM', 'Social Media', 'Email Marketing', 'Copywriting'
            ],
            business: [
                'Strategic Planning', 'Team Leadership', 'Project Management', 'Data Analysis',
                'Financial Planning', 'Process Optimization', 'Negotiation', 'Public Speaking'
            ],
            creative: [
                'Adobe Creative Suite', 'Figma', 'Sketch', 'UI/UX Design', 'Branding',
                'Typography', 'Color Theory', 'Prototyping', 'User Research', 'Design Thinking'
            ],
            default: [
                'Leadership', 'Communication', 'Project Management', 'Problem Solving',
                'Team Building', 'Strategic Thinking', 'Analysis', 'Innovation'
            ]
        };

        return skillSets[this.userData.industry] || skillSets.default;
    }

    displayGeneratedContent(contentType, content) {
        if (contentType === 'skills') {
            this.displaySkills(content);
        } else {
            const textarea = document.getElementById(contentType);
            if (textarea) {
                textarea.value = content;
                // Trigger animation
                textarea.style.opacity = '0.5';
                setTimeout(() => {
                    textarea.style.opacity = '1';
                }, 200);
            }
        }
        
        // Store in generated content
        this.generatedContent[contentType] = content;
    }

    displaySkills(skills) {
        const container = document.getElementById('skills-list');
        if (!container) return;

        container.innerHTML = '';
        
        skills.forEach(skill => {
            const skillTag = document.createElement('div');
            skillTag.className = 'skill-tag';
            skillTag.innerHTML = `
                ${skill}
                <span class="skill-remove" onclick="this.parentElement.remove()">×</span>
            `;
            container.appendChild(skillTag);
        });
    }

    addCustomSkill() {
        const skillName = prompt('Wprowadź nazwę umiejętności:');
        if (skillName && skillName.trim()) {
            const container = document.getElementById('skills-list');
            const skillTag = document.createElement('div');
            skillTag.className = 'skill-tag';
            skillTag.innerHTML = `
                ${skillName.trim()}
                <span class="skill-remove" onclick="this.parentElement.remove()">×</span>
            `;
            container.appendChild(skillTag);
        }
    }

    // Helper methods for content generation
    getExperienceText() {
        const expMap = {
            junior: 'lata',
            mid: '5 lat',
            senior: '10 lat',
            expert: '15+ lat'
        };
        return expMap[this.userData.experience] || '5 lat';
    }

    getIndustryFocus() {
        const focusMap = {
            technology: 'innowacje technologiczne',
            marketing: 'marketing cyfrowy',
            business: 'rozwój biznesu',
            creative: 'projektowanie użytkowników',
            default: 'rozwój organizacyjny'
        };
        return focusMap[this.userData.industry] || focusMap.default;
    }

    getSkillAreas() {
        const areas = {
            technology: 'development, cloud computing, DevOps',
            marketing: 'digital marketing, content strategy, analytics',
            business: 'strategic planning, team leadership, process optimization',
            creative: 'user experience, visual design, creative direction'
        };
        return areas[this.userData.industry] || 'leadership, innovation, strategic thinking';
    }

    getApproachDescription() {
        return 'data-driven insights i user-centric approach';
    }

    getAchievementAreas() {
        return `${this.getIndustryFocus()} i team leadership`;
    }

    getCurrentCompany() {
        const companies = ['TechCorp', 'Digital Solutions', 'Innovation Labs', 'Future Systems'];
        return companies[Math.floor(Math.random() * companies.length)];
    }

    getPreviousCompany() {
        const companies = ['Global Tech', 'Smart Solutions', 'Digital Pioneers', 'NextGen Corp'];
        return companies[Math.floor(Math.random() * companies.length)];
    }

    async simulateAIProcessing() {
        return new Promise(resolve => {
            setTimeout(resolve, Math.random() * 1000 + 1000); // 1-2 seconds
        });
    }

    copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            this.showNotification('Skopiowano do schowka!', 'success');
        }).catch(() => {
            this.showNotification('Błąd kopiowania', 'error');
        });
    }

    validateContentGeneration() {
        // Check if at least headline and about are generated
        return this.generatedContent.headline && this.generatedContent.about;
    }

    onContentStep() {
        // Auto-generate headline if not exists
        if (!this.generatedContent.headline) {
            setTimeout(() => {
                this.generateContent('headline');
            }, 500);
        }
    }

    // ===== STYLING ===== //
    bindStylingEvents() {
        // Color scheme selection
        document.querySelectorAll('.color-scheme').forEach(scheme => {
            scheme.addEventListener('click', (e) => {
                const schemeName = e.currentTarget.getAttribute('data-scheme');
                this.selectColorScheme(schemeName);
            });
        });

        // Font selection
        document.querySelectorAll('.font-option').forEach(font => {
            font.addEventListener('click', (e) => {
                const fontName = e.currentTarget.getAttribute('data-font');
                this.selectFont(fontName);
            });
        });

        // Layout selection
        document.querySelectorAll('.layout-option').forEach(layout => {
            layout.addEventListener('click', (e) => {
                const layoutName = e.currentTarget.getAttribute('data-layout');
                this.selectLayout(layoutName);
            });
        });
    }

    selectColorScheme(schemeName) {
        document.querySelectorAll('.color-scheme').forEach(scheme => {
            scheme.classList.remove('active');
        });
        document.querySelector(`[data-scheme="${schemeName}"]`).classList.add('active');
        
        this.userData.colorScheme = schemeName;
        this.updatePreview();
    }

    selectFont(fontName) {
        document.querySelectorAll('.font-option').forEach(font => {
            font.classList.remove('active');
        });
        document.querySelector(`[data-font="${fontName}"]`).classList.add('active');
        
        this.userData.font = fontName;
        this.updatePreview();
    }

    selectLayout(layoutName) {
        document.querySelectorAll('.layout-option').forEach(layout => {
            layout.classList.remove('active');
        });
        document.querySelector(`[data-layout="${layoutName}"]`).classList.add('active');
        
        this.userData.layout = layoutName;
        this.updatePreview();
    }

    validateStyling() {
        return true; // Styling is optional
    }

    onStylingStep() {
        // Set defaults if not selected
        if (!this.userData.colorScheme) {
            this.selectColorScheme('blue');
        }
        if (!this.userData.font) {
            this.selectFont('inter');
        }
        if (!this.userData.layout) {
            this.selectLayout('centered');
        }
    }

    // ===== PUBLICATION ===== //
    bindPublicationEvents() {
        // Domain type selection
        document.querySelectorAll('input[name="domain"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.updateDomainInput(e.target.value);
            });
        });

        // Domain availability check
        document.querySelector('.btn-check-availability')?.addEventListener('click', () => {
            this.checkDomainAvailability();
        });

        // Domain name input
        document.getElementById('domain-name')?.addEventListener('input', (e) => {
            this.updatePreviewDomain(e.target.value);
        });

        // Integration toggles
        document.querySelectorAll('.integration-toggle input').forEach(toggle => {
            toggle.addEventListener('change', (e) => {
                this.toggleIntegration(e.target.id, e.target.checked);
            });
        });

        // Publish button
        document.getElementById('publishSite')?.addEventListener('click', () => {
            this.publishWebsite();
        });
    }

    updateDomainInput(domainType) {
        const suffix = document.querySelector('.domain-suffix');
        const input = document.getElementById('domain-name');
        
        if (domainType === 'subdomain') {
            suffix.textContent = '.brandme.pl';
            input.placeholder = 'twoja-nazwa';
        } else {
            suffix.textContent = '.com';
            input.placeholder = 'twoja-domena';
        }
        
        this.userData.domainType = domainType;
    }

    checkDomainAvailability() {
        const domainName = document.getElementById('domain-name').value;
        const result = document.querySelector('.availability-result');
        
        if (!domainName.trim()) {
            this.showNotification('Wprowadź nazwę domeny', 'error');
            return;
        }

        // Simulate check
        const button = document.querySelector('.btn-check-availability');
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        
        setTimeout(() => {
            // Simulate random availability (90% chance available)
            const isAvailable = Math.random() > 0.1;
            
            if (isAvailable) {
                result.innerHTML = '<i class="fas fa-check-circle"></i><span>Domena dostępna!</span>';
                result.style.color = 'var(--success-400)';
            } else {
                result.innerHTML = '<i class="fas fa-times-circle"></i><span>Domena zajęta</span>';
                result.style.color = 'var(--error-500)';
            }
            
            result.classList.add('show');
            button.innerHTML = '<i class="fas fa-search"></i> Sprawdź';
        }, 1500);
    }

    updatePreviewDomain(domainName) {
        const suffix = this.userData.domainType === 'subdomain' ? '.brandme.pl' : '.com';
        const fullDomain = domainName ? `${domainName}${suffix}` : `twoja-nazwa${suffix}`;
        
        document.getElementById('preview-domain').textContent = fullDomain;
        this.userData.domainName = domainName;
    }

    toggleIntegration(integrationId, enabled) {
        if (!this.userData.integrations) {
            this.userData.integrations = {};
        }
        
        this.userData.integrations[integrationId.replace('-toggle', '')] = enabled;
        
        this.showNotification(
            `Integracja ${enabled ? 'włączona' : 'wyłączona'}`, 
            'info'
        );
    }

    async publishWebsite() {
        if (!this.validatePublication()) return;

        const button = document.getElementById('publishSite');
        const originalText = button.innerHTML;
        
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Publikuję...';
        button.disabled = true;

        try {
            // Simulate publication process
            await this.simulatePublicationProcess();
            
            this.showNotification('Strona została opublikowana!', 'success');
            this.showPublicationSuccess();
            
        } catch (error) {
            this.showNotification('Błąd publikacji strony', 'error');
        } finally {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    async simulatePublicationProcess() {
        const steps = [
            'Generowanie strony...',
            'Optymalizacja SEO...',
            'Konfiguracja domeny...',
            'Wdrażanie na serwer...',
            'Testowanie działania...'
        ];

        for (let i = 0; i < steps.length; i++) {
            await new Promise(resolve => setTimeout(resolve, 1000));
            // Could show progress here
        }
    }

    showPublicationSuccess() {
        const domain = `${this.userData.domainName || 'twoja-nazwa'}${this.userData.domainType === 'subdomain' ? '.brandme.pl' : '.com'}`;
        
        // Could show a success modal with the live URL
        alert(`Gratulacje! Twoja strona jest dostępna pod adresem: https://${domain}`);
    }

    validatePublication() {
        const domainName = document.getElementById('domain-name').value;
        if (!domainName.trim()) {
            this.showNotification('Wprowadź nazwę domeny', 'error');
            return false;
        }
        return true;
    }

    onPublicationStep() {
        // Auto-fill SEO data
        this.autoFillSEOData();
        
        // Set default domain name
        if (!this.userData.domainName) {
            const suggestedName = this.generateDomainSuggestion();
            document.getElementById('domain-name').value = suggestedName;
            this.updatePreviewDomain(suggestedName);
        }
    }

    autoFillSEOData() {
        const titleField = document.getElementById('meta-title');
        const descField = document.getElementById('meta-description');
        
        if (!titleField.value) {
            titleField.value = `${this.userData.fullName} - ${this.userData.profession}`;
        }
        
        if (!descField.value) {
            descField.value = `Profesjonalny profil ${this.userData.profession.toLowerCase()} z ${this.getExperienceText()} doświadczenia w branży ${this.userData.industry}.`;
        }
    }

    generateDomainSuggestion() {
        const name = this.userData.fullName.toLowerCase()
            .replace(/[^a-z0-9]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
        
        return name || 'moja-strona';
    }

    // ===== PREVIEW ===== //
    bindPreviewEvents() {
        // Device selector
        document.querySelectorAll('.device-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const device = e.currentTarget.getAttribute('data-device');
                this.changePreviewDevice(device);
            });
        });
    }

    changePreviewDevice(device) {
        document.querySelectorAll('.device-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-device="${device}"]`).classList.add('active');
        
        const previewFrame = document.querySelector('.preview-frame');
        previewFrame.className = `preview-frame ${device}`;
    }

    updatePreview() {
        // Simulate preview update
        this.showPreviewLoading();
        
        setTimeout(() => {
            this.hidePreviewLoading();
            // Here you would actually update the iframe content
            this.generatePreviewContent();
        }, 1000);
    }

    showPreviewLoading() {
        document.querySelector('.preview-loading').style.display = 'block';
        document.querySelector('.preview-frame').style.opacity = '0.5';
    }

    hidePreviewLoading() {
        document.querySelector('.preview-loading').style.display = 'none';
        document.querySelector('.preview-frame').style.opacity = '1';
    }

    generatePreviewContent() {
        // In a real application, this would generate actual HTML
        const iframe = document.getElementById('website-preview');
        
        const previewHTML = this.createPreviewHTML();
        const blob = new Blob([previewHTML], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        
        iframe.src = url;
    }

    createPreviewHTML() {
        const colorSchemes = {
            blue: { primary: '#2563eb', secondary: '#64748b', accent: '#0ea5e9' },
            purple: { primary: '#7c3aed', secondary: '#64748b', accent: '#a855f7' },
            green: { primary: '#059669', secondary: '#64748b', accent: '#10b981' }
        };

        const fonts = {
            inter: 'Inter, sans-serif',
            playfair: 'Playfair Display, serif',
            roboto: 'Roboto, sans-serif'
        };

        const colors = colorSchemes[this.userData.colorScheme] || colorSchemes.blue;
        const font = fonts[this.userData.font] || fonts.inter;

        return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${this.userData.fullName || 'Twoja Strona'}</title>
                <style>
                    body {
                        font-family: ${font};
                        margin: 0;
                        padding: 0;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: #333;
                    }
                    .container {
                        max-width: 1200px;
                        margin: 0 auto;
                        padding: 2rem;
                    }
                    .hero {
                        text-align: center;
                        background: white;
                        border-radius: 20px;
                        padding: 3rem;
                        margin-bottom: 2rem;
                        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                    }
                    .avatar {
                        width: 120px;
                        height: 120px;
                        border-radius: 50%;
                        background: ${colors.primary};
                        margin: 0 auto 1rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-size: 2rem;
                        font-weight: bold;
                    }
                    .name {
                        font-size: 2.5rem;
                        font-weight: bold;
                        color: ${colors.primary};
                        margin-bottom: 0.5rem;
                    }
                    .title {
                        font-size: 1.5rem;
                        color: ${colors.secondary};
                        margin-bottom: 1rem;
                    }
                    .about {
                        background: white;
                        border-radius: 15px;
                        padding: 2rem;
                        margin-bottom: 2rem;
                        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                    }
                    .section-title {
                        font-size: 1.8rem;
                        color: ${colors.primary};
                        margin-bottom: 1rem;
                        border-bottom: 3px solid ${colors.accent};
                        padding-bottom: 0.5rem;
                    }
                    .skills {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 0.5rem;
                        margin-top: 1rem;
                    }
                    .skill {
                        background: ${colors.primary};
                        color: white;
                        padding: 0.5rem 1rem;
                        border-radius: 25px;
                        font-size: 0.9rem;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="hero">
                        <div class="avatar">
                            ${this.userData.fullName ? this.userData.fullName.split(' ').map(n => n[0]).join('') : 'AB'}
                        </div>
                        <div class="name">${this.userData.fullName || 'Twoje Imię Nazwisko'}</div>
                        <div class="title">${this.generatedContent.headline || this.userData.profession || 'Twój Zawód'}</div>
                    </div>
                    
                    <div class="about">
                        <h2 class="section-title">O mnie</h2>
                        <p>${this.generatedContent.about || 'Opis zostanie wygenerowany przez AI na podstawie Twojego profilu.'}</p>
                        
                        ${this.generatedContent.skills ? `
                            <h3>Umiejętności</h3>
                            <div class="skills">
                                ${this.generatedContent.skills.map(skill => `<span class="skill">${skill}</span>`).join('')}
                            </div>
                        ` : ''}
                    </div>
                </div>
            </body>
            </html>
        `;
    }

    simulatePreviewLoading() {
        this.showPreviewLoading();
        setTimeout(() => {
            this.hidePreviewLoading();
            this.generatePreviewContent();
        }, 2000);
    }

    // ===== AI EVENTS ===== //
    bindAIEvents() {
        // This would handle more complex AI interactions
    }

    // ===== FORM INITIALIZATION ===== //
    initializeFormData() {
        this.userData = {
            fullName: '',
            profession: '',
            industry: '',
            experience: 'mid',
            goals: [],
            bio: '',
            colorScheme: 'blue',
            font: 'inter',
            layout: 'centered',
            domainType: 'subdomain',
            domainName: '',
            integrations: {}
        };
        
        this.generatedContent = {};
    }

    // ===== UTILITY METHODS ===== //
    saveProgress() {
        try {
            const progressData = {
                currentStep: this.currentStep,
                userData: this.userData,
                selectedTemplate: this.selectedTemplate,
                generatedContent: this.generatedContent,
                timestamp: Date.now()
            };
            
            localStorage.setItem('brandme_builder_progress', JSON.stringify(progressData));
            this.showNotification('Postęp został zapisany', 'success');
        } catch (error) {
            this.showNotification('Błąd zapisywania postępu', 'error');
        }
    }

    loadProgress() {
        try {
            const saved = localStorage.getItem('brandme_builder_progress');
            if (saved) {
                const progressData = JSON.parse(saved);
                
                this.currentStep = progressData.currentStep || 1;
                this.userData = { ...this.userData, ...progressData.userData };
                this.selectedTemplate = progressData.selectedTemplate || 'professional';
                this.generatedContent = progressData.generatedContent || {};
                
                this.updateStep();
                this.updateProgress();
                this.restoreFormData();
                
                this.showNotification('Postęp został przywrócony', 'info');
            }
        } catch (error) {
            console.error('Error loading progress:', error);
        }
    }

    restoreFormData() {
        // Restore form values
        Object.keys(this.userData).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.value = this.userData[key];
            }
        });
        
        // Restore selections
        if (this.selectedTemplate) {
            document.querySelectorAll('.template-option').forEach(option => {
                if (option.getAttribute('data-template') === this.selectedTemplate) {
                    option.classList.add('selected');
                }
            });
        }
    }

    previewSite() {
        // Open preview in new window
        const previewWindow = window.open('', '_blank', 'width=1200,height=800');
        previewWindow.document.write(this.createPreviewHTML());
        previewWindow.document.close();
    }

    showNotification(message, type = 'info') {
        // Reuse notification system from main.js
        if (window.BrandMePlatform && window.brandMePlatform) {
            window.brandMePlatform.showNotification(message, type);
        } else {
            // Fallback
            alert(message);
        }
    }
}

// Initialize Website Builder
const websiteBuilder = new WebsiteBuilder();

// Load saved progress on page load
document.addEventListener('DOMContentLoaded', () => {
    websiteBuilder.loadProgress();
});