// Data Manager - Complete Data Persistence and State Management for EventAI.pl

class DataManager {
    constructor() {
        this.config = window.AppConfig || new Config();
        this.storagePrefix = this.config.getStorageKey('');
        this.maxStorageSize = this.config.get('MAX_STORAGE_SIZE');
        
        // Initialize storage systems
        this.initializeStorage();
        this.initializeOfflineSupport();
        
        // Data stores
        this.stores = {
            events: 'events',
            vendors: 'vendors',
            users: 'users',
            analytics: 'analytics',
            cache: 'cache',
            settings: 'settings'
        };

        // Sync queue for offline operations
        this.syncQueue = [];
        this.isOnline = navigator.onLine;
        this.syncInProgress = false;

        this.setupEventListeners();
    }

    async initializeStorage() {
        // Check for IndexedDB support
        if ('indexedDB' in window) {
            try {
                this.db = await this.openIndexedDB();
                this.storageType = 'indexeddb';
                this.config.log('IndexedDB initialized successfully');
            } catch (error) {
                this.config.error('IndexedDB initialization failed', error);
                this.storageType = 'localstorage';
            }
        } else {
            this.storageType = 'localstorage';
        }

        // Initialize default data
        await this.initializeDefaultData();
    }

    async openIndexedDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open('EventAI', 3);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => resolve(request.result);
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Create object stores
                Object.values(this.stores).forEach(storeName => {
                    if (!db.objectStoreNames.contains(storeName)) {
                        const store = db.createObjectStore(storeName, { 
                            keyPath: 'id', 
                            autoIncrement: true 
                        });
                        
                        // Create indexes based on store type
                        switch (storeName) {
                            case 'events':
                                store.createIndex('type', 'type', { unique: false });
                                store.createIndex('date', 'date', { unique: false });
                                store.createIndex('status', 'status', { unique: false });
                                store.createIndex('userId', 'userId', { unique: false });
                                break;
                            case 'vendors':
                                store.createIndex('type', 'type', { unique: false });
                                store.createIndex('location', 'location', { unique: false });
                                store.createIndex('rating', 'rating', { unique: false });
                                break;
                            case 'analytics':
                                store.createIndex('eventId', 'eventId', { unique: false });
                                store.createIndex('timestamp', 'timestamp', { unique: false });
                                break;
                        }
                    }
                });
            };
        });
    }

    initializeOfflineSupport() {
        if ('serviceWorker' in navigator && this.config.get('ENABLE_OFFLINE_MODE')) {
            this.setupOfflineSupport();
        }
    }

    setupEventListeners() {
        // Online/offline detection
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.config.log('Connection restored, syncing data...');
            this.syncOfflineData();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.config.log('Connection lost, switching to offline mode');
        });

        // Storage quota monitoring
        if ('storage' in navigator && 'estimate' in navigator.storage) {
            this.monitorStorageUsage();
        }
    }

    // CRUD Operations

    async create(storeName, data, options = {}) {
        try {
            const id = this.generateId();
            const timestamp = new Date().toISOString();
            
            const record = {
                id,
                ...data,
                createdAt: timestamp,
                updatedAt: timestamp,
                version: 1,
                synced: this.isOnline
            };

            // Validate data before saving
            this.validateRecord(storeName, record);

            if (this.storageType === 'indexeddb') {
                await this.indexedDBOperation('add', storeName, record);
            } else {
                await this.localStorageOperation('add', storeName, record);
            }

            // Add to sync queue if offline
            if (!this.isOnline) {
                this.addToSyncQueue('create', storeName, record);
            }

            this.config.log(`Created record in ${storeName}:`, id);
            
            // Trigger events
            this.triggerDataEvent('created', storeName, record);

            return { success: true, id, data: record };

        } catch (error) {
            this.config.error(`Failed to create record in ${storeName}:`, error);
            return { success: false, error: error.message };
        }
    }

    async read(storeName, id = null, filters = {}) {
        try {
            if (this.storageType === 'indexeddb') {
                return await this.indexedDBOperation('get', storeName, id, filters);
            } else {
                return await this.localStorageOperation('get', storeName, id, filters);
            }
        } catch (error) {
            this.config.error(`Failed to read from ${storeName}:`, error);
            return { success: false, error: error.message };
        }
    }

    async update(storeName, id, updates, options = {}) {
        try {
            const existing = await this.read(storeName, id);
            if (!existing.success || !existing.data) {
                throw new Error('Record not found');
            }

            const updatedRecord = {
                ...existing.data,
                ...updates,
                updatedAt: new Date().toISOString(),
                version: (existing.data.version || 1) + 1,
                synced: this.isOnline
            };

            this.validateRecord(storeName, updatedRecord);

            if (this.storageType === 'indexeddb') {
                await this.indexedDBOperation('put', storeName, updatedRecord);
            } else {
                await this.localStorageOperation('put', storeName, updatedRecord);
            }

            // Add to sync queue if offline
            if (!this.isOnline) {
                this.addToSyncQueue('update', storeName, updatedRecord);
            }

            this.config.log(`Updated record in ${storeName}:`, id);
            
            // Trigger events
            this.triggerDataEvent('updated', storeName, updatedRecord);

            return { success: true, data: updatedRecord };

        } catch (error) {
            this.config.error(`Failed to update record in ${storeName}:`, error);
            return { success: false, error: error.message };
        }
    }

    async delete(storeName, id, options = {}) {
        try {
            const existing = await this.read(storeName, id);
            if (!existing.success || !existing.data) {
                throw new Error('Record not found');
            }

            if (this.storageType === 'indexeddb') {
                await this.indexedDBOperation('delete', storeName, id);
            } else {
                await this.localStorageOperation('delete', storeName, id);
            }

            // Add to sync queue if offline
            if (!this.isOnline) {
                this.addToSyncQueue('delete', storeName, { id });
            }

            this.config.log(`Deleted record from ${storeName}:`, id);
            
            // Trigger events
            this.triggerDataEvent('deleted', storeName, { id });

            return { success: true, id };

        } catch (error) {
            this.config.error(`Failed to delete record from ${storeName}:`, error);
            return { success: false, error: error.message };
        }
    }

    // IndexedDB Operations
    async indexedDBOperation(operation, storeName, data = null, filters = {}) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 
                operation === 'get' ? 'readonly' : 'readwrite'
            );
            const store = transaction.objectStore(storeName);
            
            let request;

            switch (operation) {
                case 'add':
                    request = store.add(data);
                    break;
                case 'put':
                    request = store.put(data);
                    break;
                case 'delete':
                    request = store.delete(data);
                    break;
                case 'get':
                    if (data === null) {
                        // Get all records
                        request = store.getAll();
                    } else {
                        // Get specific record
                        request = store.get(data);
                    }
                    break;
                default:
                    reject(new Error(`Unknown operation: ${operation}`));
                    return;
            }

            request.onsuccess = () => {
                const result = request.result;
                
                if (operation === 'get') {
                    if (data === null) {
                        // Apply filters to results
                        const filteredResults = this.applyFilters(result, filters);
                        resolve({ 
                            success: true, 
                            data: filteredResults,
                            count: filteredResults.length 
                        });
                    } else {
                        resolve({ 
                            success: true, 
                            data: result || null 
                        });
                    }
                } else {
                    resolve({ success: true });
                }
            };

            request.onerror = () => reject(request.error);
        });
    }

    // LocalStorage Operations
    async localStorageOperation(operation, storeName, data = null, filters = {}) {
        const key = `${this.storagePrefix}${storeName}`;
        
        try {
            let records = [];
            
            // Get existing records
            const existing = localStorage.getItem(key);
            if (existing) {
                records = JSON.parse(existing);
            }

            switch (operation) {
                case 'add':
                    records.push(data);
                    localStorage.setItem(key, JSON.stringify(records));
                    return { success: true };
                    
                case 'put':
                    const updateIndex = records.findIndex(r => r.id === data.id);
                    if (updateIndex >= 0) {
                        records[updateIndex] = data;
                        localStorage.setItem(key, JSON.stringify(records));
                        return { success: true };
                    } else {
                        throw new Error('Record not found for update');
                    }
                    
                case 'delete':
                    const deleteIndex = records.findIndex(r => r.id === data);
                    if (deleteIndex >= 0) {
                        records.splice(deleteIndex, 1);
                        localStorage.setItem(key, JSON.stringify(records));
                        return { success: true };
                    } else {
                        throw new Error('Record not found for deletion');
                    }
                    
                case 'get':
                    if (data === null) {
                        // Get all records
                        const filteredResults = this.applyFilters(records, filters);
                        return { 
                            success: true, 
                            data: filteredResults,
                            count: filteredResults.length 
                        };
                    } else {
                        // Get specific record
                        const record = records.find(r => r.id === data);
                        return { 
                            success: true, 
                            data: record || null 
                        };
                    }
                    
                default:
                    throw new Error(`Unknown operation: ${operation}`);
            }
        } catch (error) {
            if (error.name === 'QuotaExceededError') {
                await this.cleanupStorage();
                throw new Error('Storage quota exceeded');
            }
            throw error;
        }
    }

    // Filter and Query Operations
    applyFilters(records, filters) {
        if (!filters || Object.keys(filters).length === 0) {
            return records;
        }

        return records.filter(record => {
            return Object.entries(filters).every(([key, value]) => {
                if (typeof value === 'object' && value !== null) {
                    // Handle range queries
                    if (value.$gte !== undefined && record[key] < value.$gte) return false;
                    if (value.$lte !== undefined && record[key] > value.$lte) return false;
                    if (value.$gt !== undefined && record[key] <= value.$gt) return false;
                    if (value.$lt !== undefined && record[key] >= value.$lt) return false;
                    if (value.$in !== undefined && !value.$in.includes(record[key])) return false;
                    if (value.$regex !== undefined) {
                        const regex = new RegExp(value.$regex, value.$options || 'i');
                        return regex.test(record[key]);
                    }
                    return true;
                } else {
                    // Exact match
                    return record[key] === value;
                }
            });
        });
    }

    async query(storeName, queryOptions = {}) {
        const { 
            filters = {}, 
            sort = {}, 
            limit = null, 
            offset = 0,
            search = null 
        } = queryOptions;

        try {
            const result = await this.read(storeName, null, filters);
            
            if (!result.success) {
                return result;
            }

            let data = result.data;

            // Apply search
            if (search) {
                data = this.applySearch(data, search);
            }

            // Apply sorting
            if (Object.keys(sort).length > 0) {
                data = this.applySorting(data, sort);
            }

            // Apply pagination
            if (limit || offset) {
                const start = offset;
                const end = limit ? start + limit : undefined;
                data = data.slice(start, end);
            }

            return {
                success: true,
                data,
                count: data.length,
                total: result.count
            };

        } catch (error) {
            this.config.error(`Query failed for ${storeName}:`, error);
            return { success: false, error: error.message };
        }
    }

    applySearch(records, searchQuery) {
        const query = searchQuery.toLowerCase();
        
        return records.filter(record => {
            // Search in all text fields
            const searchableFields = Object.values(record).filter(value => 
                typeof value === 'string'
            );
            
            return searchableFields.some(field => 
                field.toLowerCase().includes(query)
            );
        });
    }

    applySorting(records, sortOptions) {
        return records.sort((a, b) => {
            for (const [field, direction] of Object.entries(sortOptions)) {
                const aValue = a[field];
                const bValue = b[field];
                
                if (aValue < bValue) return direction === 'asc' ? -1 : 1;
                if (aValue > bValue) return direction === 'asc' ? 1 : -1;
            }
            return 0;
        });
    }

    // Event-specific operations
    async getEventsByStatus(status) {
        return this.query(this.stores.events, {
            filters: { status }
        });
    }

    async getEventsByDateRange(startDate, endDate) {
        return this.query(this.stores.events, {
            filters: {
                date: {
                    $gte: startDate,
                    $lte: endDate
                }
            }
        });
    }

    async getUpcomingEvents(limit = 10) {
        const today = new Date().toISOString().split('T')[0];
        
        return this.query(this.stores.events, {
            filters: {
                date: { $gte: today },
                status: { $in: ['planning', 'active'] }
            },
            sort: { date: 'asc' },
            limit
        });
    }

    async getEventAnalytics(eventId) {
        return this.query(this.stores.analytics, {
            filters: { eventId }
        });
    }

    // Vendor-specific operations
    async searchVendors(searchOptions) {
        const { type, location, minRating, maxPrice, search } = searchOptions;
        
        const filters = {};
        if (type) filters.type = type;
        if (location) filters.location = { $regex: location };
        if (minRating) filters.rating = { $gte: minRating };
        if (maxPrice) filters.basePrice = { $lte: maxPrice };

        return this.query(this.stores.vendors, {
            filters,
            search,
            sort: { rating: 'desc' }
        });
    }

    // Analytics and Insights
    async getEventStatistics(timeRange = '30d') {
        const endDate = new Date();
        const startDate = new Date();
        
        switch (timeRange) {
            case '7d':
                startDate.setDate(endDate.getDate() - 7);
                break;
            case '30d':
                startDate.setDate(endDate.getDate() - 30);
                break;
            case '90d':
                startDate.setDate(endDate.getDate() - 90);
                break;
            case '1y':
                startDate.setFullYear(endDate.getFullYear() - 1);
                break;
        }

        const events = await this.getEventsByDateRange(
            startDate.toISOString().split('T')[0],
            endDate.toISOString().split('T')[0]
        );

        if (!events.success) return events;

        return this.calculateStatistics(events.data);
    }

    calculateStatistics(events) {
        const stats = {
            total: events.length,
            byStatus: {},
            byType: {},
            totalBudget: 0,
            averageBudget: 0,
            totalGuests: 0,
            averageGuests: 0,
            upcomingEvents: 0
        };

        const today = new Date().toISOString().split('T')[0];

        events.forEach(event => {
            // Status counts
            stats.byStatus[event.status] = (stats.byStatus[event.status] || 0) + 1;
            
            // Type counts
            stats.byType[event.type] = (stats.byType[event.type] || 0) + 1;
            
            // Budget calculations
            if (event.budget) {
                stats.totalBudget += event.budget;
            }
            
            // Guest calculations
            if (event.guests) {
                stats.totalGuests += event.guests;
            }
            
            // Upcoming events
            if (event.date >= today) {
                stats.upcomingEvents++;
            }
        });

        stats.averageBudget = events.length > 0 ? stats.totalBudget / events.length : 0;
        stats.averageGuests = events.length > 0 ? stats.totalGuests / events.length : 0;

        return {
            success: true,
            data: stats
        };
    }

    // Offline Support
    addToSyncQueue(operation, storeName, data) {
        this.syncQueue.push({
            id: this.generateId(),
            operation,
            storeName,
            data,
            timestamp: Date.now()
        });

        // Persist sync queue
        localStorage.setItem(
            `${this.storagePrefix}syncQueue`,
            JSON.stringify(this.syncQueue)
        );
    }

    async syncOfflineData() {
        if (this.syncInProgress || !this.isOnline) return;
        
        this.syncInProgress = true;

        try {
            // Load sync queue
            const queueData = localStorage.getItem(`${this.storagePrefix}syncQueue`);
            if (queueData) {
                this.syncQueue = JSON.parse(queueData);
            }

            this.config.log(`Syncing ${this.syncQueue.length} offline operations`);

            for (const operation of this.syncQueue) {
                try {
                    await this.executeSync(operation);
                    
                    // Remove from queue on success
                    this.syncQueue = this.syncQueue.filter(op => op.id !== operation.id);
                    
                } catch (error) {
                    this.config.error('Sync operation failed:', error);
                    // Keep in queue for retry
                }
            }

            // Update persisted queue
            localStorage.setItem(
                `${this.storagePrefix}syncQueue`,
                JSON.stringify(this.syncQueue)
            );

            this.config.log('Offline sync completed');
            
        } catch (error) {
            this.config.error('Sync process failed:', error);
        } finally {
            this.syncInProgress = false;
        }
    }

    async executeSync(operation) {
        // In a real app, this would sync with a server
        // For now, we just mark the local record as synced
        
        const { storeName, data } = operation;
        
        if (data.id) {
            const record = { ...data, synced: true };
            await this.update(storeName, data.id, record);
        }
    }

    // Storage Management
    async monitorStorageUsage() {
        if ('storage' in navigator && 'estimate' in navigator.storage) {
            const estimate = await navigator.storage.estimate();
            const usedPercent = (estimate.usage / estimate.quota) * 100;
            
            this.config.log(`Storage usage: ${usedPercent.toFixed(1)}%`);
            
            if (usedPercent > 80) {
                this.config.log('Storage almost full, cleaning up...');
                await this.cleanupStorage();
            }
        }
    }

    async cleanupStorage() {
        try {
            // Clear old cache entries
            await this.clearOldCache();
            
            // Clear old analytics data
            await this.clearOldAnalytics();
            
            // Compact data
            await this.compactStorage();
            
            this.config.log('Storage cleanup completed');
            
        } catch (error) {
            this.config.error('Storage cleanup failed:', error);
        }
    }

    async clearOldCache() {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - 7); // Keep cache for 7 days
        
        const cache = await this.read(this.stores.cache);
        if (cache.success) {
            const validEntries = cache.data.filter(entry => 
                new Date(entry.createdAt) > cutoffDate
            );
            
            // Clear and rebuild cache store
            if (this.storageType === 'indexeddb') {
                const transaction = this.db.transaction([this.stores.cache], 'readwrite');
                const store = transaction.objectStore(this.stores.cache);
                await store.clear();
                
                for (const entry of validEntries) {
                    await store.add(entry);
                }
            } else {
                localStorage.setItem(
                    `${this.storagePrefix}${this.stores.cache}`,
                    JSON.stringify(validEntries)
                );
            }
        }
    }

    async clearOldAnalytics() {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - 90); // Keep analytics for 90 days
        
        const analytics = await this.query(this.stores.analytics, {
            filters: {
                timestamp: { $gte: cutoffDate.toISOString() }
            }
        });
        
        if (analytics.success && analytics.data.length < analytics.total) {
            // Rebuild analytics store with recent data only
            if (this.storageType === 'indexeddb') {
                const transaction = this.db.transaction([this.stores.analytics], 'readwrite');
                const store = transaction.objectStore(this.stores.analytics);
                await store.clear();
                
                for (const entry of analytics.data) {
                    await store.add(entry);
                }
            } else {
                localStorage.setItem(
                    `${this.storagePrefix}${this.stores.analytics}`,
                    JSON.stringify(analytics.data)
                );
            }
        }
    }

    async compactStorage() {
        // Remove deleted records and optimize storage
        for (const storeName of Object.values(this.stores)) {
            const records = await this.read(storeName);
            if (records.success) {
                const activeRecords = records.data.filter(record => !record.deleted);
                
                if (activeRecords.length < records.data.length) {
                    // Rebuild store with active records only
                    if (this.storageType === 'indexeddb') {
                        const transaction = this.db.transaction([storeName], 'readwrite');
                        const store = transaction.objectStore(storeName);
                        await store.clear();
                        
                        for (const record of activeRecords) {
                            await store.add(record);
                        }
                    } else {
                        localStorage.setItem(
                            `${this.storagePrefix}${storeName}`,
                            JSON.stringify(activeRecords)
                        );
                    }
                }
            }
        }
    }

    // Data Validation
    validateRecord(storeName, record) {
        const validators = {
            events: this.validateEvent,
            vendors: this.validateVendor,
            users: this.validateUser,
            analytics: this.validateAnalytics
        };

        const validator = validators[storeName];
        if (validator) {
            validator.call(this, record);
        }
    }

    validateEvent(event) {
        const required = ['name', 'type', 'date', 'guests', 'budget'];
        const missing = required.filter(field => !event[field]);
        
        if (missing.length > 0) {
            throw new Error(`Missing required fields: ${missing.join(', ')}`);
        }

        if (event.guests < 1 || event.guests > 10000) {
            throw new Error('Guest count must be between 1 and 10,000');
        }

        if (event.budget < 0 || event.budget > 1000000) {
            throw new Error('Budget must be between 0 and 1,000,000 PLN');
        }

        const validTypes = ['wedding', 'corporate', 'birthday', 'conference'];
        if (!validTypes.includes(event.type)) {
            throw new Error(`Invalid event type: ${event.type}`);
        }
    }

    validateVendor(vendor) {
        const required = ['name', 'type', 'location'];
        const missing = required.filter(field => !vendor[field]);
        
        if (missing.length > 0) {
            throw new Error(`Missing required fields: ${missing.join(', ')}`);
        }

        if (vendor.rating && (vendor.rating < 0 || vendor.rating > 5)) {
            throw new Error('Rating must be between 0 and 5');
        }
    }

    validateUser(user) {
        const required = ['name', 'email'];
        const missing = required.filter(field => !user[field]);
        
        if (missing.length > 0) {
            throw new Error(`Missing required fields: ${missing.join(', ')}`);
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(user.email)) {
            throw new Error('Invalid email format');
        }
    }

    validateAnalytics(analytics) {
        const required = ['eventId', 'type', 'value'];
        const missing = required.filter(field => !analytics[field]);
        
        if (missing.length > 0) {
            throw new Error(`Missing required fields: ${missing.join(', ')}`);
        }
    }

    // Event System
    triggerDataEvent(type, storeName, data) {
        const event = new CustomEvent('dataChanged', {
            detail: { type, storeName, data }
        });
        
        window.dispatchEvent(event);
        
        // Also trigger specific events
        const specificEvent = new CustomEvent(`${storeName}${type.charAt(0).toUpperCase() + type.slice(1)}`, {
            detail: data
        });
        
        window.dispatchEvent(specificEvent);
    }

    // Utility Functions
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    async initializeDefaultData() {
        // Check if default data already exists
        const events = await this.read(this.stores.events);
        
        if (!events.success || events.data.length === 0) {
            await this.createDefaultEvents();
        }

        const vendors = await this.read(this.stores.vendors);
        
        if (!vendors.success || vendors.data.length === 0) {
            await this.createDefaultVendors();
        }
    }

    async createDefaultEvents() {
        const defaultEvents = [
            {
                name: "Ślub Anny i Piotra",
                type: "wedding",
                date: "2025-06-15",
                guests: 120,
                budget: 45000,
                location: "Warszawa",
                status: "active",
                progress: 75
            },
            {
                name: "Konferencja Tech Summit 2025",
                type: "conference",
                date: "2025-07-22",
                guests: 300,
                budget: 85000,
                location: "Kraków",
                status: "planning",
                progress: 35
            },
            {
                name: "50. urodziny Marka",
                type: "birthday",
                date: "2025-05-05",
                guests: 45,
                budget: 12000,
                location: "Gdańsk",
                status: "completed",
                progress: 100
            }
        ];

        for (const event of defaultEvents) {
            await this.create(this.stores.events, event);
        }
    }

    async createDefaultVendors() {
        const defaultVendors = [
            {
                name: "Catering Premium Plus",
                type: "catering",
                location: "Warszawa",
                rating: 4.8,
                basePrice: 150,
                description: "Ekskluzywny catering na wydarzenia biznesowe i prywatne",
                specialties: ["wedding", "corporate"],
                features: ["Kuchnia polska", "Obsługa kelnerska", "Menu wegetariańskie"]
            },
            {
                name: "Hotel Royal Events",
                type: "venue",
                location: "Warszawa", 
                rating: 4.6,
                basePrice: 8000,
                description: "Elegancki hotel z salami konferencyjnymi i balowymi",
                specialties: ["wedding", "conference"],
                features: ["Sala balowa", "Parking", "Catering własny"]
            },
            {
                name: "DJ Music Pro",
                type: "entertainment",
                location: "Warszawa",
                rating: 4.9,
                basePrice: 2500,
                description: "Profesjonalny DJ z 10-letnim doświadczeniem",
                specialties: ["wedding", "birthday"],
                features: ["Profesjonalna aparatura", "Światła LED", "Mikrofony bezprzewodowe"]
            }
        ];

        for (const vendor of defaultVendors) {
            await this.create(this.stores.vendors, vendor);
        }
    }

    // Export/Import functionality
    async exportData(storeNames = null) {
        try {
            const exportData = {
                timestamp: new Date().toISOString(),
                version: this.config.get('APP_VERSION'),
                stores: {}
            };

            const storesToExport = storeNames || Object.values(this.stores);

            for (const storeName of storesToExport) {
                const data = await this.read(storeName);
                if (data.success) {
                    exportData.stores[storeName] = data.data;
                }
            }

            return {
                success: true,
                data: exportData
            };

        } catch (error) {
            this.config.error('Data export failed:', error);
            return { success: false, error: error.message };
        }
    }

    async importData(importData, options = {}) {
        const { overwrite = false, validate = true } = options;

        try {
            if (validate) {
                if (!importData.stores || typeof importData.stores !== 'object') {
                    throw new Error('Invalid import data format');
                }
            }

            const results = {};

            for (const [storeName, records] of Object.entries(importData.stores)) {
                if (!Object.values(this.stores).includes(storeName)) {
                    this.config.log(`Skipping unknown store: ${storeName}`);
                    continue;
                }

                results[storeName] = { imported: 0, errors: 0 };

                for (const record of records) {
                    try {
                        if (overwrite && record.id) {
                            const existing = await this.read(storeName, record.id);
                            if (existing.success && existing.data) {
                                await this.update(storeName, record.id, record);
                            } else {
                                await this.create(storeName, record);
                            }
                        } else {
                            // Create new record
                            const { id, ...recordData } = record;
                            await this.create(storeName, recordData);
                        }
                        
                        results[storeName].imported++;
                        
                    } catch (error) {
                        this.config.error(`Failed to import record in ${storeName}:`, error);
                        results[storeName].errors++;
                    }
                }
            }

            return {
                success: true,
                results
            };

        } catch (error) {
            this.config.error('Data import failed:', error);
            return { success: false, error: error.message };
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataManager;
} else {
    window.DataManager = DataManager;
}