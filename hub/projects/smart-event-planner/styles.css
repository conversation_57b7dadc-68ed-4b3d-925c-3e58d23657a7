/* EventAI.pl - Enterprise-Grade Styling */

/* CSS Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette - Professional & Modern */
    --primary-color: #4F46E5;
    --primary-light: #6366F1;
    --primary-dark: #3730A3;
    --secondary-color: #10B981;
    --accent-color: #F59E0B;
    
    /* Neutral Colors */
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;
    
    /* Status Colors */
    --success-color: #10B981;
    --warning-color: #F59E0B;
    --error-color: #EF4444;
    --info-color: #3B82F6;
    
    /* Glassmorphism */
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-base: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
}

/* Base Styles */
html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header & Navigation */
.header {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--shadow-md);
}

.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-xl);
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.nav-brand i {
    font-size: 1.5rem;
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.nav-menu {
    display: flex;
    gap: var(--spacing-lg);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    text-decoration: none;
    color: var(--gray-600);
    font-weight: 500;
    transition: all var(--transition-base);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
    background: rgba(79, 70, 229, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    color: var(--primary-color);
    background: rgba(79, 70, 229, 0.15);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.btn-notification {
    position: relative;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    color: var(--gray-600);
    transition: all var(--transition-base);
}

.btn-notification:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: var(--error-color);
    color: white;
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-base);
}

.user-profile:hover {
    background: var(--gray-100);
}

.profile-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
}

.profile-name {
    font-weight: 500;
    color: var(--gray-700);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: var(--spacing-xl);
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Hero Section */
.hero-section {
    display: flex;
    align-items: center;
    min-height: 80vh;
    margin-bottom: var(--spacing-3xl);
    background: var(--gradient-hero);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-3xl);
    color: white;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>') repeat;
    opacity: 0.3;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: var(--spacing-lg);
}

.gradient-text {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.hero-subtitle {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
    line-height: 1.7;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-2xl);
}

.hero-stats {
    display: flex;
    gap: var(--spacing-xl);
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-2xl);
    font-weight: 800;
    color: #FFD700;
}

.stat-label {
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.dashboard-preview {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    width: 100%;
    max-width: 400px;
    box-shadow: var(--glass-shadow);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(1deg); }
}

.preview-header {
    display: flex;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

.preview-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
}

.preview-dot:nth-child(1) { background: #FF5F56; }
.preview-dot:nth-child(2) { background: #FFBD2E; }
.preview-dot:nth-child(3) { background: #27CA3F; }

.preview-card {
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.preview-card i {
    color: #FF69B4;
    font-size: 1.2rem;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    overflow: hidden;
    margin-left: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #FFD700 0%, #FFA500 100%);
    border-radius: 3px;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-outline {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all var(--transition-base);
    font-size: var(--font-size-base);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-200);
}

.btn-secondary:hover {
    background: var(--gray-200);
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
}

.btn-close {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--gray-600);
    transition: all var(--transition-base);
}

.btn-close:hover {
    background: rgba(0, 0, 0, 0.2);
    transform: scale(1.1);
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

.section-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-800);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--glass-shadow);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.stat-card .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    margin-bottom: var(--spacing-md);
}

.stat-card .stat-title {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

.stat-card .stat-value {
    font-size: var(--font-size-2xl);
    font-weight: 800;
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.stat-change {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--error-color);
}

.stat-change.neutral {
    color: var(--gray-500);
}

.currency {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
}

/* Events Grid */
.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.event-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--glass-shadow);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.event-card.priority-high::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
}

.event-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.event-type {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-weight: 600;
    color: var(--gray-700);
}

.event-type i {
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
    background: var(--gradient-primary);
    color: white;
}

.event-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-planning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-completed {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.event-title {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
}

.event-details {
    margin-bottom: var(--spacing-md);
}

.detail-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.detail-item i {
    color: var(--primary-color);
    width: 16px;
}

.event-progress {
    margin-bottom: var(--spacing-lg);
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.progress-percent {
    font-weight: 600;
    color: var(--primary-color);
}

.event-progress .progress-bar {
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.event-progress .progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 4px;
    transition: width var(--transition-slow);
}

.event-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.rating-stars {
    color: #FFD700;
}

.rating-text {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.event-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.event-actions button {
    flex: 1;
}

/* Wizard Section */
.wizard-section {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
}

.wizard-container {
    background: white;
    border-radius: var(--radius-2xl);
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    box-shadow: var(--shadow-xl);
}

.wizard-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--gray-200);
    text-align: center;
}

.wizard-header h2 {
    font-size: var(--font-size-2xl);
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.wizard-progress {
    display: flex;
    justify-content: center;
    padding: var(--spacing-xl) var(--spacing-lg) 0;
    position: relative;
}

.wizard-progress::before {
    content: '';
    position: absolute;
    top: calc(var(--spacing-xl) + 20px);
    left: 25%;
    right: 25%;
    height: 2px;
    background: var(--gray-200);
    z-index: 1;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gray-200);
    color: var(--gray-500);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all var(--transition-base);
}

.progress-step.active .step-number {
    background: var(--primary-color);
    color: white;
}

.step-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    text-align: center;
}

.progress-step.active .step-label {
    color: var(--primary-color);
    font-weight: 600;
}

.wizard-step {
    padding: var(--spacing-xl);
    display: none;
}

.wizard-step.active {
    display: block;
}

.wizard-step h3 {
    font-size: var(--font-size-xl);
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.event-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.event-type-card {
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-base);
    background: white;
}

.event-type-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.event-type-card.selected {
    border-color: var(--primary-color);
    background: rgba(79, 70, 229, 0.05);
}

.event-type-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.event-type-card h4 {
    font-size: var(--font-size-lg);
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.event-type-card p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-sm);
}

.type-features {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.type-features span {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-field label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

.form-field input,
.form-field select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    transition: all var(--transition-base);
    background: white;
}

.form-field input:focus,
.form-field select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.budget-field {
    grid-column: 1 / -1;
}

.budget-input {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

#budgetRange {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: var(--gray-200);
    outline: none;
    -webkit-appearance: none;
}

#budgetRange::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--shadow-md);
}

.budget-display {
    text-align: center;
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
}

.budget-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.category-item {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-sm);
    background: var(--gray-50);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
}

.category-amount {
    font-weight: 600;
    color: var(--primary-color);
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-sm);
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-base);
}

.checkbox-item:hover {
    background: var(--gray-50);
}

.checkbox-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.ai-recommendations {
    display: grid;
    gap: var(--spacing-lg);
}

.recommendation-card {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    overflow: hidden;
    background: white;
}

.rec-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.rec-header i {
    color: var(--primary-color);
}

.rec-header h4 {
    color: var(--gray-800);
    font-weight: 600;
}

.rec-content {
    padding: var(--spacing-lg);
}

.venue-suggestion,
.catering-option,
.entertainment-option {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-start;
}

.venue-suggestion img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--radius-md);
}

.venue-details,
.catering-option,
.entertainment-option {
    flex: 1;
}

.venue-details h5,
.catering-option h5,
.entertainment-option h5 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
}

.venue-details p,
.catering-option p,
.entertainment-option p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-sm);
}

.venue-price,
.catering-price,
.entertainment-price {
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.venue-rating,
.entertainment-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.stars {
    color: #FFD700;
}

.catering-features {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.catering-features span {
    font-size: var(--font-size-xs);
    color: var(--success-color);
}

.ai-insights {
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--gray-50);
    border-radius: var(--radius-xl);
}

.ai-insights h4 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
}

.insight-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.insight-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.insight-item.success i {
    color: var(--success-color);
}

.insight-item.warning i {
    color: var(--warning-color);
}

.insight-item.info i {
    color: var(--info-color);
}

.wizard-actions {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-xl);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

/* Analytics Section */
.analytics-section {
    padding: var(--spacing-xl) 0;
}

.analytics-filters {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.analytics-filters select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    background: white;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.chart-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--glass-shadow);
}

.chart-card h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.chart-container {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-50);
    border-radius: var(--radius-md);
}

.vendors-ratings {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.vendor-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.vendor-name {
    flex: 1;
    font-weight: 500;
    color: var(--gray-700);
}

.rating-bar {
    flex: 2;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.rating-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 4px;
    transition: width var(--transition-base);
}

.rating-value {
    font-weight: 600;
    color: var(--primary-color);
    min-width: 50px;
    text-align: right;
}

.savings-breakdown {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.savings-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: white;
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--success-color);
}

.savings-item i {
    color: var(--success-color);
    font-size: 1.2rem;
}

.savings-details {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.savings-type {
    color: var(--gray-700);
    font-weight: 500;
}

.savings-amount {
    color: var(--success-color);
    font-weight: 700;
}

/* Vendors Section */
.vendors-section {
    padding: var(--spacing-xl) 0;
}

.vendor-filters {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    flex-wrap: wrap;
}

.search-input {
    flex: 1;
    min-width: 200px;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.vendor-filters select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    background: white;
}

.vendors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.vendor-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--glass-shadow);
    transition: all var(--transition-base);
    position: relative;
}

.vendor-card.featured::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
}

.vendor-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.vendor-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: var(--gradient-secondary);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    z-index: 1;
}

.vendor-image {
    height: 200px;
    overflow: hidden;
}

.vendor-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-base);
}

.vendor-card:hover .vendor-image img {
    transform: scale(1.05);
}

.vendor-content {
    padding: var(--spacing-lg);
}

.vendor-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.vendor-header h3 {
    color: var(--gray-800);
    font-size: var(--font-size-lg);
    font-weight: 700;
}

.vendor-rating {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-xs);
}

.vendor-rating .stars {
    color: #FFD700;
}

.vendor-rating span {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
}

.vendor-description {
    color: var(--gray-600);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.vendor-features {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

.feature-tag {
    background: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.vendor-pricing {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.price-range {
    font-weight: 700;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

/* Loading & Modals */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-sm);
}

.loading-content p {
    opacity: 0.8;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    z-index: 2500;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
}

.modal-content {
    background: white;
    border-radius: var(--radius-xl);
    max-width: 500px;
    width: 100%;
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.modal-header {
    padding: var(--spacing-xl);
    text-align: center;
    background: var(--gray-50);
}

.success-icon {
    font-size: 3rem;
    color: var(--success-color);
    margin-bottom: var(--spacing-md);
}

.modal-header h3 {
    color: var(--gray-800);
    font-size: var(--font-size-xl);
}

.modal-body {
    padding: var(--spacing-xl);
}

.modal-body p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.next-steps h4 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.next-steps ul {
    list-style: none;
    padding: 0;
}

.next-steps li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
    color: var(--gray-600);
}

.next-steps li::before {
    content: '✓';
    color: var(--success-color);
    font-weight: bold;
}

.modal-actions {
    display: flex;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl);
    border-top: 1px solid var(--gray-200);
}

.modal-actions button {
    flex: 1;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        padding: var(--spacing-lg);
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-section {
        padding: var(--spacing-2xl);
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .events-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .navbar {
        padding: var(--spacing-sm) var(--spacing-md);
        flex-wrap: wrap;
        gap: var(--spacing-md);
    }
    
    .nav-menu {
        order: 3;
        width: 100%;
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .hero-section {
        padding: var(--spacing-xl);
        min-height: 60vh;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-actions {
        flex-direction: column;
    }
    
    .hero-stats {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .wizard-container {
        margin: var(--spacing-md);
        max-height: calc(100vh - 2rem);
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .event-types-grid {
        grid-template-columns: 1fr;
    }
    
    .vendor-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .vendors-grid {
        grid-template-columns: 1fr;
    }
    
    .analytics-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: var(--spacing-md);
    }
    
    .section-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .wizard-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .modal-actions {
        flex-direction: column;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --glass-bg: rgba(0, 0, 0, 0.25);
        --glass-border: rgba(255, 255, 255, 0.08);
    }
    
    body {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
        color: var(--gray-100);
    }
    
    .stat-card,
    .event-card,
    .chart-card,
    .vendor-card {
        background: rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 255, 255, 0.1);
    }
}

/* Print styles */
@media print {
    .navbar,
    .wizard-section,
    .loading-overlay,
    .modal-overlay {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
    }
    
    .hero-section {
        background: none !important;
        color: black !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000FF;
        --secondary-color: #008000;
        --accent-color: #FF8C00;
    }
    
    .btn-primary {
        background: var(--primary-color);
        border: 2px solid var(--primary-color);
    }
    
    .btn-secondary {
        border: 2px solid var(--gray-800);
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* Focus styles for accessibility */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
a:focus,
input:focus,
select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Selection styles */
::selection {
    background: rgba(79, 70, 229, 0.2);
    color: var(--gray-800);
}

/* Notification System */
.notification-container {
    position: fixed;
    top: var(--spacing-xl);
    right: var(--spacing-xl);
    z-index: 5000;
    max-width: 400px;
    width: 100%;
    pointer-events: none;
}

.notification {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
    transform: translateX(100%);
    transition: all var(--transition-base);
    pointer-events: auto;
    position: relative;
}

.notification.notification-show {
    transform: translateX(0);
}

.notification.notification-hide {
    transform: translateX(100%);
    opacity: 0;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
}

.notification-content i {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.notification-success .notification-content i {
    color: var(--success-color);
}

.notification-error .notification-content i {
    color: var(--error-color);
}

.notification-warning .notification-content i {
    color: var(--warning-color);
}

.notification-info .notification-content i {
    color: var(--info-color);
}

.notification-message {
    flex: 1;
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--gray-400);
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    flex-shrink: 0;
}

.notification-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.notification-progress {
    height: 3px;
    background: var(--gray-200);
    position: relative;
}

.notification-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: var(--primary-color);
    width: 100%;
    transform-origin: left;
}

.notification-success .notification-progress::after {
    background: var(--success-color);
}

.notification-error .notification-progress::after {
    background: var(--error-color);
}

.notification-warning .notification-progress::after {
    background: var(--warning-color);
}

@keyframes notificationProgress {
    from { transform: scaleX(1); }
    to { transform: scaleX(0); }
}

/* Enhanced Event Details Modal */
.event-details-modal {
    max-width: 1000px;
    max-height: 90vh;
    overflow-y: auto;
}

.event-details-grid {
    display: grid;
    gap: var(--spacing-xl);
}

.detail-section {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.detail-section h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.detail-item label {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-weight: 500;
}

.detail-item span {
    font-weight: 600;
    color: var(--gray-800);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.status-planning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-badge.status-active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-badge.status-completed {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.progress-section {
    background: white;
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
}

.progress-bar-large {
    position: relative;
    height: 20px;
    background: var(--gray-200);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: var(--spacing-md);
}

.progress-bar-large .progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 10px;
    transition: width var(--transition-slow);
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: 600;
    font-size: var(--font-size-sm);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.task-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
}

.task-stat {
    text-align: center;
    background: white;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 2px solid var(--gray-100);
}

.task-number {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.task-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.ai-recommendations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

.ai-rec-card {
    background: white;
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    border: 2px solid var(--gray-100);
}

.ai-rec-card h4 {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-base);
}

.ai-rec-card h4 i {
    color: var(--primary-color);
}

.ai-rec-price {
    font-weight: 700;
    color: var(--primary-color);
    margin-top: var(--spacing-sm);
}

.timeline-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.timeline-item {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-start;
}

.timeline-marker {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-500);
    flex-shrink: 0;
    margin-top: var(--spacing-xs);
}

.timeline-item.completed .timeline-marker {
    background: var(--success-color);
    color: white;
}

.timeline-content {
    flex: 1;
    background: white;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 2px solid var(--gray-100);
}

.timeline-content h4 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
}

.timeline-content p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-sm);
}

.timeline-tasks {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.task-tag {
    background: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* Enhanced Analytics */
.analytics-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.analytics-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--glass-shadow);
}

.analytics-card h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.analytics-card h3 i {
    color: var(--primary-color);
}

.metric-value {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.metric-trend.positive {
    color: var(--success-color);
}

.metric-trend.negative {
    color: var(--error-color);
}

.metric-trend.neutral {
    color: var(--gray-500);
}

/* Enhanced Vendor Cards */
.vendor-card-enhanced {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--glass-shadow);
    transition: all var(--transition-base);
    position: relative;
    cursor: pointer;
}

.vendor-card-enhanced:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.vendor-card-enhanced.available::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--success-color);
}

.vendor-card-enhanced.limited::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--warning-color);
}

.vendor-card-enhanced.unavailable::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--error-color);
}

.vendor-performance {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--gray-200);
}

.performance-metric {
    text-align: center;
}

.performance-value {
    font-weight: 700;
    color: var(--primary-color);
    font-size: var(--font-size-sm);
}

.performance-label {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    margin-top: var(--spacing-xs);
}

/* Loading Enhancements */
.loading-overlay.enhanced {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(20px);
}

.loading-content.enhanced {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--glass-shadow);
    text-align: center;
    max-width: 400px;
}

.loading-steps {
    margin-top: var(--spacing-lg);
    text-align: left;
}

.loading-step {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.loading-step.active {
    color: var(--primary-color);
    font-weight: 600;
}

.loading-step.completed {
    color: var(--success-color);
}

.loading-step i {
    width: 16px;
    text-align: center;
}

/* Utility classes */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

.bounce-in {
    animation: bounceIn 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% { 
        opacity: 0;
        transform: scale(0.3);
    }
    50% { 
        opacity: 1;
        transform: scale(1.05);
    }
    70% { 
        transform: scale(0.9);
    }
    100% { 
        opacity: 1;
        transform: scale(1);
    }
}