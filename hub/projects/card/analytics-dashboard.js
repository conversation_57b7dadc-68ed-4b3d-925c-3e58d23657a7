// Advanced Analytics Dashboard for SmartCard.pl
class AnalyticsDashboard {
    constructor() {
        this.data = {
            views: [],
            scans: [],
            contacts: [],
            shares: [],
            locations: [],
            devices: [],
            timeData: []
        };
        this.charts = {};
        this.realTimeEnabled = true;
        this.init();
    }

    init() {
        this.loadAnalyticsData();
        this.setupRealTimeUpdates();
        this.setupEventTracking();
    }

    loadAnalyticsData() {
        // Load existing analytics data
        const stored = localStorage.getItem('smartcard-analytics');
        if (stored) {
            try {
                this.data = { ...this.data, ...JSON.parse(stored) };
            } catch (error) {
                console.error('Failed to load analytics data:', error);
            }
        }

        // Generate sample data if empty
        if (this.data.views.length === 0) {
            this.generateSampleData();
        }
    }

    generateSampleData() {
        const now = new Date();
        const days = 30;

        // Generate views data
        for (let i = days; i >= 0; i--) {
            const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
            const views = Math.floor(Math.random() * 50) + 10;
            const scans = Math.floor(views * 0.3);
            const contacts = Math.floor(scans * 0.6);

            this.data.views.push({
                date: date.toISOString(),
                count: views,
                unique: Math.floor(views * 0.8)
            });

            this.data.scans.push({
                date: date.toISOString(),
                count: scans,
                type: 'qr'
            });

            this.data.contacts.push({
                date: date.toISOString(),
                count: contacts,
                source: 'business_card'
            });
        }

        // Generate location data
        const locations = [
            { country: 'Poland', city: 'Warszawa', count: 45 },
            { country: 'Poland', city: 'Kraków', count: 32 },
            { country: 'Germany', city: 'Berlin', count: 18 },
            { country: 'UK', city: 'London', count: 15 },
            { country: 'Poland', city: 'Gdańsk', count: 12 },
            { country: 'USA', city: 'New York', count: 8 }
        ];
        this.data.locations = locations;

        // Generate device data
        const devices = [
            { type: 'mobile', browser: 'Chrome', count: 78 },
            { type: 'desktop', browser: 'Chrome', count: 45 },
            { type: 'mobile', browser: 'Safari', count: 32 },
            { type: 'desktop', browser: 'Firefox', count: 18 },
            { type: 'tablet', browser: 'Safari', count: 12 },
            { type: 'mobile', browser: 'Edge', count: 8 }
        ];
        this.data.devices = devices;

        this.saveAnalyticsData();
    }

    showAnalyticsDashboard() {
        if (document.getElementById('analyticsDashboard')) {
            document.getElementById('analyticsDashboard').remove();
        }

        const dashboard = document.createElement('div');
        dashboard.id = 'analyticsDashboard';
        dashboard.className = 'analytics-dashboard-overlay';
        dashboard.innerHTML = this.generateDashboardHTML();

        document.body.appendChild(dashboard);

        setTimeout(() => {
            dashboard.classList.add('show');
            this.initializeCharts();
        }, 10);

        this.setupDashboardEventListeners();
    }

    generateDashboardHTML() {
        const stats = this.calculateStats();
        
        return `
            <div class="analytics-dashboard">
                <div class="analytics-header">
                    <div class="header-content">
                        <div class="header-title">
                            <h2><i class="fas fa-chart-line"></i> Analytics Dashboard</h2>
                            <p>Szczegółowe statystyki Twoich wizytówek</p>
                        </div>
                        <div class="header-actions">
                            <div class="time-range-selector">
                                <select id="timeRange" class="time-range-select">
                                    <option value="7">Ostatnie 7 dni</option>
                                    <option value="30" selected>Ostatnie 30 dni</option>
                                    <option value="90">Ostatnie 90 dni</option>
                                    <option value="365">Ostatni rok</option>
                                </select>
                            </div>
                            <button class="export-analytics-btn" id="exportAnalytics">
                                <i class="fas fa-download"></i> Eksportuj
                            </button>
                            <button class="close-analytics-btn" id="closeAnalytics">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="analytics-content">
                    <!-- Key Metrics -->
                    <div class="metrics-grid">
                        <div class="metric-card views">
                            <div class="metric-icon">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">${stats.totalViews.toLocaleString()}</div>
                                <div class="metric-label">Wyświetlenia</div>
                                <div class="metric-change ${stats.viewsChange >= 0 ? 'positive' : 'negative'}">
                                    <i class="fas fa-arrow-${stats.viewsChange >= 0 ? 'up' : 'down'}"></i>
                                    ${Math.abs(stats.viewsChange)}% vs poprzedni okres
                                </div>
                            </div>
                        </div>

                        <div class="metric-card scans">
                            <div class="metric-icon">
                                <i class="fas fa-qrcode"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">${stats.totalScans.toLocaleString()}</div>
                                <div class="metric-label">Skanowania QR</div>
                                <div class="metric-change ${stats.scansChange >= 0 ? 'positive' : 'negative'}">
                                    <i class="fas fa-arrow-${stats.scansChange >= 0 ? 'up' : 'down'}"></i>
                                    ${Math.abs(stats.scansChange)}% vs poprzedni okres
                                </div>
                            </div>
                        </div>

                        <div class="metric-card contacts">
                            <div class="metric-icon">
                                <i class="fas fa-address-book"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">${stats.totalContacts.toLocaleString()}</div>
                                <div class="metric-label">Nowe kontakty</div>
                                <div class="metric-change ${stats.contactsChange >= 0 ? 'positive' : 'negative'}">
                                    <i class="fas fa-arrow-${stats.contactsChange >= 0 ? 'up' : 'down'}"></i>
                                    ${Math.abs(stats.contactsChange)}% vs poprzedni okres
                                </div>
                            </div>
                        </div>

                        <div class="metric-card conversion">
                            <div class="metric-icon">
                                <i class="fas fa-chart-pie"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">${stats.conversionRate}%</div>
                                <div class="metric-label">Konwersja</div>
                                <div class="metric-change ${stats.conversionChange >= 0 ? 'positive' : 'negative'}">
                                    <i class="fas fa-arrow-${stats.conversionChange >= 0 ? 'up' : 'down'}"></i>
                                    ${Math.abs(stats.conversionChange)}% vs poprzedni okres
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Grid -->
                    <div class="charts-grid">
                        <!-- Views Over Time -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3>Wyświetlenia w czasie</h3>
                                <div class="chart-controls">
                                    <button class="chart-type-btn active" data-chart="line" data-target="viewsChart">
                                        <i class="fas fa-chart-line"></i>
                                    </button>
                                    <button class="chart-type-btn" data-chart="bar" data-target="viewsChart">
                                        <i class="fas fa-chart-bar"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-content">
                                <canvas id="viewsChart" width="400" height="200"></canvas>
                            </div>
                        </div>

                        <!-- Geographic Distribution -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3>Rozkład geograficzny</h3>
                                <div class="chart-legend">
                                    <div class="legend-item">
                                        <div class="legend-color" style="background: #667eea;"></div>
                                        <span>Wyświetlenia</span>
                                    </div>
                                </div>
                            </div>
                            <div class="chart-content">
                                <div class="location-list">
                                    ${this.generateLocationList()}
                                </div>
                            </div>
                        </div>

                        <!-- Device & Browser Stats -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3>Urządzenia i przeglądarki</h3>
                            </div>
                            <div class="chart-content">
                                <canvas id="devicesChart" width="400" height="200"></canvas>
                            </div>
                        </div>

                        <!-- Conversion Funnel -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3>Lejek konwersji</h3>
                                <div class="funnel-info">
                                    <i class="fas fa-info-circle" title="Ścieżka od wyświetlenia do kontaktu"></i>
                                </div>
                            </div>
                            <div class="chart-content">
                                <div class="conversion-funnel">
                                    <div class="funnel-step">
                                        <div class="step-bar" style="width: 100%; background: #667eea;">
                                            <span class="step-label">Wyświetlenia</span>
                                            <span class="step-value">${stats.totalViews}</span>
                                        </div>
                                    </div>
                                    <div class="funnel-step">
                                        <div class="step-bar" style="width: ${(stats.totalScans/stats.totalViews*100)}%; background: #764ba2;">
                                            <span class="step-label">Skanowania</span>
                                            <span class="step-value">${stats.totalScans}</span>
                                        </div>
                                    </div>
                                    <div class="funnel-step">
                                        <div class="step-bar" style="width: ${(stats.totalContacts/stats.totalViews*100)}%; background: #f093fb;">
                                            <span class="step-label">Kontakty</span>
                                            <span class="step-value">${stats.totalContacts}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Real-time Activity -->
                        <div class="chart-container realtime">
                            <div class="chart-header">
                                <h3>Aktywność na żywo</h3>
                                <div class="realtime-indicator ${this.realTimeEnabled ? 'active' : ''}">
                                    <div class="pulse-dot"></div>
                                    <span>Na żywo</span>
                                </div>
                            </div>
                            <div class="chart-content">
                                <div class="realtime-feed" id="realtimeFeed">
                                    ${this.generateRealtimeEvents()}
                                </div>
                            </div>
                        </div>

                        <!-- Performance Insights -->
                        <div class="chart-container insights">
                            <div class="chart-header">
                                <h3>Insights i rekomendacje</h3>
                            </div>
                            <div class="chart-content">
                                <div class="insights-list">
                                    ${this.generateInsights(stats)}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Tables -->
                    <div class="details-section">
                        <div class="details-tabs">
                            <button class="details-tab active" data-tab="locations">Lokalizacje</button>
                            <button class="details-tab" data-tab="referrers">Źródła ruchu</button>
                            <button class="details-tab" data-tab="cards">Wizytówki</button>
                            <button class="details-tab" data-tab="events">Wydarzenia</button>
                        </div>

                        <div class="details-content">
                            <div class="details-panel active" data-panel="locations">
                                ${this.generateLocationTable()}
                            </div>
                            
                            <div class="details-panel" data-panel="referrers">
                                ${this.generateReferrersTable()}
                            </div>
                            
                            <div class="details-panel" data-panel="cards">
                                ${this.generateCardsTable()}
                            </div>
                            
                            <div class="details-panel" data-panel="events">
                                ${this.generateEventsTable()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    calculateStats() {
        const now = new Date();
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

        // Current period (last 30 days)
        const currentViews = this.data.views.filter(v => new Date(v.date) >= thirtyDaysAgo);
        const currentScans = this.data.scans.filter(s => new Date(s.date) >= thirtyDaysAgo);
        const currentContacts = this.data.contacts.filter(c => new Date(c.date) >= thirtyDaysAgo);

        // Previous period (30-60 days ago)
        const previousViews = this.data.views.filter(v => {
            const date = new Date(v.date);
            return date >= sixtyDaysAgo && date < thirtyDaysAgo;
        });
        const previousScans = this.data.scans.filter(s => {
            const date = new Date(s.date);
            return date >= sixtyDaysAgo && date < thirtyDaysAgo;
        });
        const previousContacts = this.data.contacts.filter(c => {
            const date = new Date(c.date);
            return date >= sixtyDaysAgo && date < thirtyDaysAgo;
        });

        // Calculate totals
        const totalViews = currentViews.reduce((sum, v) => sum + v.count, 0);
        const totalScans = currentScans.reduce((sum, s) => sum + s.count, 0);
        const totalContacts = currentContacts.reduce((sum, c) => sum + c.count, 0);

        const prevTotalViews = previousViews.reduce((sum, v) => sum + v.count, 0);
        const prevTotalScans = previousScans.reduce((sum, s) => sum + s.count, 0);
        const prevTotalContacts = previousContacts.reduce((sum, c) => sum + c.count, 0);

        // Calculate changes
        const viewsChange = prevTotalViews === 0 ? 0 : Math.round(((totalViews - prevTotalViews) / prevTotalViews) * 100);
        const scansChange = prevTotalScans === 0 ? 0 : Math.round(((totalScans - prevTotalScans) / prevTotalScans) * 100);
        const contactsChange = prevTotalContacts === 0 ? 0 : Math.round(((totalContacts - prevTotalContacts) / prevTotalContacts) * 100);

        // Calculate conversion rate
        const conversionRate = totalViews === 0 ? 0 : Math.round((totalContacts / totalViews) * 100);
        const prevConversionRate = prevTotalViews === 0 ? 0 : Math.round((prevTotalContacts / prevTotalViews) * 100);
        const conversionChange = Math.round(conversionRate - prevConversionRate);

        return {
            totalViews,
            totalScans,
            totalContacts,
            conversionRate,
            viewsChange,
            scansChange,
            contactsChange,
            conversionChange
        };
    }

    generateLocationList() {
        return this.data.locations.slice(0, 6).map(location => `
            <div class="location-item">
                <div class="location-info">
                    <div class="location-name">${location.city}, ${location.country}</div>
                    <div class="location-count">${location.count} wyświetleń</div>
                </div>
                <div class="location-bar">
                    <div class="location-progress" style="width: ${(location.count / this.data.locations[0].count) * 100}%"></div>
                </div>
            </div>
        `).join('');
    }

    generateRealtimeEvents() {
        const events = [
            { type: 'view', message: 'Nowe wyświetlenie z Warszawa, Poland', time: '2 min temu', icon: 'eye' },
            { type: 'scan', message: 'Kod QR zeskanowany na telefonie', time: '5 min temu', icon: 'qrcode' },
            { type: 'contact', message: 'Kontakt dodany do książki adresowej', time: '8 min temu', icon: 'user-plus' },
            { type: 'share', message: 'Wizytówka udostępniona przez LinkedIn', time: '12 min temu', icon: 'share' },
            { type: 'view', message: 'Nowe wyświetlenie z Kraków, Poland', time: '15 min temu', icon: 'eye' }
        ];

        return events.map(event => `
            <div class="realtime-event ${event.type}">
                <div class="event-icon">
                    <i class="fas fa-${event.icon}"></i>
                </div>
                <div class="event-content">
                    <div class="event-message">${event.message}</div>
                    <div class="event-time">${event.time}</div>
                </div>
            </div>
        `).join('');
    }

    generateInsights(stats) {
        const insights = [];

        // Performance insights
        if (stats.viewsChange > 20) {
            insights.push({
                type: 'positive',
                icon: 'trending-up',
                title: 'Świetny wzrost!',
                message: `Wyświetlenia wzrosły o ${stats.viewsChange}%. Twoja wizytówka zyskuje na popularności.`
            });
        }

        if (stats.conversionRate > 15) {
            insights.push({
                type: 'positive',
                icon: 'bullseye',
                title: 'Wysoka konwersja',
                message: `${stats.conversionRate}% konwersja to świetny wynik. Twoja wizytówka skutecznie zachęca do kontaktu.`
            });
        } else if (stats.conversionRate < 5) {
            insights.push({
                type: 'warning',
                icon: 'exclamation-triangle',
                title: 'Konwersja do poprawy',
                message: 'Rozważ dodanie bardziej zachęcającego call-to-action lub popraw opis wizytówki.'
            });
        }

        // Geographic insights
        const topLocation = this.data.locations[0];
        if (topLocation && topLocation.count > stats.totalViews * 0.4) {
            insights.push({
                type: 'info',
                icon: 'map-marker-alt',
                title: 'Koncentracja geograficzna',
                message: `Większość ruchu (${Math.round((topLocation.count / stats.totalViews) * 100)}%) pochodzi z ${topLocation.city}.`
            });
        }

        // Time-based insights
        const recentActivity = this.data.views.slice(-7).reduce((sum, v) => sum + v.count, 0);
        if (recentActivity > stats.totalViews * 0.3) {
            insights.push({
                type: 'positive',
                icon: 'fire',
                title: 'Zwiększona aktywność',
                message: 'Ostatnie 7 dni to okres wzmożonej aktywności. Wykorzystaj ten moment!'
            });
        }

        if (insights.length === 0) {
            insights.push({
                type: 'info',
                icon: 'chart-line',
                title: 'Stały rozwój',
                message: 'Twoja wizytówka rozwija się stabilnie. Kontynuuj dzielenie się nią z siecią kontaktów.'
            });
        }

        return insights.map(insight => `
            <div class="insight-item ${insight.type}">
                <div class="insight-icon">
                    <i class="fas fa-${insight.icon}"></i>
                </div>
                <div class="insight-content">
                    <div class="insight-title">${insight.title}</div>
                    <div class="insight-message">${insight.message}</div>
                </div>
            </div>
        `).join('');
    }

    generateLocationTable() {
        return `
            <div class="data-table">
                <table>
                    <thead>
                        <tr>
                            <th>Lokalizacja</th>
                            <th>Wyświetlenia</th>
                            <th>Skanowania</th>
                            <th>Konwersja</th>
                            <th>Udział</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.data.locations.map(location => {
                            const scans = Math.floor(location.count * 0.3);
                            const conversion = Math.round((scans / location.count) * 100);
                            const share = Math.round((location.count / this.data.locations.reduce((sum, l) => sum + l.count, 0)) * 100);
                            
                            return `
                                <tr>
                                    <td>
                                        <div class="location-cell">
                                            <strong>${location.city}</strong>
                                            <span class="country">${location.country}</span>
                                        </div>
                                    </td>
                                    <td>${location.count}</td>
                                    <td>${scans}</td>
                                    <td>${conversion}%</td>
                                    <td>
                                        <div class="progress-cell">
                                            <div class="progress-bar">
                                                <div class="progress-fill" style="width: ${share}%"></div>
                                            </div>
                                            <span>${share}%</span>
                                        </div>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    generateReferrersTable() {
        const referrers = [
            { source: 'Direct', visits: 45, percentage: 35 },
            { source: 'LinkedIn', visits: 32, percentage: 25 },
            { source: 'Email', visits: 28, percentage: 22 },
            { source: 'QR Code', visits: 15, percentage: 12 },
            { source: 'WhatsApp', visits: 8, percentage: 6 }
        ];

        return `
            <div class="data-table">
                <table>
                    <thead>
                        <tr>
                            <th>Źródło</th>
                            <th>Wizyty</th>
                            <th>Udział</th>
                            <th>Trend</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${referrers.map(ref => `
                            <tr>
                                <td>${ref.source}</td>
                                <td>${ref.visits}</td>
                                <td>
                                    <div class="progress-cell">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: ${ref.percentage}%"></div>
                                        </div>
                                        <span>${ref.percentage}%</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="trend ${Math.random() > 0.5 ? 'up' : 'down'}">
                                        <i class="fas fa-arrow-${Math.random() > 0.5 ? 'up' : 'down'}"></i>
                                        ${Math.floor(Math.random() * 20)}%
                                    </span>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    generateCardsTable() {
        // Get user's cards if available
        const cards = window.authSystem?.currentUser?.cards || [
            { id: 1, name: 'Główna wizytówka', template: 'Modern', views: 145, scans: 43 }
        ];

        return `
            <div class="data-table">
                <table>
                    <thead>
                        <tr>
                            <th>Wizytówka</th>
                            <th>Szablon</th>
                            <th>Wyświetlenia</th>
                            <th>Skanowania</th>
                            <th>Konwersja</th>
                            <th>Akcje</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${cards.map(card => {
                            const views = card.views || Math.floor(Math.random() * 200) + 50;
                            const scans = card.scans || Math.floor(views * 0.3);
                            const conversion = Math.round((scans / views) * 100);
                            
                            return `
                                <tr>
                                    <td>${card.name || 'Wizytówka'}</td>
                                    <td>${card.template || 'Modern'}</td>
                                    <td>${views}</td>
                                    <td>${scans}</td>
                                    <td>${conversion}%</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="action-btn" onclick="editCard('${card.id}')" title="Edytuj">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn" onclick="shareCard('${card.id}')" title="Udostępnij">
                                                <i class="fas fa-share"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    generateEventsTable() {
        const events = [];
        const now = new Date();
        
        for (let i = 0; i < 20; i++) {
            const date = new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000);
            const types = ['view', 'scan', 'contact', 'share'];
            const type = types[Math.floor(Math.random() * types.length)];
            const locations = ['Warszawa', 'Kraków', 'Berlin', 'London'];
            const location = locations[Math.floor(Math.random() * locations.length)];
            
            events.push({
                date: date.toISOString(),
                type: type,
                location: location,
                device: Math.random() > 0.5 ? 'Mobile' : 'Desktop'
            });
        }

        events.sort((a, b) => new Date(b.date) - new Date(a.date));

        return `
            <div class="data-table">
                <table>
                    <thead>
                        <tr>
                            <th>Data/Czas</th>
                            <th>Typ</th>
                            <th>Lokalizacja</th>
                            <th>Urządzenie</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${events.map(event => `
                            <tr>
                                <td>${new Date(event.date).toLocaleString('pl-PL')}</td>
                                <td>
                                    <span class="event-type ${event.type}">
                                        <i class="fas fa-${this.getEventIcon(event.type)}"></i>
                                        ${this.getEventLabel(event.type)}
                                    </span>
                                </td>
                                <td>${event.location}</td>
                                <td>${event.device}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    getEventIcon(type) {
        const icons = {
            view: 'eye',
            scan: 'qrcode',
            contact: 'user-plus',
            share: 'share'
        };
        return icons[type] || 'circle';
    }

    getEventLabel(type) {
        const labels = {
            view: 'Wyświetlenie',
            scan: 'Skanowanie',
            contact: 'Kontakt',
            share: 'Udostępnienie'
        };
        return labels[type] || type;
    }

    initializeCharts() {
        this.createViewsChart();
        this.createDevicesChart();
        this.startRealTimeUpdates();
    }

    createViewsChart() {
        const canvas = document.getElementById('viewsChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const data = this.data.views.slice(-30); // Last 30 days

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Set up chart dimensions
        const padding = 40;
        const chartWidth = canvas.width - 2 * padding;
        const chartHeight = canvas.height - 2 * padding;

        // Find max value for scaling
        const maxValue = Math.max(...data.map(d => d.count)) || 1;

        // Draw grid lines
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.lineWidth = 1;

        // Horizontal grid lines
        for (let i = 0; i <= 5; i++) {
            const y = padding + (chartHeight / 5) * i;
            ctx.beginPath();
            ctx.moveTo(padding, y);
            ctx.lineTo(padding + chartWidth, y);
            ctx.stroke();
        }

        // Vertical grid lines
        const pointsCount = Math.min(data.length, 10);
        for (let i = 0; i <= pointsCount; i++) {
            const x = padding + (chartWidth / pointsCount) * i;
            ctx.beginPath();
            ctx.moveTo(x, padding);
            ctx.lineTo(x, padding + chartHeight);
            ctx.stroke();
        }

        // Draw line chart
        if (data.length > 1) {
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.beginPath();

            const stepX = chartWidth / (data.length - 1);
            data.forEach((point, index) => {
                const x = padding + index * stepX;
                const y = padding + chartHeight - (point.count / maxValue) * chartHeight;

                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });

            ctx.stroke();

            // Draw points
            ctx.fillStyle = '#667eea';
            data.forEach((point, index) => {
                const x = padding + index * stepX;
                const y = padding + chartHeight - (point.count / maxValue) * chartHeight;

                ctx.beginPath();
                ctx.arc(x, y, 4, 0, Math.PI * 2);
                ctx.fill();
            });
        }

        // Draw labels
        ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
        ctx.font = '12px Inter';
        ctx.textAlign = 'center';

        // Y-axis labels
        for (let i = 0; i <= 5; i++) {
            const value = Math.round((maxValue / 5) * (5 - i));
            const y = padding + (chartHeight / 5) * i;
            ctx.textAlign = 'right';
            ctx.fillText(value.toString(), padding - 10, y + 4);
        }

        // X-axis labels (dates)
        const labelStep = Math.max(1, Math.floor(data.length / 5));
        data.forEach((point, index) => {
            if (index % labelStep === 0) {
                const x = padding + (index / (data.length - 1)) * chartWidth;
                const date = new Date(point.date);
                const label = `${date.getDate()}/${date.getMonth() + 1}`;
                ctx.textAlign = 'center';
                ctx.fillText(label, x, padding + chartHeight + 20);
            }
        });
    }

    createDevicesChart() {
        const canvas = document.getElementById('devicesChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        // Aggregate device data
        const deviceTypes = {};
        this.data.devices.forEach(device => {
            deviceTypes[device.type] = (deviceTypes[device.type] || 0) + device.count;
        });

        const total = Object.values(deviceTypes).reduce((sum, count) => sum + count, 0);
        
        if (total === 0) return;

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Draw pie chart
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = Math.min(centerX, centerY) - 40;

        const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c'];
        let currentAngle = -Math.PI / 2;

        Object.entries(deviceTypes).forEach(([type, count], index) => {
            const sliceAngle = (count / total) * 2 * Math.PI;
            const color = colors[index % colors.length];

            // Draw slice
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            ctx.closePath();
            ctx.fill();

            // Draw label
            const labelAngle = currentAngle + sliceAngle / 2;
            const labelX = centerX + Math.cos(labelAngle) * (radius + 20);
            const labelY = centerY + Math.sin(labelAngle) * (radius + 20);
            
            ctx.fillStyle = '#ffffff';
            ctx.font = '12px Inter';
            ctx.textAlign = 'center';
            ctx.fillText(`${type}: ${Math.round((count / total) * 100)}%`, labelX, labelY);

            currentAngle += sliceAngle;
        });
    }

    setupDashboardEventListeners() {
        // Close button
        document.getElementById('closeAnalytics')?.addEventListener('click', () => {
            this.hideAnalyticsDashboard();
        });

        // Time range selector
        document.getElementById('timeRange')?.addEventListener('change', (e) => {
            this.updateTimeRange(parseInt(e.target.value));
        });

        // Export button
        document.getElementById('exportAnalytics')?.addEventListener('click', () => {
            this.exportAnalyticsData();
        });

        // Chart type buttons
        document.querySelectorAll('.chart-type-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const chartType = btn.dataset.chart;
                const targetChart = btn.dataset.target;
                this.switchChartType(targetChart, chartType);
            });
        });

        // Details tabs
        document.querySelectorAll('.details-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.dataset.tab;
                this.switchDetailsTab(tabName);
            });
        });
    }

    hideAnalyticsDashboard() {
        const dashboard = document.getElementById('analyticsDashboard');
        if (dashboard) {
            dashboard.classList.remove('show');
            setTimeout(() => {
                dashboard.remove();
            }, 300);
        }
    }

    updateTimeRange(days) {
        // Filter data based on selected time range
        const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
        
        // Re-render charts with filtered data
        this.createViewsChart();
        this.createDevicesChart();
    }

    switchChartType(chartId, type) {
        // Update button states
        const container = document.querySelector(`#${chartId}`).closest('.chart-container');
        container.querySelectorAll('.chart-type-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.chart === type);
        });

        // Redraw chart with new type
        if (chartId === 'viewsChart') {
            this.createViewsChart(type);
        }
    }

    switchDetailsTab(tabName) {
        // Update tab states
        document.querySelectorAll('.details-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });

        // Update panel visibility
        document.querySelectorAll('.details-panel').forEach(panel => {
            panel.classList.toggle('active', panel.dataset.panel === tabName);
        });
    }

    exportAnalyticsData() {
        const exportData = {
            summary: this.calculateStats(),
            views: this.data.views,
            scans: this.data.scans,
            contacts: this.data.contacts,
            locations: this.data.locations,
            devices: this.data.devices,
            exportDate: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `smartcard-analytics-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);

        this.showNotification('Analytics data exported successfully!', 'success');
    }

    setupRealTimeUpdates() {
        if (this.realTimeEnabled) {
            setInterval(() => {
                this.simulateRealTimeEvent();
            }, 15000); // Every 15 seconds
        }
    }

    startRealTimeUpdates() {
        const feed = document.getElementById('realtimeFeed');
        if (!feed) return;

        setInterval(() => {
            if (Math.random() > 0.7) { // 30% chance of new event
                const newEvent = this.generateRandomEvent();
                const eventElement = document.createElement('div');
                eventElement.className = `realtime-event ${newEvent.type} new`;
                eventElement.innerHTML = `
                    <div class="event-icon">
                        <i class="fas fa-${newEvent.icon}"></i>
                    </div>
                    <div class="event-content">
                        <div class="event-message">${newEvent.message}</div>
                        <div class="event-time">Teraz</div>
                    </div>
                `;

                feed.insertBefore(eventElement, feed.firstChild);
                
                // Remove old events (keep only last 10)
                while (feed.children.length > 10) {
                    feed.removeChild(feed.lastChild);
                }

                // Update timestamps
                this.updateEventTimestamps();
            }
        }, 10000); // Every 10 seconds
    }

    generateRandomEvent() {
        const events = [
            { type: 'view', message: 'Nowe wyświetlenie z Warszawa, Poland', icon: 'eye' },
            { type: 'scan', message: 'Kod QR zeskanowany na urządzeniu mobilnym', icon: 'qrcode' },
            { type: 'contact', message: 'Kontakt zapisany w książce adresowej', icon: 'user-plus' },
            { type: 'share', message: 'Wizytówka udostępniona przez social media', icon: 'share' }
        ];

        return events[Math.floor(Math.random() * events.length)];
    }

    updateEventTimestamps() {
        const events = document.querySelectorAll('.realtime-event');
        events.forEach((event, index) => {
            const timeElement = event.querySelector('.event-time');
            if (timeElement && index > 0) {
                const minutes = index * 2;
                timeElement.textContent = `${minutes} min temu`;
            }
        });
    }

    simulateRealTimeEvent() {
        // Add random data point
        const now = new Date();
        const randomViews = Math.floor(Math.random() * 5) + 1;
        
        this.data.views.push({
            date: now.toISOString(),
            count: randomViews,
            unique: Math.floor(randomViews * 0.8)
        });

        // Update storage
        this.saveAnalyticsData();
    }

    setupEventTracking() {
        // Track page views
        this.trackEvent('view', {
            page: window.location.pathname,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
        });

        // Track user interactions
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-track]')) {
                this.trackEvent(e.target.dataset.track, {
                    element: e.target.tagName,
                    timestamp: new Date().toISOString()
                });
            }
        });
    }

    trackEvent(type, data) {
        if (!this.data[type]) {
            this.data[type] = [];
        }

        this.data[type].push({
            ...data,
            id: Date.now() + Math.random(),
            timestamp: new Date().toISOString()
        });

        this.saveAnalyticsData();
    }

    saveAnalyticsData() {
        try {
            localStorage.setItem('smartcard-analytics', JSON.stringify(this.data));
        } catch (error) {
            console.error('Failed to save analytics data:', error);
        }
    }

    showNotification(message, type) {
        if (window.SmartCardApp) {
            const app = new window.SmartCardApp();
            app.showNotification(message, type);
        }
    }
}

// Global function to show analytics
function viewAnalytics() {
    if (!window.analyticsDashboard) {
        window.analyticsDashboard = new AnalyticsDashboard();
    }
    window.analyticsDashboard.showAnalyticsDashboard();
}

// Initialize analytics dashboard
document.addEventListener('DOMContentLoaded', () => {
    window.analyticsDashboard = new AnalyticsDashboard();
});

// Export for global access
window.AnalyticsDashboard = AnalyticsDashboard;