:root {
    --primary-green: #10b981;
    --dark-green: #059669;
    --light-green: #34d399;
    --accent-energy: #f59e0b;
    --accent-orange: #ea580c;
    --black: #000000;
    --dark-gray: #111827;
    --medium-gray: #6b7280;
    --light-gray: #f9fafb;
    --white: #ffffff;
    --gradient-primary: linear-gradient(135deg, #10b981 0%, #059669 50%, #f59e0b 100%);
    --gradient-energy: linear-gradient(135deg, #f59e0b 0%, #ea580c 100%);
    --gradient-dark: linear-gradient(135deg, #000000 0%, #111827 100%);
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--dark-gray);
    background: var(--white);
}

/* Header */
.header {
    background: var(--gradient-dark);
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.nav-brand h1 {
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 1.75rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-menu a {
    color: var(--white);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 0;
    background: var(--primary-green);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.cart-btn {
    position: relative;
    background: var(--gradient-primary);
    border: none;
    padding: 0.75rem;
    border-radius: 12px;
    color: white;
    cursor: pointer;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.cart-btn:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.cart-count {
    position: absolute;
    top: -0.5rem;
    right: -0.5rem;
    background: var(--accent-energy);
    color: white;
    border-radius: 50%;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Hero Section */
.hero {
    background: var(--gradient-dark);
    color: var(--white);
    padding: 4rem 2rem;
    display: flex;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    gap: 4rem;
    min-height: 600px;
}

.hero-content {
    flex: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 1.1;
    text-transform: uppercase;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--medium-gray);
    margin-bottom: 2rem;
}

.hero-stats {
    display: flex;
    gap: 3rem;
    margin-top: 3rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    background: var(--gradient-energy);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stat-label {
    color: var(--medium-gray);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-transformation {
    flex: 1;
}

.transformation-images {
    position: relative;
}

.before-after {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.before-img, .after-img {
    width: 180px;
    height: 240px;
    object-fit: cover;
    border-radius: 16px;
    border: 3px solid var(--primary-green);
}

.transformation-label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
    font-weight: 600;
    color: var(--accent-energy);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.transformation-label i {
    color: var(--primary-green);
}

.cta-btn {
    padding: 1.25rem 2.5rem;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-btn.primary {
    background: var(--gradient-primary);
    color: white;
}

.cta-btn.secondary {
    background: var(--gradient-energy);
    color: white;
}

.cta-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* Trust Badges */
.trust-badges {
    background: var(--light-gray);
    padding: 2rem;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--dark-gray);
    font-weight: 600;
    font-size: 0.9rem;
}

.badge i {
    color: var(--primary-green);
    font-size: 1.2rem;
}

/* Products */
.products {
    max-width: 1200px;
    margin: 4rem auto;
    padding: 0 2rem;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 3rem;
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.category-card {
    background: var(--white);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    cursor: pointer;
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.category-card img {
    width: 100%;
    height: 180px;
    object-fit: cover;
}

.category-content {
    padding: 1.5rem;
}

.category-content h3 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--dark-gray);
}

.category-content p {
    color: var(--medium-gray);
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.price-range {
    background: var(--gradient-energy);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
    font-size: 1.1rem;
}

.products-showcase {
    margin-top: 4rem;
}

.products-showcase h3 {
    font-size: 2rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 2rem;
    color: var(--dark-gray);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 2rem;
}

.product-card {
    background: var(--white);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.product-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--gradient-energy);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-badge.new {
    background: var(--gradient-primary);
}

.product-card img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-radius: 12px;
    margin-bottom: 1rem;
}

.product-card h4 {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--dark-gray);
}

.product-rating {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.stars {
    color: var(--accent-energy);
}

.product-rating span {
    color: var(--medium-gray);
    font-size: 0.85rem;
}

.price {
    font-size: 1.5rem;
    font-weight: 800;
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1rem;
}

.add-to-cart-btn {
    width: 100%;
    padding: 0.75rem;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.add-to-cart-btn:hover {
    transform: scale(1.02);
}

/* Scientific Backing */
.scientific-backing {
    background: var(--light-gray);
    padding: 4rem 2rem;
    margin: 4rem 0;
    display: flex;
    align-items: center;
    gap: 4rem;
}

.scientific-content {
    flex: 1;
    max-width: 600px;
}

.scientific-backing h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.scientific-backing p {
    font-size: 1.1rem;
    color: var(--medium-gray);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.certificates {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.certificate {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--white);
    border-radius: 12px;
    box-shadow: var(--shadow-sm);
}

.certificate i {
    font-size: 2rem;
    color: var(--primary-green);
}

.certificate h4 {
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.certificate p {
    color: var(--medium-gray);
    font-size: 0.9rem;
    margin: 0;
}

.lab-image {
    flex: 1;
}

.lab-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
}

/* Calculator */
.calculator {
    max-width: 1000px;
    margin: 4rem auto;
    padding: 0 2rem;
}

.calculator-container {
    background: var(--white);
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.calculator-tabs {
    display: flex;
    background: var(--gradient-dark);
}

.tab-btn {
    flex: 1;
    padding: 1rem 2rem;
    background: none;
    border: none;
    color: var(--medium-gray);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: var(--gradient-primary);
    color: white;
}

.calculator-content {
    padding: 2rem;
}

.calc-tab {
    display: none;
}

.calc-tab.active {
    display: block;
}

.calc-tab h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--dark-gray);
}

.calc-inputs {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.input-row {
    display: flex;
    gap: 1rem;
}

.input-group {
    flex: 1;
}

.input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-gray);
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: var(--primary-green);
}

.calc-btn {
    background: var(--gradient-primary);
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.calc-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.calc-result {
    background: var(--light-gray);
    padding: 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    color: var(--dark-gray);
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Transformations */
.transformations {
    background: var(--gradient-dark);
    color: var(--white);
    padding: 4rem 2rem;
    margin: 4rem 0;
}

.transformations .section-title {
    color: var(--white);
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.transformations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.transformation-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.transformation-images-card {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.transformation-images-card img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 50%;
    border: 3px solid var(--primary-green);
}

.transformation-story h4 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--accent-energy);
}

.transformation-story p {
    margin-bottom: 1rem;
    font-style: italic;
    line-height: 1.6;
}

.transformation-stats {
    display: flex;
    gap: 1rem;
}

.transformation-stats span {
    background: var(--gradient-primary);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.85rem;
}

/* Subscription */
.subscription {
    max-width: 1200px;
    margin: 4rem auto;
    padding: 0 2rem;
}

.subscription-content h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.subscription-content p {
    text-align: center;
    font-size: 1.1rem;
    color: var(--medium-gray);
    margin-bottom: 3rem;
}

.subscription-benefits {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 3rem;
}

.benefit {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--light-gray);
    border-radius: 12px;
}

.benefit i {
    color: var(--primary-green);
    font-size: 1.25rem;
}

.subscription-plans {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.plan {
    background: var(--white);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
}

.plan:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.plan.featured {
    background: var(--gradient-dark);
    color: var(--white);
    transform: scale(1.05);
}

.plan-badge {
    position: absolute;
    top: -1rem;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-energy);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
}

.plan h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.plan-price {
    font-size: 2rem;
    font-weight: 800;
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1.5rem;
}

.plan ul {
    list-style: none;
    margin-bottom: 2rem;
}

.plan li {
    padding: 0.5rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.plan li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-green);
    font-weight: bold;
}

.plan-btn {
    width: 100%;
    padding: 1rem;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.plan-btn:hover {
    transform: scale(1.02);
}

/* Cart Modal */
.cart-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1000;
}

.cart-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-content {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
}

.cart-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--gradient-dark);
    color: white;
    border-radius: 16px 16px 0 0;
}

.close-cart {
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    color: white;
}

.cart-items {
    padding: 1.5rem;
    min-height: 200px;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.cart-item:last-child {
    border-bottom: none;
}

.item-info h4 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.item-price {
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
}

.remove-item {
    background: var(--gradient-energy);
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    font-weight: 600;
}

.cart-footer {
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
    background: var(--light-gray);
}

.cart-total {
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 700;
    text-align: center;
}

.checkout-btn {
    width: 100%;
}

.empty-cart {
    text-align: center;
    color: var(--medium-gray);
    font-style: italic;
    padding: 2rem 0;
}

/* Footer */
.footer {
    background: var(--gradient-dark);
    color: var(--white);
    padding: 3rem 2rem 1rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: var(--medium-gray);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--primary-green);
}

.footer-bottom {
    border-top: 1px solid var(--medium-gray);
    padding-top: 2rem;
    text-align: center;
    color: var(--medium-gray);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }
    
    .nav-menu {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .hero {
        flex-direction: column;
        text-align: center;
        padding: 2rem 1rem;
        gap: 2rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-stats {
        justify-content: center;
        gap: 2rem;
    }
    
    .before-after {
        flex-direction: column;
        gap: 1rem;
    }
    
    .trust-badges {
        gap: 1rem;
    }
    
    .categories-grid,
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .scientific-backing {
        flex-direction: column;
        text-align: center;
    }
    
    .input-row {
        flex-direction: column;
    }
    
    .calculator-tabs {
        flex-direction: column;
    }
    
    .transformations-grid {
        grid-template-columns: 1fr;
    }
    
    .subscription-benefits {
        grid-template-columns: 1fr;
    }
    
    .subscription-plans {
        grid-template-columns: 1fr;
    }
    
    .plan.featured {
        transform: none;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .transformation-label {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .cart-content {
        width: 95%;
        margin: 1rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.hero-content,
.category-card,
.product-card,
.transformation-card {
    animation: fadeInUp 0.6s ease forwards;
}

.product-badge {
    animation: pulse 2s infinite;
}

.cta-btn:hover {
    animation: pulse 0.3s ease;
}