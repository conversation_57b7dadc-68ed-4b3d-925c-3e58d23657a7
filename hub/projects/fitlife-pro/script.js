class FitLifePro {
    constructor() {
        this.cart = JSON.parse(localStorage.getItem('fitlife-cart')) || [];
        this.products = {
            'whey-protein': { 
                name: 'Whey Protein Pro', 
                price: 129, 
                image: 'https://images.unsplash.com/photo-1593095948071-474c5cc2989d?w=300&h=300&fit=crop',
                category: 'protein'
            },
            'pre-blast': { 
                name: 'Pre-Blast Energy', 
                price: 109, 
                image: 'https://images.unsplash.com/photo-1544991875-5dc1b05f607d?w=300&h=300&fit=crop',
                category: 'preworkout'
            },
            'fat-burn': { 
                name: 'Fat Burn Extreme', 
                price: 149, 
                image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=300&fit=crop',
                category: 'fatburners'
            },
            'multivitamin': { 
                name: 'MultiVit Pro', 
                price: 79, 
                image: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=300&h=300&fit=crop',
                category: 'vitamins'
            }
        };
        
        this.init();
    }

    init() {
        this.updateCartCount();
        this.bindEvents();
        this.initCalculatorTabs();
        this.initSmoothScrolling();
    }

    bindEvents() {
        // Cart functionality
        document.getElementById('cartBtn').addEventListener('click', () => {
            this.toggleCart();
        });

        document.getElementById('closeCart').addEventListener('click', () => {
            this.closeCart();
        });

        document.getElementById('cartModal').addEventListener('click', (e) => {
            if (e.target.id === 'cartModal') {
                this.closeCart();
            }
        });

        // Add to cart buttons
        document.querySelectorAll('.add-to-cart-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const productId = e.target.dataset.product;
                const price = parseInt(e.target.dataset.price);
                this.addToCart(productId, price);
                this.showAddedToCartFeedback(e.target);
            });
        });

        // Category cards
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', () => {
                const category = card.dataset.category;
                this.filterByCategory(category);
            });
        });

        // Plan buttons
        document.querySelectorAll('.plan-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleSubscription(e.target);
            });
        });

        // CTA buttons
        document.querySelectorAll('.cta-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                if (e.target.classList.contains('checkout-btn')) {
                    this.handleCheckout();
                } else {
                    this.scrollToProducts();
                }
            });
        });
    }

    // Cart functionality
    addToCart(productId, price) {
        const product = this.products[productId];
        if (!product) return;

        const existingItem = this.cart.find(item => item.id === productId);
        
        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            this.cart.push({
                id: productId,
                name: product.name,
                price: price,
                quantity: 1,
                image: product.image
            });
        }

        this.saveCart();
        this.updateCartCount();
        this.updateCartDisplay();
    }

    removeFromCart(productId) {
        this.cart = this.cart.filter(item => item.id !== productId);
        this.saveCart();
        this.updateCartCount();
        this.updateCartDisplay();
    }

    updateCartCount() {
        const totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);
        document.getElementById('cartCount').textContent = totalItems;
    }

    updateCartDisplay() {
        const cartItems = document.getElementById('cartItems');
        const cartTotal = document.getElementById('cartTotal');

        if (this.cart.length === 0) {
            cartItems.innerHTML = '<p class="empty-cart">Koszyk jest pusty</p>';
            cartTotal.textContent = '0 zł';
            return;
        }

        let total = 0;
        cartItems.innerHTML = this.cart.map(item => {
            total += item.price * item.quantity;
            return `
                <div class="cart-item">
                    <div class="item-info">
                        <h4>${item.name}</h4>
                        <p class="item-price">${item.price} zł x ${item.quantity}</p>
                    </div>
                    <button class="remove-item" onclick="fitlife.removeFromCart('${item.id}')">
                        Usuń
                    </button>
                </div>
            `;
        }).join('');

        cartTotal.textContent = `${total.toLocaleString('pl-PL')} zł`;
    }

    toggleCart() {
        const modal = document.getElementById('cartModal');
        modal.classList.toggle('active');
        if (modal.classList.contains('active')) {
            this.updateCartDisplay();
        }
    }

    closeCart() {
        document.getElementById('cartModal').classList.remove('active');
    }

    saveCart() {
        localStorage.setItem('fitlife-cart', JSON.stringify(this.cart));
    }

    showAddedToCartFeedback(button) {
        const originalText = button.textContent;
        button.textContent = 'Dodano do koszyka!';
        button.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
        
        setTimeout(() => {
            button.textContent = originalText;
            button.style.background = '';
        }, 1500);
    }

    // Calculator functionality
    initCalculatorTabs() {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const calcTabs = document.querySelectorAll('.calc-tab');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetTab = btn.dataset.tab;
                
                // Remove active class from all tabs and buttons
                tabBtns.forEach(b => b.classList.remove('active'));
                calcTabs.forEach(t => t.classList.remove('active'));
                
                // Add active class to clicked button and corresponding tab
                btn.classList.add('active');
                document.getElementById(`${targetTab}-calc`).classList.add('active');
            });
        });
    }

    // Filter products by category
    filterByCategory(category) {
        this.showNotification(`Filtrowanie produktów: ${this.getCategoryName(category)}`, 'info');
        document.querySelector('.products-showcase').scrollIntoView({ behavior: 'smooth' });
        
        const productCards = document.querySelectorAll('.product-card');
        productCards.forEach(card => {
            const productId = card.dataset.product;
            const product = this.products[productId];
            
            if (product && product.category === category) {
                card.style.display = 'block';
                card.style.order = '0';
                card.style.animation = 'fadeInUp 0.6s ease forwards';
            } else {
                card.style.display = 'none';
            }
        });

        // Reset filter after 5 seconds
        setTimeout(() => {
            productCards.forEach(card => {
                card.style.display = 'block';
            });
        }, 5000);
    }

    getCategoryName(category) {
        const names = {
            'protein': 'Białko',
            'preworkout': 'Pre-workout',
            'vitamins': 'Witaminy',
            'fatburners': 'Spalacze tłuszczu'
        };
        return names[category] || category;
    }

    // Subscription handling
    handleSubscription(button) {
        const plan = button.closest('.plan');
        const planName = plan.querySelector('h4').textContent;
        const planPrice = plan.querySelector('.plan-price').textContent;
        
        button.textContent = 'Przetwarzanie...';
        button.disabled = true;
        
        setTimeout(() => {
            this.showNotification(`Dziękujemy za wybór planu ${planName}! Skontaktujemy się z Tobą w ciągu 24h.`, 'success');
            button.textContent = 'Plan wybrany!';
            button.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
            
            setTimeout(() => {
                button.textContent = 'Wybierz plan';
                button.style.background = '';
                button.disabled = false;
            }, 3000);
        }, 1500);
    }

    // Checkout functionality
    handleCheckout() {
        if (this.cart.length === 0) {
            this.showNotification('Koszyk jest pusty', 'error');
            return;
        }

        const total = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        
        this.showNotification('Przekierowywanie do płatności...', 'info');
        
        setTimeout(() => {
            this.showNotification(`Dziękujemy za zamówienie na kwotę ${total.toLocaleString('pl-PL')} zł! Rozpocznij swoją transformację już dziś!`, 'success');
            
            setTimeout(() => {
                this.cart = [];
                this.saveCart();
                this.updateCartCount();
                this.updateCartDisplay();
                this.closeCart();
            }, 2000);
        }, 1500);
    }

    // Smooth scrolling
    initSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    scrollToProducts() {
        document.querySelector('.products').scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
        });
    }

    // Notification system
    showNotification(message, type = 'info') {
        const existing = document.querySelector('.notification');
        if (existing) {
            existing.remove();
        }

        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span>${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        const colors = {
            error: '#ef4444',
            success: '#10b981',
            info: '#059669'
        };

        notification.style.cssText = `
            position: fixed;
            top: 2rem;
            right: 2rem;
            z-index: 1001;
            background: ${colors[type]};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            max-width: 400px;
            animation: slideInRight 0.3s ease;
            font-weight: 600;
        `;

        document.body.appendChild(notification);

        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Calculator functions (global scope for onclick handlers)
function calculateBMI() {
    const height = parseFloat(document.getElementById('height').value);
    const weight = parseFloat(document.getElementById('weight').value);
    const resultDiv = document.getElementById('bmi-result');

    if (!height || !weight || height <= 0 || weight <= 0) {
        resultDiv.innerHTML = '<p style="color: #ef4444;">Wprowadź prawidłowe wartości wzrostu i wagi.</p>';
        return;
    }

    const heightInMeters = height / 100;
    const bmi = weight / (heightInMeters * heightInMeters);
    
    let category = '';
    let color = '';
    
    if (bmi < 18.5) {
        category = 'Niedowaga';
        color = '#3b82f6';
    } else if (bmi < 25) {
        category = 'Waga prawidłowa';
        color = '#10b981';
    } else if (bmi < 30) {
        category = 'Nadwaga';
        color = '#f59e0b';
    } else {
        category = 'Otyłość';
        color = '#ef4444';
    }

    resultDiv.innerHTML = `
        <div style="text-align: center;">
            <h4 style="color: ${color}; font-size: 2rem; margin-bottom: 0.5rem;">${bmi.toFixed(1)}</h4>
            <p style="font-size: 1.1rem; color: ${color}; font-weight: 600;">${category}</p>
            <p style="margin-top: 1rem; color: #6b7280;">Rekomendujemy konsultację z naszymi ekspertami fitness!</p>
        </div>
    `;
}

function calculateProtein() {
    const weight = parseFloat(document.getElementById('protein-weight').value);
    const activity = parseFloat(document.getElementById('activity-level').value);
    const resultDiv = document.getElementById('protein-result');

    if (!weight || weight <= 0) {
        resultDiv.innerHTML = '<p style="color: #ef4444;">Wprowadź prawidłową wagę.</p>';
        return;
    }

    const proteinNeeds = weight * activity;
    
    resultDiv.innerHTML = `
        <div style="text-align: center;">
            <h4 style="color: #10b981; font-size: 2rem; margin-bottom: 0.5rem;">${proteinNeeds.toFixed(0)}g</h4>
            <p style="font-size: 1.1rem; color: #059669; font-weight: 600;">dziennie</p>
            <p style="margin-top: 1rem; color: #6b7280;">
                Nasze białko serwatkowe dostarcza 25g białka na porcję!<br>
                <strong>Zalecana liczba porcji: ${Math.ceil(proteinNeeds / 25)}</strong>
            </p>
        </div>
    `;
}

function calculateCalories() {
    const weight = parseFloat(document.getElementById('cal-weight').value);
    const height = parseFloat(document.getElementById('cal-height').value);
    const age = parseInt(document.getElementById('age').value);
    const gender = document.getElementById('gender').value;
    const goal = document.getElementById('goal').value;
    const resultDiv = document.getElementById('calories-result');

    if (!weight || !height || !age || weight <= 0 || height <= 0 || age <= 0) {
        resultDiv.innerHTML = '<p style="color: #ef4444;">Wprowadź wszystkie prawidłowe wartości.</p>';
        return;
    }

    // Harris-Benedict formula
    let bmr;
    if (gender === 'male') {
        bmr = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age);
    } else {
        bmr = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age);
    }

    // Assume moderate activity level
    const dailyCalories = bmr * 1.55;
    
    let targetCalories = dailyCalories;
    let goalText = '';
    
    switch(goal) {
        case 'lose':
            targetCalories = dailyCalories - 500;
            goalText = 'redukcji (deficyt 500 kcal)';
            break;
        case 'gain':
            targetCalories = dailyCalories + 300;
            goalText = 'budowy masy (nadwyżka 300 kcal)';
            break;
        default:
            goalText = 'utrzymania wagi';
    }

    resultDiv.innerHTML = `
        <div style="text-align: center;">
            <h4 style="color: #10b981; font-size: 2rem; margin-bottom: 0.5rem;">${Math.round(targetCalories)} kcal</h4>
            <p style="font-size: 1.1rem; color: #059669; font-weight: 600;">dziennie do ${goalText}</p>
            <div style="margin-top: 1rem; padding: 1rem; background: #f0fdf4; border-radius: 8px; color: #166534;">
                <p><strong>BMR:</strong> ${Math.round(bmr)} kcal</p>
                <p><strong>Aktywność:</strong> ${Math.round(dailyCalories)} kcal</p>
                <p style="margin-top: 0.5rem; font-size: 0.9rem;">
                    Skonsultuj się z naszymi ekspertami dla spersonalizowanego planu!
                </p>
            </div>
        </div>
    `;
}

// Notification styles
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .notification-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
    }

    .notification-close {
        background: none;
        border: none;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0;
        line-height: 1;
        opacity: 0.8;
    }

    .notification-close:hover {
        opacity: 1;
    }
`;
document.head.appendChild(notificationStyles);

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    window.fitlife = new FitLifePro();
    
    // Add intersection observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animations
    const animateElements = document.querySelectorAll('.category-card, .product-card, .transformation-card, .plan');
    animateElements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = `all 0.6s ease ${index * 0.1}s`;
        observer.observe(el);
    });

    // Add scroll effect to hero stats
    const heroStats = document.querySelector('.hero-stats');
    if (heroStats) {
        const statNumbers = heroStats.querySelectorAll('.stat-number');
        
        const countUp = (element, target, suffix = '') => {
            const duration = 2000;
            const start = 0;
            const increment = target / (duration / 16);
            let current = start;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    element.textContent = target + suffix;
                    clearInterval(timer);
                } else {
                    element.textContent = Math.floor(current) + suffix;
                }
            }, 16);
        };

        // Trigger count up when hero comes into view
        const heroObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    countUp(statNumbers[0], 50, 'k+');
                    countUp(statNumbers[1], 95, '%');
                    countUp(statNumbers[2], 100, '%');
                    heroObserver.unobserve(entry.target);
                }
            });
        });
        
        heroObserver.observe(heroStats);
    }
});