/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    line-height: 1.6;
    color: #1a365d;
    background-color: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header and Navigation */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
    font-size: 1.5rem;
    color: #0ea5e9;
}

.nav-logo i {
    font-size: 1.8rem;
    color: #22c55e;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-menu a {
    text-decoration: none;
    color: #475569;
    font-weight: 500;
    transition: color 0.3s ease;
    padding: 0.5rem 0;
}

.nav-menu a:hover {
    color: #0ea5e9;
}

.cta-button {
    background: linear-gradient(135deg, #0ea5e9, #22c55e);
    color: white !important;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(14, 165, 233, 0.3);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: #475569;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f0f9ff 0%, #ecfdf5 100%);
    overflow: hidden;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: #1e293b;
}

.hero-highlight {
    background: linear-gradient(135deg, #0ea5e9, #22c55e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    color: #64748b;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.hero-features {
    display: flex;
    gap: 2rem;
    margin-bottom: 2.5rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #475569;
    font-weight: 500;
}

.feature i {
    color: #22c55e;
    font-size: 1.1rem;
}

.hero-cta {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.btn-primary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #0ea5e9, #22c55e);
    color: white;
    text-decoration: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    width: fit-content;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(14, 165, 233, 0.4);
}

.consultation-note {
    color: #64748b;
    font-size: 0.9rem;
    font-style: italic;
}

.hero-image {
    position: relative;
    height: 500px;
}

.hero-illustration {
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.1), rgba(34, 197, 94, 0.1));
    border-radius: 30px;
    overflow: hidden;
}

.floating-card {
    position: absolute;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    color: #475569;
    animation: float 6s ease-in-out infinite;
}

.floating-card i {
    color: #0ea5e9;
    font-size: 1.3rem;
}

.card-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 50%;
    right: 15%;
    animation-delay: 2s;
}

.card-3 {
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Crisis Banner */
.crisis-banner {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    padding: 1rem 0;
}

.crisis-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.crisis-content i {
    font-size: 1.5rem;
    color: #fef2f2;
}

.crisis-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.crisis-btn {
    background: white;
    color: #dc2626;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.crisis-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.2rem;
    color: #64748b;
    max-width: 600px;
    margin: 0 auto;
}

/* Specializations Section */
.specializations {
    padding: 80px 0;
    background: white;
}

.specializations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.specialization-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #f1f5f9;
}

.specialization-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #0ea5e9, #22c55e);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.card-icon i {
    font-size: 1.5rem;
    color: white;
}

.specialization-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.specialization-card p {
    color: #64748b;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.card-features {
    list-style: none;
}

.card-features li {
    padding: 0.5rem 0;
    color: #475569;
    font-size: 0.9rem;
    position: relative;
    padding-left: 1.5rem;
}

.card-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #22c55e;
    font-weight: bold;
}

/* Therapists Section */
.therapists {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
}

.therapists-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.therapist-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.therapist-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.therapist-photo {
    height: 250px;
    overflow: hidden;
}

.therapist-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.therapist-card:hover .therapist-photo img {
    transform: scale(1.05);
}

.therapist-info {
    padding: 2rem;
}

.therapist-info h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.therapist-title {
    color: #0ea5e9;
    font-weight: 500;
    margin-bottom: 1rem;
}

.therapist-credentials {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.credential {
    background: #e0f2fe;
    color: #0369a1;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.therapist-approach {
    color: #64748b;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.therapist-specialties {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.therapist-specialties span {
    background: linear-gradient(135deg, #0ea5e9, #22c55e);
    color: white;
    padding: 0.4rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Process Section */
.process {
    padding: 80px 0;
    background: white;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.process-step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #0ea5e9, #22c55e);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 1.5rem;
}

.step-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.step-content p {
    color: #64738b;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.step-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.step-features span {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: #22c55e;
    font-size: 0.9rem;
    font-weight: 500;
}

.step-features i {
    font-size: 0.8rem;
}

/* Privacy Section */
.privacy {
    padding: 80px 0;
    background: linear-gradient(135deg, #1e293b, #334155);
    color: white;
}

.privacy-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.privacy-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.privacy-text > p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.privacy-features {
    display: grid;
    gap: 1.5rem;
}

.privacy-feature {
    display: flex;
    gap: 1rem;
    align-items: start;
}

.privacy-feature i {
    color: #22c55e;
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.privacy-feature h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.privacy-feature p {
    opacity: 0.8;
    line-height: 1.5;
}

.privacy-certifications h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-align: center;
}

.cert-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.cert-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.cert-item i {
    font-size: 2rem;
    color: #22c55e;
    margin-bottom: 0.5rem;
    display: block;
}

/* Insurance Section */
.insurance {
    padding: 80px 0;
    background: #f8fafc;
}

.insurance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.insurance-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    text-align: center;
    transition: all 0.3s ease;
}

.insurance-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.insurance-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #0ea5e9, #22c55e);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.insurance-icon i {
    font-size: 2rem;
    color: white;
}

.insurance-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.insurance-card p {
    color: #64748b;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.insurance-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

.feature-available {
    background: #dcfce7;
    color: #166534;
    padding: 0.4rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.feature-limited {
    background: #fef3c7;
    color: #92400e;
    padding: 0.4rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: linear-gradient(135deg, #f0f9ff, #ecfdf5);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1rem;
}

.contact-info > p {
    font-size: 1.2rem;
    color: #64748b;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.contact-features {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-feature {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.contact-feature i {
    color: #0ea5e9;
    font-size: 1.3rem;
    width: 20px;
}

.contact-feature div {
    display: flex;
    flex-direction: column;
}

.contact-feature strong {
    color: #1e293b;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.contact-feature span {
    color: #64748b;
}

.consultation-form {
    background: white;
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    color: #374151;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.checkbox-group {
    display: flex;
    align-items: start;
    gap: 0.75rem;
}

.checkbox-label {
    display: flex;
    align-items: start;
    gap: 0.75rem;
    cursor: pointer;
    line-height: 1.5;
    font-size: 0.9rem;
}

.checkbox-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: #f9fafb;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #0ea5e9;
    border-color: #0ea5e9;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.checkbox-label a {
    color: #0ea5e9;
    text-decoration: none;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

.form-note {
    text-align: center;
    color: #64748b;
    font-size: 0.9rem;
    margin-top: 1rem;
}

/* Footer */
.footer {
    background: #1e293b;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
    font-size: 1.5rem;
    color: #0ea5e9;
    margin-bottom: 1rem;
}

.footer-logo i {
    font-size: 1.8rem;
    color: #22c55e;
}

.footer-section p {
    color: #94a3b8;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #94a3b8;
    transition: all 0.3s ease;
}

.footer-social a:hover {
    background: #0ea5e9;
    color: white;
    transform: translateY(-2px);
}

.footer-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #94a3b8;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #0ea5e9;
}

.footer-contact p {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #94a3b8;
    margin-bottom: 0.75rem;
}

.footer-contact i {
    color: #0ea5e9;
    width: 16px;
}

.footer-bottom {
    border-top: 1px solid #334155;
    padding-top: 2rem;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #94a3b8;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: #94a3b8;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #0ea5e9;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }
    
    .hero-content h1 {
        font-size: 3rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
    
    .privacy-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: white;
        flex-direction: column;
        justify-content: start;
        padding-top: 2rem;
        transition: left 0.3s ease;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-features {
        flex-direction: column;
        gap: 1rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .specializations-grid,
    .therapists-grid,
    .process-steps,
    .insurance-grid {
        grid-template-columns: 1fr;
    }
    
    .crisis-content {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .cert-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .nav-container {
        padding: 0 15px;
    }
    
    .hero {
        padding: 100px 0 60px;
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .hero-description {
        font-size: 1.1rem;
    }
    
    .consultation-form {
        padding: 1.5rem;
    }
    
    .specialization-card,
    .therapist-info {
        padding: 1.5rem;
    }
    
    .floating-card {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
}

/* Animation Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .floating-card {
        animation: none;
    }
}

/* Print Styles */
@media print {
    .header,
    .crisis-banner,
    .floating-card,
    .footer {
        display: none;
    }
    
    .hero {
        padding: 20px 0;
    }
    
    * {
        color: black !important;
        background: white !important;
    }
}