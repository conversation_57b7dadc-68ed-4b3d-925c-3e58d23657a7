// Life Coaching Landing Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    
    // Mobile Navigation Toggle
    const navToggle = document.querySelector('.nav-toggle');
    const navLinks = document.querySelector('.nav-links');
    
    if (navToggle) {
        navToggle.addEventListener('click', function() {
            navLinks.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
    }
    
    // Smooth Scrolling for Navigation Links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Close mobile menu if open
                if (navLinks.classList.contains('active')) {
                    navLinks.classList.remove('active');
                    navToggle.classList.remove('active');
                }
            }
        });
    });
    
    // Life Assessment Quiz Functionality
    class LifeAssessmentQuiz {
        constructor() {
            this.currentQuestion = 1;
            this.totalQuestions = 3;
            this.answers = {};
            this.init();
        }
        
        init() {
            this.bindEvents();
            this.updateProgress();
        }
        
        bindEvents() {
            const nextBtn = document.getElementById('next-btn');
            const prevBtn = document.getElementById('prev-btn');
            
            if (nextBtn) {
                nextBtn.addEventListener('click', () => this.nextQuestion());
            }
            
            if (prevBtn) {
                prevBtn.addEventListener('click', () => this.prevQuestion());
            }
            
            // Listen for answer changes
            document.querySelectorAll('.quiz-options input[type="radio"]').forEach(input => {
                input.addEventListener('change', () => this.handleAnswerChange());
            });
        }
        
        handleAnswerChange() {
            const currentQuestionElement = document.querySelector('.question.active');
            if (currentQuestionElement) {
                const questionNumber = currentQuestionElement.dataset.question;
                const selectedAnswer = document.querySelector(`input[name="q${questionNumber}"]:checked`);
                
                if (selectedAnswer) {
                    this.answers[questionNumber] = parseInt(selectedAnswer.value);
                    
                    // Enable next button
                    const nextBtn = document.getElementById('next-btn');
                    if (nextBtn) {
                        nextBtn.disabled = false;
                    }
                }
            }
        }
        
        nextQuestion() {
            if (this.currentQuestion < this.totalQuestions) {
                this.hideCurrentQuestion();
                this.currentQuestion++;
                this.showCurrentQuestion();
                this.updateProgress();
                this.updateButtons();
            } else {
                this.showResults();
            }
        }
        
        prevQuestion() {
            if (this.currentQuestion > 1) {
                this.hideCurrentQuestion();
                this.currentQuestion--;
                this.showCurrentQuestion();
                this.updateProgress();
                this.updateButtons();
            }
        }
        
        hideCurrentQuestion() {
            const currentQuestionElement = document.querySelector('.question.active');
            if (currentQuestionElement) {
                currentQuestionElement.classList.remove('active');
            }
        }
        
        showCurrentQuestion() {
            const nextQuestionElement = document.querySelector(`[data-question="${this.currentQuestion}"]`);
            if (nextQuestionElement) {
                nextQuestionElement.classList.add('active');
            }
        }
        
        updateProgress() {
            const progressFill = document.querySelector('.progress-fill');
            const currentQuestionSpan = document.getElementById('current-question');
            
            if (progressFill) {
                const progressPercentage = (this.currentQuestion / this.totalQuestions) * 100;
                progressFill.style.width = `${progressPercentage}%`;
            }
            
            if (currentQuestionSpan) {
                currentQuestionSpan.textContent = this.currentQuestion;
            }
        }
        
        updateButtons() {
            const nextBtn = document.getElementById('next-btn');
            const prevBtn = document.getElementById('prev-btn');
            
            if (prevBtn) {
                prevBtn.disabled = this.currentQuestion === 1;
            }
            
            if (nextBtn) {
                if (this.currentQuestion === this.totalQuestions) {
                    nextBtn.textContent = 'Zobacz Wyniki';
                } else {
                    nextBtn.textContent = 'Następne';
                }
                
                // Check if current question is answered
                const currentQuestionElement = document.querySelector('.question.active');
                if (currentQuestionElement) {
                    const questionNumber = currentQuestionElement.dataset.question;
                    const selectedAnswer = document.querySelector(`input[name="q${questionNumber}"]:checked`);
                    nextBtn.disabled = !selectedAnswer;
                }
            }
        }
        
        showResults() {
            // Hide all questions and navigation
            document.querySelectorAll('.question').forEach(q => q.style.display = 'none');
            document.querySelector('.quiz-navigation').style.display = 'none';
            document.querySelector('.quiz-progress').style.display = 'none';
            
            // Calculate score
            const totalScore = Object.values(this.answers).reduce((sum, value) => sum + value, 0);
            const maxScore = this.totalQuestions * 5;
            const percentage = Math.round((totalScore / maxScore) * 100);
            
            // Show results
            const resultsElement = document.querySelector('.quiz-results');
            if (resultsElement) {
                resultsElement.style.display = 'block';
                
                const scoreNumber = resultsElement.querySelector('.score-number');
                const resultMessage = resultsElement.querySelector('.result-message');
                const resultRecommendation = resultsElement.querySelector('.result-recommendation');
                
                if (scoreNumber) {
                    scoreNumber.textContent = percentage;
                }
                
                if (resultMessage && resultRecommendation) {
                    if (percentage >= 80) {
                        resultMessage.textContent = "Świetnie! Masz stabilne fundamenty życiowe.";
                        resultRecommendation.textContent = "Skup się na wyznaczaniu ambitnych celów i rozwoju potencjału. Program 90 dni pomoże Ci osiągnąć następny poziom.";
                    } else if (percentage >= 60) {
                        resultMessage.textContent = "Dobrze! Jesteś na dobrej drodze do równowagi.";
                        resultRecommendation.textContent = "Potrzebujesz wsparcia w kilku kluczowych obszarach. Program transformacji będzie idealny dla Ciebie.";
                    } else if (percentage >= 40) {
                        resultMessage.textContent = "Widzę potencjał! Czas na pozytywne zmiany.";
                        resultRecommendation.textContent = "Masz przed sobą wspaniałą okazję do transformacji. Umów bezpłatną konsultację, żeby omówić pierwszy krok.";
                    } else {
                        resultMessage.textContent = "Każda podróż zaczyna się od pierwszego kroku.";
                        resultRecommendation.textContent = "Nie martw się - każdy zaczyna od jakiegoś punktu. Umów konsultację, żeby wspólnie stworzyć plan zmiany.";
                    }
                }
            }
            
            // Animate results
            setTimeout(() => {
                resultsElement.classList.add('fade-in-up');
            }, 100);
        }
    }
    
    // Initialize Quiz
    const quiz = new LifeAssessmentQuiz();
    
    // Form Handling
    class FormHandler {
        constructor() {
            this.init();
        }
        
        init() {
            this.bindFormEvents();
        }
        
        bindFormEvents() {
            // Guide Download Form
            const guideForm = document.getElementById('guide-form');
            if (guideForm) {
                guideForm.addEventListener('submit', (e) => this.handleGuideSubmit(e));
            }
            
            // Webinar Signup Form
            const webinarForm = document.getElementById('webinar-form');
            if (webinarForm) {
                webinarForm.addEventListener('submit', (e) => this.handleWebinarSubmit(e));
            }
            
            // Consultation Booking Form
            const consultationForm = document.getElementById('consultation-form');
            if (consultationForm) {
                consultationForm.addEventListener('submit', (e) => this.handleConsultationSubmit(e));
            }
            
            // Contact Form
            const contactForm = document.getElementById('contact-form');
            if (contactForm) {
                contactForm.addEventListener('submit', (e) => this.handleContactSubmit(e));
            }
        }
        
        handleGuideSubmit(e) {
            e.preventDefault();
            
            const form = e.target;
            const formData = new FormData(form);
            
            // Validate form
            if (!this.validateForm(form)) {
                return;
            }
            
            // Simulate API call
            this.showLoading(form);
            
            setTimeout(() => {
                this.hideLoading(form);
                this.showSuccess(form, 'guide');
                
                // Track event (in real app, send to analytics)
                this.trackEvent('guide_download', {
                    name: formData.get('name'),
                    email: formData.get('email')
                });
            }, 2000);
        }
        
        handleWebinarSubmit(e) {
            e.preventDefault();
            
            const form = e.target;
            const formData = new FormData(form);
            
            if (!this.validateForm(form)) {
                return;
            }
            
            this.showLoading(form);
            
            setTimeout(() => {
                this.hideLoading(form);
                this.showSuccessMessage(form, 'Dziękuję za rejestrację! Link do webinaru zostanie wysłany na Twój e-mail 30 minut przed rozpoczęciem.');
                
                this.trackEvent('webinar_signup', {
                    name: formData.get('name'),
                    email: formData.get('email')
                });
            }, 2000);
        }
        
        handleConsultationSubmit(e) {
            e.preventDefault();
            
            const form = e.target;
            const formData = new FormData(form);
            
            if (!this.validateForm(form)) {
                return;
            }
            
            this.showLoading(form);
            
            setTimeout(() => {
                this.hideLoading(form);
                this.showSuccessMessage(form, 'Dziękuję za zgłoszenie! Skontaktuję się z Tobą w ciągu 24 godzin, aby potwierdzić termin konsultacji.');
                
                this.trackEvent('consultation_booking', {
                    name: formData.get('name'),
                    email: formData.get('email'),
                    date: formData.get('date')
                });
            }, 2000);
        }
        
        handleContactSubmit(e) {
            e.preventDefault();
            
            const form = e.target;
            const formData = new FormData(form);
            
            if (!this.validateForm(form)) {
                return;
            }
            
            this.showLoading(form);
            
            setTimeout(() => {
                this.hideLoading(form);
                this.showSuccessMessage(form, 'Dziękuję za wiadomość! Odpowiem w ciągu 24 godzin.');
                
                this.trackEvent('contact_form', {
                    name: formData.get('name'),
                    email: formData.get('email'),
                    subject: formData.get('subject')
                });
            }, 2000);
        }
        
        validateForm(form) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    this.showFieldError(field, 'To pole jest wymagane');
                    isValid = false;
                } else {
                    this.clearFieldError(field);
                    
                    // Email validation
                    if (field.type === 'email' && !this.isValidEmail(field.value)) {
                        this.showFieldError(field, 'Proszę podać poprawny adres e-mail');
                        isValid = false;
                    }
                }
            });
            
            return isValid;
        }
        
        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        showFieldError(field, message) {
            this.clearFieldError(field);
            
            const errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            errorElement.textContent = message;
            errorElement.style.color = 'var(--error)';
            errorElement.style.fontSize = '0.875rem';
            errorElement.style.marginTop = '0.25rem';
            
            field.parentNode.appendChild(errorElement);
            field.style.borderColor = 'var(--error)';
        }
        
        clearFieldError(field) {
            const errorElement = field.parentNode.querySelector('.field-error');
            if (errorElement) {
                errorElement.remove();
            }
            field.style.borderColor = '';
        }
        
        showLoading(form) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.textContent = 'Wysyłanie...';
            }
        }
        
        hideLoading(form) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.textContent = submitBtn.dataset.originalText || 'Wyślij';
            }
        }
        
        showSuccess(form, type) {
            if (type === 'guide') {
                const successElement = form.parentNode.querySelector('.form-success');
                if (successElement) {
                    form.style.display = 'none';
                    successElement.style.display = 'block';
                }
            }
        }
        
        showSuccessMessage(form, message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'form-success';
            successDiv.innerHTML = `
                <h3>Dziękuję!</h3>
                <p>${message}</p>
            `;
            successDiv.style.display = 'block';
            
            form.style.display = 'none';
            form.parentNode.insertBefore(successDiv, form.nextSibling);
        }
        
        trackEvent(eventName, data) {
            // In a real application, this would send data to analytics
            console.log('Event tracked:', eventName, data);
            
            // Example: Google Analytics
            // gtag('event', eventName, data);
            
            // Example: Facebook Pixel
            // fbq('track', eventName, data);
        }
    }
    
    // Initialize Form Handler
    const formHandler = new FormHandler();
    
    // Scroll Animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.testimonial-card, .benefit-item, .timeline-item, .section-header').forEach(el => {
        observer.observe(el);
    });
    
    // Navbar Background on Scroll
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 50) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            navbar.style.boxShadow = 'var(--shadow-md)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = 'none';
        }
    });
    
    // Counter Animation for Hero Stats
    function animateCounter(element, start, end, duration) {
        let startTimestamp = null;
        const step = (timestamp) => {
            if (!startTimestamp) startTimestamp = timestamp;
            const progress = Math.min((timestamp - startTimestamp) / duration, 1);
            const value = Math.floor(progress * (end - start) + start);
            element.textContent = value + (element.dataset.suffix || '');
            if (progress < 1) {
                window.requestAnimationFrame(step);
            }
        };
        window.requestAnimationFrame(step);
    }
    
    // Animate hero stats when they come into view
    const heroStatsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const statNumbers = entry.target.querySelectorAll('.stat-number');
                statNumbers.forEach(stat => {
                    const text = stat.textContent;
                    const number = parseInt(text.replace(/\D/g, ''));
                    const suffix = text.replace(/\d/g, '');
                    stat.dataset.suffix = suffix;
                    animateCounter(stat, 0, number, 2000);
                });
                heroStatsObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });
    
    const heroStats = document.querySelector('.hero-stats');
    if (heroStats) {
        heroStatsObserver.observe(heroStats);
    }
    
    // Testimonials Slider (if needed for mobile)
    class TestimonialsSlider {
        constructor() {
            this.currentSlide = 0;
            this.slides = document.querySelectorAll('.testimonial-card');
            this.totalSlides = this.slides.length;
            this.init();
        }
        
        init() {
            if (window.innerWidth <= 768 && this.totalSlides > 1) {
                this.createSlider();
            }
        }
        
        createSlider() {
            const container = document.querySelector('.testimonials-grid');
            if (!container) return;
            
            container.style.display = 'flex';
            container.style.overflow = 'hidden';
            container.style.scrollBehavior = 'smooth';
            
            // Create navigation dots
            const dotsContainer = document.createElement('div');
            dotsContainer.className = 'testimonials-dots';
            dotsContainer.style.display = 'flex';
            dotsContainer.style.justifyContent = 'center';
            dotsContainer.style.gap = '0.5rem';
            dotsContainer.style.marginTop = '2rem';
            
            for (let i = 0; i < this.totalSlides; i++) {
                const dot = document.createElement('button');
                dot.className = 'testimonial-dot';
                dot.style.width = '12px';
                dot.style.height = '12px';
                dot.style.borderRadius = '50%';
                dot.style.border = '2px solid var(--primary-purple)';
                dot.style.background = i === 0 ? 'var(--primary-purple)' : 'transparent';
                dot.style.cursor = 'pointer';
                dot.addEventListener('click', () => this.goToSlide(i));
                dotsContainer.appendChild(dot);
            }
            
            container.parentNode.appendChild(dotsContainer);
            
            // Auto-slide
            setInterval(() => {
                this.nextSlide();
            }, 5000);
        }
        
        goToSlide(index) {
            this.currentSlide = index;
            const container = document.querySelector('.testimonials-grid');
            const slideWidth = container.offsetWidth;
            container.scrollLeft = slideWidth * index;
            
            // Update dots
            const dots = document.querySelectorAll('.testimonial-dot');
            dots.forEach((dot, i) => {
                dot.style.background = i === index ? 'var(--primary-purple)' : 'transparent';
            });
        }
        
        nextSlide() {
            this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
            this.goToSlide(this.currentSlide);
        }
    }
    
    // Initialize testimonials slider
    new TestimonialsSlider();
    
    // FAQ Toggle (if FAQ section exists)
    document.querySelectorAll('.faq-question').forEach(question => {
        question.addEventListener('click', function() {
            const answer = this.nextElementSibling;
            const isOpen = answer.style.display === 'block';
            
            // Close all other answers
            document.querySelectorAll('.faq-answer').forEach(a => {
                a.style.display = 'none';
            });
            
            // Toggle current answer
            answer.style.display = isOpen ? 'none' : 'block';
        });
    });
    
    // Lazy Loading for Images
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                if (img.dataset.src) {
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            }
        });
    });
    
    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });
    
    // Copy to Clipboard functionality
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            console.log('Copied to clipboard: ' + text);
        });
    }
    
    // Initialize tooltips (if needed)
    document.querySelectorAll('[data-tooltip]').forEach(element => {
        element.addEventListener('mouseenter', function() {
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = this.dataset.tooltip;
            tooltip.style.position = 'absolute';
            tooltip.style.background = 'var(--dark-gray)';
            tooltip.style.color = 'var(--white)';
            tooltip.style.padding = '0.5rem';
            tooltip.style.borderRadius = '4px';
            tooltip.style.fontSize = '0.875rem';
            tooltip.style.zIndex = '1000';
            tooltip.style.pointerEvents = 'none';
            
            document.body.appendChild(tooltip);
            
            const rect = this.getBoundingClientRect();
            tooltip.style.left = rect.left + 'px';
            tooltip.style.top = (rect.top - tooltip.offsetHeight - 10) + 'px';
        });
        
        element.addEventListener('mouseleave', function() {
            const tooltip = document.querySelector('.tooltip');
            if (tooltip) {
                tooltip.remove();
            }
        });
    });
    
    // Performance optimization: Debounce scroll events
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Debounced scroll handler
    const debouncedScrollHandler = debounce(() => {
        // Additional scroll-based functionality can be added here
    }, 100);
    
    window.addEventListener('scroll', debouncedScrollHandler);
    
    // Preload critical images
    const criticalImages = [
        'https://images.unsplash.com/photo-1594736797933-d0501ba2fe65',
        'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2'
    ];
    
    criticalImages.forEach(src => {
        const img = new Image();
        img.src = src;
    });
    
    // Error handling for forms
    window.addEventListener('error', function(e) {
        console.error('JavaScript error:', e.error);
        // In production, you might want to send this to an error tracking service
    });
    
    // Console message for developers
    console.log('🌟 Zmień Życie z Anną - Landing Page loaded successfully!');
    console.log('💜 Designed with love for life transformation');
    
});

// Service Worker registration (for production)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
            })
            .catch(function(err) {
                console.log('ServiceWorker registration failed');
            });
    });
}