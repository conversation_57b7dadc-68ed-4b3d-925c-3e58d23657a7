const translations = {
    pl: {
        // Header & Navigation
        title: "GastroAI.pl - Inteligentny System Zarzdzania Restauracj",
        tagline: "Zwiksz obroty restauracji o 40% dziki AI",
        dashboard: "Panel GB�wny",
        menuOptimization: "Optymalizacja Menu",
        tableManagement: "Zarzdzanie StoBami",
        analytics: "Analityka",
        inventory: "Magazyn",
        staff: "Personel",
        customers: "Klienci",
        reports: "Raporty",
        settings: "Ustawienia",
        
        // Dashboard Stats
        todayRevenue: "Przychody dzisiaj",
        avgOrderValue: "Zrednia warto[ zam�wienia",
        tableOccupancy: "ObBo|enie stoB�w",
        customerSatisfaction: "Zadowolenie klient�w",
        
        // Menu Optimization
        profitMarginAnalysis: "Analiza mar| zysku",
        popularDishes: "Popularne dania",
        menuRecommendations: "Rekomendacje menu",
        seasonalTrends: "Trendy sezonowe",
        optimizeMenu: "Optymalizuj Menu",
        
        // Table Management
        floorPlan: "Plan sali",
        reservations: "Rezerwacje",
        waitList: "Lista oczekujcych",
        tableStatus: "Status stoB�w",
        available: "Dostpny",
        occupied: "Zajty",
        reserved: "Zarezerwowany",
        cleaning: "Sprztanie",
        
        // Analytics
        revenueForecasting: "Prognoza przychod�w",
        customerBehavior: "Zachowania klient�w",
        peakHours: "Godziny szczytu",
        dishPopularity: "Popularno[ daD",
        weatherImpact: "WpByw pogody",
        
        // AI Features
        aiRecommendations: "Rekomendacje AI",
        chatbotReservations: "Rezerwacje Chatbot",
        autoReviewResponse: "Automatyczne odpowiedzi na opinie",
        inventoryPrediction: "Predykcja zapas�w",
        dynamicPricing: "Dynamiczne ceny",
        
        // Customer Features
        qrMenu: "Menu QR",
        waitTimeEstimator: "Szacowany czas oczekiwania",
        loyaltyProgram: "Program lojalno[ciowy",
        splitBill: "PodziaB rachunku",
        kitchenCam: "Kamera kuchni",
        
        // Buttons & Actions
        viewDetails: "Zobacz szczeg�By",
        generateReport: "Generuj raport",
        exportData: "Eksportuj dane",
        saveChanges: "Zapisz zmiany",
        cancel: "Anuluj",
        confirm: "Potwierdz",
        refresh: "Od[wie|",
        addNew: "Dodaj nowy"
    },
    
    es: {
        // Header & Navigation
        title: "GastroAI.es - Sistema Inteligente de Gesti�n de Restaurantes",
        tagline: "Aumenta los ingresos del restaurante un 40% con IA",
        dashboard: "Panel Principal",
        menuOptimization: "Optimizaci�n de Men�",
        tableManagement: "Gesti�n de Mesas",
        analytics: "Anal�tica",
        inventory: "Inventario",
        staff: "Personal",
        customers: "Clientes",
        reports: "Informes",
        settings: "Configuraci�n",
        
        // Dashboard Stats
        todayRevenue: "Ingresos de hoy",
        avgOrderValue: "Valor promedio del pedido",
        tableOccupancy: "Ocupaci�n de mesas",
        customerSatisfaction: "Satisfacci�n del cliente",
        
        // Menu Optimization
        profitMarginAnalysis: "An�lisis de m�rgenes de beneficio",
        popularDishes: "Platos populares",
        menuRecommendations: "Recomendaciones de men�",
        seasonalTrends: "Tendencias estacionales",
        optimizeMenu: "Optimizar Men�",
        
        // Table Management
        floorPlan: "Plano del local",
        reservations: "Reservas",
        waitList: "Lista de espera",
        tableStatus: "Estado de mesas",
        available: "Disponible",
        occupied: "Ocupada",
        reserved: "Reservada",
        cleaning: "Limpieza",
        
        // Analytics
        revenueForecasting: "Previsi�n de ingresos",
        customerBehavior: "Comportamiento del cliente",
        peakHours: "Horas pico",
        dishPopularity: "Popularidad de platos",
        weatherImpact: "Impacto del clima",
        
        // AI Features
        aiRecommendations: "Recomendaciones IA",
        chatbotReservations: "Reservas Chatbot",
        autoReviewResponse: "Respuestas autom�ticas a rese�as",
        inventoryPrediction: "Predicci�n de inventario",
        dynamicPricing: "Precios din�micos",
        
        // Customer Features
        qrMenu: "Men� QR",
        waitTimeEstimator: "Estimador de tiempo de espera",
        loyaltyProgram: "Programa de lealtad",
        splitBill: "Divisi�n de cuenta",
        kitchenCam: "C�mara de cocina",
        
        // Buttons & Actions
        viewDetails: "Ver detalles",
        generateReport: "Generar informe",
        exportData: "Exportar datos",
        saveChanges: "Guardar cambios",
        cancel: "Cancelar",
        confirm: "Confirmar",
        refresh: "Actualizar",
        addNew: "Agregar nuevo"
    },
    
    en: {
        // Header & Navigation
        title: "GastroAI - Intelligent Restaurant Management System",
        tagline: "Increase restaurant revenue by 40% with AI",
        dashboard: "Dashboard",
        menuOptimization: "Menu Optimization",
        tableManagement: "Table Management",
        analytics: "Analytics",
        inventory: "Inventory",
        staff: "Staff",
        customers: "Customers",
        reports: "Reports",
        settings: "Settings",
        
        // Dashboard Stats
        todayRevenue: "Today's Revenue",
        avgOrderValue: "Average Order Value",
        tableOccupancy: "Table Occupancy",
        customerSatisfaction: "Customer Satisfaction",
        
        // Menu Optimization
        profitMarginAnalysis: "Profit Margin Analysis",
        popularDishes: "Popular Dishes",
        menuRecommendations: "Menu Recommendations",
        seasonalTrends: "Seasonal Trends",
        optimizeMenu: "Optimize Menu",
        
        // Table Management
        floorPlan: "Floor Plan",
        reservations: "Reservations",
        waitList: "Wait List",
        tableStatus: "Table Status",
        available: "Available",
        occupied: "Occupied",
        reserved: "Reserved",
        cleaning: "Cleaning",
        
        // Analytics
        revenueForecasting: "Revenue Forecasting",
        customerBehavior: "Customer Behavior",
        peakHours: "Peak Hours",
        dishPopularity: "Dish Popularity",
        weatherImpact: "Weather Impact",
        
        // AI Features
        aiRecommendations: "AI Recommendations",
        chatbotReservations: "Chatbot Reservations",
        autoReviewResponse: "Auto Review Responses",
        inventoryPrediction: "Inventory Prediction",
        dynamicPricing: "Dynamic Pricing",
        
        // Customer Features
        qrMenu: "QR Menu",
        waitTimeEstimator: "Wait Time Estimator",
        loyaltyProgram: "Loyalty Program",
        splitBill: "Split Bill",
        kitchenCam: "Kitchen Cam",
        
        // Buttons & Actions
        viewDetails: "View Details",
        generateReport: "Generate Report",
        exportData: "Export Data",
        saveChanges: "Save Changes",
        cancel: "Cancel",
        confirm: "Confirm",
        refresh: "Refresh",
        addNew: "Add New"
    }
};

let currentLanguage = 'pl';

function setLanguage(lang) {
    currentLanguage = lang;
    updatePageLanguage();
    localStorage.setItem('gastroai-language', lang);
}

function t(key) {
    return translations[currentLanguage][key] || key;
}

function updatePageLanguage() {
    document.querySelectorAll('[data-translate]').forEach(element => {
        const key = element.getAttribute('data-translate');
        element.textContent = t(key);
    });
    
    document.querySelectorAll('[data-translate-placeholder]').forEach(element => {
        const key = element.getAttribute('data-translate-placeholder');
        element.placeholder = t(key);
    });
    
    document.querySelectorAll('[data-translate-title]').forEach(element => {
        const key = element.getAttribute('data-translate-title');
        element.title = t(key);
    });
    
    document.documentElement.lang = currentLanguage;
}

function initLanguage() {
    const savedLang = localStorage.getItem('gastroai-language');
    const browserLang = navigator.language.substr(0, 2);
    
    if (savedLang && translations[savedLang]) {
        currentLanguage = savedLang;
    } else if (translations[browserLang]) {
        currentLanguage = browserLang;
    }
    
    updatePageLanguage();
    
    document.querySelectorAll('.language-selector').forEach(selector => {
        selector.value = currentLanguage;
    });
}

document.addEventListener('DOMContentLoaded', initLanguage);