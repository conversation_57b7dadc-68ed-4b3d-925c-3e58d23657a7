/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #0f0f0f;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    line-height: 1.2;
}

h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
}

h2 {
    font-size: 2.8rem;
    margin-bottom: 1rem;
}

h3 {
    font-size: 2rem;
    margin-bottom: 0.8rem;
}

p {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 1rem;
}

/* Colors */
:root {
    --primary-gold: #d4af37;
    --light-gold: #f4e7b8;
    --dark-gold: #b8941f;
    --primary-black: #0f0f0f;
    --light-black: #1a1a1a;
    --gray: #666;
    --light-gray: #f8f8f8;
    --white: #ffffff;
}

.gold-text {
    color: var(--primary-gold);
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    color: var(--white);
    border: none;
    padding: 15px 30px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(212, 175, 55, 0.3);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--primary-gold);
    padding: 13px 28px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-secondary:hover {
    background: var(--primary-gold);
    color: var(--primary-black);
    transform: translateY(-2px);
}

.cta-button {
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    color: var(--white);
    border: none;
    padding: 12px 25px;
    font-size: 0.9rem;
    font-weight: 600;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(15, 15, 15, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    color: var(--primary-gold);
    font-size: 1.8rem;
    margin: 0;
}

.nav-logo span {
    color: var(--white);
    font-size: 0.9rem;
    font-weight: 300;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 2rem;
}

.nav-menu a {
    color: var(--white);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-gold);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 0;
    background-color: var(--primary-gold);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--white);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--light-black) 100%);
    display: flex;
    align-items: center;
    padding: 100px 0 50px;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text h1 {
    color: var(--white);
    font-size: 4rem;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.hero-text p {
    color: var(--light-gray);
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    max-width: 500px;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.hero-gallery {
    position: relative;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    height: 600px;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.gallery-item:first-child {
    grid-row: span 2;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(212, 175, 55, 0.3), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover::after {
    opacity: 1;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    color: var(--white);
    margin-bottom: 1rem;
}

.section-header p {
    color: var(--gray);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

/* Services Section */
.services {
    padding: 100px 0;
    background: var(--light-black);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.service-card {
    background: var(--primary-black);
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(212, 175, 55, 0.1);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(212, 175, 55, 0.1);
    border-color: var(--primary-gold);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: var(--white);
}

.service-card h3 {
    color: var(--white);
    margin-bottom: 1.5rem;
}

.service-list {
    list-style: none;
    text-align: left;
}

.service-list li {
    color: var(--light-gray);
    padding: 0.8rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.service-list li:last-child {
    border-bottom: none;
}

.service-list span {
    color: var(--primary-gold);
    font-weight: 600;
}

/* Team Section */
.team {
    padding: 100px 0;
    background: var(--primary-black);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
}

.team-member {
    background: var(--light-black);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.team-member:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.member-photo {
    height: 400px;
    overflow: hidden;
    position: relative;
}

.member-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.team-member:hover .member-photo img {
    transform: scale(1.1);
}

.member-info {
    padding: 2rem;
}

.member-info h3 {
    color: var(--white);
    margin-bottom: 0.5rem;
}

.member-title {
    color: var(--primary-gold);
    font-weight: 600;
    margin-bottom: 1rem;
}

.member-specialty {
    color: var(--gray);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.member-description {
    color: var(--light-gray);
    margin-bottom: 1.5rem;
}

.member-social {
    display: flex;
    gap: 1rem;
}

.member-social a {
    width: 40px;
    height: 40px;
    background: var(--primary-gold);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.member-social a:hover {
    background: var(--dark-gold);
    transform: translateY(-2px);
}

/* Gallery Section */
.gallery {
    padding: 100px 0;
    background: var(--light-black);
}

.before-after-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
}

.before-after-item {
    background: var(--primary-black);
    border-radius: 20px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.before-after-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.before-after-images {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.before-image,
.after-image {
    flex: 1;
    position: relative;
    border-radius: 15px;
    overflow: hidden;
}

.before-image img,
.after-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.label {
    position: absolute;
    top: 10px;
    left: 10px;
    background: var(--primary-gold);
    color: var(--white);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.transformation-description {
    color: var(--light-gray);
    text-align: center;
    font-style: italic;
}

/* Testimonials Section */
.testimonials {
    padding: 100px 0;
    background: var(--primary-black);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: var(--light-black);
    padding: 2rem;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.testimonial-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.testimonial-header img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 1rem;
    object-fit: cover;
}

.client-info h4 {
    color: var(--white);
    margin-bottom: 0.5rem;
}

.stars {
    color: var(--primary-gold);
}

.testimonial-card p {
    color: var(--light-gray);
    font-style: italic;
    line-height: 1.6;
}

/* Instagram Section */
.instagram {
    padding: 100px 0;
    background: var(--light-black);
}

.instagram-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.instagram-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 15px;
    overflow: hidden;
    cursor: pointer;
}

.instagram-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.instagram-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(212, 175, 55, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.instagram-overlay i {
    font-size: 2rem;
    color: var(--white);
}

.instagram-item:hover .instagram-overlay {
    opacity: 1;
}

.instagram-item:hover img {
    transform: scale(1.1);
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: var(--primary-black);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-bottom: 4rem;
}

.contact-info h2 {
    color: var(--white);
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.contact-item i {
    color: var(--primary-gold);
    font-size: 1.5rem;
    margin-right: 1rem;
    margin-top: 0.5rem;
}

.contact-item h4 {
    color: var(--white);
    margin-bottom: 0.5rem;
}

.contact-item p {
    color: var(--light-gray);
    margin: 0;
}

.booking-form {
    background: var(--light-black);
    padding: 2.5rem;
    border-radius: 20px;
}

.booking-form h3 {
    color: var(--white);
    margin-bottom: 2rem;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    background: var(--primary-black);
    color: var(--white);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-gold);
}

.form-group textarea {
    height: 100px;
    resize: vertical;
}

.map-container {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Footer */
.footer {
    background: var(--light-black);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-logo h3 {
    color: var(--primary-gold);
    margin-bottom: 0.5rem;
}

.footer-logo span {
    color: var(--white);
    font-weight: 300;
}

.footer-section p {
    color: var(--light-gray);
    margin-bottom: 1.5rem;
}

.footer-section h4 {
    color: var(--white);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.8rem;
}

.footer-section ul li a {
    color: var(--light-gray);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-gold);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--primary-gold);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--dark-gold);
    transform: translateY(-2px);
}

.footer-contact p {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: var(--light-gray);
}

.footer-contact i {
    color: var(--primary-gold);
    margin-right: 1rem;
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    text-align: center;
}

.footer-bottom p {
    color: var(--gray);
    margin: 0;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: var(--light-black);
    margin: 5% auto;
    padding: 3rem;
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    position: relative;
}

.close {
    color: var(--gray);
    float: right;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 2rem;
    top: 2rem;
}

.close:hover {
    color: var(--white);
}

.modal-content h2 {
    color: var(--white);
    margin-bottom: 2rem;
    text-align: center;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(15, 15, 15, 0.98);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .cta-button {
        display: none;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .hero-text h1 {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .gallery-grid {
        grid-template-columns: 1fr;
        height: auto;
    }
    
    .gallery-item {
        height: 300px;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .services-grid,
    .team-grid,
    .before-after-grid,
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .instagram-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    h1 {
        font-size: 2.5rem;
    }
    
    h2 {
        font-size: 2rem;
    }
    
    .section-header {
        margin-bottom: 2rem;
    }
}

@media (max-width: 480px) {
    .hero-text h1 {
        font-size: 2rem;
    }
    
    .hero-text p {
        font-size: 1rem;
    }
    
    .btn-primary,
    .btn-secondary {
        padding: 12px 25px;
        font-size: 0.9rem;
    }
    
    .services,
    .team,
    .gallery,
    .testimonials,
    .instagram,
    .contact {
        padding: 60px 0;
    }
    
    .service-card,
    .booking-form {
        padding: 1.5rem;
    }
    
    .modal-content {
        padding: 2rem;
        margin: 10% auto;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

/* Scroll Animations */
.scroll-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.scroll-animate.animate {
    opacity: 1;
    transform: translateY(0);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-gold);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Success/Error Messages */
.message {
    padding: 1rem;
    border-radius: 10px;
    margin: 1rem 0;
    text-align: center;
}

.message.success {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid #28a745;
}

.message.error {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border: 1px solid #dc3545;
}