/**
 * FitGenius.pl - AI Coaching System
 * Advanced real-time coaching, motivation, and habit formation
 */

const AICoach = {
    // AI Coach Configuration
    config: {
        personalityType: 'motivational', // motivational, supportive, challenging
        adaptiveResponses: true,
        realTimeAdjustments: true,
        contextAwareness: true,
        learningEnabled: true
    },
    
    // User Profile for Personalization
    userProfile: {
        name: '<PERSON>',
        goals: ['weight_loss', 'strength_building'],
        motivation_triggers: ['progress_visualization', 'social_recognition'],
        preferred_communication_style: 'encouraging',
        timezone: 'Europe/Warsaw',
        optimal_workout_times: ['07:00', '18:00'],
        current_mood: 'motivated',
        energy_level: 8,
        stress_level: 3
    },
    
    // AI Knowledge Base
    knowledgeBase: {
        // Exercise form corrections
        formCorrections: {
            squat: {
                common_mistakes: [
                    '<PERSON><PERSON> schodz<PERSON> się do środka',
                    'Plecy zaokrąglają się',
                    'Za płytki przysiad'
                ],
                corrections: [
                    'Wypy<PERSON>j kolana na zewnątrz',
                    'Utrzymaj prostą sylwetkę',
                    'Sc<PERSON><PERSON><PERSON> niżej, biodra poniżej kolan'
                ],
                cues: [
                    'Jak siadanie na krześle',
                    'Pchnij podłogę nogami',
                    'Napnij core przez cały ruch'
                ]
            },
            pushup: {
                common_mistakes: [
                    'Biodra unoszą się',
                    'Za mały zakres ruchu',
                    'Nieprawidłowe położenie rąk'
                ],
                corrections: [
                    'Utrzymuj linię prostą od głowy do stóp',
                    'Klatka musi dotknąć podłogi',
                    'Ręce pod barkami'
                ],
                cues: [
                    'Ciało jak deska',
                    'Kontrolowany ruch w dół',
                    'Mocne wybicie do góry'
                ]
            },
            plank: {
                common_mistakes: [
                    'Biodra za wysoko',
                    'Biodra za nisko',
                    'Ramiona przed łokciami'
                ],
                corrections: [
                    'Linia prosta od głowy do stóp',
                    'Napnij pośladki i brzuch',
                    'Łokcie pod barkami'
                ],
                cues: [
                    'Wyobraź sobie linijkę na plecach',
                    'Oddychaj spokojnie',
                    'Napięcie w całym ciele'
                ]
            }
        },
        
        // Motivational strategies
        motivationStrategies: {
            low_energy: [
                'Zacznij od 5 minut - to lepsze niż nic!',
                'Każdy mały krok przybliża Cię do celu',
                'Pamiętaj, jak dobrze czujesz się po treningu',
                'Twoje ciało będzie Ci dziękować później'
            ],
            plateau: [
                'Plateau to znak, że czas na zmiany',
                'Twoje ciało się adaptuje - to dobry znak!',
                'Spróbuj nowych ćwiczeń lub zwiększ intensywność',
                'Cierpliwość i konsystencja przyniosą efekty'
            ],
            lack_of_time: [
                'Intensywny 15-minutowy trening może być bardzo efektywny',
                'Podziel trening na części w ciągu dnia',
                'Każda minuta aktywności się liczy',
                'Inwestycja w siebie to najlepsze wykorzystanie czasu'
            ],
            demotivation: [
                'Trudne dni czynią Cię silniejszym',
                'Każdy ekspert był kiedyś początkującym',
                'Nie porównuj się z innymi - porównaj się z sobą z wczoraj',
                'Małe kroki każdego dnia prowadzą do wielkich zmian'
            ]
        },
        
        // Habit formation psychology
        habitFormation: {
            stages: {
                unconscious_incompetence: {
                    description: 'Nie wiesz, że nie wiesz',
                    coaching_approach: 'Edukacja i świadomość',
                    techniques: ['goal_setting', 'education', 'motivation_building']
                },
                conscious_incompetence: {
                    description: 'Wiesz, że nie wiesz',
                    coaching_approach: 'Wsparcie i nauka',
                    techniques: ['step_by_step_guidance', 'encouragement', 'skill_building']
                },
                conscious_competence: {
                    description: 'Wiesz i umiesz, ale wymaga koncentracji',
                    coaching_approach: 'Praktyka i wzmocnienie',
                    techniques: ['repetition', 'feedback', 'consistency_building']
                },
                unconscious_competence: {
                    description: 'Automatyczny nawyk',
                    coaching_approach: 'Utrzymanie i rozwój',
                    techniques: ['challenge_progression', 'variety', 'mastery']
                }
            },
            
            triggers: {
                time_based: 'O tej samej porze każdego dnia',
                location_based: 'W tym samym miejscu',
                emotional_based: 'Gdy czujesz określone emocje',
                routine_based: 'Po wykonaniu innej czynności'
            },
            
            rewards: {
                intrinsic: ['feeling_accomplished', 'energy_boost', 'stress_relief'],
                extrinsic: ['progress_tracking', 'social_recognition', 'achievement_badges']
            }
        },
        
        // Personalized advice based on user data
        personalizedAdvice: {
            beginner: [
                'Skupić się na formie, nie na intensywności',
                'Konsystencja ważniejsza niż perfekcyjność',
                'Słuchaj swojego ciała i odpoczywaj gdy trzeba',
                'Małe cele są łatwiejsze do osiągnięcia'
            ],
            intermediate: [
                'Czas na progresywne przeciążenie',
                'Eksperymentuj z nowymi ćwiczeniami',
                'Śledź swoje postępy dokładnie',
                'Rozważ periodyzację treningu'
            ],
            advanced: [
                'Optymalizuj regenerację i sen',
                'Precyzyjne planowanie makrocykli',
                'Zaawansowane techniki intensyfikacji',
                'Mentoring innych może pogłębić twoją wiedzę'
            ]
        }
    },
    
    // Real-time coaching state
    currentSession: {
        isActive: false,
        startTime: null,
        currentExercise: null,
        formAnalysis: null,
        realTimeAdjustments: [],
        motivationalMessages: [],
        energyLevel: null,
        perceivedExertion: null
    },
    
    // Initialize AI Coach
    init() {
        this.loadUserProfile();
        this.setupEventListeners();
        this.initializeContextualAwareness();
        this.startProactiveCoaching();
        this.setupRealTimeAnalysis();
        this.waitForLLMIntegration();
        console.log('🤖 AI Coach initialized - Ready to support your fitness journey!');
    },
    
    waitForLLMIntegration() {
        // Wait for LLM integration to be available
        const checkLLM = () => {
            if (window.LLMIntegration) {
                this.llm = window.LLMIntegration;
                console.log('🧠 LLM integration connected to AI Coach');
                this.enhanceWithLLM();
            } else {
                setTimeout(checkLLM, 100);
            }
        };
        checkLLM();
    },
    
    enhanceWithLLM() {
        // Replace static responses with dynamic LLM-generated content
        this.config.llmEnabled = true;
        
        // Schedule a welcome message with LLM
        setTimeout(() => {
            this.generateLLMWelcomeMessage();
        }, 2000);
    },
    
    async generateLLMWelcomeMessage() {
        if (!this.llm) return;
        
        try {
            const welcomeMessage = await this.llm.generateMotivationalMessage(
                this.userProfile.current_mood || 'motivated',
                this.getUserStreakDays(),
                this.getRecentProgress()
            );
            
            this.showCoachingMessage(welcomeMessage, 'llm_welcome');
        } catch (error) {
            console.log('LLM welcome message failed, using fallback');
            this.showCoachingMessage('Witaj! Jestem Twoim AI Trenerem i będę Cię wspierać! 💪', 'welcome');
        }
    },
    
    getUserStreakDays() {
        const workoutHistory = FitGenius.utils.loadFromLocalStorage('workout_history') || [];
        // Simple streak calculation
        let streak = 0;
        const today = new Date();
        
        for (let i = 0; i < 30; i++) {
            const checkDate = new Date(today);
            checkDate.setDate(today.getDate() - i);
            const dateStr = checkDate.toDateString();
            
            const hasWorkout = workoutHistory.some(w => 
                new Date(w.date).toDateString() === dateStr
            );
            
            if (hasWorkout) {
                streak++;
            } else if (i > 0) {
                break;
            }
        }
        
        return streak;
    },
    
    getRecentProgress() {
        const analytics = FitGenius.utils.loadFromLocalStorage('user_analytics_data') || {};
        const workoutHistory = FitGenius.utils.loadFromLocalStorage('workout_history') || [];
        
        const recent = workoutHistory.slice(-5);
        const avgCalories = recent.reduce((sum, w) => sum + (w.calories || 0), 0) / recent.length || 0;
        
        if (avgCalories > 300) return 'excellent_progress';
        if (avgCalories > 200) return 'good_progress';
        return 'starting_journey';
    },
    
    loadUserProfile() {
        const savedProfile = FitGenius.utils.loadFromLocalStorage('ai_coach_profile');
        if (savedProfile) {
            this.userProfile = { ...this.userProfile, ...savedProfile };
        }
        
        // Analyze user data to enhance profile
        this.analyzeUserBehavior();
    },
    
    analyzeUserBehavior() {
        const workoutHistory = FitGenius.utils.loadFromLocalStorage('workout_history') || [];
        const analyticsData = FitGenius.utils.loadFromLocalStorage('user_analytics_data') || {};
        
        // Determine optimal workout times
        const workoutTimes = workoutHistory.map(w => new Date(w.date).getHours());
        const mostCommonTime = this.getMostFrequent(workoutTimes);
        if (mostCommonTime) {
            this.userProfile.optimal_workout_times = [`${mostCommonTime}:00`];
        }
        
        // Determine motivation triggers
        if (workoutHistory.length > 10) {
            this.userProfile.motivation_triggers = ['progress_visualization', 'streak_maintenance'];
        }
        
        // Assess current fitness level
        const avgCalories = workoutHistory.reduce((sum, w) => sum + (w.calories || 0), 0) / workoutHistory.length;
        if (avgCalories > 400) {
            this.userProfile.fitness_level = 'advanced';
        } else if (avgCalories > 250) {
            this.userProfile.fitness_level = 'intermediate';
        } else {
            this.userProfile.fitness_level = 'beginner';
        }
    },
    
    getMostFrequent(arr) {
        if (arr.length === 0) return null;
        const frequency = {};
        let maxFreq = 0;
        let mostFrequent = null;
        
        arr.forEach(item => {
            frequency[item] = (frequency[item] || 0) + 1;
            if (frequency[item] > maxFreq) {
                maxFreq = frequency[item];
                mostFrequent = item;
            }
        });
        
        return mostFrequent;
    },
    
    setupEventListeners() {
        // Workout session events
        document.addEventListener('workoutStarted', (e) => {
            this.startCoachingSession(e.detail);
        });
        
        document.addEventListener('exerciseStarted', (e) => {
            this.provideExerciseGuidance(e.detail);
        });
        
        document.addEventListener('exerciseCompleted', (e) => {
            this.analyzeExercisePerformance(e.detail);
        });
        
        document.addEventListener('workoutCompleted', (e) => {
            this.endCoachingSession(e.detail);
        });
        
        // Form analysis events (simulated camera input)
        document.addEventListener('formAnalyzed', (e) => {
            this.provideFormFeedback(e.detail);
        });
        
        // User state changes
        document.addEventListener('userMoodChanged', (e) => {
            this.adaptToMoodChange(e.detail);
        });
        
        document.addEventListener('energyLevelChanged', (e) => {
            this.adjustIntensityRecommendations(e.detail);
        });
    },
    
    initializeContextualAwareness() {
        // Time-based awareness
        this.setupTimeBasedCoaching();
        
        // Weather-based suggestions (mock data)
        this.setupWeatherAwareness();
        
        // Stress level monitoring
        this.setupStressMonitoring();
    },
    
    setupTimeBasedCoaching() {
        const now = new Date();
        const hour = now.getHours();
        
        // Morning coaching
        if (hour >= 6 && hour < 10) {
            this.scheduleMotivationalMessage('morning_energy');
        }
        
        // Afternoon slump coaching
        if (hour >= 14 && hour < 16) {
            this.scheduleMotivationalMessage('afternoon_boost');
        }
        
        // Evening coaching
        if (hour >= 18 && hour < 22) {
            this.scheduleMotivationalMessage('evening_motivation');
        }
    },
    
    setupWeatherAwareness() {
        // Mock weather data - in production would use real weather API
        const weather = {
            condition: 'sunny',
            temperature: 22,
            humidity: 65
        };
        
        if (weather.condition === 'rainy') {
            this.suggestIndoorAlternatives();
        } else if (weather.temperature > 25) {
            this.suggestHydrationReminders();
        }
    },
    
    setupStressMonitoring() {
        // Monitor user interaction patterns for stress indicators
        let clickCount = 0;
        let rapidClicks = 0;
        
        document.addEventListener('click', () => {
            clickCount++;
            setTimeout(() => clickCount--, 1000);
            
            if (clickCount > 5) {
                rapidClicks++;
                if (rapidClicks > 3) {
                    this.detectStressSignals();
                    rapidClicks = 0;
                }
            }
        });
    },
    
    detectStressSignals() {
        this.userProfile.stress_level = Math.min(10, this.userProfile.stress_level + 1);
        
        if (this.userProfile.stress_level > 7) {
            this.provideStressReliefSuggestions();
        }
    },
    
    provideStressReliefSuggestions() {
        const suggestions = [
            '🧘 Może czas na 5 minut głębokiego oddychania?',
            '🚶‍♀️ Krótki spacer może pomóc rozładować napięcie',
            '💆‍♀️ Rozważ delikatny trening rozciągający',
            '🎵 Uspokajająca muzyka może poprawić nastrój'
        ];
        
        const suggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
        this.showCoachingMessage(suggestion, 'stress_relief');
    },
    
    startProactiveCoaching() {
        // Daily check-ins
        this.scheduleDailyCheckIn();
        
        // Progress celebrations
        this.scheduleProgressCelebrations();
        
        // Habit reinforcement
        this.scheduleHabitReinforcement();
        
        // Motivational reminders
        this.scheduleMotivationalReminders();
    },
    
    scheduleDailyCheckIn() {
        const lastCheckIn = FitGenius.utils.loadFromLocalStorage('last_ai_checkin');
        const today = new Date().toDateString();
        
        if (lastCheckIn !== today) {
            setTimeout(() => {
                this.performDailyCheckIn();
                FitGenius.utils.saveToLocalStorage('last_ai_checkin', today);
            }, 3000); // 3 seconds after initialization
        }
    },
    
    performDailyCheckIn() {
        const checkInMessages = [
            `Dzień dobry ${this.userProfile.name}! Jak się dziś czujesz i jaka jest Twoja energia? 🌅`,
            `Cześć! Gotowy/a na dzisiejsze wyzwania fitness? 💪`,
            `Dobry poranek! Sprawdźmy Twój plan treningowy na dziś 📋`
        ];
        
        const message = checkInMessages[Math.floor(Math.random() * checkInMessages.length)];
        this.showInteractiveCoachingMessage(message, 'daily_checkin');
    },
    
    showInteractiveCoachingMessage(message, type) {
        const modal = document.createElement('div');
        modal.className = 'ai-coach-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
        `;
        
        modal.innerHTML = `
            <div class="coach-content glass-effect" style="padding: 2rem; border-radius: 1rem; max-width: 500px; width: 90%; text-align: center;">
                <div class="coach-avatar" style="font-size: 4rem; margin-bottom: 1rem;">🤖</div>
                <h3 style="margin-bottom: 1rem; color: var(--text-primary);">AI Coach</h3>
                <p style="margin-bottom: 2rem; color: var(--text-secondary); line-height: 1.6;">${message}</p>
                
                ${type === 'daily_checkin' ? `
                    <div class="energy-selector" style="margin-bottom: 2rem;">
                        <p style="margin-bottom: 1rem; color: var(--text-primary);">Jak oceniasz swoją dzisiejszą energię?</p>
                        <div style="display: flex; gap: 0.5rem; justify-content: center; flex-wrap: wrap;">
                            ${[1,2,3,4,5,6,7,8,9,10].map(level => `
                                <button class="energy-btn" data-energy="${level}" style="
                                    width: 40px; height: 40px; border-radius: 50%; border: 2px solid var(--primary-color);
                                    background: ${level <= 3 ? '#ef4444' : level <= 6 ? '#f59e0b' : '#10b981'};
                                    color: white; font-weight: bold; cursor: pointer; transition: transform 0.2s;
                                " onclick="this.style.transform='scale(1.2)'; setTimeout(() => this.style.transform='scale(1)', 150);">
                                    ${level}
                                </button>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
                
                <div style="display: flex; gap: 1rem; justify-content: center;">
                    <button class="btn-primary" onclick="this.closest('.ai-coach-modal').remove(); AICoach.handleCoachingResponse('${type}', 'positive');">
                        Świetnie! 👍
                    </button>
                    <button class="btn-secondary" onclick="this.closest('.ai-coach-modal').remove(); AICoach.handleCoachingResponse('${type}', 'neutral');">
                        W porządku
                    </button>
                    <button class="btn-secondary" onclick="this.closest('.ai-coach-modal').remove(); AICoach.handleCoachingResponse('${type}', 'negative');">
                        Mogło być lepiej
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Add energy level selection handling
        modal.querySelectorAll('.energy-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const energyLevel = parseInt(btn.dataset.energy);
                this.userProfile.energy_level = energyLevel;
                this.adjustDailyRecommendations(energyLevel);
                
                // Update visual feedback
                modal.querySelectorAll('.energy-btn').forEach(b => b.style.opacity = '0.5');
                btn.style.opacity = '1';
                btn.style.transform = 'scale(1.2)';
            });
        });
        
        // Auto close after 30 seconds
        setTimeout(() => {
            if (document.body.contains(modal)) {
                modal.remove();
            }
        }, 30000);
    },
    
    handleCoachingResponse(type, response) {
        switch (type) {
            case 'daily_checkin':
                this.processDailyCheckInResponse(response);
                break;
            case 'stress_relief':
                this.processStressReliefResponse(response);
                break;
            default:
                console.log(`Coach response: ${type} - ${response}`);
        }
        
        FitGenius.analytics.trackEvent('ai_coach_interaction', {
            type: type,
            response: response,
            timestamp: new Date().toISOString()
        });
    },
    
    processDailyCheckInResponse(response) {
        this.userProfile.current_mood = response;
        
        if (response === 'negative') {
            this.scheduleMotivationalMessage('mood_boost');
        } else if (response === 'positive') {
            this.scheduleChallengingWorkout();
        }
        
        this.saveUserProfile();
    },
    
    adjustDailyRecommendations(energyLevel) {
        if (energyLevel <= 3) {
            this.recommendLowIntensityActivities();
        } else if (energyLevel >= 8) {
            this.recommendHighIntensityWorkout();
        } else {
            this.recommendModerateActivity();
        }
    },
    
    recommendLowIntensityActivities() {
        const message = '🧘 Z takim poziomem energii polecam gentle yoga lub spacer. Słuchaj swojego ciała!';
        this.showCoachingMessage(message, 'low_intensity');
    },
    
    recommendHighIntensityWorkout() {
        const message = '🔥 Masz dużo energii! To idealny moment na intensywny trening siłowy lub HIIT!';
        this.showCoachingMessage(message, 'high_intensity');
    },
    
    recommendModerateActivity() {
        const message = '💪 Świetny poziom energii na standardowy trening! Sprawdź swój plan na dziś.';
        this.showCoachingMessage(message, 'moderate_intensity');
    },
    
    // Real-time workout coaching
    startCoachingSession(workoutData) {
        this.currentSession = {
            isActive: true,
            startTime: new Date(),
            workoutData: workoutData,
            exerciseIndex: 0,
            realTimeAdjustments: [],
            formFeedback: []
        };
        
        this.showCoachingMessage(
            `💪 Rozpoczynamy trening! Mam na oku Twoją formę i będę Cię wspierać przez cały czas.`,
            'session_start'
        );
        
        // Start real-time monitoring
        this.startRealTimeMonitoring();
    },
    
    startRealTimeMonitoring() {
        if (!this.currentSession.isActive) return;
        
        // Simulated real-time analysis
        this.monitoringInterval = setInterval(() => {
            this.performRealTimeAnalysis();
        }, 10000); // Every 10 seconds
    },
    
    performRealTimeAnalysis() {
        // Simulate form analysis, heart rate monitoring, etc.
        const analyses = [
            { type: 'form', quality: 'good', message: '✅ Świetna forma! Utrzymuj to tempo.' },
            { type: 'intensity', level: 'optimal', message: '🎯 Perfekcyjna intensywność dla Twoich celów.' },
            { type: 'fatigue', level: 'moderate', message: '⚡ Czuję, że dajesz z siebie wszystko!' },
            { type: 'encouragement', message: '🔥 Jesteś niesamowity! Jeszcze kilka powtórzeń!' }
        ];
        
        const analysis = analyses[Math.floor(Math.random() * analyses.length)];
        
        if (Math.random() > 0.7) { // 30% chance of feedback
            this.showRealTimeFeedback(analysis);
        }
    },
    
    showRealTimeFeedback(analysis) {
        const feedback = document.createElement('div');
        feedback.className = 'real-time-feedback';
        feedback.style.cssText = `
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(99, 102, 241, 0.95);
            color: white;
            padding: 1rem 2rem;
            border-radius: 2rem;
            font-weight: 600;
            z-index: 10001;
            animation: slideUpFade 4s ease-out forwards;
            backdrop-filter: blur(10px);
            max-width: 300px;
            text-align: center;
        `;
        
        feedback.textContent = analysis.message;
        document.body.appendChild(feedback);
        
        // Add animation keyframes
        if (!document.querySelector('#realtime-feedback-styles')) {
            const style = document.createElement('style');
            style.id = 'realtime-feedback-styles';
            style.textContent = `
                @keyframes slideUpFade {
                    0% { transform: translateX(-50%) translateY(100px); opacity: 0; }
                    20%, 80% { transform: translateX(-50%) translateY(0); opacity: 1; }
                    100% { transform: translateX(-50%) translateY(-50px); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }
        
        setTimeout(() => {
            if (document.body.contains(feedback)) {
                feedback.remove();
            }
        }, 4000);
    },
    
    async provideExerciseGuidance(exerciseData) {
        if (this.llm && this.config.llmEnabled) {
            try {
                const guidance = await this.llm.generateFormFeedback(
                    exerciseData.name,
                    ['podstawowe wskazówki']
                );
                this.showCoachingMessage(
                    `💡 ${exerciseData.name}: ${guidance}`,
                    'llm_exercise_guidance'
                );
                return;
            } catch (error) {
                console.log('LLM guidance failed, using fallback');
            }
        }
        
        // Fallback to static guidance
        const exerciseName = exerciseData.name.toLowerCase();
        const guidance = this.knowledgeBase.formCorrections[exerciseName];
        
        if (guidance) {
            const randomCue = guidance.cues[Math.floor(Math.random() * guidance.cues.length)];
            this.showCoachingMessage(
                `💡 ${exerciseData.name}: ${randomCue}`,
                'exercise_guidance'
            );
        }
    },
    
    analyzeExercisePerformance(exerciseData) {
        // Simulate performance analysis
        const performance = {
            form_score: Math.random() * 100,
            intensity_score: Math.random() * 100,
            completion_rate: Math.random() * 100
        };
        
        if (performance.form_score < 70) {
            this.provideFormCorrection(exerciseData);
        } else if (performance.form_score > 90) {
            this.providePerfectFormEncouragement();
        }
        
        if (performance.intensity_score < 60) {
            this.suggestIntensityIncrease();
        }
    },
    
    provideFormCorrection(exerciseData) {
        const exerciseName = exerciseData.name.toLowerCase();
        const corrections = this.knowledgeBase.formCorrections[exerciseName];
        
        if (corrections) {
            const randomCorrection = corrections.corrections[Math.floor(Math.random() * corrections.corrections.length)];
            this.showCoachingMessage(
                `🎯 Korekta formy: ${randomCorrection}`,
                'form_correction'
            );
        }
    },
    
    providePerfectFormEncouragement() {
        const messages = [
            '🏆 Perfekcyjna forma! Tak trzymaj!',
            '⭐ Niesamowita technika! Jesteś mistrzem!',
            '💯 Forma na poziomie profesjonalnym!'
        ];
        
        const message = messages[Math.floor(Math.random() * messages.length)];
        this.showCoachingMessage(message, 'perfect_form');
    },
    
    suggestIntensityIncrease() {
        const suggestions = [
            '🔥 Możesz dać więcej z siebie! Zwiększ intensywność!',
            '⚡ Czuję, że masz jeszcze rezerwy. Przyspiesz!',
            '💪 Twoje ciało może więcej! Pokaż swoją siłę!'
        ];
        
        const suggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
        this.showCoachingMessage(suggestion, 'intensity_boost');
    },
    
    endCoachingSession(workoutData) {
        this.currentSession.isActive = false;
        
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
        }
        
        // Provide session summary and motivation
        this.provideSessionSummary(workoutData);
        
        // Update user profile based on session
        this.updateProfileFromSession(workoutData);
        
        // Schedule follow-up motivation
        this.schedulePostWorkoutMotivation();
    },
    
    provideSessionSummary(workoutData) {
        const duration = Math.floor((new Date() - this.currentSession.startTime) / 1000 / 60);
        const motivationalSummary = this.generateMotivationalSummary(workoutData, duration);
        
        this.showDetailedCoachingMessage(motivationalSummary, 'session_summary');
    },
    
    generateMotivationalSummary(workoutData, duration) {
        const achievements = [];
        
        if (duration >= workoutData.targetDuration) {
            achievements.push('✅ Ukończyłeś cały zaplanowany trening!');
        }
        
        if (workoutData.calories > 300) {
            achievements.push('🔥 Wspaniałe spalanie kalorii!');
        }
        
        if (workoutData.exercises >= 6) {
            achievements.push('💪 Wszechstronny trening wszystkich partii!');
        }
        
        const personalizedMessage = this.getPersonalizedMessage();
        
        return {
            title: 'Podsumowanie Treningu 📊',
            achievements: achievements,
            personalMessage: personalizedMessage,
            nextSteps: this.getNextStepsSuggestion(),
            stats: {
                duration: `${duration} minut`,
                calories: `${workoutData.calories} kcal`,
                exercises: `${workoutData.exercises} ćwiczeń`
            }
        };
    },
    
    getPersonalizedMessage() {
        const messages = {
            beginner: 'Każdy trening to ogromny krok do przodu! Jesteś na świetnej drodze! 🌟',
            intermediate: 'Widzę Twoje zaangażowanie i postępy! Kontynuuj tę fantastyczną pracę! 🚀',
            advanced: 'Twoja dedykacja jest inspirująca! Podnosisz poprzeczkę coraz wyżej! 🏆'
        };
        
        return messages[this.userProfile.fitness_level] || messages.intermediate;
    },
    
    getNextStepsSuggestion() {
        const suggestions = [
            'Pamiętaj o regeneracji i nawodnieniu 💧',
            'Zaplanuj odpoczynek między treningami 😴',
            'Śledź swoje postępy w aplikacji 📈',
            'Przygotuj plan na następny trening 📋'
        ];
        
        return suggestions[Math.floor(Math.random() * suggestions.length)];
    },
    
    showDetailedCoachingMessage(summaryData, type) {
        const modal = document.createElement('div');
        modal.className = 'detailed-coach-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
        `;
        
        modal.innerHTML = `
            <div class="coach-summary glass-effect" style="padding: 2rem; border-radius: 1rem; max-width: 600px; width: 90%; text-align: center;">
                <div class="coach-avatar" style="font-size: 4rem; margin-bottom: 1rem;">🤖🏆</div>
                <h2 style="margin-bottom: 1rem; color: var(--text-primary);">${summaryData.title}</h2>
                
                <div class="achievements" style="margin-bottom: 2rem;">
                    <h3 style="margin-bottom: 1rem; color: var(--text-primary);">Twoje Osiągnięcia:</h3>
                    ${summaryData.achievements.map(achievement => `
                        <div style="margin-bottom: 0.5rem; padding: 0.5rem; background: var(--glass-background); border-radius: 0.5rem; color: var(--text-secondary);">
                            ${achievement}
                        </div>
                    `).join('')}
                </div>
                
                <div class="stats-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; margin-bottom: 2rem;">
                    <div style="background: var(--glass-background); padding: 1rem; border-radius: 0.5rem;">
                        <div style="font-size: 1.5rem; font-weight: bold; color: var(--primary-color);">${summaryData.stats.duration}</div>
                        <div style="font-size: 0.875rem; color: var(--text-muted);">Czas</div>
                    </div>
                    <div style="background: var(--glass-background); padding: 1rem; border-radius: 0.5rem;">
                        <div style="font-size: 1.5rem; font-weight: bold; color: var(--accent-color);">${summaryData.stats.calories}</div>
                        <div style="font-size: 0.875rem; color: var(--text-muted);">Kalorie</div>
                    </div>
                    <div style="background: var(--glass-background); padding: 1rem; border-radius: 0.5rem;">
                        <div style="font-size: 1.5rem; font-weight: bold; color: var(--secondary-color);">${summaryData.stats.exercises}</div>
                        <div style="font-size: 0.875rem; color: var(--text-muted);">Ćwiczenia</div>
                    </div>
                </div>
                
                <div class="personal-message" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); padding: 1.5rem; border-radius: 1rem; margin-bottom: 2rem; color: white;">
                    <p style="font-size: 1.1rem; line-height: 1.6;">${summaryData.personalMessage}</p>
                </div>
                
                <div class="next-steps" style="margin-bottom: 2rem;">
                    <p style="color: var(--text-secondary);"><strong>Następny krok:</strong> ${summaryData.nextSteps}</p>
                </div>
                
                <button class="btn-primary" onclick="this.closest('.detailed-coach-modal').remove();">
                    Dziękuję, Trenerze! 🙏
                </button>
            </div>
        `;
        
        document.body.appendChild(modal);
    },
    
    setupRealTimeAnalysis() {
        // Simulated camera-based form analysis
        this.setupFormAnalysis();
        
        // Heart rate monitoring simulation
        this.setupHeartRateMonitoring();
        
        // Fatigue detection
        this.setupFatigueDetection();
    },
    
    setupFormAnalysis() {
        // In a real app, this would connect to camera/pose detection
        document.addEventListener('exerciseInProgress', (e) => {
            // Simulate form analysis every 5 seconds during exercise
            setTimeout(() => {
                this.analyzeFormInRealTime(e.detail);
            }, 5000);
        });
    },
    
    analyzeFormInRealTime(exerciseData) {
        // Simulate AI form analysis
        const formScore = Math.random() * 100;
        const feedback = {
            score: formScore,
            exercise: exerciseData.name,
            timestamp: new Date()
        };
        
        if (formScore < 60) {
            this.provideInstantFormCorrection(exerciseData);
        } else if (formScore > 90) {
            this.provideFormEncouragement();
        }
        
        // Store feedback for later analysis
        this.currentSession.formFeedback.push(feedback);
    },
    
    provideInstantFormCorrection(exerciseData) {
        const corrections = [
            '🎯 Sprawdź pozycję kolan - wypychaj je na zewnątrz',
            '📐 Utrzymaj prostą sylwetkę - nie zaokrąglaj pleców',
            '⚖️ Równomiernie rozłóż ciężar na obie nogi',
            '🎨 Kontroluj tempo - powolna faza ekscentryczna'
        ];
        
        const correction = corrections[Math.floor(Math.random() * corrections.length)];
        this.showRealTimeFeedback({ message: correction, type: 'form_correction' });
    },
    
    provideFormEncouragement() {
        const encouragements = [
            '✨ Perfekcyjna forma! Tak trzymaj!',
            '🏆 Technika na poziomie mistrzowskim!',
            '💎 Precyzyjne wykonanie - brawo!'
        ];
        
        const encouragement = encouragements[Math.floor(Math.random() * encouragements.length)];
        this.showRealTimeFeedback({ message: encouragement, type: 'encouragement' });
    },
    
    // Habit formation coaching
    analyzeHabitPattern() {
        const workoutHistory = FitGenius.utils.loadFromLocalStorage('workout_history') || [];
        const pattern = this.identifyWorkoutPattern(workoutHistory);
        
        return {
            consistency: pattern.consistency,
            preferredTimes: pattern.preferredTimes,
            strongestDays: pattern.strongestDays,
            habitStage: this.assessHabitStage(pattern),
            recommendations: this.generateHabitRecommendations(pattern)
        };
    },
    
    identifyWorkoutPattern(history) {
        if (history.length < 7) {
            return { consistency: 'insufficient_data' };
        }
        
        const dayOfWeek = history.map(w => new Date(w.date).getDay());
        const timeOfDay = history.map(w => new Date(w.date).getHours());
        
        return {
            consistency: this.calculateConsistency(history),
            preferredTimes: this.getMostFrequent(timeOfDay),
            strongestDays: this.getMostFrequent(dayOfWeek),
            weeklyFrequency: history.length / (history.length > 0 ? this.getWeekSpan(history) : 1)
        };
    },
    
    calculateConsistency(history) {
        // Simple consistency calculation based on gaps between workouts
        const dates = history.map(w => new Date(w.date)).sort((a, b) => a - b);
        const gaps = [];
        
        for (let i = 1; i < dates.length; i++) {
            const gap = (dates[i] - dates[i-1]) / (1000 * 60 * 60 * 24);
            gaps.push(gap);
        }
        
        const avgGap = gaps.reduce((sum, gap) => sum + gap, 0) / gaps.length;
        
        if (avgGap <= 2) return 'excellent';
        if (avgGap <= 4) return 'good';
        if (avgGap <= 7) return 'moderate';
        return 'needs_improvement';
    },
    
    getWeekSpan(history) {
        if (history.length === 0) return 1;
        
        const dates = history.map(w => new Date(w.date));
        const minDate = new Date(Math.min(...dates));
        const maxDate = new Date(Math.max(...dates));
        
        return Math.ceil((maxDate - minDate) / (1000 * 60 * 60 * 24 * 7)) || 1;
    },
    
    assessHabitStage(pattern) {
        if (pattern.consistency === 'insufficient_data') {
            return 'unconscious_incompetence';
        }
        
        if (pattern.consistency === 'excellent' && pattern.weeklyFrequency >= 4) {
            return 'unconscious_competence';
        } else if (pattern.consistency === 'good') {
            return 'conscious_competence';
        } else {
            return 'conscious_incompetence';
        }
    },
    
    generateHabitRecommendations(pattern) {
        const stage = this.assessHabitStage(pattern);
        const stageInfo = this.knowledgeBase.habitFormation.stages[stage];
        
        if (!stageInfo) return [];
        
        return stageInfo.techniques.map(technique => {
            return this.getTechniqueGuidance(technique, pattern);
        });
    },
    
    getTechniqueGuidance(technique, pattern) {
        const guidance = {
            goal_setting: 'Ustaw konkretne, mierzalne cele na następny tydzień',
            step_by_step_guidance: 'Podziel złożone ćwiczenia na prostsze kroki',
            repetition: 'Powtarzaj te same ćwiczenia dla wypracowania automatyzmu',
            consistency_building: 'Trenuj o tej samej porze każdego dnia',
            challenge_progression: 'Stopniowo zwiększaj trudność i różnorodność'
        };
        
        return guidance[technique] || 'Kontynuuj obecną praktykę';
    },
    
    // Utility functions
    showCoachingMessage(message, type) {
        const notification = document.createElement('div');
        notification.className = `coaching-message type-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 120px;
            right: 20px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 1rem;
            max-width: 350px;
            z-index: 10001;
            transform: translateX(100%);
            transition: transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
            border-left: 4px solid var(--accent-color);
        `;
        
        notification.innerHTML = `
            <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                <div class="coach-icon" style="font-size: 1.5rem; margin-top: 0.25rem;">🤖</div>
                <div>
                    <div style="font-size: 0.875rem; opacity: 0.8; margin-bottom: 0.25rem;">AI Coach</div>
                    <div style="line-height: 1.4;">${message}</div>
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 400);
        }, 6000);
        
        // Track coaching interaction
        FitGenius.analytics.trackEvent('ai_coaching_message', {
            type: type,
            message_length: message.length,
            timestamp: new Date().toISOString()
        });
    },
    
    scheduleMotivationalMessage(trigger) {
        const messages = {
            morning_energy: [
                '🌅 Dobry poranek! Rozpocznij dzień od ruchu - Twoje ciało będzie Ci wdzięczne!',
                '☀️ Nowy dzień, nowe możliwości! Jaki jest Twój plan treningowy na dziś?'
            ],
            afternoon_boost: [
                '⚡ Popołudniowa przerwa to idealny moment na krótką aktywność!',
                '🏃‍♀️ 15 minut ruchu może całkowicie zmienić Twoją energię!'
            ],
            evening_motivation: [
                '🌙 Wieczorny trening to świetny sposób na rozładowanie stresu!',
                '💪 Zakończ dzień aktywnie - zasłużysz na lepszy sen!'
            ],
            mood_boost: [
                '🌈 Pamiętaj, że każdy mały krok się liczy!',
                '💚 Ruch to naturalny antydepresant - pozwól mu zadziałać!'
            ]
        };
        
        const messageArray = messages[trigger] || messages.mood_boost;
        const message = messageArray[Math.floor(Math.random() * messageArray.length)];
        
        setTimeout(() => {
            this.showCoachingMessage(message, trigger);
        }, Math.random() * 5000 + 2000); // Random delay 2-7 seconds
    },
    
    saveUserProfile() {
        FitGenius.utils.saveToLocalStorage('ai_coach_profile', this.userProfile);
    }
};

// Initialize AI Coach when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    AICoach.init();
    
    // Simulate some events for demonstration
    setTimeout(() => {
        const demoWorkout = {
            name: 'Morning Strength Training',
            exercises: 6,
            targetDuration: 45,
            calories: 320
        };
        
        // Simulate workout start
        document.dispatchEvent(new CustomEvent('workoutStarted', { 
            detail: demoWorkout 
        }));
        
        // Simulate exercise start after 3 seconds
        setTimeout(() => {
            document.dispatchEvent(new CustomEvent('exerciseStarted', { 
                detail: { name: 'Przysiad', reps: 12, sets: 3 }
            }));
        }, 3000);
        
    }, 8000); // Start demo after 8 seconds
});

// Export for global access
window.AICoach = AICoach;