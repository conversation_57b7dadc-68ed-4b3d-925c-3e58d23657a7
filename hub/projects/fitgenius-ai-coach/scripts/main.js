/**
 * FitGenius.pl - Main JavaScript
 * Enterprise-grade AI Fitness Coaching Platform
 */

// Global App Object
const FitGenius = {
    // Configuration
    config: {
        apiEndpoint: '/api/v1',
        version: '1.0.0',
        locale: 'pl',
        analytics: true,
        debug: true
    },
    
    // State Management
    state: {
        user: null,
        currentWorkout: null,
        userData: {
            goals: null,
            equipment: null,
            level: null,
            timePreference: null
        },
        analytics: {
            pageViews: 0,
            workoutsGenerated: 0,
            userEngagement: 0
        }
    },
    
    // Utilities
    utils: {
        formatTime: (minutes) => {
            const hours = Math.floor(minutes / 60);
            const mins = minutes % 60;
            return hours > 0 ? `${hours}h ${mins}min` : `${mins} min`;
        },
        
        formatCalories: (calories) => {
            return new Intl.NumberFormat('pl-PL').format(calories);
        },
        
        formatDate: (date) => {
            return new Intl.DateTimeFormat('pl-PL', {
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            }).format(date);
        },
        
        generateId: () => {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        },
        
        debounce: (func, wait) => {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        throttle: (func, limit) => {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },
        
        saveToLocalStorage: (key, data) => {
            try {
                localStorage.setItem(`fitgenius_${key}`, JSON.stringify(data));
                return true;
            } catch (error) {
                console.error('Error saving to localStorage:', error);
                return false;
            }
        },
        
        loadFromLocalStorage: (key) => {
            try {
                const data = localStorage.getItem(`fitgenius_${key}`);
                return data ? JSON.parse(data) : null;
            } catch (error) {
                console.error('Error loading from localStorage:', error);
                return null;
            }
        },
        
        validateEmail: (email) => {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        },
        
        validatePhone: (phone) => {
            const re = /^[\+]?[1-9][\d]{0,15}$/;
            return re.test(phone.replace(/\s/g, ''));
        },
        
        sanitizeInput: (input) => {
            const div = document.createElement('div');
            div.textContent = input;
            return div.innerHTML;
        }
    },
    
    // Animation Controller
    animations: {
        observerOptions: {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        },
        
        init() {
            this.setupScrollAnimations();
            this.setupCounterAnimations();
            this.setupParallaxEffects();
        },
        
        setupScrollAnimations() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, this.observerOptions);
            
            document.querySelectorAll('.feature-card, .testimonial-card, .pricing-card').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(50px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });
        },
        
        setupCounterAnimations() {
            const counters = document.querySelectorAll('.stat-number[data-target]');
            const counterObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.animateCounter(entry.target);
                        counterObserver.unobserve(entry.target);
                    }
                });
            });
            
            counters.forEach(counter => counterObserver.observe(counter));
        },
        
        animateCounter(element) {
            const target = parseInt(element.dataset.target);
            const duration = 2000;
            const step = target / (duration / 16);
            let current = 0;
            
            const timer = setInterval(() => {
                current += step;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = this.formatCounterValue(Math.floor(current));
            }, 16);
        },
        
        formatCounterValue(value) {
            if (value >= 1000000) {
                return (value / 1000000).toFixed(1) + 'M';
            } else if (value >= 1000) {
                return (value / 1000).toFixed(0) + 'K';
            }
            return value.toString();
        },
        
        setupParallaxEffects() {
            const parallaxElements = document.querySelectorAll('.phone-mockup');
            
            window.addEventListener('scroll', FitGenius.utils.throttle(() => {
                const scrolled = window.pageYOffset;
                parallaxElements.forEach(el => {
                    const rate = scrolled * -0.5;
                    el.style.transform = `perspective(1000px) rotateY(-15deg) rotateX(5deg) translateY(${rate}px)`;
                });
            }, 16));
        }
    },
    
    // Navigation Controller
    navigation: {
        init() {
            this.setupSmoothScrolling();
            this.setupActiveNavigation();
            this.setupMobileMenu();
            this.setupScrollNavbar();
        },
        
        setupSmoothScrolling() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', (e) => {
                    e.preventDefault();
                    const target = document.querySelector(anchor.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        },
        
        setupActiveNavigation() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-link');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        navLinks.forEach(link => link.classList.remove('active'));
                        const activeLink = document.querySelector(`.nav-link[href="#${entry.target.id}"]`);
                        if (activeLink) {
                            activeLink.classList.add('active');
                        }
                    }
                });
            }, { threshold: 0.3 });
            
            sections.forEach(section => observer.observe(section));
        },
        
        setupMobileMenu() {
            const hamburger = document.querySelector('.hamburger');
            const navMenu = document.querySelector('.nav-menu');
            
            if (hamburger && navMenu) {
                hamburger.addEventListener('click', () => {
                    hamburger.classList.toggle('active');
                    navMenu.classList.toggle('active');
                });
            }
        },
        
        setupScrollNavbar() {
            const navbar = document.querySelector('.navbar');
            let lastScrollY = window.scrollY;
            
            window.addEventListener('scroll', () => {
                const currentScrollY = window.scrollY;
                
                if (currentScrollY > 100) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
                
                if (currentScrollY > lastScrollY && currentScrollY > 100) {
                    navbar.style.transform = 'translateY(-100%)';
                } else {
                    navbar.style.transform = 'translateY(0)';
                }
                
                lastScrollY = currentScrollY;
            });
        }
    },
    
    // Form Handlers
    forms: {
        init() {
            this.setupContactForm();
            this.setupValidation();
        },
        
        setupContactForm() {
            const contactForm = document.getElementById('contactForm');
            if (contactForm) {
                contactForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.handleContactSubmission(contactForm);
                });
            }
        },
        
        async handleContactSubmission(form) {
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);
            
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Wysyłanie...';
            submitBtn.disabled = true;
            
            try {
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Show success message
                this.showNotification('Wiadomość została wysłana pomyślnie!', 'success');
                form.reset();
                
                // Track form submission
                FitGenius.analytics.trackEvent('contact_form_submitted', data);
                
            } catch (error) {
                this.showNotification('Wystąpił błąd podczas wysyłania wiadomości.', 'error');
                console.error('Contact form error:', error);
            } finally {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        },
        
        setupValidation() {
            const inputs = document.querySelectorAll('input[required], textarea[required], select[required]');
            
            inputs.forEach(input => {
                input.addEventListener('blur', () => this.validateField(input));
                input.addEventListener('input', () => this.clearFieldError(input));
            });
        },
        
        validateField(field) {
            const value = field.value.trim();
            let isValid = true;
            let errorMessage = '';
            
            if (field.hasAttribute('required') && !value) {
                isValid = false;
                errorMessage = 'To pole jest wymagane';
            } else if (field.type === 'email' && value && !this.isValidEmail(value)) {
                isValid = false;
                errorMessage = 'Wprowadź poprawny adres email';
            }
            
            if (!isValid) {
                this.showFieldError(field, errorMessage);
            } else {
                this.clearFieldError(field);
            }
            
            return isValid;
        },
        
        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },
        
        showFieldError(field, message) {
            this.clearFieldError(field);
            
            field.style.borderColor = '#ef4444';
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.textContent = message;
            errorDiv.style.color = '#ef4444';
            errorDiv.style.fontSize = '0.875rem';
            errorDiv.style.marginTop = '0.25rem';
            
            field.parentNode.appendChild(errorDiv);
        },
        
        clearFieldError(field) {
            field.style.borderColor = '';
            const errorDiv = field.parentNode.querySelector('.field-error');
            if (errorDiv) {
                errorDiv.remove();
            }
        },
        
        showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 0.75rem;
                color: white;
                font-weight: 600;
                z-index: 10000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                backdrop-filter: blur(10px);
            `;
            
            if (type === 'success') {
                notification.style.background = 'linear-gradient(135deg, #10b981, #059669)';
            } else if (type === 'error') {
                notification.style.background = 'linear-gradient(135deg, #ef4444, #dc2626)';
            } else {
                notification.style.background = 'linear-gradient(135deg, #6366f1, #4f46e5)';
            }
            
            notification.textContent = message;
            document.body.appendChild(notification);
            
            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // Auto remove
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 5000);
        }
    },
    
    // Forms Management System
    forms: {
        init() {
            this.setupContactForm();
            this.setupNewsletterForms();
            this.setupPricingForms();
            this.setupUserRegistration();
            this.setupUserLogin();
            console.log('📋 Forms system initialized');
        },
        
        setupContactForm() {
            const contactForm = document.getElementById('contactForm');
            if (!contactForm) return;
            
            contactForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const formData = new FormData(contactForm);
                const data = {
                    name: FitGenius.utils.sanitizeInput(formData.get('name')),
                    email: formData.get('email'),
                    subject: formData.get('subject'),
                    message: FitGenius.utils.sanitizeInput(formData.get('message'))
                };
                
                // Validate form
                if (!this.validateContactForm(data)) {
                    return;
                }
                
                // Show loading state
                const submitBtn = contactForm.querySelector('button[type="submit"]');
                const originalText = submitBtn.textContent;
                submitBtn.textContent = 'Wysyłanie...';
                submitBtn.disabled = true;
                
                try {
                    // Simulate form submission (in production, send to server)
                    await this.simulateFormSubmission();
                    
                    // Store submission locally
                    this.storeContactSubmission(data);
                    
                    // Show success message
                    this.showNotification('Wiadomość została wysłana! Odpowiemy w ciągu 24 godzin.', 'success');
                    
                    // Reset form
                    contactForm.reset();
                    
                    // Track successful submission
                    FitGenius.analytics.trackEvent('contact_form_submitted', {
                        subject: data.subject,
                        timestamp: new Date().toISOString()
                    });
                    
                } catch (error) {
                    console.error('Contact form error:', error);
                    this.showNotification('Wystąpił błąd podczas wysyłania wiadomości. Spróbuj ponownie.', 'error');
                } finally {
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                }
            });
        },
        
        validateContactForm(data) {
            if (!data.name || data.name.length < 2) {
                this.showNotification('Podaj prawidłowe imię (min. 2 znaki)', 'error');
                return false;
            }
            
            if (!data.email || !FitGenius.utils.validateEmail(data.email)) {
                this.showNotification('Podaj prawidłowy adres email', 'error');
                return false;
            }
            
            if (!data.subject) {
                this.showNotification('Wybierz temat wiadomości', 'error');
                return false;
            }
            
            if (!data.message || data.message.length < 10) {
                this.showNotification('Wiadomość musi zawierać minimum 10 znaków', 'error');
                return false;
            }
            
            return true;
        },
        
        setupNewsletterForms() {
            // Find newsletter signup forms (if any)
            const newsletterForms = document.querySelectorAll('[id*="newsletter"], [class*="newsletter"]');
            
            newsletterForms.forEach(form => {
                form.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    
                    const emailInput = form.querySelector('input[type="email"]');
                    if (!emailInput) return;
                    
                    const email = emailInput.value;
                    
                    if (!FitGenius.utils.validateEmail(email)) {
                        this.showNotification('Podaj prawidłowy adres email', 'error');
                        return;
                    }
                    
                    try {
                        await this.simulateFormSubmission();
                        
                        // Store newsletter subscription
                        const subscriptions = FitGenius.utils.loadFromLocalStorage('newsletter_subscriptions') || [];
                        subscriptions.push({
                            email: email,
                            timestamp: new Date().toISOString(),
                            id: FitGenius.utils.generateId()
                        });
                        FitGenius.utils.saveToLocalStorage('newsletter_subscriptions', subscriptions);
                        
                        this.showNotification('Dziękujemy za zapisanie się do newslettera!', 'success');
                        emailInput.value = '';
                        
                        FitGenius.analytics.trackEvent('newsletter_signup', {
                            email_domain: email.split('@')[1],
                            timestamp: new Date().toISOString()
                        });
                        
                    } catch (error) {
                        this.showNotification('Wystąpił błąd. Spróbuj ponownie.', 'error');
                    }
                });
            });
        },
        
        setupPricingForms() {
            // Handle plan selection buttons
            document.querySelectorAll('.plan-cta').forEach(button => {
                button.addEventListener('click', (e) => {
                    const planCard = e.target.closest('.pricing-card');
                    const planName = planCard.querySelector('.plan-name').textContent;
                    const planPrice = planCard.querySelector('.price-amount').textContent;
                    
                    this.showPlanSelectionModal(planName, planPrice);
                    
                    FitGenius.analytics.trackEvent('plan_selected', {
                        plan: planName,
                        price: planPrice
                    });
                });
            });
            
            // Handle pricing toggle
            const annualToggle = document.getElementById('annualToggle');
            if (annualToggle) {
                annualToggle.addEventListener('change', (e) => {
                    this.togglePricingPeriod(e.target.checked);
                    
                    FitGenius.analytics.trackEvent('pricing_toggle', {
                        annual: e.target.checked
                    });
                });
            }
        },
        
        togglePricingPeriod(isAnnual) {
            const monthlyElements = document.querySelectorAll('.monthly');
            const annualElements = document.querySelectorAll('.annual');
            
            monthlyElements.forEach(el => {
                el.style.display = isAnnual ? 'none' : 'inline';
            });
            
            annualElements.forEach(el => {
                el.style.display = isAnnual ? 'inline' : 'none';
            });
        },
        
        showPlanSelectionModal(planName, planPrice) {
            const modal = document.createElement('div');
            modal.className = 'plan-selection-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                backdrop-filter: blur(10px);
            `;
            
            modal.innerHTML = `
                <div class="modal-content glass-effect" style="padding: 2rem; border-radius: 1rem; max-width: 500px; width: 90%; text-align: center;">
                    <h3 style="margin-bottom: 1rem; color: var(--text-primary);">Wybrano Plan: ${planName}</h3>
                    <p style="margin-bottom: 2rem; color: var(--text-secondary);">Cena: ${planPrice} zł</p>
                    
                    <form id="planSignupForm">
                        <div class="form-group" style="margin-bottom: 1rem;">
                            <input type="text" name="fullName" placeholder="Imię i nazwisko" required style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-color); border-radius: 0.5rem; background: var(--input-background);">
                        </div>
                        <div class="form-group" style="margin-bottom: 1rem;">
                            <input type="email" name="email" placeholder="Adres email" required style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-color); border-radius: 0.5rem; background: var(--input-background);">
                        </div>
                        <div class="form-group" style="margin-bottom: 2rem;">
                            <input type="tel" name="phone" placeholder="Numer telefonu (opcjonalnie)" style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-color); border-radius: 0.5rem; background: var(--input-background);">
                        </div>
                        
                        <div style="display: flex; gap: 1rem; justify-content: center;">
                            <button type="submit" class="btn-primary">Rozpocznij bezpłatny okres próbny</button>
                            <button type="button" class="btn-secondary" onclick="this.closest('.plan-selection-modal').remove();">Anuluj</button>
                        </div>
                    </form>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Handle form submission
            const form = modal.querySelector('#planSignupForm');
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const formData = new FormData(form);
                const userData = {
                    fullName: FitGenius.utils.sanitizeInput(formData.get('fullName')),
                    email: formData.get('email'),
                    phone: formData.get('phone'),
                    selectedPlan: planName,
                    selectedPrice: planPrice,
                    timestamp: new Date().toISOString()
                };
                
                if (this.validateSignupForm(userData)) {
                    await this.processSignup(userData);
                    modal.remove();
                }
            });
        },
        
        validateSignupForm(data) {
            if (!data.fullName || data.fullName.length < 3) {
                this.showNotification('Podaj prawidłowe imię i nazwisko (min. 3 znaki)', 'error');
                return false;
            }
            
            if (!data.email || !FitGenius.utils.validateEmail(data.email)) {
                this.showNotification('Podaj prawidłowy adres email', 'error');
                return false;
            }
            
            if (data.phone && !FitGenius.utils.validatePhone(data.phone)) {
                this.showNotification('Podaj prawidłowy numer telefonu', 'error');
                return false;
            }
            
            return true;
        },
        
        async processSignup(userData) {
            try {
                // Show loading
                this.showNotification('Tworzenie konta...', 'info');
                
                // Simulate account creation
                await this.simulateFormSubmission();
                
                // Store user data
                FitGenius.utils.saveToLocalStorage('user_signup_data', userData);
                
                // Track successful signup
                FitGenius.analytics.trackEvent('user_signup', {
                    plan: userData.selectedPlan,
                    timestamp: userData.timestamp
                });
                
                // Show success and redirect
                this.showNotification('Konto zostało utworzone! Sprawdź email w celu aktywacji.', 'success');
                
                // Simulate redirect to dashboard (in production)
                setTimeout(() => {
                    this.showNotification('Przekierowanie do panelu użytkownika...', 'info');
                }, 2000);
                
            } catch (error) {
                console.error('Signup error:', error);
                this.showNotification('Wystąpił błąd podczas tworzenia konta. Spróbuj ponownie.', 'error');
            }
        },
        
        setupUserRegistration() {
            // Handle registration from navigation
            document.querySelectorAll('button').forEach(btn => {
                if (btn.textContent.includes('Rozpocznij za darmo')) {
                    btn.addEventListener('click', () => {
                        this.showRegistrationModal();
                    });
                }
            });
        },
        
        setupUserLogin() {
            // Handle login from navigation
            document.querySelectorAll('button').forEach(btn => {
                if (btn.textContent.includes('Zaloguj się')) {
                    btn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }
            });
        },
        
        showRegistrationModal() {
            const modal = document.createElement('div');
            modal.className = 'registration-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                backdrop-filter: blur(10px);
            `;
            
            modal.innerHTML = `
                <div class="modal-content glass-effect" style="padding: 2rem; border-radius: 1rem; max-width: 400px; width: 90%; text-align: center;">
                    <h3 style="margin-bottom: 1rem; color: var(--text-primary);">Dołącz do FitGenius.pl</h3>
                    <p style="margin-bottom: 2rem; color: var(--text-secondary);">Rozpocznij swoją fitness journey już dziś!</p>
                    
                    <form id="registrationForm">
                        <div class="form-group" style="margin-bottom: 1rem;">
                            <input type="text" name="fullName" placeholder="Imię i nazwisko" required style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-color); border-radius: 0.5rem; background: var(--input-background);">
                        </div>
                        <div class="form-group" style="margin-bottom: 1rem;">
                            <input type="email" name="email" placeholder="Adres email" required style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-color); border-radius: 0.5rem; background: var(--input-background);">
                        </div>
                        <div class="form-group" style="margin-bottom: 2rem;">
                            <input type="password" name="password" placeholder="Hasło (min. 8 znaków)" required style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-color); border-radius: 0.5rem; background: var(--input-background);">
                        </div>
                        
                        <div style="display: flex; gap: 1rem; justify-content: center;">
                            <button type="submit" class="btn-primary">Utwórz konto</button>
                            <button type="button" class="btn-secondary" onclick="this.closest('.registration-modal').remove();">Anuluj</button>
                        </div>
                    </form>
                    
                    <p style="margin-top: 1rem; font-size: 0.875rem; color: var(--text-muted);">
                        Masz już konto? <button type="button" onclick="this.closest('.registration-modal').remove(); FitGenius.forms.showLoginModal();" style="background: none; border: none; color: var(--primary-color); cursor: pointer; text-decoration: underline;">Zaloguj się</button>
                    </p>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Handle form submission
            const form = modal.querySelector('#registrationForm');
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleRegistration(form, modal);
            });
        },
        
        showLoginModal() {
            const modal = document.createElement('div');
            modal.className = 'login-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                backdrop-filter: blur(10px);
            `;
            
            modal.innerHTML = `
                <div class="modal-content glass-effect" style="padding: 2rem; border-radius: 1rem; max-width: 400px; width: 90%; text-align: center;">
                    <h3 style="margin-bottom: 1rem; color: var(--text-primary);">Zaloguj się do FitGenius.pl</h3>
                    <p style="margin-bottom: 2rem; color: var(--text-secondary);">Witamy z powrotem!</p>
                    
                    <form id="loginForm">
                        <div class="form-group" style="margin-bottom: 1rem;">
                            <input type="email" name="email" placeholder="Adres email" required style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-color); border-radius: 0.5rem; background: var(--input-background);">
                        </div>
                        <div class="form-group" style="margin-bottom: 2rem;">
                            <input type="password" name="password" placeholder="Hasło" required style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-color); border-radius: 0.5rem; background: var(--input-background);">
                        </div>
                        
                        <div style="display: flex; gap: 1rem; justify-content: center;">
                            <button type="submit" class="btn-primary">Zaloguj się</button>
                            <button type="button" class="btn-secondary" onclick="this.closest('.login-modal').remove();">Anuluj</button>
                        </div>
                    </form>
                    
                    <p style="margin-top: 1rem; font-size: 0.875rem; color: var(--text-muted);">
                        Nie masz konta? <button type="button" onclick="this.closest('.login-modal').remove(); FitGenius.forms.showRegistrationModal();" style="background: none; border: none; color: var(--primary-color); cursor: pointer; text-decoration: underline;">Zarejestruj się</button>
                    </p>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Handle form submission
            const form = modal.querySelector('#loginForm');
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleLogin(form, modal);
            });
        },
        
        async handleRegistration(form, modal) {
            const formData = new FormData(form);
            const userData = {
                fullName: FitGenius.utils.sanitizeInput(formData.get('fullName')),
                email: formData.get('email'),
                password: formData.get('password')
            };
            
            if (!this.validateRegistrationForm(userData)) return;
            
            try {
                this.showNotification('Tworzenie konta...', 'info');
                await this.simulateFormSubmission();
                
                // Store user data
                FitGenius.utils.saveToLocalStorage('user_data', {
                    ...userData,
                    id: FitGenius.utils.generateId(),
                    registeredAt: new Date().toISOString(),
                    plan: 'starter'
                });
                
                FitGenius.analytics.trackEvent('user_registered', {
                    method: 'email',
                    timestamp: new Date().toISOString()
                });
                
                modal.remove();
                this.showNotification('Konto zostało utworzone! Witamy w FitGenius.pl!', 'success');
                
            } catch (error) {
                this.showNotification('Wystąpił błąd. Spróbuj ponownie.', 'error');
            }
        },
        
        async handleLogin(form, modal) {
            const formData = new FormData(form);
            const loginData = {
                email: formData.get('email'),
                password: formData.get('password')
            };
            
            if (!this.validateLoginForm(loginData)) return;
            
            try {
                this.showNotification('Logowanie...', 'info');
                await this.simulateFormSubmission();
                
                // Simulate successful login
                FitGenius.utils.saveToLocalStorage('user_session', {
                    email: loginData.email,
                    loginTime: new Date().toISOString(),
                    sessionId: FitGenius.utils.generateId()
                });
                
                FitGenius.analytics.trackEvent('user_logged_in', {
                    method: 'email',
                    timestamp: new Date().toISOString()
                });
                
                modal.remove();
                this.showNotification('Zalogowano pomyślnie! Witamy z powrotem!', 'success');
                
            } catch (error) {
                this.showNotification('Nieprawidłowe dane logowania.', 'error');
            }
        },
        
        validateRegistrationForm(data) {
            if (!data.fullName || data.fullName.length < 3) {
                this.showNotification('Podaj prawidłowe imię i nazwisko', 'error');
                return false;
            }
            
            if (!data.email || !FitGenius.utils.validateEmail(data.email)) {
                this.showNotification('Podaj prawidłowy adres email', 'error');
                return false;
            }
            
            if (!data.password || data.password.length < 8) {
                this.showNotification('Hasło musi mieć minimum 8 znaków', 'error');
                return false;
            }
            
            return true;
        },
        
        validateLoginForm(data) {
            if (!data.email || !FitGenius.utils.validateEmail(data.email)) {
                this.showNotification('Podaj prawidłowy adres email', 'error');
                return false;
            }
            
            if (!data.password) {
                this.showNotification('Podaj hasło', 'error');
                return false;
            }
            
            return true;
        },
        
        async simulateFormSubmission() {
            // Simulate network delay
            return new Promise(resolve => {
                setTimeout(resolve, 1000 + Math.random() * 2000);
            });
        },
        
        storeContactSubmission(data) {
            const submissions = FitGenius.utils.loadFromLocalStorage('contact_submissions') || [];
            submissions.push({
                ...data,
                id: FitGenius.utils.generateId(),
                timestamp: new Date().toISOString(),
                status: 'pending'
            });
            FitGenius.utils.saveToLocalStorage('contact_submissions', submissions);
        },
        
        showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = 'form-notification';
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 0.5rem;
                color: white;
                font-weight: 600;
                z-index: 10002;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                backdrop-filter: blur(10px);
                max-width: 350px;
            `;
            
            if (type === 'success') {
                notification.style.background = 'linear-gradient(135deg, #10b981, #059669)';
            } else if (type === 'error') {
                notification.style.background = 'linear-gradient(135deg, #ef4444, #dc2626)';
            } else {
                notification.style.background = 'linear-gradient(135deg, #6366f1, #4f46e5)';
            }
            
            notification.textContent = message;
            document.body.appendChild(notification);
            
            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // Auto remove
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 5000);
        }
    },
    
    // Analytics & Tracking
    analytics: {
        init() {
            this.trackPageView();
            this.setupEngagementTracking();
            this.setupPerformanceTracking();
        },
        
        trackPageView() {
            FitGenius.state.analytics.pageViews++;
            this.trackEvent('page_view', {
                url: window.location.href,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent
            });
        },
        
        trackEvent(eventName, eventData = {}) {
            if (!FitGenius.config.analytics) return;
            
            const event = {
                name: eventName,
                data: eventData,
                timestamp: new Date().toISOString(),
                sessionId: this.getSessionId(),
                userId: this.getUserId()
            };
            
            // Store event locally (in production, send to analytics service)
            const events = FitGenius.utils.loadFromLocalStorage('analytics_events') || [];
            events.push(event);
            FitGenius.utils.saveToLocalStorage('analytics_events', events);
            
            if (FitGenius.config.debug) {
                console.log('Analytics Event:', event);
            }
        },
        
        setupEngagementTracking() {
            // Track scroll depth
            let maxScrollDepth = 0;
            const trackScrollDepth = FitGenius.utils.throttle(() => {
                const scrollDepth = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
                if (scrollDepth > maxScrollDepth) {
                    maxScrollDepth = scrollDepth;
                    this.trackEvent('scroll_depth', { depth: scrollDepth });
                }
            }, 1000);
            
            window.addEventListener('scroll', trackScrollDepth);
            
            // Track time on page
            let startTime = Date.now();
            window.addEventListener('beforeunload', () => {
                const timeOnPage = Math.round((Date.now() - startTime) / 1000);
                this.trackEvent('time_on_page', { seconds: timeOnPage });
            });
            
            // Track button clicks
            document.addEventListener('click', (e) => {
                if (e.target.matches('.btn-primary, .btn-secondary, .feature-cta, .plan-cta')) {
                    this.trackEvent('button_click', {
                        buttonText: e.target.textContent.trim(),
                        buttonClass: e.target.className,
                        section: e.target.closest('section')?.id || 'unknown'
                    });
                }
            });
        },
        
        setupPerformanceTracking() {
            // Track page load performance
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    this.trackEvent('page_performance', {
                        loadTime: Math.round(perfData.loadEventEnd - perfData.loadEventStart),
                        domContentLoaded: Math.round(perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart),
                        firstPaint: Math.round(performance.getEntriesByType('paint')[0]?.startTime || 0)
                    });
                }, 1000);
            });
        },
        
        getSessionId() {
            let sessionId = sessionStorage.getItem('fitgenius_session_id');
            if (!sessionId) {
                sessionId = FitGenius.utils.generateId();
                sessionStorage.setItem('fitgenius_session_id', sessionId);
            }
            return sessionId;
        },
        
        getUserId() {
            let userId = FitGenius.utils.loadFromLocalStorage('user_id');
            if (!userId) {
                userId = FitGenius.utils.generateId();
                FitGenius.utils.saveToLocalStorage('user_id', userId);
            }
            return userId;
        }
    },
    
    // Feature Controllers
    features: {
        init() {
            this.setupPricingToggle();
            this.setupFeatureCards();
            this.setupTestimonialCarousel();
        },
        
        setupPricingToggle() {
            const annualToggle = document.getElementById('annualToggle');
            if (annualToggle) {
                annualToggle.addEventListener('change', (e) => {
                    const isAnnual = e.target.checked;
                    const monthlyElements = document.querySelectorAll('.monthly');
                    const annualElements = document.querySelectorAll('.annual');
                    
                    monthlyElements.forEach(el => {
                        el.style.display = isAnnual ? 'none' : 'inline';
                    });
                    
                    annualElements.forEach(el => {
                        el.style.display = isAnnual ? 'inline' : 'none';
                    });
                    
                    FitGenius.analytics.trackEvent('pricing_toggle', { 
                        mode: isAnnual ? 'annual' : 'monthly' 
                    });
                });
            }
        },
        
        setupFeatureCards() {
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach(card => {
                card.addEventListener('click', () => {
                    const feature = card.dataset.feature;
                    this.showFeatureModal(feature);
                    
                    FitGenius.analytics.trackEvent('feature_card_click', { 
                        feature: feature 
                    });
                });
            });
        },
        
        showFeatureModal(feature) {
            // Create modal overlay
            const modal = document.createElement('div');
            modal.className = 'feature-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                backdrop-filter: blur(10px);
            `;
            
            const modalContent = document.createElement('div');
            modalContent.className = 'modal-content glass-effect';
            modalContent.style.cssText = `
                max-width: 600px;
                width: 90%;
                padding: 2rem;
                border-radius: 1rem;
                text-align: center;
                position: relative;
            `;
            
            modalContent.innerHTML = this.getFeatureModalContent(feature);
            modal.appendChild(modalContent);
            document.body.appendChild(modal);
            
            // Close modal functionality
            const closeModal = () => {
                document.body.removeChild(modal);
            };
            
            modal.addEventListener('click', (e) => {
                if (e.target === modal) closeModal();
            });
            
            const closeBtn = modalContent.querySelector('.close-btn');
            if (closeBtn) {
                closeBtn.addEventListener('click', closeModal);
            }
            
            // Animate in
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.opacity = '1';
                modal.style.transition = 'opacity 0.3s ease';
            }, 10);
        },
        
        getFeatureModalContent(feature) {
            const features = {
                training: {
                    title: 'Spersonalizowane Treningi AI',
                    description: 'Nasza sztuczna inteligencja analizuje Twoje cele, dostępny sprzęt i preferencje czasowe, aby stworzyć idealny plan treningowy.',
                    benefits: [
                        'Automatyczne dostosowanie do Twojego poziomu',
                        'Progresywne przeciążenie oparte na danych',
                        'Analiza techniki przez kamerę telefonu',
                        'Prewencja kontuzji i optymalizacja regeneracji'
                    ]
                },
                nutrition: {
                    title: 'Inteligentne Odżywianie',
                    description: 'Kompleksowy system planowania żywienia z polską bazą produktów i receptur.',
                    benefits: [
                        'Spersonalizowane plany żywieniowe',
                        'Baza 50,000+ polskich produktów',
                        'Generator przepisów z listą zakupów',
                        'Śledzenie makroelementów w czasie rzeczywistym'
                    ]
                },
                analytics: {
                    title: 'Zaawansowane Analizy',
                    description: 'Szczegółowy wgląd w Twoje postępy dzięki zaawansowanym algorytmom AI.',
                    benefits: [
                        'Analiza składu ciała',
                        'Predykcja wyników treningowych',
                        'Optymalizacja czasu regeneracji',
                        'Personalne rekomendacje rozwoju'
                    ]
                },
                gamification: {
                    title: 'Gamifikacja & Społeczność',
                    description: 'System motywacyjny oparty na grywalizacji i wsparciu społeczności.',
                    benefits: [
                        'System odznak i osiągnięć',
                        'Wyzwania społecznościowe',
                        'Śledzenie serii treningowych',
                        'Matching z partnerami treningowymi'
                    ]
                },
                coaching: {
                    title: 'AI Coaching 24/7',
                    description: 'Inteligentny trener osobisty dostępny przez całą dobę.',
                    benefits: [
                        'Real-time korekta treningu',
                        'Motywacyjne wiadomości',
                        'Strategie przełamywania plateau',
                        'Psychologia kształtowania nawyków'
                    ]
                },
                form: {
                    title: 'AI Form Checker',
                    description: 'Rewolucyjna technologia sprawdzania techniki ćwiczeń.',
                    benefits: [
                        'Analiza ruchu w czasie rzeczywistym',
                        'Korekta błędów na żywo',
                        'Zapobieganie kontuzjom',
                        'Optymalizacja efektywności ćwiczeń'
                    ]
                }
            };
            
            const featureData = features[feature] || features.training;
            
            return `
                <button class="close-btn" style="position: absolute; top: 1rem; right: 1rem; background: none; border: none; font-size: 1.5rem; cursor: pointer; color: var(--text-primary);">✕</button>
                <h2 style="margin-bottom: 1rem; color: var(--text-primary);">${featureData.title}</h2>
                <p style="margin-bottom: 2rem; color: var(--text-secondary); line-height: 1.6;">${featureData.description}</p>
                <ul style="text-align: left; margin-bottom: 2rem; list-style: none;">
                    ${featureData.benefits.map(benefit => `
                        <li style="padding: 0.5rem 0; color: var(--text-secondary); position: relative; padding-left: 2rem;">
                            <span style="position: absolute; left: 0; color: var(--accent-color);">✨</span>
                            ${benefit}
                        </li>
                    `).join('')}
                </ul>
                <button class="btn-primary" onclick="this.closest('.feature-modal').remove();">Rozpocznij teraz</button>
            `;
        },
        
        setupTestimonialCarousel() {
            // Auto-rotate testimonials if needed
            const testimonials = document.querySelectorAll('.testimonial-card');
            if (testimonials.length > 3) {
                // Implement carousel functionality
                let currentIndex = 0;
                setInterval(() => {
                    testimonials[currentIndex].style.opacity = '0.5';
                    currentIndex = (currentIndex + 1) % testimonials.length;
                    testimonials[currentIndex].style.opacity = '1';
                }, 5000);
            }
        }
    },
    
    // Loading Controller
    loading: {
        init() {
            this.simulateLoading();
        },
        
        simulateLoading() {
            const loadingScreen = document.getElementById('loadingScreen');
            const progressFill = loadingScreen?.querySelector('.progress-fill');
            const progressText = loadingScreen?.querySelector('.progress-text');
            
            if (!loadingScreen) return;
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    
                    setTimeout(() => {
                        loadingScreen.style.opacity = '0';
                        setTimeout(() => {
                            loadingScreen.style.display = 'none';
                        }, 500);
                    }, 500);
                }
                
                if (progressFill) progressFill.style.width = `${progress}%`;
                if (progressText) progressText.textContent = `${Math.round(progress)}%`;
            }, 100);
        }
    },
    
    // Initialization
    init() {
        // Wait for DOM to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeApp());
        } else {
            this.initializeApp();
        }
    },
    
    initializeApp() {
        console.log('🏋️ FitGenius.pl - Initializing...');
        
        try {
            // Initialize all modules
            this.loading.init();
            this.navigation.init();
            this.animations.init();
            this.forms.init();
            this.features.init();
            this.analytics.init();
            
            // Load user data
            this.loadUserData();
            
            // Setup error handling
            this.setupGlobalErrorHandling();
            
            console.log('✅ FitGenius.pl - Initialized successfully');
            
        } catch (error) {
            console.error('❌ FitGenius.pl - Initialization failed:', error);
            this.handleInitializationError(error);
        }
    },
    
    loadUserData() {
        const userData = this.utils.loadFromLocalStorage('user_data');
        if (userData) {
            this.state.userData = { ...this.state.userData, ...userData };
        }
    },
    
    setupGlobalErrorHandling() {
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            this.analytics.trackEvent('javascript_error', {
                message: event.error.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.analytics.trackEvent('promise_rejection', {
                reason: event.reason.toString()
            });
        });
    },
    
    handleInitializationError(error) {
        // Show fallback UI
        document.body.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100vh; text-align: center; background: var(--background-main); color: var(--text-primary);">
                <div>
                    <h1>🏋️ FitGenius.pl</h1>
                    <p>Wystąpił problem z ładowaniem aplikacji.</p>
                    <button onclick="location.reload()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: var(--primary-color); color: white; border: none; border-radius: 0.5rem; cursor: pointer;">
                        Odśwież stronę
                    </button>
                </div>
            </div>
        `;
    }
};

// Initialize the application
FitGenius.init();