/**
 * FitGenius.pl - AI Workout Generator
 * Advanced workout generation with personalization
 */

const WorkoutGenerator = {
    // LLM Integration
    llm: null,
    
    // Exercise Database with Polish names
    exerciseDatabase: {
        strength: {
            none: [ // No equipment
                {
                    name: 'Pomp<PERSON> klasyczne',
                    icon: '💪',
                    difficulty: 'beginner',
                    muscleGroups: ['klatka piersiowa', 'triceps', 'core'],
                    instructions: ['<PERSON>ł<PERSON>ż się w pozycji deski', 'Opuść ciało do podłogi', 'Wypchnij się do góry'],
                    reps: '8-15',
                    sets: '3-4',
                    calories: 8
                },
                {
                    name: '<PERSON><PERSON><PERSON><PERSON><PERSON> klas<PERSON>',
                    icon: '🏋️',
                    difficulty: 'beginner',
                    muscleGroups: ['nogi', 'pośladki'],
                    instructions: ['Sta<PERSON> w rozkroku', 'Opuść biodra jak do siedzenia', '<PERSON>r<PERSON><PERSON> do pozycji startowej'],
                    reps: '10-20',
                    sets: '3-4',
                    calories: 12
                },
                {
                    name: '<PERSON><PERSON> (plank)',
                    icon: '🏆',
                    difficulty: 'beginner',
                    muscleGroups: ['core', 'ramiona'],
                    instructions: ['Przyjmij pozycję deski', 'Utrzymaj napięte ciało', 'Oddychaj spokojnie'],
                    reps: '30-60s',
                    sets: '3',
                    calories: 6
                },
                {
                    name: 'Wykroki',
                    icon: '🚶',
                    difficulty: 'intermediate',
                    muscleGroups: ['nogi', 'pośladki', 'stabilizacja'],
                    instructions: ['Zrób duży krok do przodu', 'Opuść kolano tylnej nogi', 'Wróć do pozycji startowej'],
                    reps: '8-12 (każda noga)',
                    sets: '3',
                    calories: 10
                },
                {
                    name: 'Burpees',
                    icon: '💥',
                    difficulty: 'advanced',
                    muscleGroups: ['całe ciało'],
                    instructions: ['Przysiad, ręce na podłogę', 'Skok nogami do tyłu', 'Pompka, skok do góry'],
                    reps: '5-15',
                    sets: '3-4',
                    calories: 15
                },
                {
                    name: 'Mountain Climbers',
                    icon: '⛰️',
                    difficulty: 'intermediate',
                    muscleGroups: ['core', 'cardio'],
                    instructions: ['Pozycja deski', 'Naprzemienne przyciąganie kolan', 'Szybkie tempo'],
                    reps: '20-40',
                    sets: '3',
                    calories: 12
                },
                {
                    name: 'Skręty rosyjskie',
                    icon: '🌀',
                    difficulty: 'intermediate',
                    muscleGroups: ['core', 'ukośne brzucha'],
                    instructions: ['Siądź z uniesionymi nogami', 'Skręcaj tors na boki', 'Dotykaj podłogi obok bioder'],
                    reps: '20-30',
                    sets: '3',
                    calories: 8
                }
            ],
            basic: [ // Basic equipment (dumbbells, resistance bands)
                {
                    name: 'Wyciskanie hantli leżąc',
                    icon: '🏋️‍♂️',
                    difficulty: 'intermediate',
                    muscleGroups: ['klatka piersiowa', 'triceps', 'ramiona'],
                    instructions: ['Połóż się na ławce lub podłodze', 'Wyciśnij hantle do góry', 'Kontroluj ruch w dół'],
                    reps: '8-12',
                    sets: '3-4',
                    calories: 12,
                    equipment: 'hantle'
                },
                {
                    name: 'Przysiady z hantlami',
                    icon: '🏆',
                    difficulty: 'intermediate',
                    muscleGroups: ['nogi', 'pośladki'],
                    instructions: ['Trzymaj hantle przy barkach', 'Wykonaj głęboki przysiad', 'Wróć do pozycji startowej'],
                    reps: '10-15',
                    sets: '3-4',
                    calories: 15,
                    equipment: 'hantle'
                },
                {
                    name: 'Wiosłowanie hantelką',
                    icon: '🚣',
                    difficulty: 'intermediate',
                    muscleGroups: ['plecy', 'biceps'],
                    instructions: ['Oprzyj kolano na ławce', 'Pociągnij hantel do tulowia', 'Kontroluj ruch w dół'],
                    reps: '8-12 (każda ręka)',
                    sets: '3',
                    calories: 10,
                    equipment: 'hantle'
                },
                {
                    name: 'Uginanie bicepsów',
                    icon: '💪',
                    difficulty: 'beginner',
                    muscleGroups: ['biceps', 'przedramiona'],
                    instructions: ['Stań prosto z hantlami', 'Uginaj ramiona do barków', 'Kontroluj ruch w dół'],
                    reps: '10-15',
                    sets: '3',
                    calories: 8,
                    equipment: 'hantle'
                },
                {
                    name: 'Martwy ciąg rumuński',
                    icon: '⬇️',
                    difficulty: 'intermediate',
                    muscleGroups: ['plecy', 'pośladki', 'uda tylne'],
                    instructions: ['Trzymaj hantle przed sobą', 'Pochyl się w biodrach', 'Wróć do pionu napinając pośladki'],
                    reps: '8-12',
                    sets: '3-4',
                    calories: 14,
                    equipment: 'hantle'
                }
            ],
            'home-gym': [ // Home gym equipment
                {
                    name: 'Przysiad ze sztangą',
                    icon: '🏋️‍♀️',
                    difficulty: 'advanced',
                    muscleGroups: ['nogi', 'core', 'plecy'],
                    instructions: ['Sztanga na trapezach', 'Głęboki przysiad', 'Mocne wybicie do góry'],
                    reps: '5-10',
                    sets: '4-5',
                    calories: 20,
                    equipment: 'sztanga'
                },
                {
                    name: 'Wyciskanie na ławce',
                    icon: '💺',
                    difficulty: 'intermediate',
                    muscleGroups: ['klatka piersiowa', 'triceps', 'ramiona'],
                    instructions: ['Połóż się na ławce', 'Chwyć sztangę szerzej od barków', 'Wyciśnij i kontroluj opuszczanie'],
                    reps: '6-12',
                    sets: '3-4',
                    calories: 16,
                    equipment: 'sztanga + ławka'
                },
                {
                    name: 'Martwy ciąg',
                    icon: '⚡',
                    difficulty: 'advanced',
                    muscleGroups: ['plecy', 'nogi', 'core'],
                    instructions: ['Sztanga na podłodze', 'Chwyć i podnieś mocą nóg', 'Wyprostuj się całkowicie'],
                    reps: '3-8',
                    sets: '3-5',
                    calories: 25,
                    equipment: 'sztanga'
                },
                {
                    name: 'Podciąganie na drążku',
                    icon: '🔝',
                    difficulty: 'advanced',
                    muscleGroups: ['plecy', 'biceps'],
                    instructions: ['Zwiś na drążku', 'Podciągnij się ponad drążek', 'Kontroluj opuszczanie'],
                    reps: '3-10',
                    sets: '3-4',
                    calories: 12,
                    equipment: 'drążek'
                }
            ],
            'full-gym': [ // Full gym equipment
                {
                    name: 'Wyciskanie na maszynie',
                    icon: '🔧',
                    difficulty: 'beginner',
                    muscleGroups: ['klatka piersiowa', 'triceps'],
                    instructions: ['Ustaw odpowiednią wysokość', 'Wyciśnij płynnym ruchem', 'Kontroluj fazę negatywną'],
                    reps: '8-15',
                    sets: '3-4',
                    calories: 14,
                    equipment: 'maszyna do wyciskania'
                },
                {
                    name: 'Wiosłowanie na wyciągu',
                    icon: '📡',
                    difficulty: 'intermediate',
                    muscleGroups: ['plecy', 'biceps'],
                    instructions: ['Siądź na wyciągu', 'Pociągnij rękojeść do brzucha', 'Ściągnij łopatki'],
                    reps: '10-15',
                    sets: '3',
                    calories: 12,
                    equipment: 'wyciąg dolny'
                },
                {
                    name: 'Leg Press',
                    icon: '🦵',
                    difficulty: 'intermediate',
                    muscleGroups: ['nogi', 'pośladki'],
                    instructions: ['Ustaw stopy na platformie', 'Opuść kolana do klatki', 'Wypchnij mocno'],
                    reps: '12-20',
                    sets: '3-4',
                    calories: 18,
                    equipment: 'maszyna leg press'
                }
            ]
        },
        endurance: {
            none: [
                {
                    name: 'Bieg w miejscu',
                    icon: '🏃‍♂️',
                    difficulty: 'beginner',
                    muscleGroups: ['cardio', 'nogi'],
                    instructions: ['Bieg w miejscu', 'Wysokie kolana', 'Utrzymaj rytm'],
                    reps: '30-60s',
                    sets: '5-8',
                    calories: 10
                },
                {
                    name: 'Jumping Jacks',
                    icon: '🤸',
                    difficulty: 'beginner',
                    muscleGroups: ['cardio', 'całe ciało'],
                    instructions: ['Skok z rozstawieniem nóg', 'Ręce nad głowę', 'Wróć do pozycji startowej'],
                    reps: '20-50',
                    sets: '3-5',
                    calories: 8
                },
                {
                    name: 'High Knees',
                    icon: '🦵',
                    difficulty: 'intermediate',
                    muscleGroups: ['cardio', 'core'],
                    instructions: ['Bieg z wysokimi kolanami', 'Dynamiczny ruch', 'Szybkie tempo'],
                    reps: '30-45s',
                    sets: '4-6',
                    calories: 12
                },
                {
                    name: 'Shadow Boxing',
                    icon: '🥊',
                    difficulty: 'intermediate',
                    muscleGroups: ['cardio', 'ramiona', 'core'],
                    instructions: ['Boks w powietrzu', 'Kombinacje ciosów', 'Aktywne nogi'],
                    reps: '60-90s',
                    sets: '3-4',
                    calories: 15
                }
            ],
            basic: [
                {
                    name: 'Skakanka',
                    icon: '🪢',
                    difficulty: 'intermediate',
                    muscleGroups: ['cardio', 'łydki', 'koordynacja'],
                    instructions: ['Skacz przez linę', 'Lekkie odbicia', 'Równomierny rytm'],
                    reps: '60-180s',
                    sets: '3-5',
                    calories: 20,
                    equipment: 'skakanka'
                },
                {
                    name: 'Trening z opaską',
                    icon: '🔗',
                    difficulty: 'intermediate',
                    muscleGroups: ['nogi', 'pośladki'],
                    instructions: ['Opaska na nogach', 'Kroki w bok', 'Napięcie przez cały czas'],
                    reps: '15-25',
                    sets: '3-4',
                    calories: 12,
                    equipment: 'opaska fitness'
                }
            ]
        },
        'weight-loss': {
            none: [
                {
                    name: 'HIIT Burpees',
                    icon: '🔥',
                    difficulty: 'advanced',
                    muscleGroups: ['całe ciało'],
                    instructions: ['20s burpees', '10s odpoczynku', 'Maksymalne tempo'],
                    reps: '20s/10s x 8',
                    sets: '1',
                    calories: 25
                },
                {
                    name: 'Tabata Przysiady',
                    icon: '⚡',
                    difficulty: 'intermediate',
                    muscleGroups: ['nogi', 'cardio'],
                    instructions: ['20s przysiady', '10s odpoczynku', 'Maksymalnie szybko'],
                    reps: '20s/10s x 8',
                    sets: '1',
                    calories: 20
                },
                {
                    name: 'Plank Jacks',
                    icon: '💥',
                    difficulty: 'intermediate',
                    muscleGroups: ['core', 'cardio'],
                    instructions: ['Pozycja deski', 'Skoki nogami', 'Utrzymaj stabilność core'],
                    reps: '15-30',
                    sets: '3-4',
                    calories: 14
                }
            ]
        },
        'muscle-gain': {
            basic: [
                {
                    name: 'Pompki diamentowe',
                    icon: '💎',
                    difficulty: 'advanced',
                    muscleGroups: ['triceps', 'klatka piersiowa'],
                    instructions: ['Ręce w kształcie diamentu', 'Wąski chwyt', 'Kontrolowany ruch'],
                    reps: '5-12',
                    sets: '3-4',
                    calories: 10
                },
                {
                    name: 'Pistol Squats',
                    icon: '🔫',
                    difficulty: 'advanced',
                    muscleGroups: ['nogi', 'stabilizacja'],
                    instructions: ['Przysiad na jednej nodze', 'Druga noga wyprostowana', 'Pełny zakres ruchu'],
                    reps: '3-8 (każda noga)',
                    sets: '3',
                    calories: 15
                },
                {
                    name: 'Pike Push-ups',
                    icon: '📐',
                    difficulty: 'intermediate',
                    muscleGroups: ['ramiona', 'core'],
                    instructions: ['Pozycja V odwrócone', 'Pompki ku ziemi', 'Nacisk na ramiona'],
                    reps: '5-12',
                    sets: '3',
                    calories: 12
                }
            ]
        }
    },
    
    // Current workout state
    currentWorkout: {
        goal: null,
        time: null,
        equipment: null,
        level: null,
        exercises: [],
        estimatedCalories: 0,
        difficulty: 'średni'
    },
    
    // Form state
    currentStep: 1,
    maxSteps: 4,
    
    // Initialize workout generator
    init() {
        this.setupEventListeners();
        this.loadSavedPreferences();
        this.initializeLLM();
        console.log('💪 Workout Generator initialized');
    },
    
    initializeLLM() {
        // Wait for LLM integration to be available
        const checkLLM = () => {
            if (window.LLMIntegration) {
                this.llm = window.LLMIntegration;
                console.log('🧠 LLM integration connected to Workout Generator');
            } else {
                setTimeout(checkLLM, 100);
            }
        };
        checkLLM();
    },
    
    setupEventListeners() {
        // Navigation buttons
        const nextBtn = document.getElementById('nextStep');
        const prevBtn = document.getElementById('prevStep');
        const generateBtn = document.getElementById('generateWorkout');
        
        if (nextBtn) {
            nextBtn.addEventListener('click', () => this.nextStep());
        }
        
        if (prevBtn) {
            prevBtn.addEventListener('click', () => this.prevStep());
        }
        
        if (generateBtn) {
            generateBtn.addEventListener('click', () => this.generateWorkout());
        }
        
        // Option selections
        this.setupOptionSelections();
        
        // Assessment button
        const startAssessment = document.getElementById('startAssessment');
        if (startAssessment) {
            startAssessment.addEventListener('click', () => {
                const generatorSection = document.getElementById('workout-generator');
                if (generatorSection) {
                    generatorSection.scrollIntoView({ behavior: 'smooth' });
                    
                    // Track assessment start
                    FitGenius.analytics.trackEvent('fitness_assessment_started');
                }
            });
        }
        
        // Workout actions
        this.setupWorkoutActions();
    },
    
    setupOptionSelections() {
        // Goal selection
        document.querySelectorAll('.goal-option').forEach(option => {
            option.addEventListener('click', () => {
                this.selectOption('goal', option);
                this.currentWorkout.goal = option.dataset.goal;
            });
        });
        
        // Time selection
        document.querySelectorAll('.time-option').forEach(option => {
            option.addEventListener('click', () => {
                this.selectOption('time', option);
                this.currentWorkout.time = parseInt(option.dataset.time);
            });
        });
        
        // Equipment selection
        document.querySelectorAll('.equipment-option').forEach(option => {
            option.addEventListener('click', () => {
                this.selectOption('equipment', option);
                this.currentWorkout.equipment = option.dataset.equipment;
            });
        });
        
        // Level selection
        document.querySelectorAll('.level-option').forEach(option => {
            option.addEventListener('click', () => {
                this.selectOption('level', option);
                this.currentWorkout.level = option.dataset.level;
            });
        });
    },
    
    setupWorkoutActions() {
        const startWorkoutBtn = document.getElementById('startWorkout');
        const saveWorkoutBtn = document.getElementById('saveWorkout');
        const shareWorkoutBtn = document.getElementById('shareWorkout');
        
        if (startWorkoutBtn) {
            startWorkoutBtn.addEventListener('click', () => this.startWorkout());
        }
        
        if (saveWorkoutBtn) {
            saveWorkoutBtn.addEventListener('click', () => this.saveWorkout());
        }
        
        if (shareWorkoutBtn) {
            shareWorkoutBtn.addEventListener('click', () => this.shareWorkout());
        }
    },
    
    selectOption(type, element) {
        // Remove previous selections in the same category
        const category = element.closest('.goal-options, .time-options, .equipment-options, .level-options');
        if (category) {
            category.querySelectorAll('.selected').forEach(el => el.classList.remove('selected'));
        }
        
        // Add selection to clicked element
        element.classList.add('selected');
        
        // Add visual feedback
        element.style.transform = 'scale(0.95)';
        setTimeout(() => {
            element.style.transform = 'scale(1)';
        }, 150);
        
        // Save to state
        this.saveCurrentState();
        
        // Track selection
        FitGenius.analytics.trackEvent('workout_option_selected', {
            type: type,
            value: element.dataset[type] || element.textContent.trim()
        });
    },
    
    nextStep() {
        if (!this.validateCurrentStep()) {
            this.showStepError();
            return;
        }
        
        if (this.currentStep < this.maxSteps) {
            this.hideStep(this.currentStep);
            this.currentStep++;
            this.showStep(this.currentStep);
            this.updateNavigation();
            
            // Track step progression
            FitGenius.analytics.trackEvent('workout_step_completed', {
                step: this.currentStep - 1,
                data: this.getCurrentStepData()
            });
        }
    },
    
    prevStep() {
        if (this.currentStep > 1) {
            this.hideStep(this.currentStep);
            this.currentStep--;
            this.showStep(this.currentStep);
            this.updateNavigation();
        }
    },
    
    validateCurrentStep() {
        switch (this.currentStep) {
            case 1:
                return this.currentWorkout.goal !== null;
            case 2:
                return this.currentWorkout.time !== null;
            case 3:
                return this.currentWorkout.equipment !== null;
            case 4:
                return this.currentWorkout.level !== null;
            default:
                return true;
        }
    },
    
    showStepError() {
        const stepMessages = {
            1: 'Wybierz swój główny cel treningowy',
            2: 'Wybierz czas trwania treningu',
            3: 'Wybierz dostępny sprzęt',
            4: 'Wybierz swój poziom zaawansowania'
        };
        
        FitGenius.forms.showNotification(stepMessages[this.currentStep], 'error');
    },
    
    getCurrentStepData() {
        switch (this.currentStep) {
            case 1:
                return { goal: this.currentWorkout.goal };
            case 2:
                return { time: this.currentWorkout.time };
            case 3:
                return { equipment: this.currentWorkout.equipment };
            case 4:
                return { level: this.currentWorkout.level };
            default:
                return {};
        }
    },
    
    hideStep(stepNumber) {
        const step = document.querySelector(`[data-step="${stepNumber}"]`);
        if (step) {
            step.classList.remove('active');
        }
    },
    
    showStep(stepNumber) {
        const step = document.querySelector(`[data-step="${stepNumber}"]`);
        if (step) {
            step.classList.add('active');
        }
    },
    
    updateNavigation() {
        const nextBtn = document.getElementById('nextStep');
        const prevBtn = document.getElementById('prevStep');
        const generateBtn = document.getElementById('generateWorkout');
        
        if (prevBtn) {
            prevBtn.style.display = this.currentStep > 1 ? 'inline-flex' : 'none';
        }
        
        if (this.currentStep === this.maxSteps) {
            if (nextBtn) nextBtn.style.display = 'none';
            if (generateBtn) generateBtn.style.display = 'inline-flex';
        } else {
            if (nextBtn) nextBtn.style.display = 'inline-flex';
            if (generateBtn) generateBtn.style.display = 'none';
        }
    },
    
    async generateWorkout() {
        if (!this.validateCurrentStep()) {
            this.showStepError();
            return;
        }
        
        // Show loading state
        const generateBtn = document.getElementById('generateWorkout');
        const originalText = generateBtn.textContent;
        generateBtn.textContent = 'Generowanie...';
        generateBtn.disabled = true;
        
        try {
            // Simulate AI processing
            await this.simulateAIProcessing();
            
            // Try to enhance with LLM if available
            let enhancedWorkout = null;
            if (this.llm) {
                try {
                    enhancedWorkout = await this.generateWorkoutWithLLM();
                } catch (error) {
                    console.log('LLM workout generation failed, using fallback');
                }
            }
            
            // Generate the workout (LLM enhanced or traditional)
            const workout = enhancedWorkout || this.createWorkout();
            
            // Display the workout
            this.displayWorkout(workout);
            
            // Track successful generation
            FitGenius.analytics.trackEvent('workout_generated', {
                goal: this.currentWorkout.goal,
                time: this.currentWorkout.time,
                equipment: this.currentWorkout.equipment,
                level: this.currentWorkout.level,
                exerciseCount: workout.exercises.length,
                estimatedCalories: workout.estimatedCalories,
                llmEnhanced: !!enhancedWorkout
            });
            
            // Save to history
            this.saveWorkoutToHistory(workout);
            
            // Show success message
            FitGenius.forms.showNotification('Trening został wygenerowany!', 'success');
            
        } catch (error) {
            console.error('Workout generation error:', error);
            FitGenius.forms.showNotification('Błąd podczas generowania treningu', 'error');
        } finally {
            generateBtn.textContent = originalText;
            generateBtn.disabled = false;
        }
    },
    
    async generateWorkoutWithLLM() {
        const userProfile = {
            goal: this.currentWorkout.goal,
            level: this.currentWorkout.level,
            time: this.currentWorkout.time,
            equipment: this.currentWorkout.equipment
        };
        
        const llmWorkoutPlan = await this.llm.generateWorkoutPlan(userProfile);
        
        // Parse LLM response and create structured workout
        const exercises = this.parseLLMWorkoutPlan(llmWorkoutPlan);
        const totalCalories = this.calculateTotalCalories(exercises, this.currentWorkout.time);
        
        return {
            id: FitGenius.utils.generateId(),
            timestamp: new Date().toISOString(),
            goal: this.currentWorkout.goal,
            time: this.currentWorkout.time,
            equipment: this.currentWorkout.equipment,
            level: this.currentWorkout.level,
            exercises: exercises,
            estimatedCalories: totalCalories,
            difficulty: this.currentWorkout.level,
            name: this.generateWorkoutName(this.currentWorkout.goal, this.currentWorkout.equipment),
            llmGenerated: true,
            llmPlan: llmWorkoutPlan
        };
    },
    
    parseLLMWorkoutPlan(llmPlan) {
        // Parse LLM response into structured exercises
        const lines = llmPlan.split('\n').filter(line => line.trim());
        const exercises = [];
        
        lines.forEach(line => {
            // Match patterns like "Exercise - X sets x Y reps"
            const match = line.match(/(.+?)\s*-\s*(\d+)\s*(?:serie|sets?)\s*x\s*(\d+)\s*(?:powtórzeń|reps?|s)/i);
            if (match) {
                const [, name, sets, reps] = match;
                
                // Find matching exercise from database or create new one
                const exerciseData = this.findExerciseByName(name.trim()) || {
                    name: name.trim(),
                    icon: '💪',
                    difficulty: this.currentWorkout.level,
                    muscleGroups: ['mixed'],
                    instructions: ['Wykonaj ćwiczenie zgodnie z instrukcją'],
                    calories: 8
                };
                
                exercises.push({
                    ...exerciseData,
                    sets: parseInt(sets),
                    reps: reps.includes('s') ? `${reps}` : parseInt(reps)
                });
            }
        });
        
        // If no exercises parsed, fallback to traditional generation
        if (exercises.length === 0) {
            return this.selectOptimalExercises(
                this.getExercisePool(this.currentWorkout.goal, this.currentWorkout.equipment),
                this.calculateExerciseCount(this.currentWorkout.time),
                this.currentWorkout.time
            );
        }
        
        return exercises;
    },
    
    findExerciseByName(name) {
        const normalizedName = name.toLowerCase();
        
        for (const category of Object.keys(this.exerciseDatabase)) {
            for (const equipment of Object.keys(this.exerciseDatabase[category])) {
                const exercise = this.exerciseDatabase[category][equipment].find(ex => 
                    ex.name.toLowerCase().includes(normalizedName) || 
                    normalizedName.includes(ex.name.toLowerCase())
                );
                if (exercise) return exercise;
            }
        }
        
        return null;
    },
    
    async simulateAIProcessing() {
        // Simulate realistic AI processing time
        const loadingTexts = [
            'Analizowanie Twoich preferencji...',
            'Dobieranie optymalnych ćwiczeń...',
            'Kalkulowanie intensywności...',
            'Personalizowanie treningu...',
            'Finalizowanie planu...'
        ];
        
        const generateBtn = document.getElementById('generateWorkout');
        
        for (let i = 0; i < loadingTexts.length; i++) {
            generateBtn.textContent = loadingTexts[i];
            await new Promise(resolve => setTimeout(resolve, 400 + Math.random() * 300));
        }
    },
    
    createWorkout() {
        const { goal, time, equipment, level } = this.currentWorkout;
        
        // Get exercise pool
        let exercisePool = this.getExercisePool(goal, equipment);
        
        // Filter by difficulty level
        exercisePool = this.filterByDifficulty(exercisePool, level);
        
        // Calculate number of exercises based on time
        const exerciseCount = this.calculateExerciseCount(time);
        
        // Select exercises using AI algorithm
        const selectedExercises = this.selectOptimalExercises(exercisePool, exerciseCount, time);
        
        // Calculate total calories
        const totalCalories = this.calculateTotalCalories(selectedExercises, time);
        
        // Determine difficulty
        const workoutDifficulty = this.calculateWorkoutDifficulty(selectedExercises, level);
        
        const workout = {
            id: FitGenius.utils.generateId(),
            timestamp: new Date().toISOString(),
            goal,
            time,
            equipment,
            level,
            exercises: selectedExercises,
            estimatedCalories: totalCalories,
            difficulty: workoutDifficulty,
            name: this.generateWorkoutName(goal, equipment)
        };
        
        this.currentWorkout = { ...this.currentWorkout, ...workout };
        
        return workout;
    },
    
    getExercisePool(goal, equipment) {
        let pool = [];
        
        // Primary goal exercises
        if (this.exerciseDatabase[goal] && this.exerciseDatabase[goal][equipment]) {
            pool = [...this.exerciseDatabase[goal][equipment]];
        }
        
        // Add strength exercises for all goals
        if (goal !== 'strength' && this.exerciseDatabase.strength[equipment]) {
            pool = [...pool, ...this.exerciseDatabase.strength[equipment].slice(0, 3)];
        }
        
        // Add endurance for weight loss
        if (goal === 'weight-loss' && this.exerciseDatabase.endurance[equipment]) {
            pool = [...pool, ...this.exerciseDatabase.endurance[equipment]];
        }
        
        // Fallback to no equipment if pool is empty
        if (pool.length === 0 && equipment !== 'none') {
            pool = this.exerciseDatabase.strength.none;
        }
        
        return pool;
    },
    
    filterByDifficulty(exercises, level) {
        const difficultyMap = {
            'beginner': ['beginner', 'intermediate'],
            'intermediate': ['beginner', 'intermediate', 'advanced'],
            'advanced': ['intermediate', 'advanced']
        };
        
        const allowedDifficulties = difficultyMap[level] || ['beginner', 'intermediate'];
        
        return exercises.filter(exercise => 
            allowedDifficulties.includes(exercise.difficulty)
        );
    },
    
    calculateExerciseCount(time) {
        if (time <= 20) return 4;
        if (time <= 30) return 5;
        if (time <= 45) return 6;
        return 7;
    },
    
    selectOptimalExercises(pool, count, time) {
        if (pool.length === 0) {
            // Emergency fallback
            return this.exerciseDatabase.strength.none.slice(0, count);
        }
        
        // Ensure muscle group diversity
        const selected = [];
        const usedMuscleGroups = new Set();
        
        // Shuffle pool for randomization
        const shuffledPool = [...pool].sort(() => Math.random() - 0.5);
        
        for (const exercise of shuffledPool) {
            if (selected.length >= count) break;
            
            // Check muscle group diversity
            const hasNewMuscleGroup = exercise.muscleGroups.some(group => 
                !usedMuscleGroups.has(group)
            );
            
            if (hasNewMuscleGroup || selected.length < 2) {
                selected.push({
                    ...exercise,
                    adjustedReps: this.adjustRepsForTime(exercise.reps, time),
                    adjustedSets: this.adjustSetsForTime(exercise.sets, time)
                });
                
                exercise.muscleGroups.forEach(group => usedMuscleGroups.add(group));
            }
        }
        
        // Fill remaining slots if needed
        while (selected.length < count && pool.length > 0) {
            const exercise = pool[Math.floor(Math.random() * pool.length)];
            if (!selected.some(s => s.name === exercise.name)) {
                selected.push({
                    ...exercise,
                    adjustedReps: this.adjustRepsForTime(exercise.reps, time),
                    adjustedSets: this.adjustSetsForTime(exercise.sets, time)
                });
            }
        }
        
        return selected;
    },
    
    adjustRepsForTime(reps, time) {
        if (time <= 20) {
            // Shorter workout - maintain reps but fewer sets
            return reps;
        } else if (time >= 60) {
            // Longer workout - potentially more reps
            if (reps.includes('-')) {
                const [min, max] = reps.split('-').map(n => parseInt(n.replace(/\D/g, '')));
                return `${min + 2}-${max + 5}`;
            }
        }
        return reps;
    },
    
    adjustSetsForTime(sets, time) {
        if (time <= 20) {
            // Reduce sets for shorter workouts
            const setNum = parseInt(sets.split('-')[0]) || 3;
            return Math.max(2, setNum - 1).toString();
        } else if (time >= 60) {
            // Increase sets for longer workouts
            const setNum = parseInt(sets.split('-')[0]) || 3;
            return (setNum + 1).toString();
        }
        return sets;
    },
    
    calculateTotalCalories(exercises, time) {
        const baseCalories = exercises.reduce((total, exercise) => {
            return total + (exercise.calories || 10);
        }, 0);
        
        // Adjust for workout duration
        const timeMultiplier = time / 30; // Base is 30 minutes
        
        return Math.round(baseCalories * timeMultiplier);
    },
    
    calculateWorkoutDifficulty(exercises, level) {
        const difficultyScores = {
            'beginner': 1,
            'intermediate': 2,
            'advanced': 3
        };
        
        const avgDifficulty = exercises.reduce((sum, exercise) => {
            return sum + (difficultyScores[exercise.difficulty] || 2);
        }, 0) / exercises.length;
        
        if (avgDifficulty <= 1.5) return 'Łatwy';
        if (avgDifficulty <= 2.5) return 'Średni';
        return 'Trudny';
    },
    
    generateWorkoutName(goal, equipment) {
        const goalNames = {
            'strength': 'Siła',
            'endurance': 'Wytrzymałość',
            'weight-loss': 'Redukcja',
            'muscle-gain': 'Masa'
        };
        
        const equipmentNames = {
            'none': 'Bez Sprzętu',
            'basic': 'Podstawowy',
            'home-gym': 'Domowy',
            'full-gym': 'Siłownia'
        };
        
        return `${goalNames[goal] || 'Trening'} - ${equipmentNames[equipment] || 'Personalny'}`;
    },
    
    displayWorkout(workout) {
        const formContainer = document.querySelector('.generator-form');
        const previewContainer = document.getElementById('workoutPreview');
        
        if (!previewContainer) return;
        
        // Hide form and show preview
        if (formContainer) {
            formContainer.style.display = 'none';
        }
        
        // Update workout metadata
        document.getElementById('workoutDuration').textContent = workout.time;
        document.getElementById('workoutDifficulty').textContent = workout.difficulty;
        document.getElementById('workoutCalories').textContent = workout.estimatedCalories;
        
        // Generate exercises HTML
        const exercisesContainer = document.getElementById('workoutExercises');
        exercisesContainer.innerHTML = workout.exercises.map(exercise => `
            <div class="exercise-item">
                <div class="exercise-icon">${exercise.icon}</div>
                <div class="exercise-details">
                    <h4 class="exercise-name">${exercise.name}</h4>
                    <p class="exercise-specs">
                        ${exercise.adjustedSets || exercise.sets} serie × ${exercise.adjustedReps || exercise.reps}
                        ${exercise.equipment ? ` • ${exercise.equipment}` : ''}
                    </p>
                    <div class="exercise-muscles">
                        ${exercise.muscleGroups.map(group => 
                            `<span class="muscle-tag">${group}</span>`
                        ).join('')}
                    </div>
                </div>
                <div class="exercise-difficulty ${exercise.difficulty}">
                    ${exercise.difficulty === 'beginner' ? 'Łatwe' : 
                      exercise.difficulty === 'intermediate' ? 'Średnie' : 'Trudne'}
                </div>
            </div>
        `).join('');
        
        // Show preview
        previewContainer.style.display = 'block';
        
        // Scroll to preview
        previewContainer.scrollIntoView({ behavior: 'smooth' });
        
        // Add exercise animations
        setTimeout(() => {
            const exerciseItems = previewContainer.querySelectorAll('.exercise-item');
            exerciseItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateX(-30px)';
                
                setTimeout(() => {
                    item.style.transition = 'all 0.5s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateX(0)';
                }, index * 150);
            });
        }, 100);
    },
    
    startWorkout() {
        // Track workout start
        FitGenius.analytics.trackEvent('workout_started', {
            workoutId: this.currentWorkout.id,
            goal: this.currentWorkout.goal,
            duration: this.currentWorkout.time
        });
        
        // Show workout timer/interface (would integrate with a workout tracking system)
        FitGenius.forms.showNotification('Trening rozpoczęty! Powodzenia! 💪', 'success');
        
        // Could open a workout tracking modal or redirect to workout page
        this.openWorkoutTracker();
    },
    
    openWorkoutTracker() {
        // Placeholder for workout tracking interface
        const modal = document.createElement('div');
        modal.className = 'workout-tracker-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
        `;
        
        modal.innerHTML = `
            <div class="tracker-content glass-effect" style="padding: 2rem; border-radius: 1rem; text-align: center; max-width: 400px; width: 90%;">
                <h2>🏋️ Trening w toku</h2>
                <p style="margin: 1rem 0; color: var(--text-secondary);">Timer i śledzenie postępów będzie dostępne w pełnej wersji aplikacji.</p>
                <div style="font-size: 2rem; margin: 2rem 0; color: var(--primary-color);" id="workoutTimer">00:00</div>
                <button class="btn-primary" onclick="this.closest('.workout-tracker-modal').remove();">
                    Zakończ podgląd
                </button>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Simple timer demo
        let seconds = 0;
        const timer = setInterval(() => {
            seconds++;
            const minutes = Math.floor(seconds / 60);
            const secs = seconds % 60;
            const timerElement = modal.querySelector('#workoutTimer');
            if (timerElement) {
                timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            } else {
                clearInterval(timer);
            }
        }, 1000);
        
        // Auto close after 30 seconds
        setTimeout(() => {
            if (document.body.contains(modal)) {
                document.body.removeChild(modal);
                clearInterval(timer);
            }
        }, 30000);
    },
    
    saveWorkout() {
        const workouts = FitGenius.utils.loadFromLocalStorage('saved_workouts') || [];
        workouts.push(this.currentWorkout);
        
        if (FitGenius.utils.saveToLocalStorage('saved_workouts', workouts)) {
            FitGenius.forms.showNotification('Trening został zapisany!', 'success');
            
            // Track save
            FitGenius.analytics.trackEvent('workout_saved', {
                workoutId: this.currentWorkout.id
            });
        } else {
            FitGenius.forms.showNotification('Błąd podczas zapisywania treningu', 'error');
        }
    },
    
    shareWorkout() {
        const shareText = `Sprawdź mój personalny trening wygenerowany przez AI!\n\n` +
            `🎯 Cel: ${this.currentWorkout.goal}\n` +
            `⏱️ Czas: ${this.currentWorkout.time} min\n` +
            `🔥 Kalorie: ${this.currentWorkout.estimatedCalories} kcal\n` +
            `💪 Poziom: ${this.currentWorkout.difficulty}\n\n` +
            `Dołącz do FitGenius.pl i stwórz swój własny plan!`;
        
        if (navigator.share) {
            navigator.share({
                title: 'Mój trening z FitGenius.pl',
                text: shareText,
                url: window.location.href
            }).then(() => {
                FitGenius.analytics.trackEvent('workout_shared', {
                    method: 'native',
                    workoutId: this.currentWorkout.id
                });
            });
        } else {
            // Fallback - copy to clipboard
            navigator.clipboard.writeText(shareText).then(() => {
                FitGenius.forms.showNotification('Trening skopiowany do schowka!', 'success');
                FitGenius.analytics.trackEvent('workout_shared', {
                    method: 'clipboard',
                    workoutId: this.currentWorkout.id
                });
            }).catch(() => {
                // Show share modal
                this.showShareModal(shareText);
            });
        }
    },
    
    showShareModal(shareText) {
        const modal = document.createElement('div');
        modal.className = 'share-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
        `;
        
        modal.innerHTML = `
            <div class="share-content glass-effect" style="padding: 2rem; border-radius: 1rem; max-width: 500px; width: 90%;">
                <h3 style="margin-bottom: 1rem;">Udostępnij swój trening</h3>
                <textarea readonly style="width: 100%; height: 150px; margin-bottom: 1rem; padding: 1rem; border-radius: 0.5rem; border: 1px solid var(--glass-border); background: var(--glass-background); color: var(--text-primary); resize: none;">${shareText}</textarea>
                <div style="display: flex; gap: 1rem; justify-content: center;">
                    <button class="btn-primary" onclick="navigator.clipboard.writeText(\`${shareText.replace(/`/g, '\\`')}\`).then(() => { FitGenius.forms.showNotification('Skopiowano!', 'success'); this.closest('.share-modal').remove(); })">
                        Kopiuj tekst
                    </button>
                    <button class="btn-secondary" onclick="this.closest('.share-modal').remove();">
                        Zamknij
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    },
    
    saveCurrentState() {
        FitGenius.utils.saveToLocalStorage('workout_generator_state', {
            currentStep: this.currentStep,
            currentWorkout: this.currentWorkout
        });
    },
    
    loadSavedPreferences() {
        const saved = FitGenius.utils.loadFromLocalStorage('workout_generator_state');
        if (saved) {
            this.currentStep = saved.currentStep || 1;
            this.currentWorkout = { ...this.currentWorkout, ...saved.currentWorkout };
            
            // Restore UI state
            this.restoreUIState();
        }
    },
    
    restoreUIState() {
        // Restore selected options
        if (this.currentWorkout.goal) {
            document.querySelector(`[data-goal="${this.currentWorkout.goal}"]`)?.classList.add('selected');
        }
        if (this.currentWorkout.time) {
            document.querySelector(`[data-time="${this.currentWorkout.time}"]`)?.classList.add('selected');
        }
        if (this.currentWorkout.equipment) {
            document.querySelector(`[data-equipment="${this.currentWorkout.equipment}"]`)?.classList.add('selected');
        }
        if (this.currentWorkout.level) {
            document.querySelector(`[data-level="${this.currentWorkout.level}"]`)?.classList.add('selected');
        }
    },
    
    saveWorkoutToHistory(workout) {
        const history = FitGenius.utils.loadFromLocalStorage('workout_history') || [];
        history.unshift(workout); // Add to beginning
        
        // Keep only last 50 workouts
        if (history.length > 50) {
            history.splice(50);
        }
        
        FitGenius.utils.saveToLocalStorage('workout_history', history);
    },
    
    // Reset generator
    reset() {
        this.currentWorkout = {
            goal: null,
            time: null,
            equipment: null,
            level: null,
            exercises: [],
            estimatedCalories: 0,
            difficulty: 'średni'
        };
        
        this.currentStep = 1;
        
        // Clear UI
        document.querySelectorAll('.selected').forEach(el => el.classList.remove('selected'));
        
        // Reset form visibility
        const formContainer = document.querySelector('.generator-form');
        const previewContainer = document.getElementById('workoutPreview');
        
        if (formContainer) formContainer.style.display = 'block';
        if (previewContainer) previewContainer.style.display = 'none';
        
        // Show first step
        this.hideStep(this.currentStep);
        this.currentStep = 1;
        this.showStep(this.currentStep);
        this.updateNavigation();
        
        // Clear saved state
        localStorage.removeItem('fitgenius_workout_generator_state');
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    WorkoutGenerator.init();
});

// Export for global access
window.WorkoutGenerator = WorkoutGenerator;