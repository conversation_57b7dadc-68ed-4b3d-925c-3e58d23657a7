/* ===================================
   FitGenius.pl - Modern 2025 Styling
   ================================== */

/* CSS Variables for Consistent Design */
:root {
  /* Primary Color Palette */
  --primary-color: #6366f1;
  --primary-light: #818cf8;
  --primary-dark: #4f46e5;
  --secondary-color: #06b6d4;
  --accent-color: #f59e0b;
  
  /* Neutral Colors */
  --background-main: #0f172a;
  --background-secondary: #1e293b;
  --background-card: rgba(255, 255, 255, 0.05);
  --surface-elevated: rgba(255, 255, 255, 0.1);
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --text-inverse: #1e293b;
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%);
  --gradient-secondary: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);
  --gradient-surface: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  --gradient-hero: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  
  /* Shadows & Glassmorphism */
  --glass-background: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.25);
  
  /* Border Radius */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-full: 50%;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  
  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
  --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ===================================
   Global Reset & Base Styles
   =================================== */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family);
  background: var(--background-main);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
  position: relative;
}

/* Animated Background */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
  z-index: -1;
  animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
  0%, 100% { transform: translateX(0) translateY(0) scale(1); }
  25% { transform: translateX(-2%) translateY(-1%) scale(1.02); }
  50% { transform: translateX(1%) translateY(2%) scale(0.98); }
  75% { transform: translateX(2%) translateY(-1%) scale(1.01); }
}

/* ===================================
   Utility Classes
   =================================== */

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.glass-effect {
  background: var(--glass-background);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradientMove 3s ease-in-out infinite;
}

@keyframes gradientMove {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-4xl);
}

.section-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  margin-bottom: var(--spacing-lg);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.section-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* ===================================
   Buttons
   =================================== */

.btn-primary,
.btn-secondary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: var(--font-size-base);
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left var(--transition-slow);
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--glass-background);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: var(--surface-elevated);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-primary.large,
.btn-secondary.large {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-xl);
}

/* ===================================
   Navigation
   =================================== */

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: var(--spacing-md) 0;
  transition: all var(--transition-normal);
}

.navbar.scrolled {
  background: rgba(15, 23, 42, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: 700;
}

.logo-icon {
  font-size: var(--font-size-2xl);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.logo-accent {
  color: var(--accent-color);
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: var(--spacing-xl);
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--text-primary);
  background: var(--glass-background);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: all var(--transition-normal);
  transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 80%;
}

.nav-actions {
  display: flex;
  gap: var(--spacing-md);
}

.hamburger {
  display: none;
  flex-direction: column;
  gap: 4px;
  cursor: pointer;
  padding: var(--spacing-sm);
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  border-radius: 2px;
  transition: all var(--transition-normal);
}

/* ===================================
   Hero Section
   =================================== */

.hero {
  padding: calc(80px + var(--spacing-4xl)) 0 var(--spacing-4xl);
  background: var(--gradient-hero);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 30% 20%, rgba(99, 102, 241, 0.2) 0%, transparent 60%),
    radial-gradient(circle at 70% 80%, rgba(6, 182, 212, 0.15) 0%, transparent 60%);
  animation: heroGlow 8s ease-in-out infinite;
}

@keyframes heroGlow {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 0.8; }
}

.hero-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  align-items: center;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  position: relative;
  z-index: 1;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  background: var(--glass-background);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 500;
  margin-bottom: var(--spacing-xl);
  backdrop-filter: blur(10px);
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: var(--spacing-xl);
}

.hero-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2xl);
  line-height: 1.8;
}

.hero-stats {
  display: flex;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--font-size-3xl);
  font-weight: 800;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
}

.hero-actions {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.hero-trust {
  text-align: center;
}

.trust-text {
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
}

.trust-badges {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

.badge {
  padding: var(--spacing-xs) var(--spacing-md);
  background: var(--glass-background);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-xs);
  backdrop-filter: blur(10px);
}

/* Hero Visual */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone-mockup {
  width: 300px;
  height: 600px;
  background: linear-gradient(145deg, #1a1a2e, #16213e);
  border-radius: 40px;
  padding: 20px;
  box-shadow: var(--shadow-xl);
  position: relative;
  transform: perspective(1000px) rotateY(-15deg) rotateX(5deg);
  animation: phoneFloat 4s ease-in-out infinite;
}

@keyframes phoneFloat {
  0%, 100% { transform: perspective(1000px) rotateY(-15deg) rotateX(5deg) translateY(0); }
  50% { transform: perspective(1000px) rotateY(-15deg) rotateX(5deg) translateY(-20px); }
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: var(--background-main);
  border-radius: 30px;
  overflow: hidden;
  position: relative;
}

.app-preview {
  padding: var(--spacing-lg);
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.app-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.user-avatar {
  width: 50px;
  height: 50px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
}

.user-info h4 {
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-xs);
}

.user-info p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.streak-counter {
  margin-left: auto;
  text-align: center;
  background: var(--glass-background);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
}

.streak-number {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--accent-color);
}

.streak-label {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.today-workout {
  background: var(--glass-background);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--glass-border);
}

.workout-card {
  margin-top: var(--spacing-md);
}

.workout-type,
.workout-time {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.workout-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: var(--background-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--gradient-primary);
  transition: width var(--transition-slow);
  animation: progressPulse 2s infinite;
}

@keyframes progressPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.progress-text {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.quick-stats {
  display: flex;
  gap: var(--spacing-md);
  margin-top: auto;
}

.stat-card {
  flex: 1;
  background: var(--glass-background);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  text-align: center;
  border: 1px solid var(--glass-border);
}

.stat-icon {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-xs);
}

.stat-value {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--text-primary);
}

.stat-label {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

/* ===================================
   Features Section
   =================================== */

.features {
  padding: var(--spacing-4xl) 0;
  background: var(--background-main);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-2xl);
}

.feature-card {
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: -1;
}

.feature-card:hover::before {
  opacity: 0.05;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-xl);
}

.feature-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-lg);
  display: block;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.feature-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.feature-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.7;
}

.feature-list {
  list-style: none;
  margin-bottom: var(--spacing-xl);
}

.feature-list li {
  padding: var(--spacing-sm) 0;
  color: var(--text-secondary);
  position: relative;
  padding-left: var(--spacing-lg);
}

.feature-list li::before {
  content: '✨';
  position: absolute;
  left: 0;
  color: var(--accent-color);
}

.feature-cta {
  width: 100%;
  background: var(--glass-background);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
}

.feature-cta:hover {
  background: var(--surface-elevated);
  transform: translateY(-2px);
}

/* ===================================
   Workout Generator Section
   =================================== */

.workout-generator {
  padding: var(--spacing-4xl) 0;
  background: var(--background-secondary);
}

.generator-container {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
}

.generator-form {
  position: relative;
}

.form-step {
  display: none;
  text-align: center;
  animation: fadeInUp 0.5s ease-out;
}

.form-step.active {
  display: block;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-step h3 {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-2xl);
  color: var(--text-primary);
}

.goal-options,
.time-options,
.equipment-options,
.level-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.goal-option,
.equipment-option,
.level-option {
  background: var(--glass-background);
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.goal-option:hover,
.equipment-option:hover,
.level-option:hover {
  border-color: var(--primary-color);
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.goal-option.selected,
.equipment-option.selected,
.level-option.selected {
  border-color: var(--primary-color);
  background: rgba(99, 102, 241, 0.1);
}

.goal-icon,
.equipment-icon,
.level-icon {
  font-size: var(--font-size-3xl);
}

.goal-text,
.equipment-text,
.level-text {
  font-weight: 600;
  color: var(--text-primary);
}

.time-options {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: center;
  flex-wrap: wrap;
}

.time-option {
  background: var(--glass-background);
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg) var(--spacing-xl);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: 600;
  font-size: var(--font-size-lg);
}

.time-option:hover {
  border-color: var(--primary-color);
  transform: translateY(-3px);
}

.time-option.selected {
  border-color: var(--primary-color);
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary-light);
}

.form-navigation {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: center;
  margin-top: var(--spacing-2xl);
}

/* Workout Preview */
.workout-preview {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.workout-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.workout-header h3 {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-md);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.workout-meta {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: center;
  flex-wrap: wrap;
}

.workout-duration,
.workout-difficulty,
.workout-calories {
  background: var(--glass-background);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  border: 1px solid var(--glass-border);
}

.workout-exercises {
  margin-bottom: var(--spacing-2xl);
}

.exercise-item {
  background: var(--glass-background);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  transition: all var(--transition-normal);
}

.exercise-item:hover {
  transform: translateX(10px);
  box-shadow: var(--shadow-md);
}

.exercise-icon {
  font-size: var(--font-size-2xl);
  width: 60px;
  text-align: center;
}

.exercise-details {
  flex: 1;
}

.exercise-name {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.exercise-specs {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.exercise-difficulty {
  background: var(--accent-color);
  color: var(--text-inverse);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.workout-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

/* ===================================
   Analytics Dashboard
   =================================== */

.analytics-dashboard {
  padding: var(--spacing-4xl) 0;
  background: var(--background-main);
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
}

.dashboard-card {
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  transition: all var(--transition-normal);
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.dashboard-card h3 {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.progress-chart,
.calories-chart {
  margin-bottom: var(--spacing-lg);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.calorie-ring {
  position: relative;
  width: 150px;
  height: 150px;
}

.ring-progress {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(
    var(--primary-color) 0deg,
    var(--primary-color) calc(var(--progress, 75) * 3.6deg),
    var(--background-secondary) calc(var(--progress, 75) * 3.6deg),
    var(--background-secondary) 360deg
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.ring-progress::before {
  content: '';
  position: absolute;
  width: 80%;
  height: 80%;
  background: var(--background-main);
  border-radius: 50%;
}

.ring-value {
  position: relative;
  z-index: 1;
  text-align: center;
}

.ring-number {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
}

.ring-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.chart-stats {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-md);
}

.stat {
  text-align: center;
  flex: 1;
}

.stat-label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
}

.stat-value {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--text-primary);
}

.stat-value.positive {
  color: #10b981;
}

.stat-value.negative {
  color: #ef4444;
}

/* Workout Calendar */
.frequency-calendar {
  max-width: 280px;
  margin: 0 auto;
}

.month-header {
  text-align: center;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.calendar-day.empty {
  opacity: 0.3;
}

.calendar-day.workout {
  background: var(--primary-color);
  color: white;
}

.calendar-day.today {
  border: 2px solid var(--accent-color);
}

.calendar-day:hover {
  transform: scale(1.1);
}

/* Body Composition */
.body-composition {
  max-width: 280px;
  margin: 0 auto;
}

.composition-item {
  margin-bottom: var(--spacing-md);
}

.composition-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.composition-bar {
  height: 12px;
  background: var(--background-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--spacing-xs);
}

.composition-fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width var(--transition-slow);
}

.composition-fill.muscle {
  background: var(--primary-color);
}

.composition-fill.fat {
  background: var(--accent-color);
}

.composition-fill.water {
  background: var(--secondary-color);
}

.composition-value {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
}

/* ===================================
   Pricing Section
   =================================== */

.pricing {
  padding: var(--spacing-4xl) 0;
  background: var(--background-secondary);
}

.pricing-toggle {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.toggle-label {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-md);
  cursor: pointer;
  user-select: none;
}

.toggle-label input {
  display: none;
}

.toggle-slider {
  width: 60px;
  height: 30px;
  background: var(--glass-background);
  border-radius: var(--radius-full);
  position: relative;
  transition: all var(--transition-normal);
  border: 1px solid var(--glass-border);
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  width: 22px;
  height: 22px;
  background: var(--primary-color);
  border-radius: 50%;
  transition: all var(--transition-normal);
}

.toggle-label input:checked + .toggle-text + .toggle-slider::before {
  transform: translateX(28px);
}

.toggle-text {
  font-weight: 500;
  color: var(--text-secondary);
}

.discount-badge {
  background: var(--accent-color);
  color: var(--text-inverse);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
  margin-left: var(--spacing-xs);
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
  max-width: 1000px;
  margin: 0 auto;
}

.pricing-card {
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  transition: all var(--transition-normal);
  position: relative;
  text-align: center;
}

.pricing-card.popular {
  border: 2px solid var(--primary-color);
  transform: scale(1.05);
}

.pricing-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.pricing-card.popular:hover {
  transform: translateY(-10px) scale(1.07);
}

.popular-badge {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--gradient-primary);
  color: white;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 600;
}

.plan-header {
  margin-bottom: var(--spacing-2xl);
}

.plan-name {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.plan-price {
  margin-bottom: var(--spacing-md);
}

.price-amount {
  font-size: var(--font-size-4xl);
  font-weight: 800;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.price-currency {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
}

.price-period {
  font-size: var(--font-size-base);
  color: var(--text-muted);
}

.plan-description {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
}

.plan-features {
  list-style: none;
  margin-bottom: var(--spacing-2xl);
  text-align: left;
}

.plan-features li {
  padding: var(--spacing-sm) 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.feature-included {
  color: var(--text-primary) !important;
}

.feature-excluded {
  opacity: 0.6;
}

.plan-cta {
  width: 100%;
  padding: var(--spacing-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
}

.pricing-guarantee {
  text-align: center;
  margin-top: var(--spacing-3xl);
}

.guarantee-content {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-md);
  background: var(--glass-background);
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-lg);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
}

.guarantee-icon {
  font-size: var(--font-size-2xl);
}

.guarantee-text h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.guarantee-text p {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* ===================================
   Testimonials Section
   =================================== */

.testimonials {
  padding: var(--spacing-4xl) 0;
  background: var(--background-main);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
}

.testimonial-card {
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  transition: all var(--transition-normal);
}

.testimonial-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.testimonial-content {
  margin-bottom: var(--spacing-lg);
}

.testimonial-stars {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-md);
}

.testimonial-text {
  color: var(--text-secondary);
  line-height: 1.7;
  font-style: italic;
  position: relative;
}

.testimonial-text::before {
  content: '"';
  font-size: var(--font-size-4xl);
  color: var(--primary-color);
  position: absolute;
  top: -10px;
  left: -10px;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.author-avatar {
  width: 50px;
  height: 50px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
}

.author-details {
  flex: 1;
}

.author-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.author-role {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.testimonial-results {
  margin-left: auto;
}

.result-badge {
  background: var(--gradient-secondary);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 600;
}

/* ===================================
   Contact Section
   =================================== */

.contact {
  padding: var(--spacing-4xl) 0;
  background: var(--background-secondary);
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: start;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.contact-icon {
  font-size: var(--font-size-2xl);
  width: 60px;
  text-align: center;
}

.contact-details h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
}

.contact-details p {
  color: var(--text-secondary);
}

.contact-form {
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
}

.contact-form h3 {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-xl);
  color: var(--text-primary);
  text-align: center;
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-md);
  background: var(--glass-background);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: var(--text-muted);
}

/* ===================================
   Footer
   =================================== */

.footer {
  background: var(--background-main);
  padding: var(--spacing-4xl) 0 var(--spacing-xl);
  border-top: 1px solid var(--glass-border);
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.footer-logo {
  margin-bottom: var(--spacing-lg);
}

.footer-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

.social-links {
  display: flex;
  gap: var(--spacing-md);
}

.social-link {
  width: 40px;
  height: 40px;
  background: var(--glass-background);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
}

.social-link:hover {
  background: var(--surface-elevated);
  transform: translateY(-3px);
}

.footer-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--spacing-sm);
}

.footer-links a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--primary-light);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--glass-border);
}

.footer-copyright {
  color: var(--text-muted);
}

.footer-badges {
  display: flex;
  gap: var(--spacing-md);
}

.footer-badges .badge {
  background: var(--glass-background);
  border: 1px solid var(--glass-border);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  backdrop-filter: blur(10px);
}

/* ===================================
   Loading Screen
   =================================== */

.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--background-main);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity var(--transition-slow);
}

.loading-screen.hidden {
  opacity: 0;
  pointer-events: none;
}

.loading-content {
  text-align: center;
  max-width: 400px;
}

.loading-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  font-size: var(--font-size-2xl);
  font-weight: 700;
  margin-bottom: var(--spacing-xl);
}

.loading-logo .logo-icon {
  font-size: var(--font-size-3xl);
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-size-lg);
}

.loading-progress {
  width: 100%;
}

.loading-progress .progress-bar {
  width: 100%;
  height: 6px;
  background: var(--background-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--spacing-md);
}

.loading-progress .progress-fill {
  height: 100%;
  background: var(--gradient-primary);
  width: 0%;
  animation: loadingProgress 3s ease-out forwards;
}

@keyframes loadingProgress {
  to { width: 100%; }
}

.loading-progress .progress-text {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

/* ===================================
   Responsive Design
   =================================== */

@media (max-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-2xl);
  }
  
  .hero-visual {
    order: -1;
  }
  
  .phone-mockup {
    transform: none;
    width: 250px;
    height: 500px;
  }
  
  .contact-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }
  
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xl);
  }
}

@media (max-width: 768px) {
  .nav-menu,
  .nav-actions {
    display: none;
  }
  
  .hamburger {
    display: flex;
  }
  
  .hero-title {
    font-size: var(--font-size-3xl);
  }
  
  .hero-stats {
    flex-direction: column;
    gap: var(--spacing-lg);
  }
  
  .hero-actions {
    flex-direction: column;
  }
  
  .section-title {
    font-size: var(--font-size-3xl);
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .pricing-grid {
    grid-template-columns: 1fr;
  }
  
  .pricing-card.popular {
    transform: none;
  }
  
  .testimonials-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .goal-options,
  .equipment-options,
  .level-options {
    grid-template-columns: 1fr;
  }
  
  .time-options {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-sm);
  }
  
  .hero {
    padding: calc(80px + var(--spacing-2xl)) 0 var(--spacing-2xl);
  }
  
  .hero-title {
    font-size: var(--font-size-2xl);
  }
  
  .phone-mockup {
    width: 200px;
    height: 400px;
    padding: 15px;
  }
  
  .section-title {
    font-size: var(--font-size-2xl);
  }
  
  .feature-card,
  .pricing-card,
  .testimonial-card,
  .dashboard-card {
    padding: var(--spacing-lg);
  }
  
  .workout-actions {
    flex-direction: column;
  }
  
  .trust-badges {
    flex-direction: column;
    align-items: center;
  }
}

/* ===================================
   Accessibility & Interactions
   =================================== */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for keyboard navigation */
.btn-primary:focus,
.btn-secondary:focus,
.nav-link:focus,
.feature-cta:focus,
.plan-cta:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #ffffff;
    --text-secondary: #e2e8f0;
    --background-main: #000000;
    --background-secondary: #1a1a1a;
  }
}

/* Print styles */
@media print {
  .navbar,
  .hero-visual,
  .loading-screen {
    display: none;
  }
  
  body {
    background: white;
    color: black;
  }
  
  .section-title,
  .hero-title {
    color: black;
  }
}

/* ===================================
   Custom Scrollbar
   =================================== */

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-light);
}

/* ===================================
   Selection Styles
   =================================== */

::selection {
  background: var(--primary-color);
  color: white;
}