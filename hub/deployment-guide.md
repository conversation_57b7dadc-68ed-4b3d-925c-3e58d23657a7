# PixelGarage Deployment Guide
## Domena: pixelgarage.space | Serwer: mikr.us IPv6

### CZĘŚĆ 1: KONFIGURACJA MANUALNA DNS I CLOUDFLARE

#### Krok 1: Przygotowanie Cloudflare
1. **Z<PERSON><PERSON><PERSON><PERSON> konto na Cloudflare** (je<PERSON><PERSON> nie ma<PERSON>): https://cloudflare.com
2. **Dodaj domenę pixelgarage.space** do Cloudflare:
   - <PERSON><PERSON><PERSON><PERSON> "Add a Site"
   - Wprowadź: `pixelgarage.space`
   - W<PERSON><PERSON>rz plan Free
   - Cloudflare przeskanuje istniejące rekordy DNS

#### Krok 2: Konfiguracja DNS w Cloudflare
Dodaj następujące rekordy DNS w panelu Cloudflare:

```
Type: AAAA
Name: @
Content: [TWÓJ_IPv6_SERWERA_MIKRUS]
Proxy status: Proxied (pomarańczowa chmurka)
TTL: Auto

Type: AAAA  
Name: www
Content: [TWÓJ_IPv6_SERWERA_MIKRUS]
Proxy status: Proxied (pomarańczowa chmurka)
TTL: Auto

Type: CNAME
Name: api
Content: pixelgarage.space
Proxy status: Proxied (pomarańczowa chmurka)
TTL: Auto
```

#### Krok 3: Zmiana nameserverów w home.pl
1. **Zaloguj się do panelu home.pl**
2. **Przejdź do zarządzania domeną pixelgarage.space**
3. **Zmień nameservery na te podane przez Cloudflare:**
   ```
   Nameserver 1: [PODANY_PRZEZ_CLOUDFLARE].ns.cloudflare.com
   Nameserver 2: [PODANY_PRZEZ_CLOUDFLARE].ns.cloudflare.com
   ```
4. **Zapisz zmiany** (propagacja może potrwać do 24h)

#### Krok 4: Konfiguracja SSL w Cloudflare
1. **SSL/TLS → Overview**: Ustaw na "Full (strict)"
2. **SSL/TLS → Edge Certificates**: 
   - Włącz "Always Use HTTPS"
   - Włącz "HTTP Strict Transport Security (HSTS)"
3. **Speed → Optimization**:
   - Włącz "Auto Minify" (HTML, CSS, JS)
   - Włącz "Brotli"

#### Krok 5: Przygotowanie serwera mikr.us
1. **Połącz się z serwerem przez SSH**
2. **Zainstaluj wymagane pakiety:**
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install -y nginx git nodejs npm certbot python3-certbot-nginx
sudo systemctl enable nginx
sudo systemctl start nginx
```

3. **Konfiguracja Nginx:**
```bash
sudo nano /etc/nginx/sites-available/pixelgarage.space
```

Dodaj konfigurację:
```nginx
server {
    listen 80;
    listen [::]:80;
    server_name pixelgarage.space www.pixelgarage.space;
    
    root /var/www/pixelgarage;
    index index.html index.htm;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:3000/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Cache static files
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

4. **Aktywuj konfigurację:**
```bash
sudo ln -s /etc/nginx/sites-available/pixelgarage.space /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

#### Krok 6: Utworzenie struktury katalogów
```bash
sudo mkdir -p /var/www/pixelgarage
sudo mkdir -p /var/www/pixelgarage/backups
sudo chown -R $USER:www-data /var/www/pixelgarage
sudo chmod -R 755 /var/www/pixelgarage
```

### CZĘŚĆ 2: SKRYPT AUTODEPLOYMENT

Utworzenie skryptu `/home/<USER>/deploy-pixelgarage.sh`:

```bash
#!/bin/bash

# PixelGarage Auto-Deployment Script
# Domena: pixelgarage.space | Serwer: mikr.us IPv6

set -e

# Konfiguracja
DOMAIN="pixelgarage.space"
PROJECT_DIR="/var/www/pixelgarage"
BACKUP_DIR="/var/www/pixelgarage/backups"
REPO_URL="https://github.com/TWÓJ_USERNAME/pixelgarage.git"  # Zmień na swój repo
BRANCH="main"
LOG_FILE="/var/log/pixelgarage-deploy.log"

# Funkcje pomocnicze
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

error_exit() {
    log "ERROR: $1"
    exit 1
}

# Sprawdzenie uprawnień
if [[ $EUID -eq 0 ]]; then
   error_exit "Nie uruchamiaj tego skryptu jako root!"
fi

log "=== Rozpoczęcie deployment PixelGarage ==="

# Backup istniejącej wersji
if [ -d "$PROJECT_DIR" ] && [ "$(ls -A $PROJECT_DIR)" ]; then
    log "Tworzenie backupu..."
    BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
    sudo mkdir -p "$BACKUP_DIR"
    sudo cp -r "$PROJECT_DIR" "$BACKUP_DIR/$BACKUP_NAME" || error_exit "Błąd podczas tworzenia backupu"
    log "Backup utworzony: $BACKUP_DIR/$BACKUP_NAME"
fi

# Klonowanie/aktualizacja repozytorium
if [ -d "$PROJECT_DIR/.git" ]; then
    log "Aktualizacja istniejącego repozytorium..."
    cd "$PROJECT_DIR"
    sudo git fetch origin
    sudo git reset --hard origin/$BRANCH
else
    log "Klonowanie repozytorium..."
    sudo rm -rf "$PROJECT_DIR"
    sudo git clone -b $BRANCH "$REPO_URL" "$PROJECT_DIR" || error_exit "Błąd klonowania repozytorium"
fi

cd "$PROJECT_DIR"

# Sprawdzenie czy to aplikacja Node.js
if [ -f "package.json" ]; then
    log "Wykryto aplikację Node.js - instalacja zależności..."
    sudo npm install --production || error_exit "Błąd instalacji npm"
    
    # Build jeśli istnieje skrypt build
    if npm run | grep -q "build"; then
        log "Budowanie aplikacji..."
        sudo npm run build || error_exit "Błąd budowania aplikacji"
    fi
fi

# Kopiowanie plików do katalogu web
if [ -d "dist" ]; then
    log "Kopiowanie plików z katalogu dist..."
    sudo cp -r dist/* . || error_exit "Błąd kopiowania z dist"
elif [ -d "build" ]; then
    log "Kopiowanie plików z katalogu build..."
    sudo cp -r build/* . || error_exit "Błąd kopiowania z build"
elif [ -d "public" ]; then
    log "Kopiowanie plików z katalogu public..."
    sudo cp -r public/* . || error_exit "Błąd kopiowania z public"
fi

# Ustawienie uprawnień
log "Ustawienie uprawnień..."
sudo chown -R $USER:www-data "$PROJECT_DIR"
sudo chmod -R 755 "$PROJECT_DIR"
sudo find "$PROJECT_DIR" -type f -exec chmod 644 {} \;

# Test konfiguracji Nginx
log "Test konfiguracji Nginx..."
sudo nginx -t || error_exit "Błąd konfiguracji Nginx"

# Restart usług
log "Restart Nginx..."
sudo systemctl reload nginx || error_exit "Błąd restartu Nginx"

# Sprawdzenie statusu
log "Sprawdzenie statusu serwisów..."
if sudo systemctl is-active --quiet nginx; then
    log "✓ Nginx działa poprawnie"
else
    error_exit "✗ Nginx nie działa"
fi

# Test dostępności strony
log "Test dostępności strony..."
if curl -s -o /dev/null -w "%{http_code}" "http://localhost" | grep -q "200\|301\|302"; then
    log "✓ Strona dostępna lokalnie"
else
    log "⚠ Ostrzeżenie: Strona może być niedostępna lokalnie"
fi

# Czyszczenie starych backupów (pozostaw tylko 5 najnowszych)
log "Czyszczenie starych backupów..."
if [ -d "$BACKUP_DIR" ]; then
    sudo find "$BACKUP_DIR" -maxdepth 1 -type d -name "backup-*" | sort -r | tail -n +6 | sudo xargs rm -rf
    log "Stare backupy usunięte"
fi

log "=== Deployment zakończony pomyślnie! ==="
log "Strona dostępna pod adresem: https://$DOMAIN"
log "Backup dostępny w: $BACKUP_DIR"

# Opcjonalne: Powiadomienie webhook (np. Discord, Slack)
# curl -X POST -H 'Content-type: application/json' --data '{"text":"PixelGarage deployed successfully!"}' YOUR_WEBHOOK_URL

exit 0
```

### CZĘŚĆ 3: AUTOMATYZACJA I MONITORING

#### Utworzenie skryptu szybkiego deploymentu:
```bash
# Plik: /home/<USER>/quick-deploy.sh
#!/bin/bash
cd /home/<USER>
./deploy-pixelgarage.sh 2>&1 | tee -a /var/log/pixelgarage-deploy.log
```

#### Ustawienie uprawnień:
```bash
chmod +x /home/<USER>/deploy-pixelgarage.sh
chmod +x /home/<USER>/quick-deploy.sh
```

#### Opcjonalne: Automatyczny deployment przez webhook
```bash
# Instalacja webhook listener
sudo apt install -y webhook

# Konfiguracja webhook (plik: /etc/webhook.conf)
[
  {
    "id": "pixelgarage-deploy",
    "execute-command": "/home/<USER>/deploy-pixelgarage.sh",
    "command-working-directory": "/home/<USER>",
    "response-message": "Deployment started",
    "trigger-rule": {
      "match": {
        "type": "payload-hash-sha1",
        "secret": "TWÓJ_SECRET_KEY",
        "parameter": {
          "source": "header",
          "name": "X-Hub-Signature"
        }
      }
    }
  }
]
```

### CZĘŚĆ 4: MONITORING I LOGI

#### Sprawdzenie logów:
```bash
# Logi deployment
tail -f /var/log/pixelgarage-deploy.log

# Logi Nginx
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# Status serwisów
sudo systemctl status nginx
```

#### Przydatne komendy:
```bash
# Szybki deployment
./quick-deploy.sh

# Sprawdzenie statusu
curl -I https://pixelgarage.space

# Restart wszystkich serwisów
sudo systemctl restart nginx

# Przywrócenie z backupu
sudo cp -r /var/www/pixelgarage/backups/backup-YYYYMMDD-HHMMSS/* /var/www/pixelgarage/
```

---

**UWAGA:** 
1. Zmień `REPO_URL` w skrypcie na adres swojego repozytorium
2. Upewnij się, że masz klucze SSH skonfigurowane dla Git
3. Pierwszego deploymentu wykonaj manualnie, aby sprawdzić wszystkie ścieżki
4. Monitoruj logi podczas pierwszych deploymentów

**Kolejność wykonania:**
1. Konfiguracja Cloudflare (Kroki 1-4)
2. Przygotowanie serwera (Krok 5-6)
3. Utworzenie i test skryptu deployment
4. Pierwszy deployment manualny
5. Konfiguracja automatyzacji (opcjonalnie)
