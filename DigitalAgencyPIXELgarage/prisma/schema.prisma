// PIXELgarage Royal Premium Platform - Database Schema
// This schema defines the data structure for our premium digital agency platform

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  phone     String?
  company   String?
  role      UserRole @default(CLIENT)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  projects Project[]
  orders   Order[]
  leads    Lead[]

  @@map("users")
}

enum UserRole {
  ADMIN
  CLIENT
  LEAD
}

// Service Categories
model ServiceCategory {
  id          String @id @default(cuid())
  name        String
  nameEn      String
  slug        String @unique
  description String
  descriptionEn String
  icon        String
  color       String
  order       Int    @default(0)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  services Service[]

  @@map("service_categories")
}

// Services Offered
model Service {
  id            String  @id @default(cuid())
  name          String
  nameEn        String
  slug          String  @unique
  description   String
  descriptionEn String
  shortDesc     String
  shortDescEn   String
  basePrice     Decimal
  currency      String  @default("PLN")
  duration      String
  features      Json    // Array of features
  featuresEn    Json    // Array of features in English
  isPopular     Boolean @default(false)
  isActive      Boolean @default(true)
  order         Int     @default(0)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  categoryId String
  category   ServiceCategory @relation(fields: [categoryId], references: [id])
  projects   Project[]
  orderItems OrderItem[]

  @@map("services")
}

// Portfolio Projects (Showcase)
model Project {
  id          String        @id @default(cuid())
  title       String
  titleEn     String
  description String
  descriptionEn String
  slug        String        @unique
  imageUrl    String?
  galleryUrls Json?         // Array of image URLs
  demoUrl     String?
  status      ProjectStatus @default(COMPLETED)
  isFeatured  Boolean       @default(false)
  isPublic    Boolean       @default(true)
  tags        Json?         // Array of tags
  tagsEn      Json?         // Array of tags in English
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  serviceId String?
  service   Service? @relation(fields: [serviceId], references: [id])
  clientId  String?
  client    User?    @relation(fields: [clientId], references: [id])

  @@map("projects")
}

enum ProjectStatus {
  PLANNING
  IN_PROGRESS
  COMPLETED
  ON_HOLD
}

// Lead Management
model Lead {
  id          String     @id @default(cuid())
  email       String
  name        String?
  phone       String?
  company     String?
  message     String?
  source      LeadSource @default(WEBSITE)
  status      LeadStatus @default(NEW)
  interestedServices Json? // Array of service IDs
  budget      String?
  timeline    String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  userId String?
  user   User?   @relation(fields: [userId], references: [id])

  @@map("leads")
}

enum LeadSource {
  WEBSITE
  TALLY_FORM
  CALENDLY
  REFERRAL
  SOCIAL_MEDIA
  OTHER
}

enum LeadStatus {
  NEW
  CONTACTED
  QUALIFIED
  PROPOSAL_SENT
  NEGOTIATING
  CONVERTED
  LOST
}

// Order Management
model Order {
  id          String      @id @default(cuid())
  orderNumber String      @unique
  totalAmount Decimal
  currency    String      @default("PLN")
  status      OrderStatus @default(PENDING)
  paymentId   String?     // Stripe payment ID
  notes       String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  clientId String
  client   User        @relation(fields: [clientId], references: [id])
  items    OrderItem[]

  @@map("orders")
}

enum OrderStatus {
  PENDING
  PAID
  IN_PROGRESS
  COMPLETED
  CANCELLED
  REFUNDED
}

// Order Items
model OrderItem {
  id       String  @id @default(cuid())
  quantity Int     @default(1)
  price    Decimal
  notes    String?

  // Relations
  orderId   String
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  serviceId String
  service   Service @relation(fields: [serviceId], references: [id])

  @@map("order_items")
}

// Analytics & Tracking
model Analytics {
  id        String   @id @default(cuid())
  event     String   // Event name (page_view, button_click, form_submit, etc.)
  page      String?  // Page URL
  userId    String?  // User ID if available
  sessionId String?  // Session ID
  data      Json?    // Additional event data
  createdAt DateTime @default(now())

  @@map("analytics")
}

// Configuration & Settings
model Setting {
  id        String   @id @default(cuid())
  key       String   @unique
  value     Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}
