// PIXELgarage Royal Premium Platform - Premium Button Component
// Ultra-premium button with advanced animations and effects

'use client'

import { ReactNode, useState } from 'react'
import { motion } from 'framer-motion'

interface PremiumButtonProps {
  children: ReactNode
  variant?: 'primary' | 'secondary' | 'danger' | 'success'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  onClick?: () => void
  disabled?: boolean
  className?: string
  icon?: ReactNode
  glowEffect?: boolean
  pulseEffect?: boolean
}

const PremiumButton = ({
  children,
  variant = 'primary',
  size = 'md',
  onClick,
  disabled = false,
  className = '',
  icon,
  glowEffect = true,
  pulseEffect = false
}: PremiumButtonProps) => {
  const [isHovered, setIsHovered] = useState(false)
  const [isPressed, setIsPressed] = useState(false)

  const variants = {
    primary: {
      background: 'linear-gradient(135deg, #9d72ff 0%, #7c3aed 50%, #6b21a8 100%)',
      border: '2px solid rgba(157, 114, 255, 0.5)',
      color: '#ffffff',
      shadow: '0 20px 40px rgba(157, 114, 255, 0.3)',
      hoverShadow: '0 30px 60px rgba(157, 114, 255, 0.5)',
    },
    secondary: {
      background: 'linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(217, 119, 6, 0.1))',
      border: '2px solid rgba(251, 191, 36, 0.5)',
      color: '#ffffff',
      shadow: '0 15px 30px rgba(251, 191, 36, 0.2)',
      hoverShadow: '0 25px 50px rgba(251, 191, 36, 0.4)',
    },
    danger: {
      background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%)',
      border: '2px solid rgba(220, 38, 38, 0.5)',
      color: '#ffffff',
      shadow: '0 20px 40px rgba(220, 38, 38, 0.3)',
      hoverShadow: '0 30px 60px rgba(220, 38, 38, 0.5)',
    },
    success: {
      background: 'linear-gradient(135deg, #059669 0%, #047857 50%, #065f46 100%)',
      border: '2px solid rgba(5, 150, 105, 0.5)',
      color: '#ffffff',
      shadow: '0 20px 40px rgba(5, 150, 105, 0.3)',
      hoverShadow: '0 30px 60px rgba(5, 150, 105, 0.5)',
    }
  }

  const sizes = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
    xl: 'px-10 py-5 text-xl'
  }

  const currentVariant = variants[variant]

  return (
    <motion.button
      className={`
        relative overflow-hidden font-bold rounded-xl
        transition-all duration-300 ease-out
        disabled:opacity-50 disabled:cursor-not-allowed
        ${sizes[size]}
        ${className}
      `}
      style={{
        background: currentVariant.background,
        border: currentVariant.border,
        color: currentVariant.color,
        boxShadow: isHovered ? currentVariant.hoverShadow : currentVariant.shadow,
      }}
      onClick={onClick}
      disabled={disabled}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      whileHover={{ 
        scale: 1.05, 
        y: -3,
        transition: { duration: 0.2 }
      }}
      whileTap={{ 
        scale: 0.98,
        transition: { duration: 0.1 }
      }}
      animate={pulseEffect ? {
        scale: [1, 1.02, 1],
        boxShadow: [
          currentVariant.shadow,
          currentVariant.hoverShadow,
          currentVariant.shadow
        ]
      } : {}}
      transition={pulseEffect ? {
        duration: 2,
        repeat: Infinity,
        ease: 'easeInOut'
      } : {}}
    >
      {/* Shimmer effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
        initial={{ x: '-100%' }}
        animate={isHovered ? { x: '100%' } : { x: '-100%' }}
        transition={{ duration: 0.6, ease: 'easeInOut' }}
      />

      {/* Glow effect */}
      {glowEffect && (
        <motion.div
          className="absolute inset-0 rounded-xl"
          animate={isHovered ? {
            boxShadow: [
              '0 0 20px rgba(157, 114, 255, 0.4)',
              '0 0 40px rgba(157, 114, 255, 0.6)',
              '0 0 20px rgba(157, 114, 255, 0.4)'
            ]
          } : {}}
          transition={{ duration: 1, repeat: isHovered ? Infinity : 0 }}
        />
      )}

      {/* Content */}
      <span className="relative z-10 flex items-center justify-center gap-2">
        {icon && (
          <motion.span
            animate={isHovered ? { rotate: [0, 10, -10, 0] } : {}}
            transition={{ duration: 0.5 }}
          >
            {icon}
          </motion.span>
        )}
        {children}
      </span>

      {/* Particle burst effect on click */}
      {isPressed && (
        <div className="absolute inset-0 pointer-events-none">
          {Array.from({ length: 12 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full"
              style={{
                left: '50%',
                top: '50%',
              }}
              initial={{ scale: 0, opacity: 1 }}
              animate={{
                scale: [0, 1, 0],
                opacity: [1, 0.8, 0],
                x: Math.cos((i / 12) * Math.PI * 2) * 30,
                y: Math.sin((i / 12) * Math.PI * 2) * 30,
              }}
              transition={{
                duration: 0.6,
                ease: 'easeOut'
              }}
            />
          ))}
        </div>
      )}

      {/* Border glow animation */}
      <motion.div
        className="absolute inset-0 rounded-xl"
        style={{
          background: `linear-gradient(45deg, transparent, ${variant === 'primary' ? 'rgba(157, 114, 255, 0.3)' : 'rgba(251, 191, 36, 0.3)'}, transparent)`,
          backgroundSize: '200% 200%',
        }}
        animate={isHovered ? {
          backgroundPosition: ['0% 0%', '100% 100%', '0% 0%']
        } : {}}
        transition={{ duration: 2, repeat: isHovered ? Infinity : 0 }}
      />
    </motion.button>
  )
}

export default PremiumButton
