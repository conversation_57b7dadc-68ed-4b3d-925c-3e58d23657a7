// PIXELgarage Royal Premium Platform - Pricing Section
// Interactive pricing calculator with Stripe integration

'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { useTranslations } from 'next-intl'
import { Check, Crown, Zap, Star, Calculator, ArrowRight } from 'lucide-react'

const PricingSection = () => {
  // const t = useTranslations('pricing')
  const [selectedServices, setSelectedServices] = useState<string[]>([])
  const [projectScope, setProjectScope] = useState('medium')
  const [timeline, setTimeline] = useState('standard')

  const services = [
    { id: 'logo', name: 'Logo Design', price: 2500, category: 'branding' },
    { id: 'website', name: 'Website Development', price: 5000, category: 'web' },
    { id: 'branding', name: 'Brand Identity', price: 7500, category: 'branding' },
    { id: 'ecommerce', name: 'E-commerce Platform', price: 12000, category: 'web' },
    { id: 'automation', name: 'Process Automation', price: 8000, category: 'automation' },
    { id: 'content', name: 'Content Creation', price: 3000, category: 'content' },
  ]

  const packages = [
    {
      id: 'starter',
      name: 'Starter',
      price: 2500,
      description: 'Idealny dla małych firm rozpoczynających cyfrową transformację',
      features: [
        'Logo Design',
        'Podstawowa strona internetowa',
        'Materiały marketingowe',
        'Konsultacje strategiczne',
        'Wsparcie 30 dni'
      ],
      popular: false
    },
    {
      id: 'professional',
      name: 'Professional',
      price: 7500,
      description: 'Kompleksowe rozwiązania dla rozwijających się biznesów',
      features: [
        'Pełna identyfikacja wizualna',
        'Zaawansowana strona internetowa',
        'Integracje systemów',
        'Automatyzacja procesów',
        'Wsparcie 90 dni',
        'Analityka i raportowanie'
      ],
      popular: true
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      price: 15000,
      description: 'Zaawansowane rozwiązania dla dużych organizacji',
      features: [
        'Kompleksowa transformacja cyfrowa',
        'Dedykowane aplikacje',
        'Zaawansowana automatyzacja',
        'Integracje enterprise',
        'Wsparcie 365 dni',
        'Dedykowany account manager',
        'Priorytetowe wsparcie'
      ],
      popular: false
    }
  ]

  const toggleService = (serviceId: string) => {
    setSelectedServices(prev => 
      prev.includes(serviceId)
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    )
  }

  const calculateTotal = () => {
    const baseTotal = selectedServices.reduce((total, serviceId) => {
      const service = services.find(s => s.id === serviceId)
      return total + (service?.price || 0)
    }, 0)

    const scopeMultiplier = projectScope === 'small' ? 0.8 : projectScope === 'large' ? 1.5 : 1
    const timelineMultiplier = timeline === 'rush' ? 1.5 : timeline === 'extended' ? 0.9 : 1

    return Math.round(baseTotal * scopeMultiplier * timelineMultiplier)
  }

  return (
    <section id="pricing" className="section-padding relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <motion.div
          className="absolute bottom-1/4 right-1/3 w-96 h-96 bg-royal-500/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
      </div>

      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="inline-flex items-center gap-2 glass rounded-full px-6 py-3 mb-6">
            <Calculator className="w-5 h-5 text-royal-400" />
            <span className="text-sm font-medium text-royal-400 font-display">
              TRANSPARENTNY CENNIK
            </span>
          </div>

          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            <span className="text-gradient-royal">Cennik Usług</span>
          </h2>

          <p className="text-xl text-platinum-300 max-w-3xl mx-auto">
            Transparentne ceny dla każdego budżetu. Wybierz pakiet idealny dla Twojego projektu.
          </p>
        </motion.div>

        {/* Pricing Calculator */}
        <motion.div
          className="glass rounded-3xl p-8 mb-16 max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-white mb-2">
              Kalkulator Kosztów
            </h3>
            <p className="text-platinum-300">
              Dostosuj usługi do swoich potrzeb i zobacz szacunkowy koszt
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Services Selection */}
            <div>
              <h4 className="text-lg font-semibold text-white mb-4">
                Wybierz usługi:
              </h4>
              <div className="space-y-3">
                {services.map((service) => (
                  <motion.div
                    key={service.id}
                    className={`p-4 rounded-xl border cursor-pointer transition-all duration-300 ${
                      selectedServices.includes(service.id)
                        ? 'border-royal-400 bg-royal-500/10'
                        : 'border-white/20 hover:border-white/40'
                    }`}
                    onClick={() => toggleService(service.id)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="font-medium text-white">{service.name}</h5>
                        <p className="text-sm text-platinum-400">
                          {service.price.toLocaleString()} PLN
                        </p>
                      </div>
                      <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                        selectedServices.includes(service.id)
                          ? 'border-royal-400 bg-royal-400'
                          : 'border-white/40'
                      }`}>
                        {selectedServices.includes(service.id) && (
                          <Check className="w-3 h-3 text-white" />
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Configuration & Total */}
            <div>
              <div className="space-y-6 mb-8">
                <div>
                  <label className="block text-sm font-medium text-white mb-3">
                    Zakres projektu:
                  </label>
                  <div className="grid grid-cols-3 gap-2">
                    {[
                      { id: 'small', label: 'Mały' },
                      { id: 'medium', label: 'Średni' },
                      { id: 'large', label: 'Duży' }
                    ].map((scope) => (
                      <button
                        key={scope.id}
                        onClick={() => setProjectScope(scope.id)}
                        className={`py-2 px-4 rounded-lg text-sm font-medium transition-all ${
                          projectScope === scope.id
                            ? 'bg-royal-500 text-white'
                            : 'bg-white/10 text-platinum-300 hover:bg-white/20'
                        }`}
                      >
                        {scope.label}
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-white mb-3">
                    Czas realizacji:
                  </label>
                  <div className="grid grid-cols-3 gap-2">
                    {[
                      { id: 'rush', label: 'Ekspres' },
                      { id: 'standard', label: 'Standard' },
                      { id: 'extended', label: 'Rozszerzony' }
                    ].map((time) => (
                      <button
                        key={time.id}
                        onClick={() => setTimeline(time.id)}
                        className={`py-2 px-4 rounded-lg text-sm font-medium transition-all ${
                          timeline === time.id
                            ? 'bg-royal-500 text-white'
                            : 'bg-white/10 text-platinum-300 hover:bg-white/20'
                        }`}
                      >
                        {time.label}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Total */}
              <div className="glass rounded-2xl p-6 text-center">
                <p className="text-sm text-platinum-400 mb-2">
                  Szacunkowy koszt:
                </p>
                <motion.div
                  className="text-4xl font-bold text-gradient-royal mb-4"
                  key={calculateTotal()}
                  initial={{ scale: 1.2 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  {calculateTotal().toLocaleString()} PLN
                </motion.div>
                <motion.button
                  className="w-full btn-primary"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Otrzymaj Szczegółową Wycenę
                  <ArrowRight className="w-4 h-4 ml-2" />
                </motion.button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Pricing Packages */}
        <div className="grid md:grid-cols-3 gap-8">
          {packages.map((pkg, index) => (
            <motion.div
              key={pkg.id}
              className={`relative glass rounded-3xl p-8 ${
                pkg.popular ? 'border-2 border-royal-400 shadow-royal' : ''
              }`}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -10 }}
            >
              {pkg.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-royal px-6 py-2 rounded-full">
                    <span className="text-sm font-medium text-white">
                      NAJPOPULARNIEJSZY
                    </span>
                  </div>
                </div>
              )}

              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-gradient-royal rounded-2xl flex items-center justify-center mx-auto mb-4">
                  {pkg.id === 'starter' && <Star className="w-8 h-8 text-white" />}
                  {pkg.id === 'professional' && <Crown className="w-8 h-8 text-white" />}
                  {pkg.id === 'enterprise' && <Zap className="w-8 h-8 text-white" />}
                </div>
                
                <h3 className="text-2xl font-bold text-white mb-2">
                  {pkg.name}
                </h3>
                
                <div className="text-4xl font-bold text-gradient-royal mb-4">
                  od {pkg.price.toLocaleString()} PLN
                </div>
                
                <p className="text-platinum-300 text-sm">
                  {pkg.description}
                </p>
              </div>

              <div className="space-y-3 mb-8">
                {pkg.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-royal-400 flex-shrink-0" />
                    <span className="text-platinum-300 text-sm">{feature}</span>
                  </div>
                ))}
              </div>

              <motion.button
                className={`w-full py-4 rounded-xl font-medium transition-all ${
                  pkg.popular
                    ? 'btn-primary'
                    : 'btn-secondary'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Wybierz Pakiet
              </motion.button>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default PricingSection
