// PIXELgarage Royal Premium Platform - Services Section
// Ultra-premium services showcase with manipulative copywriting

'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Crown,
  Zap,
  Code,
  Video,
  Settings,
  ArrowRight,
  Sparkles,
  Star
} from 'lucide-react'
import TiltCard from '@/components/effects/TiltCard'

const ServicesSection = () => {
  const [activeService, setActiveService] = useState(0)

  const services = [
    {
      id: 'brand_identity',
      name: 'IMPERIUM MARKI',
      description: 'Tworzymy marki, które nie konkurują - one DOMINUJĄ. Twoja konkurencja będzie próbować Cię naśladować przez lata.',
      icon: Crown,
      color: 'from-gold-400 to-gold-600',
      bgColor: 'bg-gold-500/10',
      borderColor: 'border-gold-400/30',
      services: [
        'Logo które hipnotyzuje klientów',
        'Identyfikacja wizualna klasy imperium',
        'Brand Guidelines niszczące konkurencję',
        'Materiały marketingowe klasy premium'
      ]
    },
    {
      id: 'digital_transformation',
      name: 'CYFROWA DOMINACJA',
      description: 'Nie modernizujemy - REWOLUCJONIZUJEMY. Twój biznes stanie się cyfrową potęgą, której konkurencja nie będzie w stanie dorównać.',
      icon: Zap,
      color: 'from-royal-400 to-royal-600',
      bgColor: 'bg-royal-500/10',
      borderColor: 'border-royal-400/30',
      services: [
        'Strategia totalnej dominacji',
        'Automatyzacja eliminująca konkurencję',
        'Integracje klasy enterprise',
        'Analityka przewidująca przyszłość'
      ]
    },
    {
      id: 'web_development',
      name: 'ROZWÓJ STRON INTERNETOWYCH',
      description: 'Nowoczesne, responsywne strony internetowe o wysokiej wydajności',
      icon: Code,
      color: 'from-blue-400 to-blue-600',
      bgColor: 'bg-blue-500/10',
      borderColor: 'border-blue-400/30',
      services: [
        'Strony Landing klasy AAA',
        'Strony Biznesowe premium',
        'Sklepy Online dominujące',
        'Aplikacje Webowe przyszłości'
      ]
    },
    {
      id: 'content_creation',
      name: 'TWORZENIE TREŚCI',
      description: 'Profesjonalne treści wizualne i multimedialne dla Twojej marki',
      icon: Video,
      color: 'from-purple-400 to-purple-600',
      bgColor: 'bg-purple-500/10',
      borderColor: 'border-purple-400/30',
      services: [
        'Animowane Zdjęcia viral',
        'Krótkie Filmy magnetyczne',
        'Grafiki Społecznościowe premium',
        'Treści Video hipnotyzujące'
      ]
    },
    {
      id: 'automation',
      name: 'AUTOMATYZACJA',
      description: 'Inteligentne rozwiązania automatyzujące procesy biznesowe',
      icon: Settings,
      color: 'from-emerald-400 to-emerald-600',
      bgColor: 'bg-emerald-500/10',
      borderColor: 'border-emerald-400/30',
      services: [
        'Workflow Automation AI',
        'CRM Integration premium',
        'Email Marketing dominujący',
        'Raportowanie predykcyjne'
      ]
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  }

  return (
    <section id="services" className="section-padding relative overflow-hidden">
      <div className="absolute inset-0 -z-10">
        <motion.div
          className="absolute top-1/4 right-1/4 w-96 h-96 bg-royal-500/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
      </div>

      <div className="container-custom">
        <motion.div
          className="text-center mb-16"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          <motion.div
            variants={itemVariants}
            className="relative inline-flex items-center gap-3 glass rounded-full px-8 py-4 mb-8 border border-red-500/50"
            whileHover={{ scale: 1.05, boxShadow: "0 0 50px rgba(239, 68, 68, 0.4)" }}
          >
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
            >
              <Sparkles className="w-6 h-6 text-red-400" />
            </motion.div>
            <span className="text-sm font-bold text-red-400 font-display tracking-wider">
              BROŃ MASOWEGO RAŻENIA
            </span>
            <motion.div
              animate={{ scale: [1, 1.3, 1] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              <Zap className="w-5 h-5 text-gold-400" />
            </motion.div>
            <div className="absolute inset-0 bg-gradient-to-r from-red-500/10 to-gold-500/10 rounded-full animate-pulse" />
          </motion.div>

          <motion.h2
            variants={itemVariants}
            className="text-5xl md:text-7xl lg:text-8xl font-black mb-8 leading-none"
          >
            <motion.span 
              className="block text-ultra-premium drop-shadow-2xl"
              whileHover={{ scale: 1.02 }}
              animate={{ 
                textShadow: [
                  "0 0 20px rgba(157, 114, 255, 0.5)",
                  "0 0 40px rgba(251, 191, 36, 0.5)",
                  "0 0 20px rgba(157, 114, 255, 0.5)"
                ]
              }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              BROŃ MASOWEGO RAŻENIA
            </motion.span>
          </motion.h2>

          <motion.p
            variants={itemVariants}
            className="text-2xl md:text-3xl text-platinum-200 max-w-4xl mx-auto font-bold leading-relaxed"
          >
            <span className="text-red-400">UWAGA:</span> Dla biznesów gotowych na totalne zdominowanie swojej branży
            <br />
            <span className="text-lg text-platinum-400 font-normal">
              Twoja konkurencja nie będzie wiedziała, co ją uderzyło.
            </span>
          </motion.p>
        </motion.div>

        <motion.div
          className="grid lg:grid-cols-2 gap-8 items-start"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
        >
          <div className="space-y-4">
            {services.map((service, index) => {
              const Icon = service.icon
              const isActive = activeService === index

              return (
                <motion.div
                  key={service.id}
                  variants={itemVariants}
                  className={`relative cursor-pointer transition-all duration-500 ${
                    isActive 
                      ? `${service.bgColor} ${service.borderColor} border-2 shadow-2xl` 
                      : 'glass border border-white/10 hover:border-white/20'
                  }`}
                  style={{
                    borderRadius: '1.5rem',
                    padding: '2rem',
                  }}
                  onClick={() => setActiveService(index)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-start gap-4">
                    <motion.div
                      className={`p-3 rounded-xl bg-gradient-to-r ${service.color}`}
                      animate={isActive ? { scale: [1, 1.1, 1] } : {}}
                      transition={{ duration: 0.5 }}
                    >
                      <Icon className="w-6 h-6 text-white" />
                    </motion.div>

                    <div className="flex-1">
                      <h3 className="text-xl font-bold mb-2 text-white">
                        {service.name}
                      </h3>
                      <p className="text-platinum-300 mb-4">
                        {service.description}
                      </p>

                      <motion.div
                        className="flex items-center gap-2 text-sm font-medium"
                        animate={isActive ? { x: [0, 10, 0] } : {}}
                        transition={{ duration: 0.5 }}
                      >
                        <span className={`bg-gradient-to-r ${service.color} bg-clip-text text-transparent`}>
                          Zobacz szczegóły
                        </span>
                        <ArrowRight className="w-4 h-4 text-royal-400" />
                      </motion.div>
                    </div>
                  </div>

                  {isActive && (
                    <motion.div
                      className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-1 h-16 bg-gradient-to-b from-royal-400 to-gold-400 rounded-full"
                      initial={{ scaleY: 0 }}
                      animate={{ scaleY: 1 }}
                      transition={{ duration: 0.3 }}
                    />
                  )}
                </motion.div>
              )
            })}
          </div>

          <motion.div
            className="sticky top-8"
            variants={itemVariants}
          >
            <TiltCard className="glass rounded-3xl p-8 border border-white/10">
              <motion.div
                key={activeService}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="flex items-center gap-4 mb-6">
                  <motion.div
                    className={`p-4 rounded-xl bg-gradient-to-r ${services[activeService].color}`}
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 2, ease: 'linear' }}
                  >
                    {(() => {
                      const Icon = services[activeService].icon
                      return <Icon className="w-8 h-8 text-white" />
                    })()}
                  </motion.div>
                  <div>
                    <h3 className="text-2xl font-bold text-white">
                      {services[activeService].name}
                    </h3>
                    <p className="text-royal-400 font-medium">
                      Premium Solutions
                    </p>
                  </div>
                </div>

                <p className="text-platinum-300 mb-8 text-lg leading-relaxed">
                  {services[activeService].description}
                </p>

                <div className="space-y-4 mb-8">
                  <h4 className="text-lg font-semibold text-white mb-4">
                    Zakres usług:
                  </h4>
                  {services[activeService].services.map((service, index) => (
                    <motion.div
                      key={index}
                      className="flex items-center gap-3"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Star className="w-4 h-4 text-gold-400" />
                      <span className="text-platinum-300">{service}</span>
                    </motion.div>
                  ))}
                </div>

                <motion.button
                  className="w-full btn-primary text-lg py-4"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Rozpocznij Projekt
                  <ArrowRight className="w-5 h-5 ml-2" />
                </motion.button>
              </motion.div>
            </TiltCard>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default ServicesSection
