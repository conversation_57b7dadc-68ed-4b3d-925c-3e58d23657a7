// PIXELgarage Royal Premium Platform - Hologram Logo Effect
// Ultra-premium 3D hologram effect for royal branding

'use client'

import { useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Crown } from 'lucide-react'

const HologramLogo = () => {
  const logoRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const logo = logoRef.current
    if (!logo) return

    const handleMouseMove = (e: MouseEvent) => {
      const rect = logo.getBoundingClientRect()
      const x = e.clientX - rect.left - rect.width / 2
      const y = e.clientY - rect.top - rect.height / 2
      
      const rotateX = (y / rect.height) * 30
      const rotateY = (x / rect.width) * -30
      
      logo.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(50px)`
    }

    const handleMouseLeave = () => {
      logo.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)'
    }

    logo.addEventListener('mousemove', handleMouseMove)
    logo.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      logo.removeEventListener('mousemove', handleMouseMove)
      logo.removeEventListener('mouseleave', handleMouseLeave)
    }
  }, [])

  return (
    <motion.div
      ref={logoRef}
      className="relative cursor-pointer"
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 1, ease: 'easeOut' }}
      style={{
        transformStyle: 'preserve-3d',
        transition: 'transform 0.3s ease-out',
      }}
    >
      {/* Main Logo */}
      <div className="relative z-10 flex items-center gap-3">
        <div className="relative">
          {/* Crown with hologram effect */}
          <motion.div
            animate={{ 
              rotate: [0, 360],
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              duration: 8, 
              repeat: Infinity, 
              ease: 'linear' 
            }}
            className="relative"
          >
            <Crown className="w-10 h-10 text-gold-400 filter drop-shadow-lg" />
            
            {/* Hologram rings */}
            <motion.div
              className="absolute inset-0 border-2 border-royal-400/30 rounded-full"
              animate={{ 
                scale: [1, 1.5, 1],
                opacity: [0.3, 0.8, 0.3]
              }}
              transition={{ 
                duration: 2, 
                repeat: Infinity, 
                ease: 'easeInOut' 
              }}
            />
            <motion.div
              className="absolute inset-0 border border-gold-400/20 rounded-full"
              animate={{ 
                scale: [1, 2, 1],
                opacity: [0.2, 0.6, 0.2]
              }}
              transition={{ 
                duration: 3, 
                repeat: Infinity, 
                ease: 'easeInOut',
                delay: 0.5
              }}
            />
          </motion.div>
        </div>
        
        <div>
          <motion.h1 
            className="text-2xl font-bold font-display"
            animate={{
              textShadow: [
                '0 0 10px rgba(157, 114, 255, 0.5)',
                '0 0 20px rgba(251, 191, 36, 0.5)',
                '0 0 10px rgba(157, 114, 255, 0.5)'
              ]
            }}
            transition={{ duration: 3, repeat: Infinity }}
          >
            <span className="text-gradient-royal">PIXEL</span>
            <span className="text-gold-400">garage</span>
          </motion.h1>
          <motion.p 
            className="text-xs text-platinum-400 -mt-1"
            animate={{ opacity: [0.7, 1, 0.7] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            Royal Premium Agency
          </motion.p>
        </div>
      </div>

      {/* Hologram base effect */}
      <div className="absolute inset-0 -z-10">
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-royal-500/10 via-gold-500/10 to-royal-500/10 rounded-2xl blur-xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />
      </div>

      {/* Scanning lines effect */}
      <motion.div
        className="absolute inset-0 overflow-hidden rounded-2xl"
        style={{ mixBlendMode: 'screen' }}
      >
        <motion.div
          className="absolute w-full h-0.5 bg-gradient-to-r from-transparent via-royal-400 to-transparent"
          animate={{
            y: ['-100%', '200%']
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'linear'
          }}
        />
        <motion.div
          className="absolute w-full h-0.5 bg-gradient-to-r from-transparent via-gold-400 to-transparent"
          animate={{
            y: ['-100%', '200%']
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: 'linear',
            delay: 1
          }}
        />
      </motion.div>

      {/* Particle effects */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 8 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-royal-400 rounded-full"
            style={{
              left: `${20 + i * 10}%`,
              top: `${30 + (i % 3) * 20}%`,
            }}
            animate={{
              opacity: [0, 1, 0],
              scale: [0.5, 1.5, 0.5],
              y: [0, -20, 0]
            }}
            transition={{
              duration: 2 + Math.random(),
              repeat: Infinity,
              delay: i * 0.3,
              ease: 'easeInOut'
            }}
          />
        ))}
      </div>
    </motion.div>
  )
}

export default HologramLogo
