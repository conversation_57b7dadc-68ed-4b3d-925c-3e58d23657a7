// PIXELgarage Royal Premium Platform - Matrix Rain Effect
// Ultra-premium background effect for GTA VI level experience

'use client'

import { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'

const MatrixRain = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }
    resizeCanvas()
    window.addEventListener('resize', resizeCanvas)

    // Matrix characters
    const chars = '01PIXELGARAGE王子皇帝帝国デジタル支配者'
    const charArray = chars.split('')

    const fontSize = 14
    const columns = canvas.width / fontSize

    // Drops array
    const drops: number[] = []
    for (let i = 0; i < columns; i++) {
      drops[i] = 1
    }

    // Colors for royal theme
    const colors = [
      'rgba(157, 114, 255, 0.8)', // Royal purple
      'rgba(251, 191, 36, 0.8)',  // Gold
      'rgba(34, 197, 94, 0.6)',   // Green
      'rgba(239, 68, 68, 0.6)',   // Red
    ]

    const draw = () => {
      // Black background with fade effect
      ctx.fillStyle = 'rgba(15, 23, 42, 0.05)'
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      ctx.font = `${fontSize}px monospace`

      // Draw characters
      for (let i = 0; i < drops.length; i++) {
        // Random character
        const char = charArray[Math.floor(Math.random() * charArray.length)]
        
        // Random color
        const color = colors[Math.floor(Math.random() * colors.length)]
        ctx.fillStyle = color

        // Draw character
        ctx.fillText(char, i * fontSize, drops[i] * fontSize)

        // Reset drop randomly
        if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
          drops[i] = 0
        }

        // Move drop down
        drops[i]++
      }
    }

    const interval = setInterval(draw, 50)

    return () => {
      clearInterval(interval)
      window.removeEventListener('resize', resizeCanvas)
    }
  }, [])

  return (
    <motion.canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-0"
      initial={{ opacity: 0 }}
      animate={{ opacity: 0.3 }}
      transition={{ duration: 2 }}
      style={{
        mixBlendMode: 'screen',
      }}
    />
  )
}

export default MatrixRain
