// PIXELgarage Royal Premium Platform - Cursor Trail Effect
// Ultra-premium cursor effects for royal experience

'use client'

import { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'

interface TrailPoint {
  x: number
  y: number
  timestamp: number
}

const CursorTrail = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const trailRef = useRef<TrailPoint[]>([])
  const mouseRef = useRef({ x: 0, y: 0 })

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }
    resizeCanvas()
    window.addEventListener('resize', resizeCanvas)

    // Mouse tracking
    const handleMouseMove = (e: MouseEvent) => {
      mouseRef.current = { x: e.clientX, y: e.clientY }
      
      // Add point to trail
      trailRef.current.push({
        x: e.clientX,
        y: e.clientY,
        timestamp: Date.now()
      })

      // Keep only recent points (last 500ms)
      const now = Date.now()
      trailRef.current = trailRef.current.filter(point => now - point.timestamp < 500)
    }
    window.addEventListener('mousemove', handleMouseMove)

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      if (trailRef.current.length > 1) {
        const now = Date.now()
        
        // Draw trail
        for (let i = 1; i < trailRef.current.length; i++) {
          const point = trailRef.current[i]
          const prevPoint = trailRef.current[i - 1]
          const age = now - point.timestamp
          const alpha = Math.max(0, 1 - age / 500)
          
          // Create gradient for royal effect
          const gradient = ctx.createLinearGradient(
            prevPoint.x, prevPoint.y,
            point.x, point.y
          )
          gradient.addColorStop(0, `rgba(157, 114, 255, ${alpha * 0.8})`)
          gradient.addColorStop(0.5, `rgba(251, 191, 36, ${alpha * 0.6})`)
          gradient.addColorStop(1, `rgba(239, 68, 68, ${alpha * 0.4})`)

          ctx.strokeStyle = gradient
          ctx.lineWidth = Math.max(1, alpha * 3)
          ctx.lineCap = 'round'
          ctx.lineJoin = 'round'

          ctx.beginPath()
          ctx.moveTo(prevPoint.x, prevPoint.y)
          ctx.lineTo(point.x, point.y)
          ctx.stroke()
        }

        // Draw glow effect at cursor
        if (trailRef.current.length > 0) {
          const lastPoint = trailRef.current[trailRef.current.length - 1]
          const glowGradient = ctx.createRadialGradient(
            lastPoint.x, lastPoint.y, 0,
            lastPoint.x, lastPoint.y, 20
          )
          glowGradient.addColorStop(0, 'rgba(157, 114, 255, 0.8)')
          glowGradient.addColorStop(0.5, 'rgba(251, 191, 36, 0.4)')
          glowGradient.addColorStop(1, 'rgba(157, 114, 255, 0)')

          ctx.fillStyle = glowGradient
          ctx.beginPath()
          ctx.arc(lastPoint.x, lastPoint.y, 20, 0, Math.PI * 2)
          ctx.fill()
        }
      }

      requestAnimationFrame(animate)
    }

    animate()

    return () => {
      window.removeEventListener('resize', resizeCanvas)
      window.removeEventListener('mousemove', handleMouseMove)
    }
  }, [])

  return (
    <motion.canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 1 }}
      style={{
        mixBlendMode: 'screen',
      }}
    />
  )
}

export default CursorTrail
