// PIXELgarage Royal Premium Platform - 3D Tilt Card Effect
// Ultra-premium 3D tilt effect for cards and containers

'use client'

import { ReactNode, useRef, useEffect, useState } from 'react'
import { motion } from 'framer-motion'

interface TiltCardProps {
  children: ReactNode
  className?: string
  tiltMaxAngle?: number
  perspective?: number
  scale?: number
  speed?: number
  glowEffect?: boolean
  shadowEffect?: boolean
}

const TiltCard = ({
  children,
  className = '',
  tiltMaxAngle = 15,
  perspective = 1000,
  scale = 1.05,
  speed = 300,
  glowEffect = true,
  shadowEffect = true
}: TiltCardProps) => {
  const cardRef = useRef<HTMLDivElement>(null)
  const [isHovered, setIsHovered] = useState(false)
  const [transform, setTransform] = useState('')

  useEffect(() => {
    const card = cardRef.current
    if (!card) return

    const handleMouseMove = (e: MouseEvent) => {
      const rect = card.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top
      
      const centerX = rect.width / 2
      const centerY = rect.height / 2
      
      const rotateX = ((y - centerY) / centerY) * -tiltMaxAngle
      const rotateY = ((x - centerX) / centerX) * tiltMaxAngle
      
      const transformString = `
        perspective(${perspective}px) 
        rotateX(${rotateX}deg) 
        rotateY(${rotateY}deg) 
        scale3d(${scale}, ${scale}, ${scale})
      `
      
      setTransform(transformString)
    }

    const handleMouseEnter = () => {
      setIsHovered(true)
    }

    const handleMouseLeave = () => {
      setIsHovered(false)
      setTransform(`
        perspective(${perspective}px) 
        rotateX(0deg) 
        rotateY(0deg) 
        scale3d(1, 1, 1)
      `)
    }

    card.addEventListener('mousemove', handleMouseMove)
    card.addEventListener('mouseenter', handleMouseEnter)
    card.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      card.removeEventListener('mousemove', handleMouseMove)
      card.removeEventListener('mouseenter', handleMouseEnter)
      card.removeEventListener('mouseleave', handleMouseLeave)
    }
  }, [tiltMaxAngle, perspective, scale])

  return (
    <motion.div
      ref={cardRef}
      className={`relative cursor-pointer ${className}`}
      style={{
        transform,
        transformStyle: 'preserve-3d',
        transition: `transform ${speed}ms ease-out`,
      }}
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, ease: 'easeOut' }}
    >
      {/* Main content */}
      <div className="relative z-10">
        {children}
      </div>

      {/* Glow effect */}
      {glowEffect && isHovered && (
        <motion.div
          className="absolute inset-0 rounded-2xl"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          style={{
            background: 'linear-gradient(45deg, rgba(157, 114, 255, 0.1), rgba(251, 191, 36, 0.1))',
            filter: 'blur(20px)',
            transform: 'translateZ(-10px)',
          }}
        />
      )}

      {/* Enhanced shadow */}
      {shadowEffect && (
        <motion.div
          className="absolute inset-0 rounded-2xl"
          style={{
            background: 'linear-gradient(45deg, rgba(157, 114, 255, 0.2), rgba(251, 191, 36, 0.2))',
            filter: 'blur(30px)',
            transform: 'translateZ(-20px)',
            opacity: isHovered ? 0.6 : 0.3,
            transition: `opacity ${speed}ms ease-out`,
          }}
        />
      )}

      {/* Reflection effect */}
      {isHovered && (
        <motion.div
          className="absolute inset-0 rounded-2xl overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          style={{
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%)',
            transform: 'translateZ(1px)',
          }}
        />
      )}

      {/* Particle effects on hover */}
      {isHovered && (
        <div className="absolute inset-0 pointer-events-none">
          {Array.from({ length: 6 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-royal-400 rounded-full"
              style={{
                left: `${20 + i * 15}%`,
                top: `${30 + (i % 2) * 40}%`,
              }}
              initial={{ scale: 0, opacity: 0 }}
              animate={{
                scale: [0, 1, 0],
                opacity: [0, 1, 0],
                y: [0, -20, -40],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.2,
                ease: 'easeOut'
              }}
            />
          ))}
        </div>
      )}

      {/* Border glow animation */}
      {isHovered && (
        <motion.div
          className="absolute inset-0 rounded-2xl"
          style={{
            background: 'linear-gradient(45deg, transparent, rgba(157, 114, 255, 0.3), transparent, rgba(251, 191, 36, 0.3), transparent)',
            backgroundSize: '400% 400%',
            padding: '2px',
            transform: 'translateZ(-1px)',
          }}
          animate={{
            backgroundPosition: ['0% 0%', '100% 100%', '0% 0%']
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: 'linear'
          }}
        >
          <div className="w-full h-full bg-transparent rounded-2xl" />
        </motion.div>
      )}
    </motion.div>
  )
}

export default TiltCard
