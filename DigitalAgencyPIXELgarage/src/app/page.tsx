// PIXELgarage Royal Premium Platform - Main Page
// Universe-grade landing experience with cinematic animations

'use client'

import { useEffect, useRef } from 'react'
import { motion, useScroll, useTransform } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { ArrowRight, Sparkles, Crown, Zap, Star } from 'lucide-react'
import HeroSection from '@/components/sections/HeroSection'
import ServicesSection from '@/components/sections/ServicesSection'
import PricingSection from '@/components/sections/PricingSection'
import PortfolioSection from '@/components/sections/PortfolioSection'
import ContactSection from '@/components/sections/ContactSection'
import Navigation from '@/components/layout/Navigation'
import Footer from '@/components/layout/Footer'
import MatrixRain from '@/components/effects/MatrixRain'
import ParticleSystem from '@/components/effects/ParticleSystem'
import SoundEffects from '@/components/effects/SoundEffects'
import CursorTrail from '@/components/effects/CursorTrail'

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger)
}

export default function HomePage() {
  const containerRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll()

  // Transform values for parallax effects
  const backgroundY = useTransform(scrollYProgress, [0, 1], ['0%', '50%'])
  const textY = useTransform(scrollYProgress, [0, 1], ['0%', '200%'])

  useEffect(() => {
    // Initialize GSAP animations
    const ctx = gsap.context(() => {
      // Hero entrance animation
      gsap.timeline()
        .from('.hero-title', {
          duration: 1.2,
          y: 100,
          opacity: 0,
          ease: 'power4.out',
          stagger: 0.2,
        })
        .from('.hero-subtitle', {
          duration: 1,
          y: 50,
          opacity: 0,
          ease: 'power3.out',
        }, '-=0.8')
        .from('.hero-description', {
          duration: 1,
          y: 30,
          opacity: 0,
          ease: 'power2.out',
        }, '-=0.6')
        .from('.hero-cta', {
          duration: 0.8,
          y: 20,
          opacity: 0,
          ease: 'power2.out',
          stagger: 0.1,
        }, '-=0.4')

      // Floating elements animation
      gsap.to('.floating-element', {
        y: -20,
        duration: 2,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
        stagger: 0.3,
      })

      // Scroll-triggered animations
      gsap.utils.toArray('.animate-on-scroll').forEach((element: any) => {
        gsap.fromTo(element, 
          {
            opacity: 0,
            y: 100,
          },
          {
            opacity: 1,
            y: 0,
            duration: 1,
            ease: 'power3.out',
            scrollTrigger: {
              trigger: element,
              start: 'top 80%',
              end: 'bottom 20%',
              toggleActions: 'play none none reverse',
            },
          }
        )
      })

      // Parallax backgrounds
      gsap.utils.toArray('.parallax-bg').forEach((element: any) => {
        gsap.to(element, {
          yPercent: -50,
          ease: 'none',
          scrollTrigger: {
            trigger: element,
            start: 'top bottom',
            end: 'bottom top',
            scrub: true,
          },
        })
      })

    }, containerRef)

    return () => ctx.revert()
  }, [])

  return (
    <div ref={containerRef} className="relative overflow-hidden">
      {/* Ultra Premium Effects */}
      <MatrixRain />
      <ParticleSystem />
      <SoundEffects />
      <CursorTrail />

      {/* Ultra Premium Background Elements */}
      <div className="fixed inset-0 -z-10 overflow-hidden">
        {/* Dynamic gradient background */}
        <motion.div
          style={{ y: backgroundY }}
          className="absolute inset-0"
          animate={{
            background: [
              'radial-gradient(circle at 20% 50%, rgba(157, 114, 255, 0.15) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(251, 191, 36, 0.15) 0%, transparent 50%), linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%)',
              'radial-gradient(circle at 80% 80%, rgba(157, 114, 255, 0.2) 0%, transparent 50%), radial-gradient(circle at 20% 80%, rgba(251, 191, 36, 0.2) 0%, transparent 50%), linear-gradient(135deg, #1e293b 0%, #0f172a 50%, #1e293b 100%)',
              'radial-gradient(circle at 20% 50%, rgba(157, 114, 255, 0.15) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(251, 191, 36, 0.15) 0%, transparent 50%), linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%)'
            ]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />

        {/* Premium particle system */}
        <div className="absolute inset-0">
          {Array.from({ length: 50 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute rounded-full floating-element"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                width: `${2 + Math.random() * 4}px`,
                height: `${2 + Math.random() * 4}px`,
                background: i % 3 === 0 ? '#9d72ff' : i % 3 === 1 ? '#f59e0b' : '#64748b',
              }}
              animate={{
                opacity: [0.1, 0.8, 0.1],
                scale: [0.5, 2, 0.5],
                y: [0, -100, 0],
                x: [0, Math.random() * 50 - 25, 0],
              }}
              transition={{
                duration: 8 + Math.random() * 4,
                repeat: Infinity,
                delay: Math.random() * 5,
                ease: 'easeInOut'
              }}
            />
          ))}
        </div>

        {/* Animated royal symbols */}
        <motion.div
          className="absolute top-20 right-20 opacity-20"
          animate={{
            rotate: [0, 360],
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.3, 0.1]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: 'linear'
          }}
        >
          <Crown className="w-40 h-40 text-gold-400" />
        </motion.div>

        <motion.div
          className="absolute bottom-40 left-20 opacity-20"
          animate={{
            rotate: [360, 0],
            scale: [1, 1.3, 1],
            opacity: [0.1, 0.4, 0.1]
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        >
          <Sparkles className="w-32 h-32 text-royal-400" />
        </motion.div>

        <motion.div
          className="absolute top-1/2 right-1/4 opacity-20"
          animate={{
            rotate: [0, -360],
            scale: [1, 1.5, 1],
            opacity: [0.1, 0.5, 0.1]
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        >
          <Star className="w-24 h-24 text-gold-500" />
        </motion.div>

        {/* Premium light rays */}
        <motion.div
          className="absolute top-0 left-1/4 w-1 h-full bg-gradient-to-b from-transparent via-royal-400/30 to-transparent"
          animate={{
            opacity: [0, 0.6, 0],
            scaleY: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />
        <motion.div
          className="absolute top-0 right-1/3 w-1 h-full bg-gradient-to-b from-transparent via-gold-400/30 to-transparent"
          animate={{
            opacity: [0, 0.8, 0],
            scaleY: [0.3, 1, 0.3]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2
          }}
        />
      </div>

      {/* Navigation */}
      <Navigation />

      {/* Main Content */}
      <main className="relative z-10">
        {/* Hero Section */}
        <HeroSection />

        {/* Services Section */}
        <ServicesSection />

        {/* Portfolio Section */}
        <PortfolioSection />

        {/* Pricing Section */}
        <PricingSection />

        {/* Contact Section */}
        <ContactSection />
      </main>

      {/* Footer */}
      <Footer />

      {/* Scroll Progress Indicator */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-gradient-royal z-50 origin-left"
        style={{ scaleX: scrollYProgress }}
      />

      {/* Royal Premium Badge */}
      <div className="fixed bottom-8 right-8 z-40">
        <motion.div
          className="glass rounded-full p-4 glow-hover cursor-pointer"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        >
          <Crown className="w-6 h-6 text-gold-400" />
        </motion.div>
      </div>
    </div>
  )
}
