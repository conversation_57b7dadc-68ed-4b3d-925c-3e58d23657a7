// PIXELgarage Royal Premium Platform - Stripe Integration
// Professional payment processing with live keys

import <PERSON><PERSON> from 'stripe'

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not defined in environment variables')
}

// Initialize Stripe with live keys
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-06-20',
  typescript: true,
})

// Client-side Stripe
export const getStripePublishableKey = () => {
  if (!process.env.STRIPE_PUBLISHABLE_KEY) {
    throw new Error('STRIPE_PUBLISHABLE_KEY is not defined')
  }
  return process.env.STRIPE_PUBLISHABLE_KEY
}

// Payment Intent Creation
export async function createPaymentIntent(
  amount: number,
  currency: string = 'pln',
  metadata?: Record<string, string>
) {
  try {
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: currency.toLowerCase(),
      automatic_payment_methods: {
        enabled: true,
      },
      metadata: {
        platform: 'PIXELgarage',
        ...metadata,
      },
    })

    return {
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
    }
  } catch (error) {
    console.error('Error creating payment intent:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}

// Subscription Creation (for recurring services)
export async function createSubscription(
  customerId: string,
  priceId: string,
  metadata?: Record<string, string>
) {
  try {
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent'],
      metadata: {
        platform: 'PIXELgarage',
        ...metadata,
      },
    })

    return {
      success: true,
      subscription,
      clientSecret: (subscription.latest_invoice as any)?.payment_intent?.client_secret,
    }
  } catch (error) {
    console.error('Error creating subscription:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}

// Customer Creation
export async function createCustomer(
  email: string,
  name?: string,
  phone?: string,
  metadata?: Record<string, string>
) {
  try {
    const customer = await stripe.customers.create({
      email,
      name,
      phone,
      metadata: {
        platform: 'PIXELgarage',
        ...metadata,
      },
    })

    return {
      success: true,
      customer,
    }
  } catch (error) {
    console.error('Error creating customer:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}

// Price Creation (for services)
export async function createPrice(
  amount: number,
  currency: string = 'pln',
  productName: string,
  recurring?: {
    interval: 'month' | 'year'
    interval_count?: number
  }
) {
  try {
    // First create a product
    const product = await stripe.products.create({
      name: productName,
      metadata: {
        platform: 'PIXELgarage',
      },
    })

    // Then create a price for the product
    const price = await stripe.prices.create({
      unit_amount: Math.round(amount * 100),
      currency: currency.toLowerCase(),
      product: product.id,
      recurring: recurring || undefined,
    })

    return {
      success: true,
      product,
      price,
    }
  } catch (error) {
    console.error('Error creating price:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}

// Webhook signature verification
export function verifyWebhookSignature(
  payload: string | Buffer,
  signature: string,
  secret: string
) {
  try {
    return stripe.webhooks.constructEvent(payload, signature, secret)
  } catch (error) {
    console.error('Webhook signature verification failed:', error)
    throw error
  }
}

// Payment status check
export async function getPaymentStatus(paymentIntentId: string) {
  try {
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId)
    return {
      success: true,
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      metadata: paymentIntent.metadata,
    }
  } catch (error) {
    console.error('Error retrieving payment status:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}

export default stripe
